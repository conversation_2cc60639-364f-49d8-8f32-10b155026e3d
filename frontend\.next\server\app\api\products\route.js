/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/products/route";
exports.ids = ["app/api/products/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fproducts%2Froute&page=%2Fapi%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Froute.ts&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CGraduation%20Toqs%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CGraduation%20Toqs%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fproducts%2Froute&page=%2Fapi%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Froute.ts&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CGraduation%20Toqs%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CGraduation%20Toqs%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_faiss_Desktop_Graduation_Toqs_frontend_src_app_api_products_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/products/route.ts */ \"(rsc)/./src/app/api/products/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([C_Users_faiss_Desktop_Graduation_Toqs_frontend_src_app_api_products_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nC_Users_faiss_Desktop_Graduation_Toqs_frontend_src_app_api_products_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/products/route\",\n        pathname: \"/api/products\",\n        filename: \"route\",\n        bundlePath: \"app/api/products/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Graduation Toqs\\\\frontend\\\\src\\\\app\\\\api\\\\products\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_faiss_Desktop_Graduation_Toqs_frontend_src_app_api_products_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fproducts%2Froute&page=%2Fapi%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Froute.ts&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CGraduation%20Toqs%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CGraduation%20Toqs%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/products/route.ts":
/*!***************************************!*\
  !*** ./src/app/api/products/route.ts ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_models_Product__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/models/Product */ \"(rsc)/./src/lib/models/Product.ts\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/database */ \"(rsc)/./src/lib/database.ts\");\n/* harmony import */ var _lib_mockData__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/mockData */ \"(rsc)/./src/lib/mockData.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_models_Product__WEBPACK_IMPORTED_MODULE_1__, _lib_database__WEBPACK_IMPORTED_MODULE_2__]);\n([_lib_models_Product__WEBPACK_IMPORTED_MODULE_1__, _lib_database__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n// GET - جلب جميع المنتجات (مع fallback للبيانات الوهمية)\nasync function GET(request) {\n    try {\n        // التحقق من حالة قاعدة البيانات\n        const isHealthy = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_2__.checkDatabaseHealth)();\n        if (!isHealthy) {\n            console.log('قاعدة البيانات غير متاحة، استخدام البيانات الوهمية...');\n            // استخدام البيانات الوهمية كبديل\n            const { searchParams } = new URL(request.url);\n            const category = searchParams.get('category');\n            const search = searchParams.get('search');\n            const page = parseInt(searchParams.get('page') || '1');\n            const limit = parseInt(searchParams.get('limit') || '12');\n            let products = _lib_mockData__WEBPACK_IMPORTED_MODULE_3__.MockDataManager.getProducts();\n            // تطبيق الفلاتر\n            if (category && category !== 'all') {\n                products = products.filter((p)=>p.category === category);\n            }\n            if (search) {\n                const searchLower = search.toLowerCase();\n                products = products.filter((p)=>p.name.toLowerCase().includes(searchLower) || p.description.toLowerCase().includes(searchLower));\n            }\n            // تطبيق التصفح\n            const offset = (page - 1) * limit;\n            const paginatedProducts = products.slice(offset, offset + limit);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                products: paginatedProducts,\n                total: products.length,\n                page: page,\n                totalPages: Math.ceil(products.length / limit),\n                source: 'mock_data'\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        // استخراج معاملات الفلترة\n        const filters = {\n            category: searchParams.get('category') || undefined,\n            available: searchParams.get('available') === 'true' ? true : searchParams.get('available') === 'false' ? false : undefined,\n            published: searchParams.get('published') === 'true' ? true : searchParams.get('published') === 'false' ? false : undefined,\n            search: searchParams.get('search') || undefined,\n            minPrice: searchParams.get('minPrice') ? parseFloat(searchParams.get('minPrice')) : undefined,\n            maxPrice: searchParams.get('maxPrice') ? parseFloat(searchParams.get('maxPrice')) : undefined,\n            limit: searchParams.get('limit') ? parseInt(searchParams.get('limit')) : 50,\n            offset: searchParams.get('offset') ? parseInt(searchParams.get('offset')) : 0,\n            sortBy: searchParams.get('sortBy') || 'created_at',\n            sortOrder: searchParams.get('sortOrder') || 'DESC'\n        };\n        // جلب المنتجات من قاعدة البيانات\n        const result = await _lib_models_Product__WEBPACK_IMPORTED_MODULE_1__.ProductModel.getAll(filters);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            products: result.products,\n            total: result.total,\n            page: Math.floor(filters.offset / filters.limit) + 1,\n            totalPages: Math.ceil(result.total / filters.limit),\n            filters: filters,\n            source: 'postgresql'\n        });\n    } catch (error) {\n        console.error('Error fetching products:', error);\n        // في حالة فشل قاعدة البيانات، استخدم البيانات الوهمية\n        console.log('فشل في الاتصال بقاعدة البيانات، استخدام البيانات الوهمية...');\n        const { searchParams } = new URL(request.url);\n        const category = searchParams.get('category');\n        const search = searchParams.get('search');\n        const page = parseInt(searchParams.get('page') || '1');\n        const limit = parseInt(searchParams.get('limit') || '12');\n        let products = _lib_mockData__WEBPACK_IMPORTED_MODULE_3__.MockDataManager.getProducts();\n        // تطبيق الفلاتر\n        if (category && category !== 'all') {\n            products = products.filter((p)=>p.category === category);\n        }\n        if (search) {\n            const searchLower = search.toLowerCase();\n            products = products.filter((p)=>p.name.toLowerCase().includes(searchLower) || p.description.toLowerCase().includes(searchLower));\n        }\n        // تطبيق التصفح\n        const offset = (page - 1) * limit;\n        const paginatedProducts = products.slice(offset, offset + limit);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            products: paginatedProducts,\n            total: products.length,\n            page: page,\n            totalPages: Math.ceil(products.length / limit),\n            source: 'mock_data_fallback'\n        });\n    }\n}\n// POST - إنشاء منتج جديد (مع fallback للبيانات الوهمية)\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        // التحقق من البيانات المطلوبة\n        if (!body.name || !body.price) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'اسم المنتج والسعر مطلوبان'\n            }, {\n                status: 400\n            });\n        }\n        // التحقق من حالة قاعدة البيانات\n        const isHealthy = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_2__.checkDatabaseHealth)();\n        if (!isHealthy) {\n            console.log('قاعدة البيانات غير متاحة، استخدام البيانات الوهمية...');\n            // إنشاء منتج جديد في البيانات الوهمية\n            const newProduct = {\n                id: _lib_mockData__WEBPACK_IMPORTED_MODULE_3__.MockDataManager.generateId(),\n                name: body.name,\n                description: body.description || '',\n                category: body.category || 'gown',\n                price: parseFloat(body.price),\n                rental_price: body.rental_price ? parseFloat(body.rental_price) : undefined,\n                colors: Array.isArray(body.colors) ? body.colors : [],\n                sizes: Array.isArray(body.sizes) ? body.sizes : [],\n                images: Array.isArray(body.images) ? body.images : [],\n                stock_quantity: parseInt(body.stock_quantity) || 0,\n                is_available: body.is_available ?? true,\n                is_published: body.is_published ?? true,\n                created_at: new Date().toISOString(),\n                updated_at: new Date().toISOString(),\n                rating: 0,\n                reviews_count: 0,\n                features: Array.isArray(body.features) ? body.features : [],\n                specifications: body.specifications || {}\n            };\n            const products = _lib_mockData__WEBPACK_IMPORTED_MODULE_3__.MockDataManager.getProducts();\n            products.push(newProduct);\n            _lib_mockData__WEBPACK_IMPORTED_MODULE_3__.MockDataManager.saveProducts(products);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                message: 'تم إضافة المنتج بنجاح',\n                product: newProduct,\n                source: 'mock_data'\n            }, {\n                status: 201\n            });\n        }\n        // إنشاء المنتج في قاعدة البيانات\n        const product = await _lib_models_Product__WEBPACK_IMPORTED_MODULE_1__.ProductModel.create({\n            name: body.name,\n            description: body.description || '',\n            category_id: body.category_id,\n            price: parseFloat(body.price),\n            rental_price: body.rental_price ? parseFloat(body.rental_price) : undefined,\n            colors: Array.isArray(body.colors) ? body.colors : [],\n            sizes: Array.isArray(body.sizes) ? body.sizes : [],\n            images: Array.isArray(body.images) ? body.images : [],\n            stock_quantity: parseInt(body.stock_quantity) || 0,\n            is_available: body.is_available ?? true,\n            is_published: body.is_published ?? true,\n            features: Array.isArray(body.features) ? body.features : [],\n            specifications: body.specifications || {},\n            rating: 0,\n            reviews_count: 0\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: 'تم إنشاء المنتج بنجاح',\n            product: product\n        }, {\n            status: 201\n        });\n    } catch (error) {\n        console.error('Error creating product:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'فشل في إنشاء المنتج'\n        }, {\n            status: 500\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/products/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkDatabaseHealth: () => (/* binding */ checkDatabaseHealth),\n/* harmony export */   closePool: () => (/* binding */ closePool),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getConnection: () => (/* binding */ getConnection),\n/* harmony export */   initializeDatabase: () => (/* binding */ initializeDatabase),\n/* harmony export */   query: () => (/* binding */ query),\n/* harmony export */   transaction: () => (/* binding */ transaction)\n/* harmony export */ });\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pg */ \"pg\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([pg__WEBPACK_IMPORTED_MODULE_0__]);\npg__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n// إعداد اتصال PostgreSQL\nconst pool = new pg__WEBPACK_IMPORTED_MODULE_0__.Pool({\n    user: process.env.POSTGRES_USER || 'postgres',\n    host: process.env.POSTGRES_HOST || 'localhost',\n    database: process.env.POSTGRES_DB || 'graduation_platform',\n    password: process.env.POSTGRES_PASSWORD || 'password',\n    port: parseInt(process.env.POSTGRES_PORT || '5432'),\n    max: 20,\n    idleTimeoutMillis: 30000,\n    connectionTimeoutMillis: 2000,\n    ssl:  false ? 0 : false\n});\n// دالة للحصول على اتصال من المجموعة\nasync function getConnection() {\n    try {\n        const client = await pool.connect();\n        return client;\n    } catch (error) {\n        console.error('خطأ في الاتصال بقاعدة البيانات:', error);\n        throw new Error('فشل في الاتصال بقاعدة البيانات');\n    }\n}\n// دالة لتنفيذ استعلام مع معاملات\nasync function query(text, params) {\n    const client = await getConnection();\n    try {\n        const start = Date.now();\n        const result = await client.query(text, params);\n        const duration = Date.now() - start;\n        // تسجيل الاستعلامات في وضع التطوير\n        if (true) {\n            console.log('Executed query:', {\n                text,\n                duration,\n                rows: result.rowCount\n            });\n        }\n        return result;\n    } catch (error) {\n        console.error('خطأ في تنفيذ الاستعلام:', error);\n        throw error;\n    } finally{\n        client.release();\n    }\n}\n// دالة لتنفيذ معاملة (transaction)\nasync function transaction(callback) {\n    const client = await getConnection();\n    try {\n        await client.query('BEGIN');\n        const result = await callback(client);\n        await client.query('COMMIT');\n        return result;\n    } catch (error) {\n        await client.query('ROLLBACK');\n        throw error;\n    } finally{\n        client.release();\n    }\n}\n// دالة لإغلاق مجموعة الاتصالات\nasync function closePool() {\n    await pool.end();\n}\n// دالة للتحقق من حالة قاعدة البيانات\nasync function checkDatabaseHealth() {\n    try {\n        const result = await query('SELECT NOW() as current_time');\n        return result.rows.length > 0;\n    } catch (error) {\n        console.error('فشل في فحص حالة قاعدة البيانات:', error);\n        return false;\n    }\n}\n// دالة لإنشاء الجداول الأساسية\nasync function initializeDatabase() {\n    try {\n        console.log('بدء تهيئة قاعدة البيانات...');\n        // إنشاء جدول المستخدمين\n        await query(`\n      CREATE TABLE IF NOT EXISTS users (\n        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\n        email VARCHAR(255) UNIQUE NOT NULL,\n        password_hash VARCHAR(255) NOT NULL,\n        first_name VARCHAR(100) NOT NULL,\n        last_name VARCHAR(100) NOT NULL,\n        phone VARCHAR(20),\n        role VARCHAR(20) DEFAULT 'customer' CHECK (role IN ('admin', 'customer', 'school', 'delivery')),\n        is_active BOOLEAN DEFAULT true,\n        email_verified BOOLEAN DEFAULT false,\n        profile_image TEXT,\n        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\n      )\n    `);\n        // إنشاء جدول الفئات\n        await query(`\n      CREATE TABLE IF NOT EXISTS categories (\n        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\n        name_ar VARCHAR(100) NOT NULL,\n        name_en VARCHAR(100),\n        name_fr VARCHAR(100),\n        slug VARCHAR(100) UNIQUE NOT NULL,\n        description TEXT,\n        icon VARCHAR(50),\n        parent_id UUID REFERENCES categories(id) ON DELETE CASCADE,\n        order_index INTEGER DEFAULT 0,\n        is_active BOOLEAN DEFAULT true,\n        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\n      )\n    `);\n        // إنشاء جدول المنتجات\n        await query(`\n      CREATE TABLE IF NOT EXISTS products (\n        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\n        name VARCHAR(255) NOT NULL,\n        description TEXT,\n        category_id UUID REFERENCES categories(id) ON DELETE SET NULL,\n        price DECIMAL(10,2) NOT NULL,\n        rental_price DECIMAL(10,2),\n        colors TEXT[] DEFAULT '{}',\n        sizes TEXT[] DEFAULT '{}',\n        images TEXT[] DEFAULT '{}',\n        stock_quantity INTEGER DEFAULT 0,\n        is_available BOOLEAN DEFAULT true,\n        is_published BOOLEAN DEFAULT true,\n        features TEXT[] DEFAULT '{}',\n        specifications JSONB DEFAULT '{}',\n        rating DECIMAL(3,2) DEFAULT 0,\n        reviews_count INTEGER DEFAULT 0,\n        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\n      )\n    `);\n        // إنشاء جدول المدارس\n        await query(`\n      CREATE TABLE IF NOT EXISTS schools (\n        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\n        admin_id UUID REFERENCES users(id) ON DELETE CASCADE,\n        name VARCHAR(255) NOT NULL,\n        name_en VARCHAR(255),\n        name_fr VARCHAR(255),\n        address TEXT,\n        city VARCHAR(100),\n        phone VARCHAR(20),\n        email VARCHAR(255),\n        website VARCHAR(255),\n        logo_url TEXT,\n        graduation_date DATE,\n        student_count INTEGER DEFAULT 0,\n        is_active BOOLEAN DEFAULT true,\n        settings JSONB DEFAULT '{}',\n        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\n      )\n    `);\n        // إنشاء جدول الطلبات\n        await query(`\n      CREATE TABLE IF NOT EXISTS orders (\n        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\n        user_id UUID REFERENCES users(id) ON DELETE CASCADE,\n        school_id UUID REFERENCES schools(id) ON DELETE SET NULL,\n        order_number VARCHAR(50) UNIQUE NOT NULL,\n        status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled')),\n        total_amount DECIMAL(10,2) NOT NULL,\n        shipping_amount DECIMAL(10,2) DEFAULT 0,\n        tax_amount DECIMAL(10,2) DEFAULT 0,\n        discount_amount DECIMAL(10,2) DEFAULT 0,\n        payment_method VARCHAR(50),\n        payment_status VARCHAR(20) DEFAULT 'pending' CHECK (payment_status IN ('pending', 'paid', 'failed', 'refunded')),\n        shipping_address JSONB,\n        billing_address JSONB,\n        notes TEXT,\n        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\n      )\n    `);\n        // إنشاء جدول عناصر الطلب\n        await query(`\n      CREATE TABLE IF NOT EXISTS order_items (\n        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\n        order_id UUID REFERENCES orders(id) ON DELETE CASCADE,\n        product_id UUID REFERENCES products(id) ON DELETE CASCADE,\n        quantity INTEGER NOT NULL,\n        unit_price DECIMAL(10,2) NOT NULL,\n        total_price DECIMAL(10,2) NOT NULL,\n        type VARCHAR(20) DEFAULT 'purchase' CHECK (type IN ('purchase', 'rental')),\n        size VARCHAR(50),\n        color VARCHAR(50),\n        customizations JSONB DEFAULT '{}',\n        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\n      )\n    `);\n        // إنشاء جدول التقييمات\n        await query(`\n      CREATE TABLE IF NOT EXISTS reviews (\n        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\n        user_id UUID REFERENCES users(id) ON DELETE CASCADE,\n        product_id UUID REFERENCES products(id) ON DELETE CASCADE,\n        order_id UUID REFERENCES orders(id) ON DELETE CASCADE,\n        rating INTEGER CHECK (rating >= 1 AND rating <= 5),\n        comment TEXT,\n        images TEXT[] DEFAULT '{}',\n        is_verified BOOLEAN DEFAULT false,\n        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n        UNIQUE(user_id, product_id, order_id)\n      )\n    `);\n        // إنشاء جدول عناصر القائمة\n        await query(`\n      CREATE TABLE IF NOT EXISTS menu_items (\n        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\n        title_ar VARCHAR(100) NOT NULL,\n        title_en VARCHAR(100),\n        title_fr VARCHAR(100),\n        slug VARCHAR(100) NOT NULL,\n        icon VARCHAR(50),\n        parent_id UUID REFERENCES menu_items(id) ON DELETE CASCADE,\n        order_index INTEGER DEFAULT 0,\n        is_active BOOLEAN DEFAULT true,\n        target_type VARCHAR(20) DEFAULT 'internal' CHECK (target_type IN ('internal', 'external', 'page')),\n        target_value VARCHAR(255),\n        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\n      )\n    `);\n        // إنشاء الفهارس لتحسين الأداء\n        await query('CREATE INDEX IF NOT EXISTS idx_products_category ON products(category_id)');\n        await query('CREATE INDEX IF NOT EXISTS idx_products_published ON products(is_published)');\n        await query('CREATE INDEX IF NOT EXISTS idx_products_available ON products(is_available)');\n        await query('CREATE INDEX IF NOT EXISTS idx_orders_user ON orders(user_id)');\n        await query('CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status)');\n        await query('CREATE INDEX IF NOT EXISTS idx_order_items_order ON order_items(order_id)');\n        await query('CREATE INDEX IF NOT EXISTS idx_reviews_product ON reviews(product_id)');\n        await query('CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)');\n        await query('CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)');\n        console.log('تم تهيئة قاعدة البيانات بنجاح!');\n    } catch (error) {\n        console.error('خطأ في تهيئة قاعدة البيانات:', error);\n        throw error;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (pool);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/mockData.ts":
/*!*****************************!*\
  !*** ./src/lib/mockData.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MockDataManager: () => (/* binding */ MockDataManager),\n/* harmony export */   mockCategories: () => (/* binding */ mockCategories),\n/* harmony export */   mockMenuItems: () => (/* binding */ mockMenuItems),\n/* harmony export */   mockOrders: () => (/* binding */ mockOrders),\n/* harmony export */   mockPages: () => (/* binding */ mockPages),\n/* harmony export */   mockProducts: () => (/* binding */ mockProducts),\n/* harmony export */   mockSchools: () => (/* binding */ mockSchools)\n/* harmony export */ });\n// بيانات وهمية للتطوير والاختبار\n// بيانات وهمية للصفحات\nconst mockPages = [\n    {\n        id: '1',\n        slug: 'about-us',\n        is_published: true,\n        author_id: 'admin-1',\n        featured_image: '/images/about-hero.jpg',\n        created_at: '2024-01-15T10:00:00Z',\n        updated_at: '2024-01-20T14:30:00Z',\n        profiles: {\n            full_name: 'مدير النظام'\n        },\n        page_content: [\n            {\n                id: '1-ar',\n                page_id: '1',\n                language: 'ar',\n                title: 'من نحن',\n                content: '<h2>مرحباً بكم في منصة أزياء التخرج</h2><p>نحن منصة متخصصة في تأجير وبيع أزياء التخرج المغربية الأصيلة. نهدف إلى جعل يوم تخرجكم لا يُنسى من خلال توفير أجمل الأزياء التقليدية.</p><p>تأسست منصتنا عام 2024 بهدف خدمة الطلاب والطالبات في جميع أنحاء المغرب، ونفتخر بتقديم خدمات عالية الجودة وأسعار مناسبة.</p>',\n                meta_description: 'تعرف على منصة أزياء التخرج المغربية - خدمات تأجير وبيع الأزياء التقليدية',\n                meta_keywords: 'أزياء التخرج، المغرب، تأجير، بيع، تقليدي'\n            },\n            {\n                id: '1-en',\n                page_id: '1',\n                language: 'en',\n                title: 'About Us',\n                content: '<h2>Welcome to Graduation Attire Platform</h2><p>We are a specialized platform for renting and selling authentic Moroccan graduation attire. We aim to make your graduation day unforgettable by providing the most beautiful traditional outfits.</p><p>Our platform was founded in 2024 to serve students throughout Morocco, and we pride ourselves on providing high-quality services at affordable prices.</p>',\n                meta_description: 'Learn about the Moroccan Graduation Attire Platform - traditional outfit rental and sales services',\n                meta_keywords: 'graduation attire, Morocco, rental, sales, traditional'\n            }\n        ]\n    },\n    {\n        id: '2',\n        slug: 'services',\n        is_published: true,\n        author_id: 'admin-1',\n        created_at: '2024-01-16T09:00:00Z',\n        updated_at: '2024-01-22T16:45:00Z',\n        profiles: {\n            full_name: 'مدير النظام'\n        },\n        page_content: [\n            {\n                id: '2-ar',\n                page_id: '2',\n                language: 'ar',\n                title: 'خدماتنا',\n                content: '<h2>خدماتنا المتميزة</h2><h3>تأجير الأزياء</h3><p>نوفر خدمة تأجير أزياء التخرج لفترات مرنة مع ضمان النظافة والجودة.</p><h3>بيع الأزياء</h3><p>إمكانية شراء الأزياء للاحتفاظ بها كذكرى جميلة من يوم التخرج.</p><h3>التخصيص</h3><p>خدمات تخصيص الأزياء حسب المقاسات والتفضيلات الشخصية.</p>',\n                meta_description: 'خدمات منصة أزياء التخرج - تأجير وبيع وتخصيص الأزياء التقليدية المغربية',\n                meta_keywords: 'خدمات، تأجير، بيع، تخصيص، أزياء التخرج'\n            }\n        ]\n    },\n    {\n        id: '3',\n        slug: 'contact',\n        is_published: true,\n        author_id: 'admin-1',\n        created_at: '2024-01-17T11:00:00Z',\n        updated_at: '2024-01-17T11:00:00Z',\n        profiles: {\n            full_name: 'مدير النظام'\n        },\n        page_content: [\n            {\n                id: '3-ar',\n                page_id: '3',\n                language: 'ar',\n                title: 'اتصل بنا',\n                content: '<h2>تواصل معنا</h2><p>نحن هنا لخدمتكم في أي وقت. يمكنكم التواصل معنا عبر:</p><ul><li>الهاتف: +212 5XX-XXXXXX</li><li>البريد الإلكتروني: <EMAIL></li><li>العنوان: الدار البيضاء، المغرب</li></ul>',\n                meta_description: 'تواصل مع منصة أزياء التخرج المغربية',\n                meta_keywords: 'اتصال، تواصل، خدمة العملاء'\n            }\n        ]\n    }\n];\n// بيانات وهمية للقوائم\nconst mockMenuItems = [\n    {\n        id: '1',\n        title_ar: 'الرئيسية',\n        title_en: 'Home',\n        title_fr: 'Accueil',\n        slug: 'home',\n        icon: 'Home',\n        order_index: 1,\n        is_active: true,\n        target_type: 'internal',\n        target_value: '/',\n        created_at: '2024-01-15T10:00:00Z',\n        updated_at: '2024-01-15T10:00:00Z'\n    },\n    {\n        id: '2',\n        title_ar: 'من نحن',\n        title_en: 'About Us',\n        title_fr: 'À propos',\n        slug: 'about',\n        icon: 'Info',\n        order_index: 2,\n        is_active: true,\n        target_type: 'internal',\n        target_value: '/about',\n        created_at: '2024-01-15T10:05:00Z',\n        updated_at: '2024-01-15T10:05:00Z'\n    },\n    {\n        id: '3',\n        title_ar: 'خدماتنا',\n        title_en: 'Services',\n        title_fr: 'Services',\n        slug: 'services',\n        icon: 'Settings',\n        order_index: 3,\n        is_active: true,\n        target_type: 'internal',\n        target_value: '/services',\n        created_at: '2024-01-15T10:10:00Z',\n        updated_at: '2024-01-15T10:10:00Z'\n    },\n    {\n        id: '4',\n        title_ar: 'المنتجات',\n        title_en: 'Products',\n        title_fr: 'Produits',\n        slug: 'products',\n        icon: 'Package',\n        order_index: 4,\n        is_active: true,\n        target_type: 'internal',\n        target_value: '/catalog',\n        created_at: '2024-01-15T10:15:00Z',\n        updated_at: '2024-01-15T10:15:00Z'\n    },\n    {\n        id: '5',\n        title_ar: 'تأجير الأزياء',\n        title_en: 'Rental',\n        title_fr: 'Location',\n        slug: 'rental',\n        parent_id: '4',\n        icon: 'Calendar',\n        order_index: 1,\n        is_active: true,\n        target_type: 'internal',\n        target_value: '/catalog?type=rental',\n        created_at: '2024-01-15T10:20:00Z',\n        updated_at: '2024-01-15T10:20:00Z'\n    },\n    {\n        id: '6',\n        title_ar: 'بيع الأزياء',\n        title_en: 'Sales',\n        title_fr: 'Vente',\n        slug: 'sales',\n        parent_id: '4',\n        icon: 'ShoppingCart',\n        order_index: 2,\n        is_active: true,\n        target_type: 'internal',\n        target_value: '/catalog?type=sale',\n        created_at: '2024-01-15T10:25:00Z',\n        updated_at: '2024-01-15T10:25:00Z'\n    },\n    {\n        id: '8',\n        title_ar: 'اتصل بنا',\n        title_en: 'Contact',\n        title_fr: 'Contact',\n        slug: 'contact',\n        icon: 'Phone',\n        order_index: 6,\n        is_active: true,\n        target_type: 'internal',\n        target_value: '/contact',\n        created_at: '2024-01-15T10:35:00Z',\n        updated_at: '2024-01-15T10:35:00Z'\n    }\n];\n// بيانات وهمية للفئات\nconst mockCategories = [\n    {\n        id: '1',\n        name_ar: 'أثواب التخرج',\n        name_en: 'Graduation Gowns',\n        name_fr: 'Robes de Graduation',\n        slug: 'gown',\n        icon: '👘',\n        description: 'أثواب التخرج الأكاديمية التقليدية',\n        is_active: true,\n        order_index: 1,\n        created_at: '2024-01-15T10:00:00Z',\n        updated_at: '2024-01-15T10:00:00Z'\n    },\n    {\n        id: '2',\n        name_ar: 'قبعات التخرج',\n        name_en: 'Graduation Caps',\n        name_fr: 'Chapeaux de Graduation',\n        slug: 'cap',\n        icon: '🎩',\n        description: 'قبعات التخرج الأكاديمية',\n        is_active: true,\n        order_index: 2,\n        created_at: '2024-01-15T10:05:00Z',\n        updated_at: '2024-01-15T10:05:00Z'\n    },\n    {\n        id: '3',\n        name_ar: 'شرابات التخرج',\n        name_en: 'Graduation Tassels',\n        name_fr: 'Glands de Graduation',\n        slug: 'tassel',\n        icon: '🏷️',\n        description: 'شرابات التخرج الملونة',\n        is_active: true,\n        order_index: 3,\n        created_at: '2024-01-15T10:10:00Z',\n        updated_at: '2024-01-15T10:10:00Z'\n    },\n    {\n        id: '4',\n        name_ar: 'أوشحة التخرج',\n        name_en: 'Graduation Stoles',\n        name_fr: 'Étoles de Graduation',\n        slug: 'stole',\n        icon: '🧣',\n        description: 'أوشحة التخرج المميزة',\n        is_active: true,\n        order_index: 4,\n        created_at: '2024-01-15T10:15:00Z',\n        updated_at: '2024-01-15T10:15:00Z'\n    },\n    {\n        id: '5',\n        name_ar: 'القلانس الأكاديمية',\n        name_en: 'Academic Hoods',\n        name_fr: 'Capuches Académiques',\n        slug: 'hood',\n        icon: '🎓',\n        description: 'القلانس الأكاديمية للدرجات العليا',\n        is_active: true,\n        order_index: 5,\n        created_at: '2024-01-15T10:20:00Z',\n        updated_at: '2024-01-15T10:20:00Z'\n    }\n];\n// بيانات وهمية للمنتجات\nconst mockProducts = [\n    {\n        id: '1',\n        name: 'ثوب التخرج الكلاسيكي',\n        description: 'ثوب تخرج أنيق مصنوع من أجود الخامات، مناسب لجميع المناسبات الأكاديمية',\n        category: 'gown',\n        price: 299.99,\n        rental_price: 99.99,\n        colors: [\n            'أسود',\n            'أزرق داكن',\n            'بورجوندي'\n        ],\n        sizes: [\n            'S',\n            'M',\n            'L',\n            'XL',\n            'XXL'\n        ],\n        images: [\n            '/images/products/gown-classic-1.jpg',\n            '/images/products/gown-classic-2.jpg'\n        ],\n        stock_quantity: 25,\n        is_available: true,\n        is_published: true,\n        created_at: '2024-01-15T10:00:00Z',\n        updated_at: '2024-01-20T14:30:00Z',\n        rating: 4.8,\n        reviews_count: 42,\n        features: [\n            'مقاوم للتجاعيد',\n            'قابل للغسيل',\n            'خامة عالية الجودة'\n        ],\n        specifications: {\n            material: 'بوليستر عالي الجودة',\n            weight: '0.8 كيلو',\n            care: 'غسيل جاف أو غسيل عادي'\n        }\n    },\n    {\n        id: '2',\n        name: 'قبعة التخرج التقليدية',\n        description: 'قبعة تخرج تقليدية مع شرابة ذهبية، رمز الإنجاز الأكاديمي',\n        category: 'cap',\n        price: 79.99,\n        rental_price: 29.99,\n        colors: [\n            'أسود',\n            'أزرق داكن'\n        ],\n        sizes: [\n            'One Size'\n        ],\n        images: [\n            '/images/products/cap-traditional-1.jpg'\n        ],\n        stock_quantity: 50,\n        is_available: true,\n        is_published: true,\n        created_at: '2024-01-16T09:00:00Z',\n        updated_at: '2024-01-22T16:45:00Z',\n        rating: 4.6,\n        reviews_count: 28,\n        features: [\n            'مقاس واحد يناسب الجميع',\n            'شرابة ذهبية',\n            'تصميم تقليدي'\n        ],\n        specifications: {\n            material: 'قطن مخلوط',\n            tassel_color: 'ذهبي',\n            adjustable: 'نعم'\n        }\n    },\n    {\n        id: '3',\n        name: 'وشاح التخرج المطرز',\n        description: 'وشاح تخرج مطرز بخيوط ذهبية، يضيف لمسة من الأناقة والتميز',\n        category: 'stole',\n        price: 149.99,\n        rental_price: 49.99,\n        colors: [\n            'أبيض مع ذهبي',\n            'أزرق مع فضي',\n            'أحمر مع ذهبي'\n        ],\n        sizes: [\n            'One Size'\n        ],\n        images: [\n            '/images/products/stole-embroidered-1.jpg',\n            '/images/products/stole-embroidered-2.jpg'\n        ],\n        stock_quantity: 15,\n        is_available: true,\n        is_published: true,\n        created_at: '2024-01-17T11:00:00Z',\n        updated_at: '2024-01-25T10:15:00Z',\n        rating: 4.9,\n        reviews_count: 18,\n        features: [\n            'تطريز يدوي',\n            'خيوط ذهبية',\n            'تصميم فاخر'\n        ],\n        specifications: {\n            material: 'حرير طبيعي',\n            embroidery: 'خيوط ذهبية وفضية',\n            length: '150 سم'\n        }\n    },\n    {\n        id: '4',\n        name: 'شرابة التخرج الذهبية',\n        description: 'شرابة تخرج ذهبية اللون، رمز التفوق والإنجاز الأكاديمي',\n        category: 'tassel',\n        price: 39.99,\n        rental_price: 15.99,\n        colors: [\n            'ذهبي',\n            'فضي',\n            'أزرق',\n            'أحمر'\n        ],\n        sizes: [\n            'One Size'\n        ],\n        images: [\n            '/images/products/tassel-gold-1.jpg'\n        ],\n        stock_quantity: 100,\n        is_available: true,\n        is_published: true,\n        created_at: '2024-01-18T14:00:00Z',\n        updated_at: '2024-01-26T09:30:00Z',\n        rating: 4.7,\n        reviews_count: 35,\n        features: [\n            'خيوط عالية الجودة',\n            'ألوان ثابتة',\n            'سهل التركيب'\n        ],\n        specifications: {\n            material: 'خيوط حريرية',\n            length: '23 سم',\n            attachment: 'مشبك معدني'\n        }\n    },\n    {\n        id: '5',\n        name: 'قلنسوة الدكتوراه الفاخرة',\n        description: 'قلنسوة دكتوراه فاخرة مصممة خصيصاً لحفلات التخرج الأكاديمية العليا',\n        category: 'hood',\n        price: 199.99,\n        rental_price: 79.99,\n        colors: [\n            'أسود مع ذهبي',\n            'أزرق مع فضي'\n        ],\n        sizes: [\n            'M',\n            'L',\n            'XL'\n        ],\n        images: [\n            '/images/products/hood-doctorate-1.jpg',\n            '/images/products/hood-doctorate-2.jpg'\n        ],\n        stock_quantity: 8,\n        is_available: true,\n        is_published: true,\n        created_at: '2024-01-19T16:00:00Z',\n        updated_at: '2024-01-27T12:00:00Z',\n        rating: 5.0,\n        reviews_count: 12,\n        features: [\n            'تصميم أكاديمي أصيل',\n            'خامة فاخرة',\n            'مناسب للدكتوراه'\n        ],\n        specifications: {\n            material: 'مخمل عالي الجودة',\n            lining: 'حرير ملون',\n            academic_level: 'دكتوراه'\n        }\n    }\n];\n// بيانات وهمية للمدارس\nconst mockSchools = [\n    {\n        id: '1',\n        admin_id: 'admin-school-1',\n        name: 'جامعة الإمارات العربية المتحدة',\n        name_en: 'United Arab Emirates University',\n        name_fr: 'Université des Émirats Arabes Unis',\n        address: 'شارع الجامعة، العين',\n        city: 'العين',\n        phone: '+971-3-713-5000',\n        email: '<EMAIL>',\n        website: 'https://www.uaeu.ac.ae',\n        logo_url: '/images/schools/uaeu-logo.png',\n        graduation_date: '2024-06-15',\n        student_count: 14500,\n        is_active: true,\n        settings: {\n            graduation_ceremony_location: 'قاعة الاحتفالات الكبرى',\n            dress_code: 'formal',\n            photography_allowed: true\n        },\n        created_at: '2024-01-10T08:00:00Z',\n        updated_at: '2024-01-25T10:30:00Z'\n    },\n    {\n        id: '2',\n        admin_id: 'admin-school-2',\n        name: 'الجامعة الأمريكية في الشارقة',\n        name_en: 'American University of Sharjah',\n        name_fr: 'Université Américaine de Sharjah',\n        address: 'شارع الجامعة، الشارقة',\n        city: 'الشارقة',\n        phone: '+971-6-515-5555',\n        email: '<EMAIL>',\n        website: 'https://www.aus.edu',\n        logo_url: '/images/schools/aus-logo.png',\n        graduation_date: '2024-05-20',\n        student_count: 6200,\n        is_active: true,\n        settings: {\n            graduation_ceremony_location: 'مسرح الجامعة',\n            dress_code: 'academic',\n            photography_allowed: true\n        },\n        created_at: '2024-01-12T09:15:00Z',\n        updated_at: '2024-01-28T14:20:00Z'\n    },\n    {\n        id: '3',\n        admin_id: 'admin-school-3',\n        name: 'جامعة زايد',\n        name_en: 'Zayed University',\n        name_fr: 'Université Zayed',\n        address: 'شارع الشيخ زايد، دبي',\n        city: 'دبي',\n        phone: '+971-4-402-1111',\n        email: '<EMAIL>',\n        website: 'https://www.zu.ac.ae',\n        logo_url: '/images/schools/zu-logo.png',\n        graduation_date: '2024-06-10',\n        student_count: 9800,\n        is_active: true,\n        settings: {\n            graduation_ceremony_location: 'مركز المؤتمرات',\n            dress_code: 'formal',\n            photography_allowed: false\n        },\n        created_at: '2024-01-15T11:00:00Z',\n        updated_at: '2024-02-01T16:45:00Z'\n    },\n    {\n        id: '4',\n        admin_id: 'admin-school-4',\n        name: 'كلية الإمارات للتكنولوجيا',\n        name_en: 'Emirates Institute of Technology',\n        name_fr: 'Institut de Technologie des Émirats',\n        address: 'المنطقة الأكاديمية، أبوظبي',\n        city: 'أبوظبي',\n        phone: '+971-2-401-4000',\n        email: '<EMAIL>',\n        website: 'https://www.eit.ac.ae',\n        logo_url: '/images/schools/eit-logo.png',\n        graduation_date: '2024-07-05',\n        student_count: 3500,\n        is_active: true,\n        settings: {\n            graduation_ceremony_location: 'القاعة الرئيسية',\n            dress_code: 'business',\n            photography_allowed: true\n        },\n        created_at: '2024-01-18T13:30:00Z',\n        updated_at: '2024-02-05T09:15:00Z'\n    },\n    {\n        id: '5',\n        admin_id: 'admin-school-5',\n        name: 'معهد أبوظبي للتعليم التقني',\n        name_en: 'Abu Dhabi Technical Institute',\n        name_fr: 'Institut Technique d\\'Abu Dhabi',\n        address: 'المنطقة الصناعية، أبوظبي',\n        city: 'أبوظبي',\n        phone: '+971-2-505-2000',\n        email: '<EMAIL>',\n        website: 'https://www.adti.ac.ae',\n        graduation_date: '2024-06-25',\n        student_count: 2800,\n        is_active: false,\n        settings: {\n            graduation_ceremony_location: 'مركز التدريب',\n            dress_code: 'casual',\n            photography_allowed: true\n        },\n        created_at: '2024-01-20T15:45:00Z',\n        updated_at: '2024-02-10T12:00:00Z'\n    }\n];\n// بيانات وهمية للطلبات\nconst mockOrders = [\n    {\n        id: '1',\n        order_number: 'GT-240120-001',\n        customer_id: 'student-1',\n        customer_name: 'أحمد محمد علي',\n        customer_email: '<EMAIL>',\n        customer_phone: '+971-50-123-4567',\n        status: 'in_production',\n        items: [\n            {\n                id: '1',\n                order_id: '1',\n                product_id: '1',\n                product_name: 'ثوب التخرج الكلاسيكي',\n                product_image: '/images/products/gown-classic-1.jpg',\n                category: 'gown',\n                quantity: 1,\n                unit_price: 299.99,\n                total_price: 299.99,\n                customizations: {\n                    color: 'أسود',\n                    size: 'L',\n                    embroidery: 'أحمد علي - بكالوريوس هندسة'\n                }\n            },\n            {\n                id: '2',\n                order_id: '1',\n                product_id: '2',\n                product_name: 'قبعة التخرج الأكاديمية',\n                product_image: '/images/products/cap-academic-1.jpg',\n                category: 'cap',\n                quantity: 1,\n                unit_price: 89.99,\n                total_price: 89.99,\n                customizations: {\n                    color: 'أسود',\n                    size: 'M'\n                }\n            }\n        ],\n        subtotal: 389.98,\n        tax: 19.50,\n        shipping_cost: 25.00,\n        total: 434.48,\n        payment_status: 'paid',\n        payment_method: 'credit_card',\n        shipping_address: {\n            street: 'شارع الجامعة، مبنى 12، شقة 304',\n            city: 'العين',\n            state: 'أبوظبي',\n            postal_code: '17666',\n            country: 'الإمارات العربية المتحدة'\n        },\n        tracking_number: 'TRK-GT-001-2024',\n        notes: 'يرجى التسليم قبل حفل التخرج',\n        created_at: '2024-01-20T10:30:00Z',\n        updated_at: '2024-01-22T14:15:00Z',\n        delivery_date: '2024-02-15T00:00:00Z',\n        school_id: '1',\n        school_name: 'جامعة الإمارات العربية المتحدة'\n    },\n    {\n        id: '2',\n        order_number: 'GT-**********',\n        customer_id: 'student-2',\n        customer_name: 'فاطمة سالم الزهراني',\n        customer_email: '<EMAIL>',\n        customer_phone: '+971-56-789-0123',\n        status: 'delivered',\n        items: [\n            {\n                id: '3',\n                order_id: '2',\n                product_id: '3',\n                product_name: 'ثوب التخرج المميز',\n                product_image: '/images/products/gown-premium-1.jpg',\n                category: 'gown',\n                quantity: 1,\n                unit_price: 399.99,\n                total_price: 399.99,\n                customizations: {\n                    color: 'أزرق داكن',\n                    size: 'M',\n                    embroidery: 'فاطمة الزهراني - ماجستير إدارة أعمال'\n                }\n            }\n        ],\n        subtotal: 399.99,\n        tax: 20.00,\n        shipping_cost: 30.00,\n        total: 449.99,\n        payment_status: 'paid',\n        payment_method: 'bank_transfer',\n        shipping_address: {\n            street: 'شارع الكورنيش، برج الإمارات، الطابق 15',\n            city: 'الشارقة',\n            state: 'الشارقة',\n            postal_code: '27272',\n            country: 'الإمارات العربية المتحدة'\n        },\n        tracking_number: 'TRK-GT-002-2024',\n        created_at: '2024-01-21T09:15:00Z',\n        updated_at: '2024-01-25T16:30:00Z',\n        delivery_date: '2024-01-28T00:00:00Z',\n        school_id: '2',\n        school_name: 'الجامعة الأمريكية في الشارقة'\n    },\n    {\n        id: '3',\n        order_number: 'GT-**********',\n        customer_id: 'student-3',\n        customer_name: 'خالد عبدالله المنصوري',\n        customer_email: '<EMAIL>',\n        customer_phone: '+971-52-456-7890',\n        status: 'pending',\n        items: [\n            {\n                id: '4',\n                order_id: '3',\n                product_id: '1',\n                product_name: 'ثوب التخرج الكلاسيكي',\n                product_image: '/images/products/gown-classic-1.jpg',\n                category: 'gown',\n                quantity: 1,\n                unit_price: 299.99,\n                total_price: 299.99,\n                customizations: {\n                    color: 'بورجوندي',\n                    size: 'XL'\n                }\n            },\n            {\n                id: '5',\n                order_id: '3',\n                product_id: '4',\n                product_name: 'وشاح التخرج المطرز',\n                product_image: '/images/products/stole-embroidered-1.jpg',\n                category: 'stole',\n                quantity: 1,\n                unit_price: 149.99,\n                total_price: 149.99,\n                customizations: {\n                    color: 'ذهبي',\n                    embroidery: 'كلية الهندسة'\n                }\n            }\n        ],\n        subtotal: 449.98,\n        tax: 22.50,\n        shipping_cost: 25.00,\n        total: 497.48,\n        payment_status: 'pending',\n        shipping_address: {\n            street: 'شارع الشيخ زايد، مجمع دبي الأكاديمي',\n            city: 'دبي',\n            state: 'دبي',\n            postal_code: '391186',\n            country: 'الإمارات العربية المتحدة'\n        },\n        created_at: '2024-01-22T14:45:00Z',\n        updated_at: '2024-01-22T14:45:00Z',\n        school_id: '3',\n        school_name: 'جامعة زايد'\n    }\n];\n// مساعدات للتعامل مع البيانات الوهمية\nclass MockDataManager {\n    static getStorageKey(type) {\n        return `mockData_${type}`;\n    }\n    static getPages() {\n        if (true) return mockPages;\n        const stored = localStorage.getItem(this.getStorageKey('pages'));\n        return stored ? JSON.parse(stored) : mockPages;\n    }\n    static getMenuItems() {\n        if (true) return mockMenuItems;\n        const stored = localStorage.getItem(this.getStorageKey('menuItems'));\n        return stored ? JSON.parse(stored) : mockMenuItems;\n    }\n    static getProducts() {\n        if (true) {\n            console.log('Server side - returning mock products:', mockProducts.length);\n            return mockProducts;\n        }\n        const stored = localStorage.getItem(this.getStorageKey('products'));\n        const products = stored ? JSON.parse(stored) : mockProducts;\n        console.log('Client side - loaded products:', products.length);\n        console.log('Product IDs:', products.map((p)=>p.id));\n        return products;\n    }\n    static getCategories() {\n        if (true) return mockCategories;\n        const stored = localStorage.getItem(this.getStorageKey('categories'));\n        return stored ? JSON.parse(stored) : mockCategories;\n    }\n    static getSchools() {\n        if (true) return mockSchools;\n        const stored = localStorage.getItem(this.getStorageKey('schools'));\n        return stored ? JSON.parse(stored) : mockSchools;\n    }\n    static getOrders() {\n        if (true) return mockOrders;\n        const stored = localStorage.getItem(this.getStorageKey('orders'));\n        return stored ? JSON.parse(stored) : mockOrders;\n    }\n    static savePages(pages) {\n        if (false) {}\n    }\n    static saveMenuItems(items) {\n        if (false) {}\n    }\n    static saveProducts(products) {\n        if (false) {}\n    }\n    static saveCategories(categories) {\n        if (false) {}\n    }\n    static saveSchools(schools) {\n        if (false) {}\n    }\n    static saveOrders(orders) {\n        if (false) {}\n    }\n    static generateId() {\n        return Date.now().toString() + Math.random().toString(36).substring(2, 11);\n    }\n    // مسح جميع البيانات المحفوظة (للاختبار)\n    static clearAllData() {\n        if (false) {}\n    }\n    static generateOrderNumber() {\n        const date = new Date();\n        const year = date.getFullYear().toString().slice(-2);\n        const month = (date.getMonth() + 1).toString().padStart(2, '0');\n        const day = date.getDate().toString().padStart(2, '0');\n        const orders = this.getOrders();\n        const todayOrders = orders.filter((order)=>order.created_at.startsWith(`${date.getFullYear()}-${month}-${day}`));\n        const orderCount = (todayOrders.length + 1).toString().padStart(3, '0');\n        return `GT-${year}${month}${day}-${orderCount}`;\n    }\n    // إدارة نماذج الذكاء الاصطناعي\n    static getAIModels() {\n        if (true) return this.defaultAIModels;\n        const stored = localStorage.getItem('mockAIModels');\n        if (stored) {\n            return JSON.parse(stored);\n        }\n        return this.defaultAIModels;\n    }\n    static saveAIModels(models) {\n        if (false) {}\n    }\n    static getModelActivities() {\n        if (true) return this.defaultModelActivities;\n        const stored = localStorage.getItem('mockModelActivities');\n        if (stored) {\n            return JSON.parse(stored);\n        }\n        return this.defaultModelActivities;\n    }\n    static saveModelActivities(activities) {\n        if (false) {}\n    }\n    // إدارة قوالب الصفحات\n    static getPageTemplates() {\n        if (true) return this.defaultPageTemplates;\n        const stored = localStorage.getItem('mockPageTemplates');\n        if (stored) {\n            return JSON.parse(stored);\n        }\n        return this.defaultPageTemplates;\n    }\n    static savePageTemplates(templates) {\n        if (false) {}\n    }\n    static getPageProjects() {\n        if (true) return this.defaultPageProjects;\n        const stored = localStorage.getItem('mockPageProjects');\n        if (stored) {\n            return JSON.parse(stored);\n        }\n        return this.defaultPageProjects;\n    }\n    static savePageProjects(projects) {\n        if (false) {}\n    }\n    static getComponentLibrary() {\n        if (true) return this.defaultComponentLibrary;\n        const stored = localStorage.getItem('mockComponentLibrary');\n        if (stored) {\n            return JSON.parse(stored);\n        }\n        return this.defaultComponentLibrary;\n    }\n    static saveComponentLibrary(components) {\n        if (false) {}\n    }\n    static{\n        // البيانات الافتراضية لنماذج الذكاء الاصطناعي (فارغة للبداية)\n        this.defaultAIModels = [];\n    }\n    static{\n        this.defaultModelActivities = [];\n    }\n    static{\n        // البيانات الافتراضية لقوالب الصفحات\n        this.defaultPageTemplates = [\n            {\n                id: 'template-landing-1',\n                name: 'Landing Page - Modern',\n                nameAr: 'صفحة هبوط - عصرية',\n                nameEn: 'Landing Page - Modern',\n                nameFr: 'Page d\\'atterrissage - Moderne',\n                description: 'قالب صفحة هبوط عصرية مع تصميم جذاب',\n                category: 'landing',\n                components: [\n                    {\n                        id: 'hero-1',\n                        type: 'hero',\n                        name: 'Hero Section',\n                        props: {\n                            content: 'مرحباً بكم في منصة أزياء التخرج',\n                            style: {\n                                backgroundColor: '#1F2937',\n                                color: '#FFFFFF'\n                            }\n                        },\n                        position: {\n                            x: 0,\n                            y: 0\n                        },\n                        size: {\n                            width: '100%',\n                            height: '500px'\n                        },\n                        isVisible: true\n                    }\n                ],\n                preview: '/images/templates/landing-modern.jpg',\n                thumbnail: '/images/templates/landing-modern-thumb.jpg',\n                isAIGenerated: false,\n                isPremium: false,\n                tags: [\n                    'landing',\n                    'modern',\n                    'business'\n                ],\n                createdAt: '2024-01-01T00:00:00Z',\n                updatedAt: '2024-01-01T00:00:00Z',\n                usageCount: 45,\n                rating: 4.8,\n                metadata: {\n                    colors: [\n                        '#1F2937',\n                        '#FFFFFF',\n                        '#3B82F6'\n                    ],\n                    fonts: [\n                        'Inter',\n                        'Cairo'\n                    ],\n                    layout: 'single-page',\n                    responsive: true\n                }\n            }\n        ];\n    }\n    static{\n        // البيانات الافتراضية لمشاريع الصفحات\n        this.defaultPageProjects = [\n            {\n                id: 'project-1',\n                name: 'موقع أزياء التخرج الرئيسي',\n                description: 'الموقع الرئيسي لعرض منتجات أزياء التخرج',\n                components: [],\n                templateId: 'template-landing-1',\n                generationMode: 'template',\n                settings: {\n                    title: 'أزياء التخرج - منصة مغربية متخصصة',\n                    description: 'أول منصة مغربية لتأجير وبيع أزياء التخرج',\n                    keywords: [\n                        'أزياء التخرج',\n                        'تأجير',\n                        'المغرب'\n                    ],\n                    language: 'ar',\n                    direction: 'rtl'\n                },\n                isPublished: false,\n                createdAt: '2024-01-15T00:00:00Z',\n                updatedAt: '2024-01-20T10:30:00Z',\n                createdBy: 'admin-1',\n                version: 1\n            }\n        ];\n    }\n    static{\n        // البيانات الافتراضية لمكتبة المكونات\n        this.defaultComponentLibrary = [\n            {\n                id: 'comp-hero',\n                name: 'Hero Section',\n                nameAr: 'قسم البطل',\n                type: 'hero',\n                category: 'layout',\n                description: 'قسم رئيسي جذاب في أعلى الصفحة',\n                icon: 'Layout',\n                preview: '/images/components/hero-preview.jpg',\n                defaultProps: {\n                    content: 'عنوان رئيسي جذاب',\n                    style: {\n                        backgroundColor: '#1F2937',\n                        color: '#FFFFFF'\n                    }\n                },\n                defaultSize: {\n                    width: '100%',\n                    height: '500px'\n                },\n                isCustom: false,\n                isPremium: false,\n                tags: [\n                    'layout',\n                    'header',\n                    'hero'\n                ],\n                usageCount: 156\n            },\n            {\n                id: 'comp-button',\n                name: 'Button',\n                nameAr: 'زر',\n                type: 'button',\n                category: 'interactive',\n                description: 'زر تفاعلي قابل للتخصيص',\n                icon: 'MousePointer',\n                preview: '/images/components/button-preview.jpg',\n                defaultProps: {\n                    content: 'انقر هنا',\n                    style: {\n                        backgroundColor: '#3B82F6',\n                        color: '#FFFFFF'\n                    }\n                },\n                defaultSize: {\n                    width: '120px',\n                    height: '40px'\n                },\n                isCustom: false,\n                isPremium: false,\n                tags: [\n                    'interactive',\n                    'button',\n                    'cta'\n                ],\n                usageCount: 234\n            }\n        ];\n    }\n    // إدارة مزودي الذكاء الاصطناعي\n    static getAIProviders() {\n        if (true) return [];\n        const stored = localStorage.getItem(this.getStorageKey('aiProviders'));\n        return stored ? JSON.parse(stored) : [];\n    }\n    static addAIProvider(provider) {\n        if (true) return provider;\n        const providers = this.getAIProviders();\n        providers.push(provider);\n        localStorage.setItem(this.getStorageKey('aiProviders'), JSON.stringify(providers));\n        return provider;\n    }\n    static updateAIProvider(id, updatedProvider) {\n        if (true) return null;\n        const providers = this.getAIProviders();\n        const index = providers.findIndex((p)=>p.id === id);\n        if (index === -1) return null;\n        providers[index] = {\n            ...providers[index],\n            ...updatedProvider\n        };\n        localStorage.setItem(this.getStorageKey('aiProviders'), JSON.stringify(providers));\n        return providers[index];\n    }\n    static deleteAIProvider(id) {\n        if (true) return false;\n        const providers = this.getAIProviders();\n        const filteredProviders = providers.filter((p)=>p.id !== id);\n        if (filteredProviders.length === providers.length) return false;\n        localStorage.setItem(this.getStorageKey('aiProviders'), JSON.stringify(filteredProviders));\n        return true;\n    }\n    static getAIProviderById(id) {\n        if (true) return null;\n        const providers = this.getAIProviders();\n        return providers.find((p)=>p.id === id) || null;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL21vY2tEYXRhLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBQSxpQ0FBaUM7QUFpS2pDLHVCQUF1QjtBQUNoQixNQUFNQSxZQUF3QjtJQUNuQztRQUNFQyxJQUFJO1FBQ0pDLE1BQU07UUFDTkMsY0FBYztRQUNkQyxXQUFXO1FBQ1hDLGdCQUFnQjtRQUNoQkMsWUFBWTtRQUNaQyxZQUFZO1FBQ1pDLFVBQVU7WUFDUkMsV0FBVztRQUNiO1FBQ0FDLGNBQWM7WUFDWjtnQkFDRVQsSUFBSTtnQkFDSlUsU0FBUztnQkFDVEMsVUFBVTtnQkFDVkMsT0FBTztnQkFDUEMsU0FBUztnQkFDVEMsa0JBQWtCO2dCQUNsQkMsZUFBZTtZQUNqQjtZQUNBO2dCQUNFZixJQUFJO2dCQUNKVSxTQUFTO2dCQUNUQyxVQUFVO2dCQUNWQyxPQUFPO2dCQUNQQyxTQUFTO2dCQUNUQyxrQkFBa0I7Z0JBQ2xCQyxlQUFlO1lBQ2pCO1NBQ0Q7SUFDSDtJQUNBO1FBQ0VmLElBQUk7UUFDSkMsTUFBTTtRQUNOQyxjQUFjO1FBQ2RDLFdBQVc7UUFDWEUsWUFBWTtRQUNaQyxZQUFZO1FBQ1pDLFVBQVU7WUFDUkMsV0FBVztRQUNiO1FBQ0FDLGNBQWM7WUFDWjtnQkFDRVQsSUFBSTtnQkFDSlUsU0FBUztnQkFDVEMsVUFBVTtnQkFDVkMsT0FBTztnQkFDUEMsU0FBUztnQkFDVEMsa0JBQWtCO2dCQUNsQkMsZUFBZTtZQUNqQjtTQUNEO0lBQ0g7SUFDQTtRQUNFZixJQUFJO1FBQ0pDLE1BQU07UUFDTkMsY0FBYztRQUNkQyxXQUFXO1FBQ1hFLFlBQVk7UUFDWkMsWUFBWTtRQUNaQyxVQUFVO1lBQ1JDLFdBQVc7UUFDYjtRQUNBQyxjQUFjO1lBQ1o7Z0JBQ0VULElBQUk7Z0JBQ0pVLFNBQVM7Z0JBQ1RDLFVBQVU7Z0JBQ1ZDLE9BQU87Z0JBQ1BDLFNBQVM7Z0JBQ1RDLGtCQUFrQjtnQkFDbEJDLGVBQWU7WUFDakI7U0FDRDtJQUNIO0NBQ0Q7QUFFRCx1QkFBdUI7QUFDaEIsTUFBTUMsZ0JBQWdDO0lBQzNDO1FBQ0VoQixJQUFJO1FBQ0ppQixVQUFVO1FBQ1ZDLFVBQVU7UUFDVkMsVUFBVTtRQUNWbEIsTUFBTTtRQUNObUIsTUFBTTtRQUNOQyxhQUFhO1FBQ2JDLFdBQVc7UUFDWEMsYUFBYTtRQUNiQyxjQUFjO1FBQ2RuQixZQUFZO1FBQ1pDLFlBQVk7SUFDZDtJQUNBO1FBQ0VOLElBQUk7UUFDSmlCLFVBQVU7UUFDVkMsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZsQixNQUFNO1FBQ05tQixNQUFNO1FBQ05DLGFBQWE7UUFDYkMsV0FBVztRQUNYQyxhQUFhO1FBQ2JDLGNBQWM7UUFDZG5CLFlBQVk7UUFDWkMsWUFBWTtJQUNkO0lBQ0E7UUFDRU4sSUFBSTtRQUNKaUIsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZDLFVBQVU7UUFDVmxCLE1BQU07UUFDTm1CLE1BQU07UUFDTkMsYUFBYTtRQUNiQyxXQUFXO1FBQ1hDLGFBQWE7UUFDYkMsY0FBYztRQUNkbkIsWUFBWTtRQUNaQyxZQUFZO0lBQ2Q7SUFDQTtRQUNFTixJQUFJO1FBQ0ppQixVQUFVO1FBQ1ZDLFVBQVU7UUFDVkMsVUFBVTtRQUNWbEIsTUFBTTtRQUNObUIsTUFBTTtRQUNOQyxhQUFhO1FBQ2JDLFdBQVc7UUFDWEMsYUFBYTtRQUNiQyxjQUFjO1FBQ2RuQixZQUFZO1FBQ1pDLFlBQVk7SUFDZDtJQUNBO1FBQ0VOLElBQUk7UUFDSmlCLFVBQVU7UUFDVkMsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZsQixNQUFNO1FBQ053QixXQUFXO1FBQ1hMLE1BQU07UUFDTkMsYUFBYTtRQUNiQyxXQUFXO1FBQ1hDLGFBQWE7UUFDYkMsY0FBYztRQUNkbkIsWUFBWTtRQUNaQyxZQUFZO0lBQ2Q7SUFDQTtRQUNFTixJQUFJO1FBQ0ppQixVQUFVO1FBQ1ZDLFVBQVU7UUFDVkMsVUFBVTtRQUNWbEIsTUFBTTtRQUNOd0IsV0FBVztRQUNYTCxNQUFNO1FBQ05DLGFBQWE7UUFDYkMsV0FBVztRQUNYQyxhQUFhO1FBQ2JDLGNBQWM7UUFDZG5CLFlBQVk7UUFDWkMsWUFBWTtJQUNkO0lBRUE7UUFDRU4sSUFBSTtRQUNKaUIsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZDLFVBQVU7UUFDVmxCLE1BQU07UUFDTm1CLE1BQU07UUFDTkMsYUFBYTtRQUNiQyxXQUFXO1FBQ1hDLGFBQWE7UUFDYkMsY0FBYztRQUNkbkIsWUFBWTtRQUNaQyxZQUFZO0lBQ2Q7Q0FDRDtBQUVELHNCQUFzQjtBQUNmLE1BQU1vQixpQkFBaUM7SUFDNUM7UUFDRTFCLElBQUk7UUFDSjJCLFNBQVM7UUFDVEMsU0FBUztRQUNUQyxTQUFTO1FBQ1Q1QixNQUFNO1FBQ05tQixNQUFNO1FBQ05VLGFBQWE7UUFDYlIsV0FBVztRQUNYRCxhQUFhO1FBQ2JoQixZQUFZO1FBQ1pDLFlBQVk7SUFDZDtJQUNBO1FBQ0VOLElBQUk7UUFDSjJCLFNBQVM7UUFDVEMsU0FBUztRQUNUQyxTQUFTO1FBQ1Q1QixNQUFNO1FBQ05tQixNQUFNO1FBQ05VLGFBQWE7UUFDYlIsV0FBVztRQUNYRCxhQUFhO1FBQ2JoQixZQUFZO1FBQ1pDLFlBQVk7SUFDZDtJQUNBO1FBQ0VOLElBQUk7UUFDSjJCLFNBQVM7UUFDVEMsU0FBUztRQUNUQyxTQUFTO1FBQ1Q1QixNQUFNO1FBQ05tQixNQUFNO1FBQ05VLGFBQWE7UUFDYlIsV0FBVztRQUNYRCxhQUFhO1FBQ2JoQixZQUFZO1FBQ1pDLFlBQVk7SUFDZDtJQUNBO1FBQ0VOLElBQUk7UUFDSjJCLFNBQVM7UUFDVEMsU0FBUztRQUNUQyxTQUFTO1FBQ1Q1QixNQUFNO1FBQ05tQixNQUFNO1FBQ05VLGFBQWE7UUFDYlIsV0FBVztRQUNYRCxhQUFhO1FBQ2JoQixZQUFZO1FBQ1pDLFlBQVk7SUFDZDtJQUNBO1FBQ0VOLElBQUk7UUFDSjJCLFNBQVM7UUFDVEMsU0FBUztRQUNUQyxTQUFTO1FBQ1Q1QixNQUFNO1FBQ05tQixNQUFNO1FBQ05VLGFBQWE7UUFDYlIsV0FBVztRQUNYRCxhQUFhO1FBQ2JoQixZQUFZO1FBQ1pDLFlBQVk7SUFDZDtDQUNEO0FBRUQsd0JBQXdCO0FBQ2pCLE1BQU15QixlQUE4QjtJQUN6QztRQUNFL0IsSUFBSTtRQUNKZ0MsTUFBTTtRQUNORixhQUFhO1FBQ2JHLFVBQVU7UUFDVkMsT0FBTztRQUNQQyxjQUFjO1FBQ2RDLFFBQVE7WUFBQztZQUFRO1lBQWE7U0FBVztRQUN6Q0MsT0FBTztZQUFDO1lBQUs7WUFBSztZQUFLO1lBQU07U0FBTTtRQUNuQ0MsUUFBUTtZQUFDO1lBQXVDO1NBQXNDO1FBQ3RGQyxnQkFBZ0I7UUFDaEJDLGNBQWM7UUFDZHRDLGNBQWM7UUFDZEcsWUFBWTtRQUNaQyxZQUFZO1FBQ1ptQyxRQUFRO1FBQ1JDLGVBQWU7UUFDZkMsVUFBVTtZQUFDO1lBQWtCO1lBQWU7U0FBb0I7UUFDaEVDLGdCQUFnQjtZQUNkQyxVQUFVO1lBQ1ZDLFFBQVE7WUFDUkMsTUFBTTtRQUNSO0lBQ0Y7SUFDQTtRQUNFL0MsSUFBSTtRQUNKZ0MsTUFBTTtRQUNORixhQUFhO1FBQ2JHLFVBQVU7UUFDVkMsT0FBTztRQUNQQyxjQUFjO1FBQ2RDLFFBQVE7WUFBQztZQUFRO1NBQVk7UUFDN0JDLE9BQU87WUFBQztTQUFXO1FBQ25CQyxRQUFRO1lBQUM7U0FBeUM7UUFDbERDLGdCQUFnQjtRQUNoQkMsY0FBYztRQUNkdEMsY0FBYztRQUNkRyxZQUFZO1FBQ1pDLFlBQVk7UUFDWm1DLFFBQVE7UUFDUkMsZUFBZTtRQUNmQyxVQUFVO1lBQUM7WUFBMEI7WUFBZTtTQUFlO1FBQ25FQyxnQkFBZ0I7WUFDZEMsVUFBVTtZQUNWRyxjQUFjO1lBQ2RDLFlBQVk7UUFDZDtJQUNGO0lBQ0E7UUFDRWpELElBQUk7UUFDSmdDLE1BQU07UUFDTkYsYUFBYTtRQUNiRyxVQUFVO1FBQ1ZDLE9BQU87UUFDUEMsY0FBYztRQUNkQyxRQUFRO1lBQUM7WUFBZ0I7WUFBZTtTQUFlO1FBQ3ZEQyxPQUFPO1lBQUM7U0FBVztRQUNuQkMsUUFBUTtZQUFDO1lBQTRDO1NBQTJDO1FBQ2hHQyxnQkFBZ0I7UUFDaEJDLGNBQWM7UUFDZHRDLGNBQWM7UUFDZEcsWUFBWTtRQUNaQyxZQUFZO1FBQ1ptQyxRQUFRO1FBQ1JDLGVBQWU7UUFDZkMsVUFBVTtZQUFDO1lBQWM7WUFBYztTQUFhO1FBQ3BEQyxnQkFBZ0I7WUFDZEMsVUFBVTtZQUNWSyxZQUFZO1lBQ1pDLFFBQVE7UUFDVjtJQUNGO0lBQ0E7UUFDRW5ELElBQUk7UUFDSmdDLE1BQU07UUFDTkYsYUFBYTtRQUNiRyxVQUFVO1FBQ1ZDLE9BQU87UUFDUEMsY0FBYztRQUNkQyxRQUFRO1lBQUM7WUFBUTtZQUFPO1lBQVE7U0FBTztRQUN2Q0MsT0FBTztZQUFDO1NBQVc7UUFDbkJDLFFBQVE7WUFBQztTQUFxQztRQUM5Q0MsZ0JBQWdCO1FBQ2hCQyxjQUFjO1FBQ2R0QyxjQUFjO1FBQ2RHLFlBQVk7UUFDWkMsWUFBWTtRQUNabUMsUUFBUTtRQUNSQyxlQUFlO1FBQ2ZDLFVBQVU7WUFBQztZQUFxQjtZQUFlO1NBQWM7UUFDN0RDLGdCQUFnQjtZQUNkQyxVQUFVO1lBQ1ZNLFFBQVE7WUFDUkMsWUFBWTtRQUNkO0lBQ0Y7SUFDQTtRQUNFcEQsSUFBSTtRQUNKZ0MsTUFBTTtRQUNORixhQUFhO1FBQ2JHLFVBQVU7UUFDVkMsT0FBTztRQUNQQyxjQUFjO1FBQ2RDLFFBQVE7WUFBQztZQUFnQjtTQUFjO1FBQ3ZDQyxPQUFPO1lBQUM7WUFBSztZQUFLO1NBQUs7UUFDdkJDLFFBQVE7WUFBQztZQUF5QztTQUF3QztRQUMxRkMsZ0JBQWdCO1FBQ2hCQyxjQUFjO1FBQ2R0QyxjQUFjO1FBQ2RHLFlBQVk7UUFDWkMsWUFBWTtRQUNabUMsUUFBUTtRQUNSQyxlQUFlO1FBQ2ZDLFVBQVU7WUFBQztZQUFzQjtZQUFjO1NBQWtCO1FBQ2pFQyxnQkFBZ0I7WUFDZEMsVUFBVTtZQUNWUSxRQUFRO1lBQ1JDLGdCQUFnQjtRQUNsQjtJQUNGO0NBQ0Q7QUFFRCx1QkFBdUI7QUFDaEIsTUFBTUMsY0FBNEI7SUFDdkM7UUFDRXZELElBQUk7UUFDSndELFVBQVU7UUFDVnhCLE1BQU07UUFDTkosU0FBUztRQUNUQyxTQUFTO1FBQ1Q0QixTQUFTO1FBQ1RDLE1BQU07UUFDTkMsT0FBTztRQUNQQyxPQUFPO1FBQ1BDLFNBQVM7UUFDVEMsVUFBVTtRQUNWQyxpQkFBaUI7UUFDakJDLGVBQWU7UUFDZjFDLFdBQVc7UUFDWDJDLFVBQVU7WUFDUkMsOEJBQThCO1lBQzlCQyxZQUFZO1lBQ1pDLHFCQUFxQjtRQUN2QjtRQUNBL0QsWUFBWTtRQUNaQyxZQUFZO0lBQ2Q7SUFDQTtRQUNFTixJQUFJO1FBQ0p3RCxVQUFVO1FBQ1Z4QixNQUFNO1FBQ05KLFNBQVM7UUFDVEMsU0FBUztRQUNUNEIsU0FBUztRQUNUQyxNQUFNO1FBQ05DLE9BQU87UUFDUEMsT0FBTztRQUNQQyxTQUFTO1FBQ1RDLFVBQVU7UUFDVkMsaUJBQWlCO1FBQ2pCQyxlQUFlO1FBQ2YxQyxXQUFXO1FBQ1gyQyxVQUFVO1lBQ1JDLDhCQUE4QjtZQUM5QkMsWUFBWTtZQUNaQyxxQkFBcUI7UUFDdkI7UUFDQS9ELFlBQVk7UUFDWkMsWUFBWTtJQUNkO0lBQ0E7UUFDRU4sSUFBSTtRQUNKd0QsVUFBVTtRQUNWeEIsTUFBTTtRQUNOSixTQUFTO1FBQ1RDLFNBQVM7UUFDVDRCLFNBQVM7UUFDVEMsTUFBTTtRQUNOQyxPQUFPO1FBQ1BDLE9BQU87UUFDUEMsU0FBUztRQUNUQyxVQUFVO1FBQ1ZDLGlCQUFpQjtRQUNqQkMsZUFBZTtRQUNmMUMsV0FBVztRQUNYMkMsVUFBVTtZQUNSQyw4QkFBOEI7WUFDOUJDLFlBQVk7WUFDWkMscUJBQXFCO1FBQ3ZCO1FBQ0EvRCxZQUFZO1FBQ1pDLFlBQVk7SUFDZDtJQUNBO1FBQ0VOLElBQUk7UUFDSndELFVBQVU7UUFDVnhCLE1BQU07UUFDTkosU0FBUztRQUNUQyxTQUFTO1FBQ1Q0QixTQUFTO1FBQ1RDLE1BQU07UUFDTkMsT0FBTztRQUNQQyxPQUFPO1FBQ1BDLFNBQVM7UUFDVEMsVUFBVTtRQUNWQyxpQkFBaUI7UUFDakJDLGVBQWU7UUFDZjFDLFdBQVc7UUFDWDJDLFVBQVU7WUFDUkMsOEJBQThCO1lBQzlCQyxZQUFZO1lBQ1pDLHFCQUFxQjtRQUN2QjtRQUNBL0QsWUFBWTtRQUNaQyxZQUFZO0lBQ2Q7SUFDQTtRQUNFTixJQUFJO1FBQ0p3RCxVQUFVO1FBQ1Z4QixNQUFNO1FBQ05KLFNBQVM7UUFDVEMsU0FBUztRQUNUNEIsU0FBUztRQUNUQyxNQUFNO1FBQ05DLE9BQU87UUFDUEMsT0FBTztRQUNQQyxTQUFTO1FBQ1RFLGlCQUFpQjtRQUNqQkMsZUFBZTtRQUNmMUMsV0FBVztRQUNYMkMsVUFBVTtZQUNSQyw4QkFBOEI7WUFDOUJDLFlBQVk7WUFDWkMscUJBQXFCO1FBQ3ZCO1FBQ0EvRCxZQUFZO1FBQ1pDLFlBQVk7SUFDZDtDQUNEO0FBRUQsdUJBQXVCO0FBQ2hCLE1BQU0rRCxhQUEwQjtJQUNyQztRQUNFckUsSUFBSTtRQUNKc0UsY0FBYztRQUNkQyxhQUFhO1FBQ2JDLGVBQWU7UUFDZkMsZ0JBQWdCO1FBQ2hCQyxnQkFBZ0I7UUFDaEJDLFFBQVE7UUFDUkMsT0FBTztZQUNMO2dCQUNFNUUsSUFBSTtnQkFDSjZFLFVBQVU7Z0JBQ1ZDLFlBQVk7Z0JBQ1pDLGNBQWM7Z0JBQ2RDLGVBQWU7Z0JBQ2YvQyxVQUFVO2dCQUNWZ0QsVUFBVTtnQkFDVkMsWUFBWTtnQkFDWkMsYUFBYTtnQkFDYkMsZ0JBQWdCO29CQUNkQyxPQUFPO29CQUNQQyxNQUFNO29CQUNOcEMsWUFBWTtnQkFDZDtZQUNGO1lBQ0E7Z0JBQ0VsRCxJQUFJO2dCQUNKNkUsVUFBVTtnQkFDVkMsWUFBWTtnQkFDWkMsY0FBYztnQkFDZEMsZUFBZTtnQkFDZi9DLFVBQVU7Z0JBQ1ZnRCxVQUFVO2dCQUNWQyxZQUFZO2dCQUNaQyxhQUFhO2dCQUNiQyxnQkFBZ0I7b0JBQ2RDLE9BQU87b0JBQ1BDLE1BQU07Z0JBQ1I7WUFDRjtTQUNEO1FBQ0RDLFVBQVU7UUFDVkMsS0FBSztRQUNMQyxlQUFlO1FBQ2ZDLE9BQU87UUFDUEMsZ0JBQWdCO1FBQ2hCQyxnQkFBZ0I7UUFDaEJDLGtCQUFrQjtZQUNoQkMsUUFBUTtZQUNScEMsTUFBTTtZQUNOcUMsT0FBTztZQUNQQyxhQUFhO1lBQ2JDLFNBQVM7UUFDWDtRQUNBQyxpQkFBaUI7UUFDakJDLE9BQU87UUFDUDlGLFlBQVk7UUFDWkMsWUFBWTtRQUNaOEYsZUFBZTtRQUNmQyxXQUFXO1FBQ1hDLGFBQWE7SUFDZjtJQUNBO1FBQ0V0RyxJQUFJO1FBQ0pzRSxjQUFjO1FBQ2RDLGFBQWE7UUFDYkMsZUFBZTtRQUNmQyxnQkFBZ0I7UUFDaEJDLGdCQUFnQjtRQUNoQkMsUUFBUTtRQUNSQyxPQUFPO1lBQ0w7Z0JBQ0U1RSxJQUFJO2dCQUNKNkUsVUFBVTtnQkFDVkMsWUFBWTtnQkFDWkMsY0FBYztnQkFDZEMsZUFBZTtnQkFDZi9DLFVBQVU7Z0JBQ1ZnRCxVQUFVO2dCQUNWQyxZQUFZO2dCQUNaQyxhQUFhO2dCQUNiQyxnQkFBZ0I7b0JBQ2RDLE9BQU87b0JBQ1BDLE1BQU07b0JBQ05wQyxZQUFZO2dCQUNkO1lBQ0Y7U0FDRDtRQUNEcUMsVUFBVTtRQUNWQyxLQUFLO1FBQ0xDLGVBQWU7UUFDZkMsT0FBTztRQUNQQyxnQkFBZ0I7UUFDaEJDLGdCQUFnQjtRQUNoQkMsa0JBQWtCO1lBQ2hCQyxRQUFRO1lBQ1JwQyxNQUFNO1lBQ05xQyxPQUFPO1lBQ1BDLGFBQWE7WUFDYkMsU0FBUztRQUNYO1FBQ0FDLGlCQUFpQjtRQUNqQjdGLFlBQVk7UUFDWkMsWUFBWTtRQUNaOEYsZUFBZTtRQUNmQyxXQUFXO1FBQ1hDLGFBQWE7SUFDZjtJQUNBO1FBQ0V0RyxJQUFJO1FBQ0pzRSxjQUFjO1FBQ2RDLGFBQWE7UUFDYkMsZUFBZTtRQUNmQyxnQkFBZ0I7UUFDaEJDLGdCQUFnQjtRQUNoQkMsUUFBUTtRQUNSQyxPQUFPO1lBQ0w7Z0JBQ0U1RSxJQUFJO2dCQUNKNkUsVUFBVTtnQkFDVkMsWUFBWTtnQkFDWkMsY0FBYztnQkFDZEMsZUFBZTtnQkFDZi9DLFVBQVU7Z0JBQ1ZnRCxVQUFVO2dCQUNWQyxZQUFZO2dCQUNaQyxhQUFhO2dCQUNiQyxnQkFBZ0I7b0JBQ2RDLE9BQU87b0JBQ1BDLE1BQU07Z0JBQ1I7WUFDRjtZQUNBO2dCQUNFdEYsSUFBSTtnQkFDSjZFLFVBQVU7Z0JBQ1ZDLFlBQVk7Z0JBQ1pDLGNBQWM7Z0JBQ2RDLGVBQWU7Z0JBQ2YvQyxVQUFVO2dCQUNWZ0QsVUFBVTtnQkFDVkMsWUFBWTtnQkFDWkMsYUFBYTtnQkFDYkMsZ0JBQWdCO29CQUNkQyxPQUFPO29CQUNQbkMsWUFBWTtnQkFDZDtZQUNGO1NBQ0Q7UUFDRHFDLFVBQVU7UUFDVkMsS0FBSztRQUNMQyxlQUFlO1FBQ2ZDLE9BQU87UUFDUEMsZ0JBQWdCO1FBQ2hCRSxrQkFBa0I7WUFDaEJDLFFBQVE7WUFDUnBDLE1BQU07WUFDTnFDLE9BQU87WUFDUEMsYUFBYTtZQUNiQyxTQUFTO1FBQ1g7UUFDQTVGLFlBQVk7UUFDWkMsWUFBWTtRQUNaK0YsV0FBVztRQUNYQyxhQUFhO0lBQ2Y7Q0FDRDtBQUVELHNDQUFzQztBQUMvQixNQUFNQztJQUNYLE9BQWVDLGNBQWNDLElBQThGLEVBQVU7UUFDbkksT0FBTyxDQUFDLFNBQVMsRUFBRUEsTUFBTTtJQUMzQjtJQUVBLE9BQU9DLFdBQXVCO1FBQzVCLElBQUksSUFBNkIsRUFBRSxPQUFPM0c7UUFFMUMsTUFBTTRHLFNBQVNDLGFBQWFDLE9BQU8sQ0FBQyxJQUFJLENBQUNMLGFBQWEsQ0FBQztRQUN2RCxPQUFPRyxTQUFTRyxLQUFLQyxLQUFLLENBQUNKLFVBQVU1RztJQUN2QztJQUVBLE9BQU9pSCxlQUErQjtRQUNwQyxJQUFJLElBQTZCLEVBQUUsT0FBT2hHO1FBRTFDLE1BQU0yRixTQUFTQyxhQUFhQyxPQUFPLENBQUMsSUFBSSxDQUFDTCxhQUFhLENBQUM7UUFDdkQsT0FBT0csU0FBU0csS0FBS0MsS0FBSyxDQUFDSixVQUFVM0Y7SUFDdkM7SUFFQSxPQUFPaUcsY0FBNkI7UUFDbEMsSUFBSSxJQUE2QixFQUFFO1lBQ2pDQyxRQUFRQyxHQUFHLENBQUMsMENBQTBDcEYsYUFBYW9CLE1BQU07WUFDekUsT0FBT3BCO1FBQ1Q7UUFFQSxNQUFNNEUsU0FBU0MsYUFBYUMsT0FBTyxDQUFDLElBQUksQ0FBQ0wsYUFBYSxDQUFDO1FBQ3ZELE1BQU1ZLFdBQVdULFNBQVNHLEtBQUtDLEtBQUssQ0FBQ0osVUFBVTVFO1FBQy9DbUYsUUFBUUMsR0FBRyxDQUFDLGtDQUFrQ0MsU0FBU2pFLE1BQU07UUFDN0QrRCxRQUFRQyxHQUFHLENBQUMsZ0JBQWdCQyxTQUFTQyxHQUFHLENBQUMsQ0FBQ0MsSUFBbUJBLEVBQUV0SCxFQUFFO1FBQ2pFLE9BQU9vSDtJQUNUO0lBRUEsT0FBT0csZ0JBQWdDO1FBQ3JDLElBQUksSUFBNkIsRUFBRSxPQUFPN0Y7UUFFMUMsTUFBTWlGLFNBQVNDLGFBQWFDLE9BQU8sQ0FBQyxJQUFJLENBQUNMLGFBQWEsQ0FBQztRQUN2RCxPQUFPRyxTQUFTRyxLQUFLQyxLQUFLLENBQUNKLFVBQVVqRjtJQUN2QztJQUVBLE9BQU84RixhQUEyQjtRQUNoQyxJQUFJLElBQTZCLEVBQUUsT0FBT2pFO1FBRTFDLE1BQU1vRCxTQUFTQyxhQUFhQyxPQUFPLENBQUMsSUFBSSxDQUFDTCxhQUFhLENBQUM7UUFDdkQsT0FBT0csU0FBU0csS0FBS0MsS0FBSyxDQUFDSixVQUFVcEQ7SUFDdkM7SUFFQSxPQUFPa0UsWUFBeUI7UUFDOUIsSUFBSSxJQUE2QixFQUFFLE9BQU9wRDtRQUUxQyxNQUFNc0MsU0FBU0MsYUFBYUMsT0FBTyxDQUFDLElBQUksQ0FBQ0wsYUFBYSxDQUFDO1FBQ3ZELE9BQU9HLFNBQVNHLEtBQUtDLEtBQUssQ0FBQ0osVUFBVXRDO0lBQ3ZDO0lBRUEsT0FBT3FELFVBQVVDLEtBQWlCLEVBQVE7UUFDeEMsSUFBSSxLQUE2QixFQUFFLEVBRWxDO0lBQ0g7SUFFQSxPQUFPRyxjQUFjbEQsS0FBcUIsRUFBUTtRQUNoRCxJQUFJLEtBQTZCLEVBQUUsRUFFbEM7SUFDSDtJQUVBLE9BQU9tRCxhQUFhWCxRQUF1QixFQUFRO1FBQ2pELElBQUksS0FBNkIsRUFBRSxFQVFsQztJQUNIO0lBRUEsT0FBT2MsZUFBZUMsVUFBMEIsRUFBUTtRQUN0RCxJQUFJLEtBQTZCLEVBQUUsRUFFbEM7SUFDSDtJQUVBLE9BQU9DLFlBQVlDLE9BQXFCLEVBQVE7UUFDOUMsSUFBSSxLQUE2QixFQUFFLEVBRWxDO0lBQ0g7SUFFQSxPQUFPQyxXQUFXQyxNQUFtQixFQUFRO1FBQzNDLElBQUksS0FBNkIsRUFBRSxFQUVsQztJQUNIO0lBRUEsT0FBT0MsYUFBcUI7UUFDMUIsT0FBT0MsS0FBS0MsR0FBRyxHQUFHQyxRQUFRLEtBQUtDLEtBQUtDLE1BQU0sR0FBR0YsUUFBUSxDQUFDLElBQUlHLFNBQVMsQ0FBQyxHQUFHO0lBQ3pFO0lBRUEsd0NBQXdDO0lBQ3hDLE9BQU9DLGVBQXFCO1FBQzFCLElBQUksS0FBNkIsRUFBRSxFQU9sQztJQUNIO0lBRUEsT0FBT0Usc0JBQThCO1FBQ25DLE1BQU1DLE9BQU8sSUFBSVQ7UUFDakIsTUFBTVUsT0FBT0QsS0FBS0UsV0FBVyxHQUFHVCxRQUFRLEdBQUdVLEtBQUssQ0FBQyxDQUFDO1FBQ2xELE1BQU1DLFFBQVEsQ0FBQ0osS0FBS0ssUUFBUSxLQUFLLEdBQUdaLFFBQVEsR0FBR2EsUUFBUSxDQUFDLEdBQUc7UUFDM0QsTUFBTUMsTUFBTVAsS0FBS1EsT0FBTyxHQUFHZixRQUFRLEdBQUdhLFFBQVEsQ0FBQyxHQUFHO1FBQ2xELE1BQU1qQixTQUFTLElBQUksQ0FBQ2QsU0FBUztRQUM3QixNQUFNa0MsY0FBY3BCLE9BQU9xQixNQUFNLENBQUNDLENBQUFBLFFBQ2hDQSxNQUFNeEosVUFBVSxDQUFDeUosVUFBVSxDQUFDLEdBQUdaLEtBQUtFLFdBQVcsR0FBRyxDQUFDLEVBQUVFLE1BQU0sQ0FBQyxFQUFFRyxLQUFLO1FBRXJFLE1BQU1NLGFBQWEsQ0FBQ0osWUFBWXhHLE1BQU0sR0FBRyxHQUFHd0YsUUFBUSxHQUFHYSxRQUFRLENBQUMsR0FBRztRQUNuRSxPQUFPLENBQUMsR0FBRyxFQUFFTCxPQUFPRyxRQUFRRyxJQUFJLENBQUMsRUFBRU0sWUFBWTtJQUNqRDtJQUVBLCtCQUErQjtJQUMvQixPQUFPQyxjQUF5QjtRQUM5QixJQUFJLElBQTZCLEVBQUUsT0FBTyxJQUFJLENBQUNDLGVBQWU7UUFFOUQsTUFBTXRELFNBQVNDLGFBQWFDLE9BQU8sQ0FBQztRQUNwQyxJQUFJRixRQUFRO1lBQ1YsT0FBT0csS0FBS0MsS0FBSyxDQUFDSjtRQUNwQjtRQUNBLE9BQU8sSUFBSSxDQUFDc0QsZUFBZTtJQUM3QjtJQUVBLE9BQU9DLGFBQWFDLE1BQWlCLEVBQVE7UUFDM0MsSUFBSSxLQUE2QixFQUFFLEVBRWxDO0lBQ0g7SUFFQSxPQUFPQyxxQkFBc0M7UUFDM0MsSUFBSSxJQUE2QixFQUFFLE9BQU8sSUFBSSxDQUFDQyxzQkFBc0I7UUFFckUsTUFBTTFELFNBQVNDLGFBQWFDLE9BQU8sQ0FBQztRQUNwQyxJQUFJRixRQUFRO1lBQ1YsT0FBT0csS0FBS0MsS0FBSyxDQUFDSjtRQUNwQjtRQUNBLE9BQU8sSUFBSSxDQUFDMEQsc0JBQXNCO0lBQ3BDO0lBRUEsT0FBT0Msb0JBQW9CQyxVQUEyQixFQUFRO1FBQzVELElBQUksS0FBNkIsRUFBRSxFQUVsQztJQUNIO0lBRUEsc0JBQXNCO0lBQ3RCLE9BQU9DLG1CQUFtQztRQUN4QyxJQUFJLElBQTZCLEVBQUUsT0FBTyxJQUFJLENBQUNDLG9CQUFvQjtRQUVuRSxNQUFNOUQsU0FBU0MsYUFBYUMsT0FBTyxDQUFDO1FBQ3BDLElBQUlGLFFBQVE7WUFDVixPQUFPRyxLQUFLQyxLQUFLLENBQUNKO1FBQ3BCO1FBQ0EsT0FBTyxJQUFJLENBQUM4RCxvQkFBb0I7SUFDbEM7SUFFQSxPQUFPQyxrQkFBa0JDLFNBQXlCLEVBQVE7UUFDeEQsSUFBSSxLQUE2QixFQUFFLEVBRWxDO0lBQ0g7SUFFQSxPQUFPQyxrQkFBaUM7UUFDdEMsSUFBSSxJQUE2QixFQUFFLE9BQU8sSUFBSSxDQUFDQyxtQkFBbUI7UUFFbEUsTUFBTWxFLFNBQVNDLGFBQWFDLE9BQU8sQ0FBQztRQUNwQyxJQUFJRixRQUFRO1lBQ1YsT0FBT0csS0FBS0MsS0FBSyxDQUFDSjtRQUNwQjtRQUNBLE9BQU8sSUFBSSxDQUFDa0UsbUJBQW1CO0lBQ2pDO0lBRUEsT0FBT0MsaUJBQWlCQyxRQUF1QixFQUFRO1FBQ3JELElBQUksS0FBNkIsRUFBRSxFQUVsQztJQUNIO0lBRUEsT0FBT0Msc0JBQThDO1FBQ25ELElBQUksSUFBNkIsRUFBRSxPQUFPLElBQUksQ0FBQ0MsdUJBQXVCO1FBRXRFLE1BQU10RSxTQUFTQyxhQUFhQyxPQUFPLENBQUM7UUFDcEMsSUFBSUYsUUFBUTtZQUNWLE9BQU9HLEtBQUtDLEtBQUssQ0FBQ0o7UUFDcEI7UUFDQSxPQUFPLElBQUksQ0FBQ3NFLHVCQUF1QjtJQUNyQztJQUVBLE9BQU9DLHFCQUFxQkMsVUFBa0MsRUFBUTtRQUNwRSxJQUFJLEtBQTZCLEVBQUUsRUFFbEM7SUFDSDs7UUFFQSw4REFBOEQ7YUFDdkRsQixrQkFBNkIsRUFBRTs7O2FBRS9CSSx5QkFBMEMsRUFBRTs7O1FBRW5ELHFDQUFxQzthQUM5QkksdUJBQXVDO1lBQzVDO2dCQUNFekssSUFBSTtnQkFDSmdDLE1BQU07Z0JBQ05vSixRQUFRO2dCQUNSQyxRQUFRO2dCQUNSQyxRQUFRO2dCQUNSeEosYUFBYTtnQkFDYkcsVUFBVTtnQkFDVmtKLFlBQVk7b0JBQ1Y7d0JBQ0VuTCxJQUFJO3dCQUNKeUcsTUFBTTt3QkFDTnpFLE1BQU07d0JBQ051SixPQUFPOzRCQUNMMUssU0FBUzs0QkFDVDJLLE9BQU87Z0NBQUVDLGlCQUFpQjtnQ0FBV3BHLE9BQU87NEJBQVU7d0JBQ3hEO3dCQUNBcUcsVUFBVTs0QkFBRUMsR0FBRzs0QkFBR0MsR0FBRzt3QkFBRTt3QkFDdkJ0RyxNQUFNOzRCQUFFdUcsT0FBTzs0QkFBUUMsUUFBUTt3QkFBUTt3QkFDdkNDLFdBQVc7b0JBQ2I7aUJBQ0Q7Z0JBQ0RDLFNBQVM7Z0JBQ1RDLFdBQVc7Z0JBQ1hDLGVBQWU7Z0JBQ2ZDLFdBQVc7Z0JBQ1hDLE1BQU07b0JBQUM7b0JBQVc7b0JBQVU7aUJBQVc7Z0JBQ3ZDQyxXQUFXO2dCQUNYQyxXQUFXO2dCQUNYQyxZQUFZO2dCQUNaOUosUUFBUTtnQkFDUitKLFVBQVU7b0JBQ1JwSyxRQUFRO3dCQUFDO3dCQUFXO3dCQUFXO3FCQUFVO29CQUN6Q3FLLE9BQU87d0JBQUM7d0JBQVM7cUJBQVE7b0JBQ3pCQyxRQUFRO29CQUNSQyxZQUFZO2dCQUNkO1lBQ0Y7U0FDRDs7O1FBRUQsc0NBQXNDO2FBQy9COUIsc0JBQXFDO1lBQzFDO2dCQUNFN0ssSUFBSTtnQkFDSmdDLE1BQU07Z0JBQ05GLGFBQWE7Z0JBQ2JxSixZQUFZLEVBQUU7Z0JBQ2R5QixZQUFZO2dCQUNaQyxnQkFBZ0I7Z0JBQ2hCNUksVUFBVTtvQkFDUnJELE9BQU87b0JBQ1BrQixhQUFhO29CQUNiZ0wsVUFBVTt3QkFBQzt3QkFBZ0I7d0JBQVM7cUJBQVM7b0JBQzdDbk0sVUFBVTtvQkFDVm9NLFdBQVc7Z0JBQ2I7Z0JBQ0FDLGFBQWE7Z0JBQ2JYLFdBQVc7Z0JBQ1hDLFdBQVc7Z0JBQ1hXLFdBQVc7Z0JBQ1hDLFNBQVM7WUFDWDtTQUNEOzs7UUFFRCxzQ0FBc0M7YUFDL0JqQywwQkFBa0Q7WUFDdkQ7Z0JBQ0VqTCxJQUFJO2dCQUNKZ0MsTUFBTTtnQkFDTm9KLFFBQVE7Z0JBQ1IzRSxNQUFNO2dCQUNOeEUsVUFBVTtnQkFDVkgsYUFBYTtnQkFDYlYsTUFBTTtnQkFDTjRLLFNBQVM7Z0JBQ1RtQixjQUFjO29CQUNadE0sU0FBUztvQkFDVDJLLE9BQU87d0JBQUVDLGlCQUFpQjt3QkFBV3BHLE9BQU87b0JBQVU7Z0JBQ3hEO2dCQUNBK0gsYUFBYTtvQkFBRXZCLE9BQU87b0JBQVFDLFFBQVE7Z0JBQVE7Z0JBQzlDdUIsVUFBVTtnQkFDVmxCLFdBQVc7Z0JBQ1hDLE1BQU07b0JBQUM7b0JBQVU7b0JBQVU7aUJBQU87Z0JBQ2xDRyxZQUFZO1lBQ2Q7WUFDQTtnQkFDRXZNLElBQUk7Z0JBQ0pnQyxNQUFNO2dCQUNOb0osUUFBUTtnQkFDUjNFLE1BQU07Z0JBQ054RSxVQUFVO2dCQUNWSCxhQUFhO2dCQUNiVixNQUFNO2dCQUNONEssU0FBUztnQkFDVG1CLGNBQWM7b0JBQ1p0TSxTQUFTO29CQUNUMkssT0FBTzt3QkFBRUMsaUJBQWlCO3dCQUFXcEcsT0FBTztvQkFBVTtnQkFDeEQ7Z0JBQ0ErSCxhQUFhO29CQUFFdkIsT0FBTztvQkFBU0MsUUFBUTtnQkFBTztnQkFDOUN1QixVQUFVO2dCQUNWbEIsV0FBVztnQkFDWEMsTUFBTTtvQkFBQztvQkFBZTtvQkFBVTtpQkFBTTtnQkFDdENHLFlBQVk7WUFDZDtTQUNEOztJQUVELCtCQUErQjtJQUMvQixPQUFPZSxpQkFBbUM7UUFDeEMsSUFBSSxJQUE2QixFQUFFLE9BQU8sRUFBRTtRQUU1QyxNQUFNM0csU0FBU0MsYUFBYUMsT0FBTyxDQUFDLElBQUksQ0FBQ0wsYUFBYSxDQUFDO1FBQ3ZELE9BQU9HLFNBQVNHLEtBQUtDLEtBQUssQ0FBQ0osVUFBVSxFQUFFO0lBQ3pDO0lBRUEsT0FBTzRHLGNBQWNDLFFBQXdCLEVBQWtCO1FBQzdELElBQUksSUFBNkIsRUFBRSxPQUFPQTtRQUUxQyxNQUFNQyxZQUFZLElBQUksQ0FBQ0gsY0FBYztRQUNyQ0csVUFBVUMsSUFBSSxDQUFDRjtRQUNmNUcsYUFBYWdCLE9BQU8sQ0FBQyxJQUFJLENBQUNwQixhQUFhLENBQUMsZ0JBQWdCTSxLQUFLZSxTQUFTLENBQUM0RjtRQUN2RSxPQUFPRDtJQUNUO0lBRUEsT0FBT0csaUJBQWlCM04sRUFBVSxFQUFFNE4sZUFBd0MsRUFBeUI7UUFDbkcsSUFBSSxJQUE2QixFQUFFLE9BQU87UUFFMUMsTUFBTUgsWUFBWSxJQUFJLENBQUNILGNBQWM7UUFDckMsTUFBTU8sUUFBUUosVUFBVUssU0FBUyxDQUFDeEcsQ0FBQUEsSUFBS0EsRUFBRXRILEVBQUUsS0FBS0E7UUFFaEQsSUFBSTZOLFVBQVUsQ0FBQyxHQUFHLE9BQU87UUFFekJKLFNBQVMsQ0FBQ0ksTUFBTSxHQUFHO1lBQUUsR0FBR0osU0FBUyxDQUFDSSxNQUFNO1lBQUUsR0FBR0QsZUFBZTtRQUFDO1FBQzdEaEgsYUFBYWdCLE9BQU8sQ0FBQyxJQUFJLENBQUNwQixhQUFhLENBQUMsZ0JBQWdCTSxLQUFLZSxTQUFTLENBQUM0RjtRQUN2RSxPQUFPQSxTQUFTLENBQUNJLE1BQU07SUFDekI7SUFFQSxPQUFPRSxpQkFBaUIvTixFQUFVLEVBQVc7UUFDM0MsSUFBSSxJQUE2QixFQUFFLE9BQU87UUFFMUMsTUFBTXlOLFlBQVksSUFBSSxDQUFDSCxjQUFjO1FBQ3JDLE1BQU1VLG9CQUFvQlAsVUFBVTdELE1BQU0sQ0FBQ3RDLENBQUFBLElBQUtBLEVBQUV0SCxFQUFFLEtBQUtBO1FBRXpELElBQUlnTyxrQkFBa0I3SyxNQUFNLEtBQUtzSyxVQUFVdEssTUFBTSxFQUFFLE9BQU87UUFFMUR5RCxhQUFhZ0IsT0FBTyxDQUFDLElBQUksQ0FBQ3BCLGFBQWEsQ0FBQyxnQkFBZ0JNLEtBQUtlLFNBQVMsQ0FBQ21HO1FBQ3ZFLE9BQU87SUFDVDtJQUVBLE9BQU9DLGtCQUFrQmpPLEVBQVUsRUFBeUI7UUFDMUQsSUFBSSxJQUE2QixFQUFFLE9BQU87UUFFMUMsTUFBTXlOLFlBQVksSUFBSSxDQUFDSCxjQUFjO1FBQ3JDLE9BQU9HLFVBQVVTLElBQUksQ0FBQzVHLENBQUFBLElBQUtBLEVBQUV0SCxFQUFFLEtBQUtBLE9BQU87SUFDN0M7QUFDRiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxmYWlzc1xcRGVza3RvcFxcR3JhZHVhdGlvbiBUb3FzXFxmcm9udGVuZFxcc3JjXFxsaWJcXG1vY2tEYXRhLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vINio2YrYp9mG2KfYqiDZiNmH2YXZitipINmE2YTYqti32YjZitixINmI2KfZhNin2K7Yqtio2KfYsVxuaW1wb3J0IHsgQUlNb2RlbCwgQUlTdWJNb2RlbCwgTW9kZWxBY3Rpdml0eSB9IGZyb20gJ0AvdHlwZXMvYWktbW9kZWxzJ1xuaW1wb3J0IHsgUGFnZVRlbXBsYXRlLCBQYWdlUHJvamVjdCwgQ29tcG9uZW50TGlicmFyeUl0ZW0sIFBhZ2VDb21wb25lbnQgfSBmcm9tICdAL3R5cGVzL3BhZ2UtYnVpbGRlcidcblxuZXhwb3J0IGludGVyZmFjZSBNb2NrUGFnZSB7XG4gIGlkOiBzdHJpbmdcbiAgc2x1Zzogc3RyaW5nXG4gIGlzX3B1Ymxpc2hlZDogYm9vbGVhblxuICBhdXRob3JfaWQ6IHN0cmluZ1xuICBmZWF0dXJlZF9pbWFnZT86IHN0cmluZ1xuICBjcmVhdGVkX2F0OiBzdHJpbmdcbiAgdXBkYXRlZF9hdDogc3RyaW5nXG4gIHBhZ2VfY29udGVudDogTW9ja1BhZ2VDb250ZW50W11cbiAgcHJvZmlsZXM/OiB7XG4gICAgZnVsbF9uYW1lOiBzdHJpbmdcbiAgfVxufVxuXG5leHBvcnQgaW50ZXJmYWNlIE1vY2tQYWdlQ29udGVudCB7XG4gIGlkOiBzdHJpbmdcbiAgcGFnZV9pZDogc3RyaW5nXG4gIGxhbmd1YWdlOiAnYXInIHwgJ2VuJyB8ICdmcidcbiAgdGl0bGU6IHN0cmluZ1xuICBjb250ZW50OiBzdHJpbmdcbiAgbWV0YV9kZXNjcmlwdGlvbj86IHN0cmluZ1xuICBtZXRhX2tleXdvcmRzPzogc3RyaW5nXG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgTW9ja01lbnVJdGVtIHtcbiAgaWQ6IHN0cmluZ1xuICB0aXRsZV9hcjogc3RyaW5nXG4gIHRpdGxlX2VuPzogc3RyaW5nXG4gIHRpdGxlX2ZyPzogc3RyaW5nXG4gIHNsdWc6IHN0cmluZ1xuICBpY29uPzogc3RyaW5nXG4gIHBhcmVudF9pZD86IHN0cmluZ1xuICBvcmRlcl9pbmRleDogbnVtYmVyXG4gIGlzX2FjdGl2ZTogYm9vbGVhblxuICB0YXJnZXRfdHlwZTogJ2ludGVybmFsJyB8ICdleHRlcm5hbCcgfCAncGFnZSdcbiAgdGFyZ2V0X3ZhbHVlOiBzdHJpbmdcbiAgY3JlYXRlZF9hdDogc3RyaW5nXG4gIHVwZGF0ZWRfYXQ6IHN0cmluZ1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIE1vY2tBSVByb3ZpZGVyIHtcbiAgaWQ6IHN0cmluZ1xuICBwcm92aWRlcjogc3RyaW5nXG4gIHByb3ZpZGVyTmFtZTogc3RyaW5nXG4gIGJhc2VVcmw6IHN0cmluZ1xuICBhcGlLZXk6IHN0cmluZ1xuICBtb2RlbHM6IHN0cmluZ1tdXG4gIGRlc2NyaXB0aW9uPzogc3RyaW5nXG4gIHN0YXR1czogJ2FjdGl2ZScgfCAnaW5hY3RpdmUnXG4gIGNyZWF0ZWRBdDogc3RyaW5nXG4gIHVwZGF0ZWRBdDogc3RyaW5nXG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgTW9ja0NhdGVnb3J5IHtcbiAgaWQ6IHN0cmluZ1xuICBuYW1lX2FyOiBzdHJpbmdcbiAgbmFtZV9lbj86IHN0cmluZ1xuICBuYW1lX2ZyPzogc3RyaW5nXG4gIHNsdWc6IHN0cmluZ1xuICBpY29uPzogc3RyaW5nXG4gIGRlc2NyaXB0aW9uPzogc3RyaW5nXG4gIGlzX2FjdGl2ZTogYm9vbGVhblxuICBvcmRlcl9pbmRleDogbnVtYmVyXG4gIGNyZWF0ZWRfYXQ6IHN0cmluZ1xuICB1cGRhdGVkX2F0OiBzdHJpbmdcbn1cblxuZXhwb3J0IGludGVyZmFjZSBNb2NrUHJvZHVjdCB7XG4gIGlkOiBzdHJpbmdcbiAgbmFtZTogc3RyaW5nXG4gIGRlc2NyaXB0aW9uOiBzdHJpbmdcbiAgY2F0ZWdvcnk6IHN0cmluZyAvLyDYqti62YrZitixINmF2YYgdW5pb24gdHlwZSDYpdmE2Ykgc3RyaW5nINmE2YTZhdix2YjZhtipXG4gIHByaWNlOiBudW1iZXJcbiAgcmVudGFsX3ByaWNlPzogbnVtYmVyXG4gIGNvbG9yczogc3RyaW5nW11cbiAgc2l6ZXM6IHN0cmluZ1tdXG4gIGltYWdlczogc3RyaW5nW11cbiAgc3RvY2tfcXVhbnRpdHk6IG51bWJlclxuICBpc19hdmFpbGFibGU6IGJvb2xlYW5cbiAgaXNfcHVibGlzaGVkOiBib29sZWFuIC8vINit2YLZhCDYrNiv2YrYryDZhNmE2KrYrdmD2YUg2YHZiiDYp9mE2YbYtNixXG4gIGNyZWF0ZWRfYXQ6IHN0cmluZ1xuICB1cGRhdGVkX2F0OiBzdHJpbmdcbiAgcmF0aW5nPzogbnVtYmVyXG4gIHJldmlld3NfY291bnQ/OiBudW1iZXJcbiAgZmVhdHVyZXM/OiBzdHJpbmdbXVxuICBzcGVjaWZpY2F0aW9ucz86IFJlY29yZDxzdHJpbmcsIGFueT5cbn1cblxuZXhwb3J0IGludGVyZmFjZSBNb2NrU2Nob29sIHtcbiAgaWQ6IHN0cmluZ1xuICBhZG1pbl9pZD86IHN0cmluZ1xuICBuYW1lOiBzdHJpbmdcbiAgbmFtZV9lbj86IHN0cmluZ1xuICBuYW1lX2ZyPzogc3RyaW5nXG4gIGFkZHJlc3M/OiBzdHJpbmdcbiAgY2l0eT86IHN0cmluZ1xuICBwaG9uZT86IHN0cmluZ1xuICBlbWFpbD86IHN0cmluZ1xuICB3ZWJzaXRlPzogc3RyaW5nXG4gIGxvZ29fdXJsPzogc3RyaW5nXG4gIGdyYWR1YXRpb25fZGF0ZT86IHN0cmluZ1xuICBzdHVkZW50X2NvdW50OiBudW1iZXJcbiAgaXNfYWN0aXZlOiBib29sZWFuXG4gIHNldHRpbmdzPzogUmVjb3JkPHN0cmluZywgYW55PlxuICBjcmVhdGVkX2F0OiBzdHJpbmdcbiAgdXBkYXRlZF9hdDogc3RyaW5nXG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgTW9ja09yZGVyIHtcbiAgaWQ6IHN0cmluZ1xuICBvcmRlcl9udW1iZXI6IHN0cmluZ1xuICBjdXN0b21lcl9pZDogc3RyaW5nXG4gIGN1c3RvbWVyX25hbWU6IHN0cmluZ1xuICBjdXN0b21lcl9lbWFpbDogc3RyaW5nXG4gIGN1c3RvbWVyX3Bob25lPzogc3RyaW5nXG4gIHN0YXR1czogJ3BlbmRpbmcnIHwgJ2NvbmZpcm1lZCcgfCAnaW5fcHJvZHVjdGlvbicgfCAnc2hpcHBlZCcgfCAnZGVsaXZlcmVkJyB8ICdjYW5jZWxsZWQnXG4gIGl0ZW1zOiBNb2NrT3JkZXJJdGVtW11cbiAgc3VidG90YWw6IG51bWJlclxuICB0YXg6IG51bWJlclxuICBzaGlwcGluZ19jb3N0OiBudW1iZXJcbiAgdG90YWw6IG51bWJlclxuICBwYXltZW50X3N0YXR1czogJ3BlbmRpbmcnIHwgJ3BhaWQnIHwgJ2ZhaWxlZCcgfCAncmVmdW5kZWQnXG4gIHBheW1lbnRfbWV0aG9kPzogc3RyaW5nXG4gIHNoaXBwaW5nX2FkZHJlc3M6IHtcbiAgICBzdHJlZXQ6IHN0cmluZ1xuICAgIGNpdHk6IHN0cmluZ1xuICAgIHN0YXRlOiBzdHJpbmdcbiAgICBwb3N0YWxfY29kZTogc3RyaW5nXG4gICAgY291bnRyeTogc3RyaW5nXG4gIH1cbiAgdHJhY2tpbmdfbnVtYmVyPzogc3RyaW5nXG4gIG5vdGVzPzogc3RyaW5nXG4gIGNyZWF0ZWRfYXQ6IHN0cmluZ1xuICB1cGRhdGVkX2F0OiBzdHJpbmdcbiAgZGVsaXZlcnlfZGF0ZT86IHN0cmluZ1xuICBzY2hvb2xfaWQ/OiBzdHJpbmdcbiAgc2Nob29sX25hbWU/OiBzdHJpbmdcbn1cblxuZXhwb3J0IGludGVyZmFjZSBNb2NrT3JkZXJJdGVtIHtcbiAgaWQ6IHN0cmluZ1xuICBvcmRlcl9pZDogc3RyaW5nXG4gIHByb2R1Y3RfaWQ6IHN0cmluZ1xuICBwcm9kdWN0X25hbWU6IHN0cmluZ1xuICBwcm9kdWN0X2ltYWdlOiBzdHJpbmdcbiAgY2F0ZWdvcnk6IHN0cmluZ1xuICBxdWFudGl0eTogbnVtYmVyXG4gIHVuaXRfcHJpY2U6IG51bWJlclxuICB0b3RhbF9wcmljZTogbnVtYmVyXG4gIGN1c3RvbWl6YXRpb25zPzoge1xuICAgIGNvbG9yPzogc3RyaW5nXG4gICAgc2l6ZT86IHN0cmluZ1xuICAgIGVtYnJvaWRlcnk/OiBzdHJpbmdcbiAgICBzcGVjaWFsX3JlcXVlc3RzPzogc3RyaW5nXG4gIH1cbn1cblxuLy8g2KjZitin2YbYp9iqINmI2YfZhdmK2Kkg2YTZhNi12YHYrdin2KpcbmV4cG9ydCBjb25zdCBtb2NrUGFnZXM6IE1vY2tQYWdlW10gPSBbXG4gIHtcbiAgICBpZDogJzEnLFxuICAgIHNsdWc6ICdhYm91dC11cycsXG4gICAgaXNfcHVibGlzaGVkOiB0cnVlLFxuICAgIGF1dGhvcl9pZDogJ2FkbWluLTEnLFxuICAgIGZlYXR1cmVkX2ltYWdlOiAnL2ltYWdlcy9hYm91dC1oZXJvLmpwZycsXG4gICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMTVUMTA6MDA6MDBaJyxcbiAgICB1cGRhdGVkX2F0OiAnMjAyNC0wMS0yMFQxNDozMDowMFonLFxuICAgIHByb2ZpbGVzOiB7XG4gICAgICBmdWxsX25hbWU6ICfZhdiv2YrYsSDYp9mE2YbYuNin2YUnXG4gICAgfSxcbiAgICBwYWdlX2NvbnRlbnQ6IFtcbiAgICAgIHtcbiAgICAgICAgaWQ6ICcxLWFyJyxcbiAgICAgICAgcGFnZV9pZDogJzEnLFxuICAgICAgICBsYW5ndWFnZTogJ2FyJyxcbiAgICAgICAgdGl0bGU6ICfZhdmGINmG2K3ZhicsXG4gICAgICAgIGNvbnRlbnQ6ICc8aDI+2YXYsdit2KjYp9mLINio2YPZhSDZgdmKINmF2YbYtdipINij2LLZitin2KEg2KfZhNiq2K7YsdisPC9oMj48cD7Zhtit2YYg2YXZhti12Kkg2YXYqtiu2LXYtdipINmB2Yog2KrYo9is2YrYsSDZiNio2YrYuSDYo9iy2YrYp9ihINin2YTYqtiu2LHYrCDYp9mE2YXYutix2KjZitipINin2YTYo9i12YrZhNipLiDZhtmH2K/ZgSDYpdmE2Ykg2KzYudmEINmK2YjZhSDYqtiu2LHYrNmD2YUg2YTYpyDZitmP2YbYs9mJINmF2YYg2K7ZhNin2YQg2KrZiNmB2YrYsSDYo9is2YXZhCDYp9mE2KPYstmK2KfYoSDYp9mE2KrZgtmE2YrYr9mK2KkuPC9wPjxwPtiq2KPYs9iz2Kog2YXZhti12KrZhtinINi52KfZhSAyMDI0INio2YfYr9mBINiu2K/ZhdipINin2YTYt9mE2KfYqCDZiNin2YTYt9in2YTYqNin2Kog2YHZiiDYrNmF2YrYuSDYo9mG2K3Yp9ihINin2YTZhdi62LHYqNiMINmI2YbZgdiq2K7YsSDYqNiq2YLYr9mK2YUg2K7Yr9mF2KfYqiDYudin2YTZitipINin2YTYrNmI2K/YqSDZiNij2LPYudin2LEg2YXZhtin2LPYqNipLjwvcD4nLFxuICAgICAgICBtZXRhX2Rlc2NyaXB0aW9uOiAn2KrYudix2YEg2LnZhNmJINmF2YbYtdipINij2LLZitin2KEg2KfZhNiq2K7YsdisINin2YTZhdi62LHYqNmK2KkgLSDYrtiv2YXYp9iqINiq2KPYrNmK2LEg2YjYqNmK2Lkg2KfZhNij2LLZitin2KEg2KfZhNiq2YLZhNmK2K/ZitipJyxcbiAgICAgICAgbWV0YV9rZXl3b3JkczogJ9ij2LLZitin2KEg2KfZhNiq2K7Ysdis2Iwg2KfZhNmF2LrYsdio2Iwg2KrYo9is2YrYsdiMINio2YrYudiMINiq2YLZhNmK2K/ZiidcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIGlkOiAnMS1lbicsXG4gICAgICAgIHBhZ2VfaWQ6ICcxJyxcbiAgICAgICAgbGFuZ3VhZ2U6ICdlbicsXG4gICAgICAgIHRpdGxlOiAnQWJvdXQgVXMnLFxuICAgICAgICBjb250ZW50OiAnPGgyPldlbGNvbWUgdG8gR3JhZHVhdGlvbiBBdHRpcmUgUGxhdGZvcm08L2gyPjxwPldlIGFyZSBhIHNwZWNpYWxpemVkIHBsYXRmb3JtIGZvciByZW50aW5nIGFuZCBzZWxsaW5nIGF1dGhlbnRpYyBNb3JvY2NhbiBncmFkdWF0aW9uIGF0dGlyZS4gV2UgYWltIHRvIG1ha2UgeW91ciBncmFkdWF0aW9uIGRheSB1bmZvcmdldHRhYmxlIGJ5IHByb3ZpZGluZyB0aGUgbW9zdCBiZWF1dGlmdWwgdHJhZGl0aW9uYWwgb3V0Zml0cy48L3A+PHA+T3VyIHBsYXRmb3JtIHdhcyBmb3VuZGVkIGluIDIwMjQgdG8gc2VydmUgc3R1ZGVudHMgdGhyb3VnaG91dCBNb3JvY2NvLCBhbmQgd2UgcHJpZGUgb3Vyc2VsdmVzIG9uIHByb3ZpZGluZyBoaWdoLXF1YWxpdHkgc2VydmljZXMgYXQgYWZmb3JkYWJsZSBwcmljZXMuPC9wPicsXG4gICAgICAgIG1ldGFfZGVzY3JpcHRpb246ICdMZWFybiBhYm91dCB0aGUgTW9yb2NjYW4gR3JhZHVhdGlvbiBBdHRpcmUgUGxhdGZvcm0gLSB0cmFkaXRpb25hbCBvdXRmaXQgcmVudGFsIGFuZCBzYWxlcyBzZXJ2aWNlcycsXG4gICAgICAgIG1ldGFfa2V5d29yZHM6ICdncmFkdWF0aW9uIGF0dGlyZSwgTW9yb2NjbywgcmVudGFsLCBzYWxlcywgdHJhZGl0aW9uYWwnXG4gICAgICB9XG4gICAgXVxuICB9LFxuICB7XG4gICAgaWQ6ICcyJyxcbiAgICBzbHVnOiAnc2VydmljZXMnLFxuICAgIGlzX3B1Ymxpc2hlZDogdHJ1ZSxcbiAgICBhdXRob3JfaWQ6ICdhZG1pbi0xJyxcbiAgICBjcmVhdGVkX2F0OiAnMjAyNC0wMS0xNlQwOTowMDowMFonLFxuICAgIHVwZGF0ZWRfYXQ6ICcyMDI0LTAxLTIyVDE2OjQ1OjAwWicsXG4gICAgcHJvZmlsZXM6IHtcbiAgICAgIGZ1bGxfbmFtZTogJ9mF2K/ZitixINin2YTZhti42KfZhSdcbiAgICB9LFxuICAgIHBhZ2VfY29udGVudDogW1xuICAgICAge1xuICAgICAgICBpZDogJzItYXInLFxuICAgICAgICBwYWdlX2lkOiAnMicsXG4gICAgICAgIGxhbmd1YWdlOiAnYXInLFxuICAgICAgICB0aXRsZTogJ9iu2K/Zhdin2KrZhtinJyxcbiAgICAgICAgY29udGVudDogJzxoMj7Yrtiv2YXYp9iq2YbYpyDYp9mE2YXYqtmF2YrYstipPC9oMj48aDM+2KrYo9is2YrYsSDYp9mE2KPYstmK2KfYoTwvaDM+PHA+2YbZiNmB2LEg2K7Yr9mF2Kkg2KrYo9is2YrYsSDYo9iy2YrYp9ihINin2YTYqtiu2LHYrCDZhNmB2KrYsdin2Kog2YXYsdmG2Kkg2YXYuSDYttmF2KfZhiDYp9mE2YbYuNin2YHYqSDZiNin2YTYrNmI2K/YqS48L3A+PGgzPtio2YrYuSDYp9mE2KPYstmK2KfYoTwvaDM+PHA+2KXZhdmD2KfZhtmK2Kkg2LTYsdin2KEg2KfZhNij2LLZitin2KEg2YTZhNin2K3YqtmB2KfYuCDYqNmH2Kcg2YPYsNmD2LHZiSDYrNmF2YrZhNipINmF2YYg2YrZiNmFINin2YTYqtiu2LHYrC48L3A+PGgzPtin2YTYqtiu2LXZiti1PC9oMz48cD7Yrtiv2YXYp9iqINiq2K7YtdmK2LUg2KfZhNij2LLZitin2KEg2K3Ys9ioINin2YTZhdmC2KfYs9in2Kog2YjYp9mE2KrZgdi22YrZhNin2Kog2KfZhNi02K7YtdmK2KkuPC9wPicsXG4gICAgICAgIG1ldGFfZGVzY3JpcHRpb246ICfYrtiv2YXYp9iqINmF2YbYtdipINij2LLZitin2KEg2KfZhNiq2K7YsdisIC0g2KrYo9is2YrYsSDZiNio2YrYuSDZiNiq2K7YtdmK2LUg2KfZhNij2LLZitin2KEg2KfZhNiq2YLZhNmK2K/ZitipINin2YTZhdi62LHYqNmK2KknLFxuICAgICAgICBtZXRhX2tleXdvcmRzOiAn2K7Yr9mF2KfYqtiMINiq2KPYrNmK2LHYjCDYqNmK2LnYjCDYqtiu2LXZiti12Iwg2KPYstmK2KfYoSDYp9mE2KrYrtix2KwnXG4gICAgICB9XG4gICAgXVxuICB9LFxuICB7XG4gICAgaWQ6ICczJyxcbiAgICBzbHVnOiAnY29udGFjdCcsXG4gICAgaXNfcHVibGlzaGVkOiB0cnVlLFxuICAgIGF1dGhvcl9pZDogJ2FkbWluLTEnLFxuICAgIGNyZWF0ZWRfYXQ6ICcyMDI0LTAxLTE3VDExOjAwOjAwWicsXG4gICAgdXBkYXRlZF9hdDogJzIwMjQtMDEtMTdUMTE6MDA6MDBaJyxcbiAgICBwcm9maWxlczoge1xuICAgICAgZnVsbF9uYW1lOiAn2YXYr9mK2LEg2KfZhNmG2LjYp9mFJ1xuICAgIH0sXG4gICAgcGFnZV9jb250ZW50OiBbXG4gICAgICB7XG4gICAgICAgIGlkOiAnMy1hcicsXG4gICAgICAgIHBhZ2VfaWQ6ICczJyxcbiAgICAgICAgbGFuZ3VhZ2U6ICdhcicsXG4gICAgICAgIHRpdGxlOiAn2KfYqti12YQg2KjZhtinJyxcbiAgICAgICAgY29udGVudDogJzxoMj7YqtmI2KfYtdmEINmF2LnZhtinPC9oMj48cD7Zhtit2YYg2YfZhtinINmE2K7Yr9mF2KrZg9mFINmB2Yog2KPZiiDZiNmC2KouINmK2YXZg9mG2YPZhSDYp9mE2KrZiNin2LXZhCDZhdi52YbYpyDYudio2LE6PC9wPjx1bD48bGk+2KfZhNmH2KfYqtmBOiArMjEyIDVYWC1YWFhYWFg8L2xpPjxsaT7Yp9mE2KjYsdmK2K8g2KfZhNil2YTZg9iq2LHZiNmG2Yo6IGluZm9AZ3JhZHVhdGlvbnRvcXMuY29tPC9saT48bGk+2KfZhNi52YbZiNin2YY6INin2YTYr9in2LEg2KfZhNio2YrYttin2KHYjCDYp9mE2YXYutix2Kg8L2xpPjwvdWw+JyxcbiAgICAgICAgbWV0YV9kZXNjcmlwdGlvbjogJ9iq2YjYp9i12YQg2YXYuSDZhdmG2LXYqSDYo9iy2YrYp9ihINin2YTYqtiu2LHYrCDYp9mE2YXYutix2KjZitipJyxcbiAgICAgICAgbWV0YV9rZXl3b3JkczogJ9in2KrYtdin2YTYjCDYqtmI2KfYtdmE2Iwg2K7Yr9mF2Kkg2KfZhNi52YXZhNin2KEnXG4gICAgICB9XG4gICAgXVxuICB9XG5dXG5cbi8vINio2YrYp9mG2KfYqiDZiNmH2YXZitipINmE2YTZgtmI2KfYptmFXG5leHBvcnQgY29uc3QgbW9ja01lbnVJdGVtczogTW9ja01lbnVJdGVtW10gPSBbXG4gIHtcbiAgICBpZDogJzEnLFxuICAgIHRpdGxlX2FyOiAn2KfZhNix2KbZitiz2YrYqScsXG4gICAgdGl0bGVfZW46ICdIb21lJyxcbiAgICB0aXRsZV9mcjogJ0FjY3VlaWwnLFxuICAgIHNsdWc6ICdob21lJyxcbiAgICBpY29uOiAnSG9tZScsXG4gICAgb3JkZXJfaW5kZXg6IDEsXG4gICAgaXNfYWN0aXZlOiB0cnVlLFxuICAgIHRhcmdldF90eXBlOiAnaW50ZXJuYWwnLFxuICAgIHRhcmdldF92YWx1ZTogJy8nLFxuICAgIGNyZWF0ZWRfYXQ6ICcyMDI0LTAxLTE1VDEwOjAwOjAwWicsXG4gICAgdXBkYXRlZF9hdDogJzIwMjQtMDEtMTVUMTA6MDA6MDBaJ1xuICB9LFxuICB7XG4gICAgaWQ6ICcyJyxcbiAgICB0aXRsZV9hcjogJ9mF2YYg2YbYrdmGJyxcbiAgICB0aXRsZV9lbjogJ0Fib3V0IFVzJyxcbiAgICB0aXRsZV9mcjogJ8OAIHByb3BvcycsXG4gICAgc2x1ZzogJ2Fib3V0JyxcbiAgICBpY29uOiAnSW5mbycsXG4gICAgb3JkZXJfaW5kZXg6IDIsXG4gICAgaXNfYWN0aXZlOiB0cnVlLFxuICAgIHRhcmdldF90eXBlOiAnaW50ZXJuYWwnLFxuICAgIHRhcmdldF92YWx1ZTogJy9hYm91dCcsXG4gICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMTVUMTA6MDU6MDBaJyxcbiAgICB1cGRhdGVkX2F0OiAnMjAyNC0wMS0xNVQxMDowNTowMFonXG4gIH0sXG4gIHtcbiAgICBpZDogJzMnLFxuICAgIHRpdGxlX2FyOiAn2K7Yr9mF2KfYqtmG2KcnLFxuICAgIHRpdGxlX2VuOiAnU2VydmljZXMnLFxuICAgIHRpdGxlX2ZyOiAnU2VydmljZXMnLFxuICAgIHNsdWc6ICdzZXJ2aWNlcycsXG4gICAgaWNvbjogJ1NldHRpbmdzJyxcbiAgICBvcmRlcl9pbmRleDogMyxcbiAgICBpc19hY3RpdmU6IHRydWUsXG4gICAgdGFyZ2V0X3R5cGU6ICdpbnRlcm5hbCcsXG4gICAgdGFyZ2V0X3ZhbHVlOiAnL3NlcnZpY2VzJyxcbiAgICBjcmVhdGVkX2F0OiAnMjAyNC0wMS0xNVQxMDoxMDowMFonLFxuICAgIHVwZGF0ZWRfYXQ6ICcyMDI0LTAxLTE1VDEwOjEwOjAwWidcbiAgfSxcbiAge1xuICAgIGlkOiAnNCcsXG4gICAgdGl0bGVfYXI6ICfYp9mE2YXZhtiq2KzYp9iqJyxcbiAgICB0aXRsZV9lbjogJ1Byb2R1Y3RzJyxcbiAgICB0aXRsZV9mcjogJ1Byb2R1aXRzJyxcbiAgICBzbHVnOiAncHJvZHVjdHMnLFxuICAgIGljb246ICdQYWNrYWdlJyxcbiAgICBvcmRlcl9pbmRleDogNCxcbiAgICBpc19hY3RpdmU6IHRydWUsXG4gICAgdGFyZ2V0X3R5cGU6ICdpbnRlcm5hbCcsXG4gICAgdGFyZ2V0X3ZhbHVlOiAnL2NhdGFsb2cnLFxuICAgIGNyZWF0ZWRfYXQ6ICcyMDI0LTAxLTE1VDEwOjE1OjAwWicsXG4gICAgdXBkYXRlZF9hdDogJzIwMjQtMDEtMTVUMTA6MTU6MDBaJ1xuICB9LFxuICB7XG4gICAgaWQ6ICc1JyxcbiAgICB0aXRsZV9hcjogJ9iq2KPYrNmK2LEg2KfZhNij2LLZitin2KEnLFxuICAgIHRpdGxlX2VuOiAnUmVudGFsJyxcbiAgICB0aXRsZV9mcjogJ0xvY2F0aW9uJyxcbiAgICBzbHVnOiAncmVudGFsJyxcbiAgICBwYXJlbnRfaWQ6ICc0JyxcbiAgICBpY29uOiAnQ2FsZW5kYXInLFxuICAgIG9yZGVyX2luZGV4OiAxLFxuICAgIGlzX2FjdGl2ZTogdHJ1ZSxcbiAgICB0YXJnZXRfdHlwZTogJ2ludGVybmFsJyxcbiAgICB0YXJnZXRfdmFsdWU6ICcvY2F0YWxvZz90eXBlPXJlbnRhbCcsXG4gICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMTVUMTA6MjA6MDBaJyxcbiAgICB1cGRhdGVkX2F0OiAnMjAyNC0wMS0xNVQxMDoyMDowMFonXG4gIH0sXG4gIHtcbiAgICBpZDogJzYnLFxuICAgIHRpdGxlX2FyOiAn2KjZiti5INin2YTYo9iy2YrYp9ihJyxcbiAgICB0aXRsZV9lbjogJ1NhbGVzJyxcbiAgICB0aXRsZV9mcjogJ1ZlbnRlJyxcbiAgICBzbHVnOiAnc2FsZXMnLFxuICAgIHBhcmVudF9pZDogJzQnLFxuICAgIGljb246ICdTaG9wcGluZ0NhcnQnLFxuICAgIG9yZGVyX2luZGV4OiAyLFxuICAgIGlzX2FjdGl2ZTogdHJ1ZSxcbiAgICB0YXJnZXRfdHlwZTogJ2ludGVybmFsJyxcbiAgICB0YXJnZXRfdmFsdWU6ICcvY2F0YWxvZz90eXBlPXNhbGUnLFxuICAgIGNyZWF0ZWRfYXQ6ICcyMDI0LTAxLTE1VDEwOjI1OjAwWicsXG4gICAgdXBkYXRlZF9hdDogJzIwMjQtMDEtMTVUMTA6MjU6MDBaJ1xuICB9LFxuXG4gIHtcbiAgICBpZDogJzgnLFxuICAgIHRpdGxlX2FyOiAn2KfYqti12YQg2KjZhtinJyxcbiAgICB0aXRsZV9lbjogJ0NvbnRhY3QnLFxuICAgIHRpdGxlX2ZyOiAnQ29udGFjdCcsXG4gICAgc2x1ZzogJ2NvbnRhY3QnLFxuICAgIGljb246ICdQaG9uZScsXG4gICAgb3JkZXJfaW5kZXg6IDYsXG4gICAgaXNfYWN0aXZlOiB0cnVlLFxuICAgIHRhcmdldF90eXBlOiAnaW50ZXJuYWwnLFxuICAgIHRhcmdldF92YWx1ZTogJy9jb250YWN0JyxcbiAgICBjcmVhdGVkX2F0OiAnMjAyNC0wMS0xNVQxMDozNTowMFonLFxuICAgIHVwZGF0ZWRfYXQ6ICcyMDI0LTAxLTE1VDEwOjM1OjAwWidcbiAgfVxuXVxuXG4vLyDYqNmK2KfZhtin2Kog2YjZh9mF2YrYqSDZhNmE2YHYptin2KpcbmV4cG9ydCBjb25zdCBtb2NrQ2F0ZWdvcmllczogTW9ja0NhdGVnb3J5W10gPSBbXG4gIHtcbiAgICBpZDogJzEnLFxuICAgIG5hbWVfYXI6ICfYo9ir2YjYp9ioINin2YTYqtiu2LHYrCcsXG4gICAgbmFtZV9lbjogJ0dyYWR1YXRpb24gR293bnMnLFxuICAgIG5hbWVfZnI6ICdSb2JlcyBkZSBHcmFkdWF0aW9uJyxcbiAgICBzbHVnOiAnZ293bicsXG4gICAgaWNvbjogJ/CfkZgnLFxuICAgIGRlc2NyaXB0aW9uOiAn2KPYq9mI2KfYqCDYp9mE2KrYrtix2Kwg2KfZhNij2YPYp9iv2YrZhdmK2Kkg2KfZhNiq2YLZhNmK2K/ZitipJyxcbiAgICBpc19hY3RpdmU6IHRydWUsXG4gICAgb3JkZXJfaW5kZXg6IDEsXG4gICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMTVUMTA6MDA6MDBaJyxcbiAgICB1cGRhdGVkX2F0OiAnMjAyNC0wMS0xNVQxMDowMDowMFonXG4gIH0sXG4gIHtcbiAgICBpZDogJzInLFxuICAgIG5hbWVfYXI6ICfZgtio2LnYp9iqINin2YTYqtiu2LHYrCcsXG4gICAgbmFtZV9lbjogJ0dyYWR1YXRpb24gQ2FwcycsXG4gICAgbmFtZV9mcjogJ0NoYXBlYXV4IGRlIEdyYWR1YXRpb24nLFxuICAgIHNsdWc6ICdjYXAnLFxuICAgIGljb246ICfwn46pJyxcbiAgICBkZXNjcmlwdGlvbjogJ9mC2KjYudin2Kog2KfZhNiq2K7YsdisINin2YTYo9mD2KfYr9mK2YXZitipJyxcbiAgICBpc19hY3RpdmU6IHRydWUsXG4gICAgb3JkZXJfaW5kZXg6IDIsXG4gICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMTVUMTA6MDU6MDBaJyxcbiAgICB1cGRhdGVkX2F0OiAnMjAyNC0wMS0xNVQxMDowNTowMFonXG4gIH0sXG4gIHtcbiAgICBpZDogJzMnLFxuICAgIG5hbWVfYXI6ICfYtNix2KfYqNin2Kog2KfZhNiq2K7YsdisJyxcbiAgICBuYW1lX2VuOiAnR3JhZHVhdGlvbiBUYXNzZWxzJyxcbiAgICBuYW1lX2ZyOiAnR2xhbmRzIGRlIEdyYWR1YXRpb24nLFxuICAgIHNsdWc6ICd0YXNzZWwnLFxuICAgIGljb246ICfwn4+377iPJyxcbiAgICBkZXNjcmlwdGlvbjogJ9i02LHYp9io2KfYqiDYp9mE2KrYrtix2Kwg2KfZhNmF2YTZiNmG2KknLFxuICAgIGlzX2FjdGl2ZTogdHJ1ZSxcbiAgICBvcmRlcl9pbmRleDogMyxcbiAgICBjcmVhdGVkX2F0OiAnMjAyNC0wMS0xNVQxMDoxMDowMFonLFxuICAgIHVwZGF0ZWRfYXQ6ICcyMDI0LTAxLTE1VDEwOjEwOjAwWidcbiAgfSxcbiAge1xuICAgIGlkOiAnNCcsXG4gICAgbmFtZV9hcjogJ9ij2YjYtNit2Kkg2KfZhNiq2K7YsdisJyxcbiAgICBuYW1lX2VuOiAnR3JhZHVhdGlvbiBTdG9sZXMnLFxuICAgIG5hbWVfZnI6ICfDiXRvbGVzIGRlIEdyYWR1YXRpb24nLFxuICAgIHNsdWc6ICdzdG9sZScsXG4gICAgaWNvbjogJ/Cfp6MnLFxuICAgIGRlc2NyaXB0aW9uOiAn2KPZiNi02K3YqSDYp9mE2KrYrtix2Kwg2KfZhNmF2YXZitiy2KknLFxuICAgIGlzX2FjdGl2ZTogdHJ1ZSxcbiAgICBvcmRlcl9pbmRleDogNCxcbiAgICBjcmVhdGVkX2F0OiAnMjAyNC0wMS0xNVQxMDoxNTowMFonLFxuICAgIHVwZGF0ZWRfYXQ6ICcyMDI0LTAxLTE1VDEwOjE1OjAwWidcbiAgfSxcbiAge1xuICAgIGlkOiAnNScsXG4gICAgbmFtZV9hcjogJ9in2YTZgtmE2KfZhtizINin2YTYo9mD2KfYr9mK2YXZitipJyxcbiAgICBuYW1lX2VuOiAnQWNhZGVtaWMgSG9vZHMnLFxuICAgIG5hbWVfZnI6ICdDYXB1Y2hlcyBBY2Fkw6ltaXF1ZXMnLFxuICAgIHNsdWc6ICdob29kJyxcbiAgICBpY29uOiAn8J+OkycsXG4gICAgZGVzY3JpcHRpb246ICfYp9mE2YLZhNin2YbYsyDYp9mE2KPZg9in2K/ZitmF2YrYqSDZhNmE2K/Ysdis2KfYqiDYp9mE2LnZhNmK2KcnLFxuICAgIGlzX2FjdGl2ZTogdHJ1ZSxcbiAgICBvcmRlcl9pbmRleDogNSxcbiAgICBjcmVhdGVkX2F0OiAnMjAyNC0wMS0xNVQxMDoyMDowMFonLFxuICAgIHVwZGF0ZWRfYXQ6ICcyMDI0LTAxLTE1VDEwOjIwOjAwWidcbiAgfVxuXVxuXG4vLyDYqNmK2KfZhtin2Kog2YjZh9mF2YrYqSDZhNmE2YXZhtiq2KzYp9iqXG5leHBvcnQgY29uc3QgbW9ja1Byb2R1Y3RzOiBNb2NrUHJvZHVjdFtdID0gW1xuICB7XG4gICAgaWQ6ICcxJyxcbiAgICBuYW1lOiAn2KvZiNioINin2YTYqtiu2LHYrCDYp9mE2YPZhNin2LPZitmD2YonLFxuICAgIGRlc2NyaXB0aW9uOiAn2KvZiNioINiq2K7YsdisINij2YbZitmCINmF2LXZhtmI2Lkg2YXZhiDYo9is2YjYryDYp9mE2K7Yp9mF2KfYqtiMINmF2YbYp9iz2Kgg2YTYrNmF2YrYuSDYp9mE2YXZhtin2LPYqNin2Kog2KfZhNij2YPYp9iv2YrZhdmK2KknLFxuICAgIGNhdGVnb3J5OiAnZ293bicsXG4gICAgcHJpY2U6IDI5OS45OSxcbiAgICByZW50YWxfcHJpY2U6IDk5Ljk5LFxuICAgIGNvbG9yczogWyfYo9iz2YjYrycsICfYo9iy2LHZgiDYr9in2YPZhicsICfYqNmI2LHYrNmI2YbYr9mKJ10sXG4gICAgc2l6ZXM6IFsnUycsICdNJywgJ0wnLCAnWEwnLCAnWFhMJ10sXG4gICAgaW1hZ2VzOiBbJy9pbWFnZXMvcHJvZHVjdHMvZ293bi1jbGFzc2ljLTEuanBnJywgJy9pbWFnZXMvcHJvZHVjdHMvZ293bi1jbGFzc2ljLTIuanBnJ10sXG4gICAgc3RvY2tfcXVhbnRpdHk6IDI1LFxuICAgIGlzX2F2YWlsYWJsZTogdHJ1ZSxcbiAgICBpc19wdWJsaXNoZWQ6IHRydWUsXG4gICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMTVUMTA6MDA6MDBaJyxcbiAgICB1cGRhdGVkX2F0OiAnMjAyNC0wMS0yMFQxNDozMDowMFonLFxuICAgIHJhdGluZzogNC44LFxuICAgIHJldmlld3NfY291bnQ6IDQyLFxuICAgIGZlYXR1cmVzOiBbJ9mF2YLYp9mI2YUg2YTZhNiq2KzYp9i52YrYrycsICfZgtin2KjZhCDZhNmE2LrYs9mK2YQnLCAn2K7Yp9mF2Kkg2LnYp9mE2YrYqSDYp9mE2KzZiNiv2KknXSxcbiAgICBzcGVjaWZpY2F0aW9uczoge1xuICAgICAgbWF0ZXJpYWw6ICfYqNmI2YTZitiz2KrYsSDYudin2YTZiiDYp9mE2KzZiNiv2KknLFxuICAgICAgd2VpZ2h0OiAnMC44INmD2YrZhNmIJyxcbiAgICAgIGNhcmU6ICfYutiz2YrZhCDYrNin2YEg2KPZiCDYutiz2YrZhCDYudin2K/ZiidcbiAgICB9XG4gIH0sXG4gIHtcbiAgICBpZDogJzInLFxuICAgIG5hbWU6ICfZgtio2LnYqSDYp9mE2KrYrtix2Kwg2KfZhNiq2YLZhNmK2K/ZitipJyxcbiAgICBkZXNjcmlwdGlvbjogJ9mC2KjYudipINiq2K7YsdisINiq2YLZhNmK2K/ZitipINmF2Lkg2LTYsdin2KjYqSDYsNmH2KjZitip2Iwg2LHZhdiyINin2YTYpdmG2KzYp9iyINin2YTYo9mD2KfYr9mK2YXZiicsXG4gICAgY2F0ZWdvcnk6ICdjYXAnLFxuICAgIHByaWNlOiA3OS45OSxcbiAgICByZW50YWxfcHJpY2U6IDI5Ljk5LFxuICAgIGNvbG9yczogWyfYo9iz2YjYrycsICfYo9iy2LHZgiDYr9in2YPZhiddLFxuICAgIHNpemVzOiBbJ09uZSBTaXplJ10sXG4gICAgaW1hZ2VzOiBbJy9pbWFnZXMvcHJvZHVjdHMvY2FwLXRyYWRpdGlvbmFsLTEuanBnJ10sXG4gICAgc3RvY2tfcXVhbnRpdHk6IDUwLFxuICAgIGlzX2F2YWlsYWJsZTogdHJ1ZSxcbiAgICBpc19wdWJsaXNoZWQ6IHRydWUsXG4gICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMTZUMDk6MDA6MDBaJyxcbiAgICB1cGRhdGVkX2F0OiAnMjAyNC0wMS0yMlQxNjo0NTowMFonLFxuICAgIHJhdGluZzogNC42LFxuICAgIHJldmlld3NfY291bnQ6IDI4LFxuICAgIGZlYXR1cmVzOiBbJ9mF2YLYp9izINmI2KfYrdivINmK2YbYp9iz2Kgg2KfZhNis2YXZiti5JywgJ9i02LHYp9io2Kkg2LDZh9io2YrYqScsICfYqti12YXZitmFINiq2YLZhNmK2K/ZiiddLFxuICAgIHNwZWNpZmljYXRpb25zOiB7XG4gICAgICBtYXRlcmlhbDogJ9mC2LfZhiDZhdiu2YTZiNi3JyxcbiAgICAgIHRhc3NlbF9jb2xvcjogJ9iw2YfYqNmKJyxcbiAgICAgIGFkanVzdGFibGU6ICfZhti52YUnXG4gICAgfVxuICB9LFxuICB7XG4gICAgaWQ6ICczJyxcbiAgICBuYW1lOiAn2YjYtNin2K0g2KfZhNiq2K7YsdisINin2YTZhdi32LHYsicsXG4gICAgZGVzY3JpcHRpb246ICfZiNi02KfYrSDYqtiu2LHYrCDZhdi32LHYsiDYqNiu2YrZiNi3INiw2YfYqNmK2KnYjCDZiti22YrZgSDZhNmF2LPYqSDZhdmGINin2YTYo9mG2KfZgtipINmI2KfZhNiq2YXZitiyJyxcbiAgICBjYXRlZ29yeTogJ3N0b2xlJyxcbiAgICBwcmljZTogMTQ5Ljk5LFxuICAgIHJlbnRhbF9wcmljZTogNDkuOTksXG4gICAgY29sb3JzOiBbJ9ij2KjZiti2INmF2Lkg2LDZh9io2YonLCAn2KPYstix2YIg2YXYuSDZgdi22YonLCAn2KPYrdmF2LEg2YXYuSDYsNmH2KjZiiddLFxuICAgIHNpemVzOiBbJ09uZSBTaXplJ10sXG4gICAgaW1hZ2VzOiBbJy9pbWFnZXMvcHJvZHVjdHMvc3RvbGUtZW1icm9pZGVyZWQtMS5qcGcnLCAnL2ltYWdlcy9wcm9kdWN0cy9zdG9sZS1lbWJyb2lkZXJlZC0yLmpwZyddLFxuICAgIHN0b2NrX3F1YW50aXR5OiAxNSxcbiAgICBpc19hdmFpbGFibGU6IHRydWUsXG4gICAgaXNfcHVibGlzaGVkOiB0cnVlLFxuICAgIGNyZWF0ZWRfYXQ6ICcyMDI0LTAxLTE3VDExOjAwOjAwWicsXG4gICAgdXBkYXRlZF9hdDogJzIwMjQtMDEtMjVUMTA6MTU6MDBaJyxcbiAgICByYXRpbmc6IDQuOSxcbiAgICByZXZpZXdzX2NvdW50OiAxOCxcbiAgICBmZWF0dXJlczogWyfYqti32LHZitiyINmK2K/ZiNmKJywgJ9iu2YrZiNi3INiw2YfYqNmK2KknLCAn2KrYtdmF2YrZhSDZgdin2K7YsSddLFxuICAgIHNwZWNpZmljYXRpb25zOiB7XG4gICAgICBtYXRlcmlhbDogJ9it2LHZitixINi32KjZiti52YonLFxuICAgICAgZW1icm9pZGVyeTogJ9iu2YrZiNi3INiw2YfYqNmK2Kkg2YjZgdi22YrYqScsXG4gICAgICBsZW5ndGg6ICcxNTAg2LPZhSdcbiAgICB9XG4gIH0sXG4gIHtcbiAgICBpZDogJzQnLFxuICAgIG5hbWU6ICfYtNix2KfYqNipINin2YTYqtiu2LHYrCDYp9mE2LDZh9io2YrYqScsXG4gICAgZGVzY3JpcHRpb246ICfYtNix2KfYqNipINiq2K7YsdisINiw2YfYqNmK2Kkg2KfZhNmE2YjZhtiMINix2YXYsiDYp9mE2KrZgdmI2YIg2YjYp9mE2KXZhtis2KfYsiDYp9mE2KPZg9in2K/ZitmF2YonLFxuICAgIGNhdGVnb3J5OiAndGFzc2VsJyxcbiAgICBwcmljZTogMzkuOTksXG4gICAgcmVudGFsX3ByaWNlOiAxNS45OSxcbiAgICBjb2xvcnM6IFsn2LDZh9io2YonLCAn2YHYttmKJywgJ9ij2LLYsdmCJywgJ9ij2K3ZhdixJ10sXG4gICAgc2l6ZXM6IFsnT25lIFNpemUnXSxcbiAgICBpbWFnZXM6IFsnL2ltYWdlcy9wcm9kdWN0cy90YXNzZWwtZ29sZC0xLmpwZyddLFxuICAgIHN0b2NrX3F1YW50aXR5OiAxMDAsXG4gICAgaXNfYXZhaWxhYmxlOiB0cnVlLFxuICAgIGlzX3B1Ymxpc2hlZDogdHJ1ZSxcbiAgICBjcmVhdGVkX2F0OiAnMjAyNC0wMS0xOFQxNDowMDowMFonLFxuICAgIHVwZGF0ZWRfYXQ6ICcyMDI0LTAxLTI2VDA5OjMwOjAwWicsXG4gICAgcmF0aW5nOiA0LjcsXG4gICAgcmV2aWV3c19jb3VudDogMzUsXG4gICAgZmVhdHVyZXM6IFsn2K7ZitmI2Lcg2LnYp9mE2YrYqSDYp9mE2KzZiNiv2KknLCAn2KPZhNmI2KfZhiDYq9in2KjYqtipJywgJ9iz2YfZhCDYp9mE2KrYsdmD2YrYqCddLFxuICAgIHNwZWNpZmljYXRpb25zOiB7XG4gICAgICBtYXRlcmlhbDogJ9iu2YrZiNi3INit2LHZitix2YrYqScsXG4gICAgICBsZW5ndGg6ICcyMyDYs9mFJyxcbiAgICAgIGF0dGFjaG1lbnQ6ICfZhdi02KjZgyDZhdi52K/ZhtmKJ1xuICAgIH1cbiAgfSxcbiAge1xuICAgIGlkOiAnNScsXG4gICAgbmFtZTogJ9mC2YTZhtiz2YjYqSDYp9mE2K/Zg9iq2YjYsdin2Ycg2KfZhNmB2KfYrtix2KknLFxuICAgIGRlc2NyaXB0aW9uOiAn2YLZhNmG2LPZiNipINiv2YPYqtmI2LHYp9mHINmB2KfYrtix2Kkg2YXYtdmF2YXYqSDYrti12YrYtdin2Ysg2YTYrdmB2YTYp9iqINin2YTYqtiu2LHYrCDYp9mE2KPZg9in2K/ZitmF2YrYqSDYp9mE2LnZhNmK2KcnLFxuICAgIGNhdGVnb3J5OiAnaG9vZCcsXG4gICAgcHJpY2U6IDE5OS45OSxcbiAgICByZW50YWxfcHJpY2U6IDc5Ljk5LFxuICAgIGNvbG9yczogWyfYo9iz2YjYryDZhdi5INiw2YfYqNmKJywgJ9ij2LLYsdmCINmF2Lkg2YHYttmKJ10sXG4gICAgc2l6ZXM6IFsnTScsICdMJywgJ1hMJ10sXG4gICAgaW1hZ2VzOiBbJy9pbWFnZXMvcHJvZHVjdHMvaG9vZC1kb2N0b3JhdGUtMS5qcGcnLCAnL2ltYWdlcy9wcm9kdWN0cy9ob29kLWRvY3RvcmF0ZS0yLmpwZyddLFxuICAgIHN0b2NrX3F1YW50aXR5OiA4LFxuICAgIGlzX2F2YWlsYWJsZTogdHJ1ZSxcbiAgICBpc19wdWJsaXNoZWQ6IHRydWUsXG4gICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMTlUMTY6MDA6MDBaJyxcbiAgICB1cGRhdGVkX2F0OiAnMjAyNC0wMS0yN1QxMjowMDowMFonLFxuICAgIHJhdGluZzogNS4wLFxuICAgIHJldmlld3NfY291bnQ6IDEyLFxuICAgIGZlYXR1cmVzOiBbJ9iq2LXZhdmK2YUg2KPZg9in2K/ZitmF2Yog2KPYtdmK2YQnLCAn2K7Yp9mF2Kkg2YHYp9iu2LHYqScsICfZhdmG2KfYs9ioINmE2YTYr9mD2KrZiNix2KfZhyddLFxuICAgIHNwZWNpZmljYXRpb25zOiB7XG4gICAgICBtYXRlcmlhbDogJ9mF2K7ZhdmEINi52KfZhNmKINin2YTYrNmI2K/YqScsXG4gICAgICBsaW5pbmc6ICfYrdix2YrYsSDZhdmE2YjZhicsXG4gICAgICBhY2FkZW1pY19sZXZlbDogJ9iv2YPYqtmI2LHYp9mHJ1xuICAgIH1cbiAgfVxuXVxuXG4vLyDYqNmK2KfZhtin2Kog2YjZh9mF2YrYqSDZhNmE2YXYr9in2LHYs1xuZXhwb3J0IGNvbnN0IG1vY2tTY2hvb2xzOiBNb2NrU2Nob29sW10gPSBbXG4gIHtcbiAgICBpZDogJzEnLFxuICAgIGFkbWluX2lkOiAnYWRtaW4tc2Nob29sLTEnLFxuICAgIG5hbWU6ICfYrNin2YXYudipINin2YTYpdmF2KfYsdin2Kog2KfZhNi52LHYqNmK2Kkg2KfZhNmF2KrYrdiv2KknLFxuICAgIG5hbWVfZW46ICdVbml0ZWQgQXJhYiBFbWlyYXRlcyBVbml2ZXJzaXR5JyxcbiAgICBuYW1lX2ZyOiAnVW5pdmVyc2l0w6kgZGVzIMOJbWlyYXRzIEFyYWJlcyBVbmlzJyxcbiAgICBhZGRyZXNzOiAn2LTYp9ix2Lkg2KfZhNis2KfZhdi52KnYjCDYp9mE2LnZitmGJyxcbiAgICBjaXR5OiAn2KfZhNi52YrZhicsXG4gICAgcGhvbmU6ICcrOTcxLTMtNzEzLTUwMDAnLFxuICAgIGVtYWlsOiAnaW5mb0B1YWV1LmFjLmFlJyxcbiAgICB3ZWJzaXRlOiAnaHR0cHM6Ly93d3cudWFldS5hYy5hZScsXG4gICAgbG9nb191cmw6ICcvaW1hZ2VzL3NjaG9vbHMvdWFldS1sb2dvLnBuZycsXG4gICAgZ3JhZHVhdGlvbl9kYXRlOiAnMjAyNC0wNi0xNScsXG4gICAgc3R1ZGVudF9jb3VudDogMTQ1MDAsXG4gICAgaXNfYWN0aXZlOiB0cnVlLFxuICAgIHNldHRpbmdzOiB7XG4gICAgICBncmFkdWF0aW9uX2NlcmVtb255X2xvY2F0aW9uOiAn2YLYp9i52Kkg2KfZhNin2K3YqtmB2KfZhNin2Kog2KfZhNmD2KjYsdmJJyxcbiAgICAgIGRyZXNzX2NvZGU6ICdmb3JtYWwnLFxuICAgICAgcGhvdG9ncmFwaHlfYWxsb3dlZDogdHJ1ZVxuICAgIH0sXG4gICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMTBUMDg6MDA6MDBaJyxcbiAgICB1cGRhdGVkX2F0OiAnMjAyNC0wMS0yNVQxMDozMDowMFonXG4gIH0sXG4gIHtcbiAgICBpZDogJzInLFxuICAgIGFkbWluX2lkOiAnYWRtaW4tc2Nob29sLTInLFxuICAgIG5hbWU6ICfYp9mE2KzYp9mF2LnYqSDYp9mE2KPZhdix2YrZg9mK2Kkg2YHZiiDYp9mE2LTYp9ix2YLYqScsXG4gICAgbmFtZV9lbjogJ0FtZXJpY2FuIFVuaXZlcnNpdHkgb2YgU2hhcmphaCcsXG4gICAgbmFtZV9mcjogJ1VuaXZlcnNpdMOpIEFtw6lyaWNhaW5lIGRlIFNoYXJqYWgnLFxuICAgIGFkZHJlc3M6ICfYtNin2LHYuSDYp9mE2KzYp9mF2LnYqdiMINin2YTYtNin2LHZgtipJyxcbiAgICBjaXR5OiAn2KfZhNi02KfYsdmC2KknLFxuICAgIHBob25lOiAnKzk3MS02LTUxNS01NTU1JyxcbiAgICBlbWFpbDogJ2luZm9AYXVzLmVkdScsXG4gICAgd2Vic2l0ZTogJ2h0dHBzOi8vd3d3LmF1cy5lZHUnLFxuICAgIGxvZ29fdXJsOiAnL2ltYWdlcy9zY2hvb2xzL2F1cy1sb2dvLnBuZycsXG4gICAgZ3JhZHVhdGlvbl9kYXRlOiAnMjAyNC0wNS0yMCcsXG4gICAgc3R1ZGVudF9jb3VudDogNjIwMCxcbiAgICBpc19hY3RpdmU6IHRydWUsXG4gICAgc2V0dGluZ3M6IHtcbiAgICAgIGdyYWR1YXRpb25fY2VyZW1vbnlfbG9jYXRpb246ICfZhdiz2LHYrSDYp9mE2KzYp9mF2LnYqScsXG4gICAgICBkcmVzc19jb2RlOiAnYWNhZGVtaWMnLFxuICAgICAgcGhvdG9ncmFwaHlfYWxsb3dlZDogdHJ1ZVxuICAgIH0sXG4gICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMTJUMDk6MTU6MDBaJyxcbiAgICB1cGRhdGVkX2F0OiAnMjAyNC0wMS0yOFQxNDoyMDowMFonXG4gIH0sXG4gIHtcbiAgICBpZDogJzMnLFxuICAgIGFkbWluX2lkOiAnYWRtaW4tc2Nob29sLTMnLFxuICAgIG5hbWU6ICfYrNin2YXYudipINiy2KfZitivJyxcbiAgICBuYW1lX2VuOiAnWmF5ZWQgVW5pdmVyc2l0eScsXG4gICAgbmFtZV9mcjogJ1VuaXZlcnNpdMOpIFpheWVkJyxcbiAgICBhZGRyZXNzOiAn2LTYp9ix2Lkg2KfZhNi02YrYriDYstin2YrYr9iMINiv2KjZiicsXG4gICAgY2l0eTogJ9iv2KjZiicsXG4gICAgcGhvbmU6ICcrOTcxLTQtNDAyLTExMTEnLFxuICAgIGVtYWlsOiAnaW5mb0B6dS5hYy5hZScsXG4gICAgd2Vic2l0ZTogJ2h0dHBzOi8vd3d3Lnp1LmFjLmFlJyxcbiAgICBsb2dvX3VybDogJy9pbWFnZXMvc2Nob29scy96dS1sb2dvLnBuZycsXG4gICAgZ3JhZHVhdGlvbl9kYXRlOiAnMjAyNC0wNi0xMCcsXG4gICAgc3R1ZGVudF9jb3VudDogOTgwMCxcbiAgICBpc19hY3RpdmU6IHRydWUsXG4gICAgc2V0dGluZ3M6IHtcbiAgICAgIGdyYWR1YXRpb25fY2VyZW1vbnlfbG9jYXRpb246ICfZhdix2YPYsiDYp9mE2YXYpNiq2YXYsdin2KonLFxuICAgICAgZHJlc3NfY29kZTogJ2Zvcm1hbCcsXG4gICAgICBwaG90b2dyYXBoeV9hbGxvd2VkOiBmYWxzZVxuICAgIH0sXG4gICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMTVUMTE6MDA6MDBaJyxcbiAgICB1cGRhdGVkX2F0OiAnMjAyNC0wMi0wMVQxNjo0NTowMFonXG4gIH0sXG4gIHtcbiAgICBpZDogJzQnLFxuICAgIGFkbWluX2lkOiAnYWRtaW4tc2Nob29sLTQnLFxuICAgIG5hbWU6ICfZg9mE2YrYqSDYp9mE2KXZhdin2LHYp9iqINmE2YTYqtmD2YbZiNmE2YjYrNmK2KcnLFxuICAgIG5hbWVfZW46ICdFbWlyYXRlcyBJbnN0aXR1dGUgb2YgVGVjaG5vbG9neScsXG4gICAgbmFtZV9mcjogJ0luc3RpdHV0IGRlIFRlY2hub2xvZ2llIGRlcyDDiW1pcmF0cycsXG4gICAgYWRkcmVzczogJ9in2YTZhdmG2LfZgtipINin2YTYo9mD2KfYr9mK2YXZitip2Iwg2KPYqNmI2LjYqNmKJyxcbiAgICBjaXR5OiAn2KPYqNmI2LjYqNmKJyxcbiAgICBwaG9uZTogJys5NzEtMi00MDEtNDAwMCcsXG4gICAgZW1haWw6ICdpbmZvQGVpdC5hYy5hZScsXG4gICAgd2Vic2l0ZTogJ2h0dHBzOi8vd3d3LmVpdC5hYy5hZScsXG4gICAgbG9nb191cmw6ICcvaW1hZ2VzL3NjaG9vbHMvZWl0LWxvZ28ucG5nJyxcbiAgICBncmFkdWF0aW9uX2RhdGU6ICcyMDI0LTA3LTA1JyxcbiAgICBzdHVkZW50X2NvdW50OiAzNTAwLFxuICAgIGlzX2FjdGl2ZTogdHJ1ZSxcbiAgICBzZXR0aW5nczoge1xuICAgICAgZ3JhZHVhdGlvbl9jZXJlbW9ueV9sb2NhdGlvbjogJ9in2YTZgtin2LnYqSDYp9mE2LHYptmK2LPZitipJyxcbiAgICAgIGRyZXNzX2NvZGU6ICdidXNpbmVzcycsXG4gICAgICBwaG90b2dyYXBoeV9hbGxvd2VkOiB0cnVlXG4gICAgfSxcbiAgICBjcmVhdGVkX2F0OiAnMjAyNC0wMS0xOFQxMzozMDowMFonLFxuICAgIHVwZGF0ZWRfYXQ6ICcyMDI0LTAyLTA1VDA5OjE1OjAwWidcbiAgfSxcbiAge1xuICAgIGlkOiAnNScsXG4gICAgYWRtaW5faWQ6ICdhZG1pbi1zY2hvb2wtNScsXG4gICAgbmFtZTogJ9mF2LnZh9ivINij2KjZiNi42KjZiiDZhNmE2KrYudmE2YrZhSDYp9mE2KrZgtmG2YonLFxuICAgIG5hbWVfZW46ICdBYnUgRGhhYmkgVGVjaG5pY2FsIEluc3RpdHV0ZScsXG4gICAgbmFtZV9mcjogJ0luc3RpdHV0IFRlY2huaXF1ZSBkXFwnQWJ1IERoYWJpJyxcbiAgICBhZGRyZXNzOiAn2KfZhNmF2YbYt9mC2Kkg2KfZhNi12YbYp9i52YrYqdiMINij2KjZiNi42KjZiicsXG4gICAgY2l0eTogJ9ij2KjZiNi42KjZiicsXG4gICAgcGhvbmU6ICcrOTcxLTItNTA1LTIwMDAnLFxuICAgIGVtYWlsOiAnaW5mb0BhZHRpLmFjLmFlJyxcbiAgICB3ZWJzaXRlOiAnaHR0cHM6Ly93d3cuYWR0aS5hYy5hZScsXG4gICAgZ3JhZHVhdGlvbl9kYXRlOiAnMjAyNC0wNi0yNScsXG4gICAgc3R1ZGVudF9jb3VudDogMjgwMCxcbiAgICBpc19hY3RpdmU6IGZhbHNlLFxuICAgIHNldHRpbmdzOiB7XG4gICAgICBncmFkdWF0aW9uX2NlcmVtb255X2xvY2F0aW9uOiAn2YXYsdmD2LIg2KfZhNiq2K/YsdmK2KgnLFxuICAgICAgZHJlc3NfY29kZTogJ2Nhc3VhbCcsXG4gICAgICBwaG90b2dyYXBoeV9hbGxvd2VkOiB0cnVlXG4gICAgfSxcbiAgICBjcmVhdGVkX2F0OiAnMjAyNC0wMS0yMFQxNTo0NTowMFonLFxuICAgIHVwZGF0ZWRfYXQ6ICcyMDI0LTAyLTEwVDEyOjAwOjAwWidcbiAgfVxuXVxuXG4vLyDYqNmK2KfZhtin2Kog2YjZh9mF2YrYqSDZhNmE2LfZhNio2KfYqlxuZXhwb3J0IGNvbnN0IG1vY2tPcmRlcnM6IE1vY2tPcmRlcltdID0gW1xuICB7XG4gICAgaWQ6ICcxJyxcbiAgICBvcmRlcl9udW1iZXI6ICdHVC0yNDAxMjAtMDAxJyxcbiAgICBjdXN0b21lcl9pZDogJ3N0dWRlbnQtMScsXG4gICAgY3VzdG9tZXJfbmFtZTogJ9ij2K3ZhdivINmF2K3ZhdivINi52YTZiicsXG4gICAgY3VzdG9tZXJfZW1haWw6ICdhaG1lZC5hbGlAc3R1ZGVudC51YWV1LmFjLmFlJyxcbiAgICBjdXN0b21lcl9waG9uZTogJys5NzEtNTAtMTIzLTQ1NjcnLFxuICAgIHN0YXR1czogJ2luX3Byb2R1Y3Rpb24nLFxuICAgIGl0ZW1zOiBbXG4gICAgICB7XG4gICAgICAgIGlkOiAnMScsXG4gICAgICAgIG9yZGVyX2lkOiAnMScsXG4gICAgICAgIHByb2R1Y3RfaWQ6ICcxJyxcbiAgICAgICAgcHJvZHVjdF9uYW1lOiAn2KvZiNioINin2YTYqtiu2LHYrCDYp9mE2YPZhNin2LPZitmD2YonLFxuICAgICAgICBwcm9kdWN0X2ltYWdlOiAnL2ltYWdlcy9wcm9kdWN0cy9nb3duLWNsYXNzaWMtMS5qcGcnLFxuICAgICAgICBjYXRlZ29yeTogJ2dvd24nLFxuICAgICAgICBxdWFudGl0eTogMSxcbiAgICAgICAgdW5pdF9wcmljZTogMjk5Ljk5LFxuICAgICAgICB0b3RhbF9wcmljZTogMjk5Ljk5LFxuICAgICAgICBjdXN0b21pemF0aW9uczoge1xuICAgICAgICAgIGNvbG9yOiAn2KPYs9mI2K8nLFxuICAgICAgICAgIHNpemU6ICdMJyxcbiAgICAgICAgICBlbWJyb2lkZXJ5OiAn2KPYrdmF2K8g2LnZhNmKIC0g2KjZg9in2YTZiNix2YrZiNizINmH2YbYr9iz2KknXG4gICAgICAgIH1cbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIGlkOiAnMicsXG4gICAgICAgIG9yZGVyX2lkOiAnMScsXG4gICAgICAgIHByb2R1Y3RfaWQ6ICcyJyxcbiAgICAgICAgcHJvZHVjdF9uYW1lOiAn2YLYqNi52Kkg2KfZhNiq2K7YsdisINin2YTYo9mD2KfYr9mK2YXZitipJyxcbiAgICAgICAgcHJvZHVjdF9pbWFnZTogJy9pbWFnZXMvcHJvZHVjdHMvY2FwLWFjYWRlbWljLTEuanBnJyxcbiAgICAgICAgY2F0ZWdvcnk6ICdjYXAnLFxuICAgICAgICBxdWFudGl0eTogMSxcbiAgICAgICAgdW5pdF9wcmljZTogODkuOTksXG4gICAgICAgIHRvdGFsX3ByaWNlOiA4OS45OSxcbiAgICAgICAgY3VzdG9taXphdGlvbnM6IHtcbiAgICAgICAgICBjb2xvcjogJ9ij2LPZiNivJyxcbiAgICAgICAgICBzaXplOiAnTSdcbiAgICAgICAgfVxuICAgICAgfVxuICAgIF0sXG4gICAgc3VidG90YWw6IDM4OS45OCxcbiAgICB0YXg6IDE5LjUwLFxuICAgIHNoaXBwaW5nX2Nvc3Q6IDI1LjAwLFxuICAgIHRvdGFsOiA0MzQuNDgsXG4gICAgcGF5bWVudF9zdGF0dXM6ICdwYWlkJyxcbiAgICBwYXltZW50X21ldGhvZDogJ2NyZWRpdF9jYXJkJyxcbiAgICBzaGlwcGluZ19hZGRyZXNzOiB7XG4gICAgICBzdHJlZXQ6ICfYtNin2LHYuSDYp9mE2KzYp9mF2LnYqdiMINmF2KjZhtmJIDEy2Iwg2LTZgtipIDMwNCcsXG4gICAgICBjaXR5OiAn2KfZhNi52YrZhicsXG4gICAgICBzdGF0ZTogJ9ij2KjZiNi42KjZiicsXG4gICAgICBwb3N0YWxfY29kZTogJzE3NjY2JyxcbiAgICAgIGNvdW50cnk6ICfYp9mE2KXZhdin2LHYp9iqINin2YTYudix2KjZitipINin2YTZhdiq2K3Yr9ipJ1xuICAgIH0sXG4gICAgdHJhY2tpbmdfbnVtYmVyOiAnVFJLLUdULTAwMS0yMDI0JyxcbiAgICBub3RlczogJ9mK2LHYrNmJINin2YTYqtiz2YTZitmFINmC2KjZhCDYrdmB2YQg2KfZhNiq2K7YsdisJyxcbiAgICBjcmVhdGVkX2F0OiAnMjAyNC0wMS0yMFQxMDozMDowMFonLFxuICAgIHVwZGF0ZWRfYXQ6ICcyMDI0LTAxLTIyVDE0OjE1OjAwWicsXG4gICAgZGVsaXZlcnlfZGF0ZTogJzIwMjQtMDItMTVUMDA6MDA6MDBaJyxcbiAgICBzY2hvb2xfaWQ6ICcxJyxcbiAgICBzY2hvb2xfbmFtZTogJ9is2KfZhdi52Kkg2KfZhNil2YXYp9ix2KfYqiDYp9mE2LnYsdio2YrYqSDYp9mE2YXYqtit2K/YqSdcbiAgfSxcbiAge1xuICAgIGlkOiAnMicsXG4gICAgb3JkZXJfbnVtYmVyOiAnR1QtMjQwMTIxLTAwMicsXG4gICAgY3VzdG9tZXJfaWQ6ICdzdHVkZW50LTInLFxuICAgIGN1c3RvbWVyX25hbWU6ICfZgdin2LfZhdipINiz2KfZhNmFINin2YTYstmH2LHYp9mG2YonLFxuICAgIGN1c3RvbWVyX2VtYWlsOiAnZmF0aW1hLmFsemFocmFuaUBhdXMuZWR1JyxcbiAgICBjdXN0b21lcl9waG9uZTogJys5NzEtNTYtNzg5LTAxMjMnLFxuICAgIHN0YXR1czogJ2RlbGl2ZXJlZCcsXG4gICAgaXRlbXM6IFtcbiAgICAgIHtcbiAgICAgICAgaWQ6ICczJyxcbiAgICAgICAgb3JkZXJfaWQ6ICcyJyxcbiAgICAgICAgcHJvZHVjdF9pZDogJzMnLFxuICAgICAgICBwcm9kdWN0X25hbWU6ICfYq9mI2Kgg2KfZhNiq2K7YsdisINin2YTZhdmF2YrYsicsXG4gICAgICAgIHByb2R1Y3RfaW1hZ2U6ICcvaW1hZ2VzL3Byb2R1Y3RzL2dvd24tcHJlbWl1bS0xLmpwZycsXG4gICAgICAgIGNhdGVnb3J5OiAnZ293bicsXG4gICAgICAgIHF1YW50aXR5OiAxLFxuICAgICAgICB1bml0X3ByaWNlOiAzOTkuOTksXG4gICAgICAgIHRvdGFsX3ByaWNlOiAzOTkuOTksXG4gICAgICAgIGN1c3RvbWl6YXRpb25zOiB7XG4gICAgICAgICAgY29sb3I6ICfYo9iy2LHZgiDYr9in2YPZhicsXG4gICAgICAgICAgc2l6ZTogJ00nLFxuICAgICAgICAgIGVtYnJvaWRlcnk6ICfZgdin2LfZhdipINin2YTYstmH2LHYp9mG2YogLSDZhdin2KzYs9iq2YrYsSDYpdiv2KfYsdipINij2LnZhdin2YQnXG4gICAgICAgIH1cbiAgICAgIH1cbiAgICBdLFxuICAgIHN1YnRvdGFsOiAzOTkuOTksXG4gICAgdGF4OiAyMC4wMCxcbiAgICBzaGlwcGluZ19jb3N0OiAzMC4wMCxcbiAgICB0b3RhbDogNDQ5Ljk5LFxuICAgIHBheW1lbnRfc3RhdHVzOiAncGFpZCcsXG4gICAgcGF5bWVudF9tZXRob2Q6ICdiYW5rX3RyYW5zZmVyJyxcbiAgICBzaGlwcGluZ19hZGRyZXNzOiB7XG4gICAgICBzdHJlZXQ6ICfYtNin2LHYuSDYp9mE2YPZiNix2YbZiti02Iwg2KjYsdisINin2YTYpdmF2KfYsdin2KrYjCDYp9mE2LfYp9io2YIgMTUnLFxuICAgICAgY2l0eTogJ9in2YTYtNin2LHZgtipJyxcbiAgICAgIHN0YXRlOiAn2KfZhNi02KfYsdmC2KknLFxuICAgICAgcG9zdGFsX2NvZGU6ICcyNzI3MicsXG4gICAgICBjb3VudHJ5OiAn2KfZhNil2YXYp9ix2KfYqiDYp9mE2LnYsdio2YrYqSDYp9mE2YXYqtit2K/YqSdcbiAgICB9LFxuICAgIHRyYWNraW5nX251bWJlcjogJ1RSSy1HVC0wMDItMjAyNCcsXG4gICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMjFUMDk6MTU6MDBaJyxcbiAgICB1cGRhdGVkX2F0OiAnMjAyNC0wMS0yNVQxNjozMDowMFonLFxuICAgIGRlbGl2ZXJ5X2RhdGU6ICcyMDI0LTAxLTI4VDAwOjAwOjAwWicsXG4gICAgc2Nob29sX2lkOiAnMicsXG4gICAgc2Nob29sX25hbWU6ICfYp9mE2KzYp9mF2LnYqSDYp9mE2KPZhdix2YrZg9mK2Kkg2YHZiiDYp9mE2LTYp9ix2YLYqSdcbiAgfSxcbiAge1xuICAgIGlkOiAnMycsXG4gICAgb3JkZXJfbnVtYmVyOiAnR1QtMjQwMTIyLTAwMycsXG4gICAgY3VzdG9tZXJfaWQ6ICdzdHVkZW50LTMnLFxuICAgIGN1c3RvbWVyX25hbWU6ICfYrtin2YTYryDYudio2K/Yp9mE2YTZhyDYp9mE2YXZhti12YjYsdmKJyxcbiAgICBjdXN0b21lcl9lbWFpbDogJ2toYWxpZC5hbG1hbnNvdXJpQHp1LmFjLmFlJyxcbiAgICBjdXN0b21lcl9waG9uZTogJys5NzEtNTItNDU2LTc4OTAnLFxuICAgIHN0YXR1czogJ3BlbmRpbmcnLFxuICAgIGl0ZW1zOiBbXG4gICAgICB7XG4gICAgICAgIGlkOiAnNCcsXG4gICAgICAgIG9yZGVyX2lkOiAnMycsXG4gICAgICAgIHByb2R1Y3RfaWQ6ICcxJyxcbiAgICAgICAgcHJvZHVjdF9uYW1lOiAn2KvZiNioINin2YTYqtiu2LHYrCDYp9mE2YPZhNin2LPZitmD2YonLFxuICAgICAgICBwcm9kdWN0X2ltYWdlOiAnL2ltYWdlcy9wcm9kdWN0cy9nb3duLWNsYXNzaWMtMS5qcGcnLFxuICAgICAgICBjYXRlZ29yeTogJ2dvd24nLFxuICAgICAgICBxdWFudGl0eTogMSxcbiAgICAgICAgdW5pdF9wcmljZTogMjk5Ljk5LFxuICAgICAgICB0b3RhbF9wcmljZTogMjk5Ljk5LFxuICAgICAgICBjdXN0b21pemF0aW9uczoge1xuICAgICAgICAgIGNvbG9yOiAn2KjZiNix2KzZiNmG2K/ZiicsXG4gICAgICAgICAgc2l6ZTogJ1hMJ1xuICAgICAgICB9XG4gICAgICB9LFxuICAgICAge1xuICAgICAgICBpZDogJzUnLFxuICAgICAgICBvcmRlcl9pZDogJzMnLFxuICAgICAgICBwcm9kdWN0X2lkOiAnNCcsXG4gICAgICAgIHByb2R1Y3RfbmFtZTogJ9mI2LTYp9itINin2YTYqtiu2LHYrCDYp9mE2YXYt9ix2LInLFxuICAgICAgICBwcm9kdWN0X2ltYWdlOiAnL2ltYWdlcy9wcm9kdWN0cy9zdG9sZS1lbWJyb2lkZXJlZC0xLmpwZycsXG4gICAgICAgIGNhdGVnb3J5OiAnc3RvbGUnLFxuICAgICAgICBxdWFudGl0eTogMSxcbiAgICAgICAgdW5pdF9wcmljZTogMTQ5Ljk5LFxuICAgICAgICB0b3RhbF9wcmljZTogMTQ5Ljk5LFxuICAgICAgICBjdXN0b21pemF0aW9uczoge1xuICAgICAgICAgIGNvbG9yOiAn2LDZh9io2YonLFxuICAgICAgICAgIGVtYnJvaWRlcnk6ICfZg9mE2YrYqSDYp9mE2YfZhtiv2LPYqSdcbiAgICAgICAgfVxuICAgICAgfVxuICAgIF0sXG4gICAgc3VidG90YWw6IDQ0OS45OCxcbiAgICB0YXg6IDIyLjUwLFxuICAgIHNoaXBwaW5nX2Nvc3Q6IDI1LjAwLFxuICAgIHRvdGFsOiA0OTcuNDgsXG4gICAgcGF5bWVudF9zdGF0dXM6ICdwZW5kaW5nJyxcbiAgICBzaGlwcGluZ19hZGRyZXNzOiB7XG4gICAgICBzdHJlZXQ6ICfYtNin2LHYuSDYp9mE2LTZitiuINiy2KfZitiv2Iwg2YXYrNmF2Lkg2K/YqNmKINin2YTYo9mD2KfYr9mK2YXZiicsXG4gICAgICBjaXR5OiAn2K/YqNmKJyxcbiAgICAgIHN0YXRlOiAn2K/YqNmKJyxcbiAgICAgIHBvc3RhbF9jb2RlOiAnMzkxMTg2JyxcbiAgICAgIGNvdW50cnk6ICfYp9mE2KXZhdin2LHYp9iqINin2YTYudix2KjZitipINin2YTZhdiq2K3Yr9ipJ1xuICAgIH0sXG4gICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMjJUMTQ6NDU6MDBaJyxcbiAgICB1cGRhdGVkX2F0OiAnMjAyNC0wMS0yMlQxNDo0NTowMFonLFxuICAgIHNjaG9vbF9pZDogJzMnLFxuICAgIHNjaG9vbF9uYW1lOiAn2KzYp9mF2LnYqSDYstin2YrYrydcbiAgfVxuXVxuXG4vLyDZhdiz2KfYudiv2KfYqiDZhNmE2KrYudin2YXZhCDZhdi5INin2YTYqNmK2KfZhtin2Kog2KfZhNmI2YfZhdmK2KlcbmV4cG9ydCBjbGFzcyBNb2NrRGF0YU1hbmFnZXIge1xuICBwcml2YXRlIHN0YXRpYyBnZXRTdG9yYWdlS2V5KHR5cGU6ICdwYWdlcycgfCAnbWVudUl0ZW1zJyB8ICdwcm9kdWN0cycgfCAnY2F0ZWdvcmllcycgfCAnc2Nob29scycgfCAnb3JkZXJzJyB8ICdhaVByb3ZpZGVycycpOiBzdHJpbmcge1xuICAgIHJldHVybiBgbW9ja0RhdGFfJHt0eXBlfWBcbiAgfVxuXG4gIHN0YXRpYyBnZXRQYWdlcygpOiBNb2NrUGFnZVtdIHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHJldHVybiBtb2NrUGFnZXNcblxuICAgIGNvbnN0IHN0b3JlZCA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKHRoaXMuZ2V0U3RvcmFnZUtleSgncGFnZXMnKSlcbiAgICByZXR1cm4gc3RvcmVkID8gSlNPTi5wYXJzZShzdG9yZWQpIDogbW9ja1BhZ2VzXG4gIH1cblxuICBzdGF0aWMgZ2V0TWVudUl0ZW1zKCk6IE1vY2tNZW51SXRlbVtdIHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHJldHVybiBtb2NrTWVudUl0ZW1zXG5cbiAgICBjb25zdCBzdG9yZWQgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSh0aGlzLmdldFN0b3JhZ2VLZXkoJ21lbnVJdGVtcycpKVxuICAgIHJldHVybiBzdG9yZWQgPyBKU09OLnBhcnNlKHN0b3JlZCkgOiBtb2NrTWVudUl0ZW1zXG4gIH1cblxuICBzdGF0aWMgZ2V0UHJvZHVjdHMoKTogTW9ja1Byb2R1Y3RbXSB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICBjb25zb2xlLmxvZygnU2VydmVyIHNpZGUgLSByZXR1cm5pbmcgbW9jayBwcm9kdWN0czonLCBtb2NrUHJvZHVjdHMubGVuZ3RoKVxuICAgICAgcmV0dXJuIG1vY2tQcm9kdWN0c1xuICAgIH1cblxuICAgIGNvbnN0IHN0b3JlZCA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKHRoaXMuZ2V0U3RvcmFnZUtleSgncHJvZHVjdHMnKSlcbiAgICBjb25zdCBwcm9kdWN0cyA9IHN0b3JlZCA/IEpTT04ucGFyc2Uoc3RvcmVkKSA6IG1vY2tQcm9kdWN0c1xuICAgIGNvbnNvbGUubG9nKCdDbGllbnQgc2lkZSAtIGxvYWRlZCBwcm9kdWN0czonLCBwcm9kdWN0cy5sZW5ndGgpXG4gICAgY29uc29sZS5sb2coJ1Byb2R1Y3QgSURzOicsIHByb2R1Y3RzLm1hcCgocDogTW9ja1Byb2R1Y3QpID0+IHAuaWQpKVxuICAgIHJldHVybiBwcm9kdWN0c1xuICB9XG5cbiAgc3RhdGljIGdldENhdGVnb3JpZXMoKTogTW9ja0NhdGVnb3J5W10ge1xuICAgIGlmICh0eXBlb2Ygd2luZG93ID09PSAndW5kZWZpbmVkJykgcmV0dXJuIG1vY2tDYXRlZ29yaWVzXG5cbiAgICBjb25zdCBzdG9yZWQgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSh0aGlzLmdldFN0b3JhZ2VLZXkoJ2NhdGVnb3JpZXMnKSlcbiAgICByZXR1cm4gc3RvcmVkID8gSlNPTi5wYXJzZShzdG9yZWQpIDogbW9ja0NhdGVnb3JpZXNcbiAgfVxuXG4gIHN0YXRpYyBnZXRTY2hvb2xzKCk6IE1vY2tTY2hvb2xbXSB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSByZXR1cm4gbW9ja1NjaG9vbHNcblxuICAgIGNvbnN0IHN0b3JlZCA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKHRoaXMuZ2V0U3RvcmFnZUtleSgnc2Nob29scycpKVxuICAgIHJldHVybiBzdG9yZWQgPyBKU09OLnBhcnNlKHN0b3JlZCkgOiBtb2NrU2Nob29sc1xuICB9XG5cbiAgc3RhdGljIGdldE9yZGVycygpOiBNb2NrT3JkZXJbXSB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSByZXR1cm4gbW9ja09yZGVyc1xuXG4gICAgY29uc3Qgc3RvcmVkID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0odGhpcy5nZXRTdG9yYWdlS2V5KCdvcmRlcnMnKSlcbiAgICByZXR1cm4gc3RvcmVkID8gSlNPTi5wYXJzZShzdG9yZWQpIDogbW9ja09yZGVyc1xuICB9XG5cbiAgc3RhdGljIHNhdmVQYWdlcyhwYWdlczogTW9ja1BhZ2VbXSk6IHZvaWQge1xuICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0odGhpcy5nZXRTdG9yYWdlS2V5KCdwYWdlcycpLCBKU09OLnN0cmluZ2lmeShwYWdlcykpXG4gICAgfVxuICB9XG5cbiAgc3RhdGljIHNhdmVNZW51SXRlbXMoaXRlbXM6IE1vY2tNZW51SXRlbVtdKTogdm9pZCB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSh0aGlzLmdldFN0b3JhZ2VLZXkoJ21lbnVJdGVtcycpLCBKU09OLnN0cmluZ2lmeShpdGVtcykpXG4gICAgfVxuICB9XG5cbiAgc3RhdGljIHNhdmVQcm9kdWN0cyhwcm9kdWN0czogTW9ja1Byb2R1Y3RbXSk6IHZvaWQge1xuICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgY29uc29sZS5sb2coJ1NhdmluZyBwcm9kdWN0cyB0byBsb2NhbFN0b3JhZ2U6JywgcHJvZHVjdHMubGVuZ3RoKVxuICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0odGhpcy5nZXRTdG9yYWdlS2V5KCdwcm9kdWN0cycpLCBKU09OLnN0cmluZ2lmeShwcm9kdWN0cykpXG5cbiAgICAgIC8vINin2YTYqtit2YLZgiDZhdmGINij2YYg2KfZhNio2YrYp9mG2KfYqiDYrdmP2YHYuNiqINio2LTZg9mEINi12K3ZititXG4gICAgICBjb25zdCBzYXZlZCA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKHRoaXMuZ2V0U3RvcmFnZUtleSgncHJvZHVjdHMnKSlcbiAgICAgIGNvbnN0IHBhcnNlZFNhdmVkID0gc2F2ZWQgPyBKU09OLnBhcnNlKHNhdmVkKSA6IFtdXG4gICAgICBjb25zb2xlLmxvZygnVmVyaWZpZWQgc2F2ZWQgcHJvZHVjdHMgY291bnQ6JywgcGFyc2VkU2F2ZWQubGVuZ3RoKVxuICAgIH1cbiAgfVxuXG4gIHN0YXRpYyBzYXZlQ2F0ZWdvcmllcyhjYXRlZ29yaWVzOiBNb2NrQ2F0ZWdvcnlbXSk6IHZvaWQge1xuICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0odGhpcy5nZXRTdG9yYWdlS2V5KCdjYXRlZ29yaWVzJyksIEpTT04uc3RyaW5naWZ5KGNhdGVnb3JpZXMpKVxuICAgIH1cbiAgfVxuXG4gIHN0YXRpYyBzYXZlU2Nob29scyhzY2hvb2xzOiBNb2NrU2Nob29sW10pOiB2b2lkIHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKHRoaXMuZ2V0U3RvcmFnZUtleSgnc2Nob29scycpLCBKU09OLnN0cmluZ2lmeShzY2hvb2xzKSlcbiAgICB9XG4gIH1cblxuICBzdGF0aWMgc2F2ZU9yZGVycyhvcmRlcnM6IE1vY2tPcmRlcltdKTogdm9pZCB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSh0aGlzLmdldFN0b3JhZ2VLZXkoJ29yZGVycycpLCBKU09OLnN0cmluZ2lmeShvcmRlcnMpKVxuICAgIH1cbiAgfVxuXG4gIHN0YXRpYyBnZW5lcmF0ZUlkKCk6IHN0cmluZyB7XG4gICAgcmV0dXJuIERhdGUubm93KCkudG9TdHJpbmcoKSArIE1hdGgucmFuZG9tKCkudG9TdHJpbmcoMzYpLnN1YnN0cmluZygyLCAxMSlcbiAgfVxuXG4gIC8vINmF2LPYrSDYrNmF2YrYuSDYp9mE2KjZitin2YbYp9iqINin2YTZhdit2YHZiNi42KkgKNmE2YTYp9iu2KrYqNin2LEpXG4gIHN0YXRpYyBjbGVhckFsbERhdGEoKTogdm9pZCB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgnbW9ja0FJTW9kZWxzJylcbiAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdtb2NrTW9kZWxBY3Rpdml0aWVzJylcbiAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdtb2NrUGFnZXMnKVxuICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ21vY2tQYWdlVGVtcGxhdGVzJylcbiAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdtb2NrUGFnZVByb2plY3RzJylcbiAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdtb2NrQ29tcG9uZW50TGlicmFyeScpXG4gICAgfVxuICB9XG5cbiAgc3RhdGljIGdlbmVyYXRlT3JkZXJOdW1iZXIoKTogc3RyaW5nIHtcbiAgICBjb25zdCBkYXRlID0gbmV3IERhdGUoKVxuICAgIGNvbnN0IHllYXIgPSBkYXRlLmdldEZ1bGxZZWFyKCkudG9TdHJpbmcoKS5zbGljZSgtMilcbiAgICBjb25zdCBtb250aCA9IChkYXRlLmdldE1vbnRoKCkgKyAxKS50b1N0cmluZygpLnBhZFN0YXJ0KDIsICcwJylcbiAgICBjb25zdCBkYXkgPSBkYXRlLmdldERhdGUoKS50b1N0cmluZygpLnBhZFN0YXJ0KDIsICcwJylcbiAgICBjb25zdCBvcmRlcnMgPSB0aGlzLmdldE9yZGVycygpXG4gICAgY29uc3QgdG9kYXlPcmRlcnMgPSBvcmRlcnMuZmlsdGVyKG9yZGVyID0+XG4gICAgICBvcmRlci5jcmVhdGVkX2F0LnN0YXJ0c1dpdGgoYCR7ZGF0ZS5nZXRGdWxsWWVhcigpfS0ke21vbnRofS0ke2RheX1gKVxuICAgIClcbiAgICBjb25zdCBvcmRlckNvdW50ID0gKHRvZGF5T3JkZXJzLmxlbmd0aCArIDEpLnRvU3RyaW5nKCkucGFkU3RhcnQoMywgJzAnKVxuICAgIHJldHVybiBgR1QtJHt5ZWFyfSR7bW9udGh9JHtkYXl9LSR7b3JkZXJDb3VudH1gXG4gIH1cblxuICAvLyDYpdiv2KfYsdipINmG2YXYp9iw2Kwg2KfZhNiw2YPYp9ihINin2YTYp9i12LfZhtin2LnZilxuICBzdGF0aWMgZ2V0QUlNb2RlbHMoKTogQUlNb2RlbFtdIHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHJldHVybiB0aGlzLmRlZmF1bHRBSU1vZGVsc1xuXG4gICAgY29uc3Qgc3RvcmVkID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ21vY2tBSU1vZGVscycpXG4gICAgaWYgKHN0b3JlZCkge1xuICAgICAgcmV0dXJuIEpTT04ucGFyc2Uoc3RvcmVkKVxuICAgIH1cbiAgICByZXR1cm4gdGhpcy5kZWZhdWx0QUlNb2RlbHNcbiAgfVxuXG4gIHN0YXRpYyBzYXZlQUlNb2RlbHMobW9kZWxzOiBBSU1vZGVsW10pOiB2b2lkIHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdtb2NrQUlNb2RlbHMnLCBKU09OLnN0cmluZ2lmeShtb2RlbHMpKVxuICAgIH1cbiAgfVxuXG4gIHN0YXRpYyBnZXRNb2RlbEFjdGl2aXRpZXMoKTogTW9kZWxBY3Rpdml0eVtdIHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHJldHVybiB0aGlzLmRlZmF1bHRNb2RlbEFjdGl2aXRpZXNcblxuICAgIGNvbnN0IHN0b3JlZCA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdtb2NrTW9kZWxBY3Rpdml0aWVzJylcbiAgICBpZiAoc3RvcmVkKSB7XG4gICAgICByZXR1cm4gSlNPTi5wYXJzZShzdG9yZWQpXG4gICAgfVxuICAgIHJldHVybiB0aGlzLmRlZmF1bHRNb2RlbEFjdGl2aXRpZXNcbiAgfVxuXG4gIHN0YXRpYyBzYXZlTW9kZWxBY3Rpdml0aWVzKGFjdGl2aXRpZXM6IE1vZGVsQWN0aXZpdHlbXSk6IHZvaWQge1xuICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ21vY2tNb2RlbEFjdGl2aXRpZXMnLCBKU09OLnN0cmluZ2lmeShhY3Rpdml0aWVzKSlcbiAgICB9XG4gIH1cblxuICAvLyDYpdiv2KfYsdipINmC2YjYp9mE2Kgg2KfZhNi12YHYrdin2KpcbiAgc3RhdGljIGdldFBhZ2VUZW1wbGF0ZXMoKTogUGFnZVRlbXBsYXRlW10ge1xuICAgIGlmICh0eXBlb2Ygd2luZG93ID09PSAndW5kZWZpbmVkJykgcmV0dXJuIHRoaXMuZGVmYXVsdFBhZ2VUZW1wbGF0ZXNcblxuICAgIGNvbnN0IHN0b3JlZCA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdtb2NrUGFnZVRlbXBsYXRlcycpXG4gICAgaWYgKHN0b3JlZCkge1xuICAgICAgcmV0dXJuIEpTT04ucGFyc2Uoc3RvcmVkKVxuICAgIH1cbiAgICByZXR1cm4gdGhpcy5kZWZhdWx0UGFnZVRlbXBsYXRlc1xuICB9XG5cbiAgc3RhdGljIHNhdmVQYWdlVGVtcGxhdGVzKHRlbXBsYXRlczogUGFnZVRlbXBsYXRlW10pOiB2b2lkIHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdtb2NrUGFnZVRlbXBsYXRlcycsIEpTT04uc3RyaW5naWZ5KHRlbXBsYXRlcykpXG4gICAgfVxuICB9XG5cbiAgc3RhdGljIGdldFBhZ2VQcm9qZWN0cygpOiBQYWdlUHJvamVjdFtdIHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHJldHVybiB0aGlzLmRlZmF1bHRQYWdlUHJvamVjdHNcblxuICAgIGNvbnN0IHN0b3JlZCA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdtb2NrUGFnZVByb2plY3RzJylcbiAgICBpZiAoc3RvcmVkKSB7XG4gICAgICByZXR1cm4gSlNPTi5wYXJzZShzdG9yZWQpXG4gICAgfVxuICAgIHJldHVybiB0aGlzLmRlZmF1bHRQYWdlUHJvamVjdHNcbiAgfVxuXG4gIHN0YXRpYyBzYXZlUGFnZVByb2plY3RzKHByb2plY3RzOiBQYWdlUHJvamVjdFtdKTogdm9pZCB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgnbW9ja1BhZ2VQcm9qZWN0cycsIEpTT04uc3RyaW5naWZ5KHByb2plY3RzKSlcbiAgICB9XG4gIH1cblxuICBzdGF0aWMgZ2V0Q29tcG9uZW50TGlicmFyeSgpOiBDb21wb25lbnRMaWJyYXJ5SXRlbVtdIHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHJldHVybiB0aGlzLmRlZmF1bHRDb21wb25lbnRMaWJyYXJ5XG5cbiAgICBjb25zdCBzdG9yZWQgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnbW9ja0NvbXBvbmVudExpYnJhcnknKVxuICAgIGlmIChzdG9yZWQpIHtcbiAgICAgIHJldHVybiBKU09OLnBhcnNlKHN0b3JlZClcbiAgICB9XG4gICAgcmV0dXJuIHRoaXMuZGVmYXVsdENvbXBvbmVudExpYnJhcnlcbiAgfVxuXG4gIHN0YXRpYyBzYXZlQ29tcG9uZW50TGlicmFyeShjb21wb25lbnRzOiBDb21wb25lbnRMaWJyYXJ5SXRlbVtdKTogdm9pZCB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgnbW9ja0NvbXBvbmVudExpYnJhcnknLCBKU09OLnN0cmluZ2lmeShjb21wb25lbnRzKSlcbiAgICB9XG4gIH1cblxuICAvLyDYp9mE2KjZitin2YbYp9iqINin2YTYp9mB2KrYsdin2LbZitipINmE2YbZhdin2LDYrCDYp9mE2LDZg9in2KEg2KfZhNin2LXYt9mG2KfYudmKICjZgdin2LHYutipINmE2YTYqNiv2KfZitipKVxuICBzdGF0aWMgZGVmYXVsdEFJTW9kZWxzOiBBSU1vZGVsW10gPSBbXVxuXG4gIHN0YXRpYyBkZWZhdWx0TW9kZWxBY3Rpdml0aWVzOiBNb2RlbEFjdGl2aXR5W10gPSBbXVxuXG4gIC8vINin2YTYqNmK2KfZhtin2Kog2KfZhNin2YHYqtix2KfYttmK2Kkg2YTZgtmI2KfZhNioINin2YTYtdmB2K3Yp9iqXG4gIHN0YXRpYyBkZWZhdWx0UGFnZVRlbXBsYXRlczogUGFnZVRlbXBsYXRlW10gPSBbXG4gICAge1xuICAgICAgaWQ6ICd0ZW1wbGF0ZS1sYW5kaW5nLTEnLFxuICAgICAgbmFtZTogJ0xhbmRpbmcgUGFnZSAtIE1vZGVybicsXG4gICAgICBuYW1lQXI6ICfYtdmB2K3YqSDZh9io2YjYtyAtINi52LXYsdmK2KknLFxuICAgICAgbmFtZUVuOiAnTGFuZGluZyBQYWdlIC0gTW9kZXJuJyxcbiAgICAgIG5hbWVGcjogJ1BhZ2UgZFxcJ2F0dGVycmlzc2FnZSAtIE1vZGVybmUnLFxuICAgICAgZGVzY3JpcHRpb246ICfZgtin2YTYqCDYtdmB2K3YqSDZh9io2YjYtyDYudi12LHZitipINmF2Lkg2KrYtdmF2YrZhSDYrNiw2KfYqCcsXG4gICAgICBjYXRlZ29yeTogJ2xhbmRpbmcnLFxuICAgICAgY29tcG9uZW50czogW1xuICAgICAgICB7XG4gICAgICAgICAgaWQ6ICdoZXJvLTEnLFxuICAgICAgICAgIHR5cGU6ICdoZXJvJyxcbiAgICAgICAgICBuYW1lOiAnSGVybyBTZWN0aW9uJyxcbiAgICAgICAgICBwcm9wczoge1xuICAgICAgICAgICAgY29udGVudDogJ9mF2LHYrdio2KfZiyDYqNmD2YUg2YHZiiDZhdmG2LXYqSDYo9iy2YrYp9ihINin2YTYqtiu2LHYrCcsXG4gICAgICAgICAgICBzdHlsZTogeyBiYWNrZ3JvdW5kQ29sb3I6ICcjMUYyOTM3JywgY29sb3I6ICcjRkZGRkZGJyB9XG4gICAgICAgICAgfSxcbiAgICAgICAgICBwb3NpdGlvbjogeyB4OiAwLCB5OiAwIH0sXG4gICAgICAgICAgc2l6ZTogeyB3aWR0aDogJzEwMCUnLCBoZWlnaHQ6ICc1MDBweCcgfSxcbiAgICAgICAgICBpc1Zpc2libGU6IHRydWVcbiAgICAgICAgfVxuICAgICAgXSxcbiAgICAgIHByZXZpZXc6ICcvaW1hZ2VzL3RlbXBsYXRlcy9sYW5kaW5nLW1vZGVybi5qcGcnLFxuICAgICAgdGh1bWJuYWlsOiAnL2ltYWdlcy90ZW1wbGF0ZXMvbGFuZGluZy1tb2Rlcm4tdGh1bWIuanBnJyxcbiAgICAgIGlzQUlHZW5lcmF0ZWQ6IGZhbHNlLFxuICAgICAgaXNQcmVtaXVtOiBmYWxzZSxcbiAgICAgIHRhZ3M6IFsnbGFuZGluZycsICdtb2Rlcm4nLCAnYnVzaW5lc3MnXSxcbiAgICAgIGNyZWF0ZWRBdDogJzIwMjQtMDEtMDFUMDA6MDA6MDBaJyxcbiAgICAgIHVwZGF0ZWRBdDogJzIwMjQtMDEtMDFUMDA6MDA6MDBaJyxcbiAgICAgIHVzYWdlQ291bnQ6IDQ1LFxuICAgICAgcmF0aW5nOiA0LjgsXG4gICAgICBtZXRhZGF0YToge1xuICAgICAgICBjb2xvcnM6IFsnIzFGMjkzNycsICcjRkZGRkZGJywgJyMzQjgyRjYnXSxcbiAgICAgICAgZm9udHM6IFsnSW50ZXInLCAnQ2Fpcm8nXSxcbiAgICAgICAgbGF5b3V0OiAnc2luZ2xlLXBhZ2UnLFxuICAgICAgICByZXNwb25zaXZlOiB0cnVlXG4gICAgICB9XG4gICAgfVxuICBdXG5cbiAgLy8g2KfZhNio2YrYp9mG2KfYqiDYp9mE2KfZgdiq2LHYp9i22YrYqSDZhNmF2LTYp9ix2YrYuSDYp9mE2LXZgdit2KfYqlxuICBzdGF0aWMgZGVmYXVsdFBhZ2VQcm9qZWN0czogUGFnZVByb2plY3RbXSA9IFtcbiAgICB7XG4gICAgICBpZDogJ3Byb2plY3QtMScsXG4gICAgICBuYW1lOiAn2YXZiNmC2Lkg2KPYstmK2KfYoSDYp9mE2KrYrtix2Kwg2KfZhNix2KbZitiz2YonLFxuICAgICAgZGVzY3JpcHRpb246ICfYp9mE2YXZiNmC2Lkg2KfZhNix2KbZitiz2Yog2YTYudix2LYg2YXZhtiq2KzYp9iqINij2LLZitin2KEg2KfZhNiq2K7YsdisJyxcbiAgICAgIGNvbXBvbmVudHM6IFtdLFxuICAgICAgdGVtcGxhdGVJZDogJ3RlbXBsYXRlLWxhbmRpbmctMScsXG4gICAgICBnZW5lcmF0aW9uTW9kZTogJ3RlbXBsYXRlJyxcbiAgICAgIHNldHRpbmdzOiB7XG4gICAgICAgIHRpdGxlOiAn2KPYstmK2KfYoSDYp9mE2KrYrtix2KwgLSDZhdmG2LXYqSDZhdi62LHYqNmK2Kkg2YXYqtiu2LXYtdipJyxcbiAgICAgICAgZGVzY3JpcHRpb246ICfYo9mI2YQg2YXZhti12Kkg2YXYutix2KjZitipINmE2KrYo9is2YrYsSDZiNio2YrYuSDYo9iy2YrYp9ihINin2YTYqtiu2LHYrCcsXG4gICAgICAgIGtleXdvcmRzOiBbJ9ij2LLZitin2KEg2KfZhNiq2K7YsdisJywgJ9iq2KPYrNmK2LEnLCAn2KfZhNmF2LrYsdioJ10sXG4gICAgICAgIGxhbmd1YWdlOiAnYXInLFxuICAgICAgICBkaXJlY3Rpb246ICdydGwnXG4gICAgICB9LFxuICAgICAgaXNQdWJsaXNoZWQ6IGZhbHNlLFxuICAgICAgY3JlYXRlZEF0OiAnMjAyNC0wMS0xNVQwMDowMDowMFonLFxuICAgICAgdXBkYXRlZEF0OiAnMjAyNC0wMS0yMFQxMDozMDowMFonLFxuICAgICAgY3JlYXRlZEJ5OiAnYWRtaW4tMScsXG4gICAgICB2ZXJzaW9uOiAxXG4gICAgfVxuICBdXG5cbiAgLy8g2KfZhNio2YrYp9mG2KfYqiDYp9mE2KfZgdiq2LHYp9i22YrYqSDZhNmF2YPYqtio2Kkg2KfZhNmF2YPZiNmG2KfYqlxuICBzdGF0aWMgZGVmYXVsdENvbXBvbmVudExpYnJhcnk6IENvbXBvbmVudExpYnJhcnlJdGVtW10gPSBbXG4gICAge1xuICAgICAgaWQ6ICdjb21wLWhlcm8nLFxuICAgICAgbmFtZTogJ0hlcm8gU2VjdGlvbicsXG4gICAgICBuYW1lQXI6ICfZgtiz2YUg2KfZhNio2LfZhCcsXG4gICAgICB0eXBlOiAnaGVybycsXG4gICAgICBjYXRlZ29yeTogJ2xheW91dCcsXG4gICAgICBkZXNjcmlwdGlvbjogJ9mC2LPZhSDYsdim2YrYs9mKINis2LDYp9ioINmB2Yog2KPYudmE2Ykg2KfZhNi12YHYrdipJyxcbiAgICAgIGljb246ICdMYXlvdXQnLFxuICAgICAgcHJldmlldzogJy9pbWFnZXMvY29tcG9uZW50cy9oZXJvLXByZXZpZXcuanBnJyxcbiAgICAgIGRlZmF1bHRQcm9wczoge1xuICAgICAgICBjb250ZW50OiAn2LnZhtmI2KfZhiDYsdim2YrYs9mKINis2LDYp9ioJyxcbiAgICAgICAgc3R5bGU6IHsgYmFja2dyb3VuZENvbG9yOiAnIzFGMjkzNycsIGNvbG9yOiAnI0ZGRkZGRicgfVxuICAgICAgfSxcbiAgICAgIGRlZmF1bHRTaXplOiB7IHdpZHRoOiAnMTAwJScsIGhlaWdodDogJzUwMHB4JyB9LFxuICAgICAgaXNDdXN0b206IGZhbHNlLFxuICAgICAgaXNQcmVtaXVtOiBmYWxzZSxcbiAgICAgIHRhZ3M6IFsnbGF5b3V0JywgJ2hlYWRlcicsICdoZXJvJ10sXG4gICAgICB1c2FnZUNvdW50OiAxNTZcbiAgICB9LFxuICAgIHtcbiAgICAgIGlkOiAnY29tcC1idXR0b24nLFxuICAgICAgbmFtZTogJ0J1dHRvbicsXG4gICAgICBuYW1lQXI6ICfYstixJyxcbiAgICAgIHR5cGU6ICdidXR0b24nLFxuICAgICAgY2F0ZWdvcnk6ICdpbnRlcmFjdGl2ZScsXG4gICAgICBkZXNjcmlwdGlvbjogJ9iy2LEg2KrZgdin2LnZhNmKINmC2KfYqNmEINmE2YTYqtiu2LXZiti1JyxcbiAgICAgIGljb246ICdNb3VzZVBvaW50ZXInLFxuICAgICAgcHJldmlldzogJy9pbWFnZXMvY29tcG9uZW50cy9idXR0b24tcHJldmlldy5qcGcnLFxuICAgICAgZGVmYXVsdFByb3BzOiB7XG4gICAgICAgIGNvbnRlbnQ6ICfYp9mG2YLYsSDZh9mG2KcnLFxuICAgICAgICBzdHlsZTogeyBiYWNrZ3JvdW5kQ29sb3I6ICcjM0I4MkY2JywgY29sb3I6ICcjRkZGRkZGJyB9XG4gICAgICB9LFxuICAgICAgZGVmYXVsdFNpemU6IHsgd2lkdGg6ICcxMjBweCcsIGhlaWdodDogJzQwcHgnIH0sXG4gICAgICBpc0N1c3RvbTogZmFsc2UsXG4gICAgICBpc1ByZW1pdW06IGZhbHNlLFxuICAgICAgdGFnczogWydpbnRlcmFjdGl2ZScsICdidXR0b24nLCAnY3RhJ10sXG4gICAgICB1c2FnZUNvdW50OiAyMzRcbiAgICB9XG4gIF1cblxuICAvLyDYpdiv2KfYsdipINmF2LLZiNiv2Yog2KfZhNiw2YPYp9ihINin2YTYp9i12LfZhtin2LnZilxuICBzdGF0aWMgZ2V0QUlQcm92aWRlcnMoKTogTW9ja0FJUHJvdmlkZXJbXSB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSByZXR1cm4gW11cblxuICAgIGNvbnN0IHN0b3JlZCA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKHRoaXMuZ2V0U3RvcmFnZUtleSgnYWlQcm92aWRlcnMnKSlcbiAgICByZXR1cm4gc3RvcmVkID8gSlNPTi5wYXJzZShzdG9yZWQpIDogW11cbiAgfVxuXG4gIHN0YXRpYyBhZGRBSVByb3ZpZGVyKHByb3ZpZGVyOiBNb2NrQUlQcm92aWRlcik6IE1vY2tBSVByb3ZpZGVyIHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHJldHVybiBwcm92aWRlclxuXG4gICAgY29uc3QgcHJvdmlkZXJzID0gdGhpcy5nZXRBSVByb3ZpZGVycygpXG4gICAgcHJvdmlkZXJzLnB1c2gocHJvdmlkZXIpXG4gICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0odGhpcy5nZXRTdG9yYWdlS2V5KCdhaVByb3ZpZGVycycpLCBKU09OLnN0cmluZ2lmeShwcm92aWRlcnMpKVxuICAgIHJldHVybiBwcm92aWRlclxuICB9XG5cbiAgc3RhdGljIHVwZGF0ZUFJUHJvdmlkZXIoaWQ6IHN0cmluZywgdXBkYXRlZFByb3ZpZGVyOiBQYXJ0aWFsPE1vY2tBSVByb3ZpZGVyPik6IE1vY2tBSVByb3ZpZGVyIHwgbnVsbCB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSByZXR1cm4gbnVsbFxuXG4gICAgY29uc3QgcHJvdmlkZXJzID0gdGhpcy5nZXRBSVByb3ZpZGVycygpXG4gICAgY29uc3QgaW5kZXggPSBwcm92aWRlcnMuZmluZEluZGV4KHAgPT4gcC5pZCA9PT0gaWQpXG5cbiAgICBpZiAoaW5kZXggPT09IC0xKSByZXR1cm4gbnVsbFxuXG4gICAgcHJvdmlkZXJzW2luZGV4XSA9IHsgLi4ucHJvdmlkZXJzW2luZGV4XSwgLi4udXBkYXRlZFByb3ZpZGVyIH1cbiAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSh0aGlzLmdldFN0b3JhZ2VLZXkoJ2FpUHJvdmlkZXJzJyksIEpTT04uc3RyaW5naWZ5KHByb3ZpZGVycykpXG4gICAgcmV0dXJuIHByb3ZpZGVyc1tpbmRleF1cbiAgfVxuXG4gIHN0YXRpYyBkZWxldGVBSVByb3ZpZGVyKGlkOiBzdHJpbmcpOiBib29sZWFuIHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHJldHVybiBmYWxzZVxuXG4gICAgY29uc3QgcHJvdmlkZXJzID0gdGhpcy5nZXRBSVByb3ZpZGVycygpXG4gICAgY29uc3QgZmlsdGVyZWRQcm92aWRlcnMgPSBwcm92aWRlcnMuZmlsdGVyKHAgPT4gcC5pZCAhPT0gaWQpXG5cbiAgICBpZiAoZmlsdGVyZWRQcm92aWRlcnMubGVuZ3RoID09PSBwcm92aWRlcnMubGVuZ3RoKSByZXR1cm4gZmFsc2VcblxuICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKHRoaXMuZ2V0U3RvcmFnZUtleSgnYWlQcm92aWRlcnMnKSwgSlNPTi5zdHJpbmdpZnkoZmlsdGVyZWRQcm92aWRlcnMpKVxuICAgIHJldHVybiB0cnVlXG4gIH1cblxuICBzdGF0aWMgZ2V0QUlQcm92aWRlckJ5SWQoaWQ6IHN0cmluZyk6IE1vY2tBSVByb3ZpZGVyIHwgbnVsbCB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSByZXR1cm4gbnVsbFxuXG4gICAgY29uc3QgcHJvdmlkZXJzID0gdGhpcy5nZXRBSVByb3ZpZGVycygpXG4gICAgcmV0dXJuIHByb3ZpZGVycy5maW5kKHAgPT4gcC5pZCA9PT0gaWQpIHx8IG51bGxcbiAgfVxufVxuIl0sIm5hbWVzIjpbIm1vY2tQYWdlcyIsImlkIiwic2x1ZyIsImlzX3B1Ymxpc2hlZCIsImF1dGhvcl9pZCIsImZlYXR1cmVkX2ltYWdlIiwiY3JlYXRlZF9hdCIsInVwZGF0ZWRfYXQiLCJwcm9maWxlcyIsImZ1bGxfbmFtZSIsInBhZ2VfY29udGVudCIsInBhZ2VfaWQiLCJsYW5ndWFnZSIsInRpdGxlIiwiY29udGVudCIsIm1ldGFfZGVzY3JpcHRpb24iLCJtZXRhX2tleXdvcmRzIiwibW9ja01lbnVJdGVtcyIsInRpdGxlX2FyIiwidGl0bGVfZW4iLCJ0aXRsZV9mciIsImljb24iLCJvcmRlcl9pbmRleCIsImlzX2FjdGl2ZSIsInRhcmdldF90eXBlIiwidGFyZ2V0X3ZhbHVlIiwicGFyZW50X2lkIiwibW9ja0NhdGVnb3JpZXMiLCJuYW1lX2FyIiwibmFtZV9lbiIsIm5hbWVfZnIiLCJkZXNjcmlwdGlvbiIsIm1vY2tQcm9kdWN0cyIsIm5hbWUiLCJjYXRlZ29yeSIsInByaWNlIiwicmVudGFsX3ByaWNlIiwiY29sb3JzIiwic2l6ZXMiLCJpbWFnZXMiLCJzdG9ja19xdWFudGl0eSIsImlzX2F2YWlsYWJsZSIsInJhdGluZyIsInJldmlld3NfY291bnQiLCJmZWF0dXJlcyIsInNwZWNpZmljYXRpb25zIiwibWF0ZXJpYWwiLCJ3ZWlnaHQiLCJjYXJlIiwidGFzc2VsX2NvbG9yIiwiYWRqdXN0YWJsZSIsImVtYnJvaWRlcnkiLCJsZW5ndGgiLCJhdHRhY2htZW50IiwibGluaW5nIiwiYWNhZGVtaWNfbGV2ZWwiLCJtb2NrU2Nob29scyIsImFkbWluX2lkIiwiYWRkcmVzcyIsImNpdHkiLCJwaG9uZSIsImVtYWlsIiwid2Vic2l0ZSIsImxvZ29fdXJsIiwiZ3JhZHVhdGlvbl9kYXRlIiwic3R1ZGVudF9jb3VudCIsInNldHRpbmdzIiwiZ3JhZHVhdGlvbl9jZXJlbW9ueV9sb2NhdGlvbiIsImRyZXNzX2NvZGUiLCJwaG90b2dyYXBoeV9hbGxvd2VkIiwibW9ja09yZGVycyIsIm9yZGVyX251bWJlciIsImN1c3RvbWVyX2lkIiwiY3VzdG9tZXJfbmFtZSIsImN1c3RvbWVyX2VtYWlsIiwiY3VzdG9tZXJfcGhvbmUiLCJzdGF0dXMiLCJpdGVtcyIsIm9yZGVyX2lkIiwicHJvZHVjdF9pZCIsInByb2R1Y3RfbmFtZSIsInByb2R1Y3RfaW1hZ2UiLCJxdWFudGl0eSIsInVuaXRfcHJpY2UiLCJ0b3RhbF9wcmljZSIsImN1c3RvbWl6YXRpb25zIiwiY29sb3IiLCJzaXplIiwic3VidG90YWwiLCJ0YXgiLCJzaGlwcGluZ19jb3N0IiwidG90YWwiLCJwYXltZW50X3N0YXR1cyIsInBheW1lbnRfbWV0aG9kIiwic2hpcHBpbmdfYWRkcmVzcyIsInN0cmVldCIsInN0YXRlIiwicG9zdGFsX2NvZGUiLCJjb3VudHJ5IiwidHJhY2tpbmdfbnVtYmVyIiwibm90ZXMiLCJkZWxpdmVyeV9kYXRlIiwic2Nob29sX2lkIiwic2Nob29sX25hbWUiLCJNb2NrRGF0YU1hbmFnZXIiLCJnZXRTdG9yYWdlS2V5IiwidHlwZSIsImdldFBhZ2VzIiwic3RvcmVkIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsIkpTT04iLCJwYXJzZSIsImdldE1lbnVJdGVtcyIsImdldFByb2R1Y3RzIiwiY29uc29sZSIsImxvZyIsInByb2R1Y3RzIiwibWFwIiwicCIsImdldENhdGVnb3JpZXMiLCJnZXRTY2hvb2xzIiwiZ2V0T3JkZXJzIiwic2F2ZVBhZ2VzIiwicGFnZXMiLCJzZXRJdGVtIiwic3RyaW5naWZ5Iiwic2F2ZU1lbnVJdGVtcyIsInNhdmVQcm9kdWN0cyIsInNhdmVkIiwicGFyc2VkU2F2ZWQiLCJzYXZlQ2F0ZWdvcmllcyIsImNhdGVnb3JpZXMiLCJzYXZlU2Nob29scyIsInNjaG9vbHMiLCJzYXZlT3JkZXJzIiwib3JkZXJzIiwiZ2VuZXJhdGVJZCIsIkRhdGUiLCJub3ciLCJ0b1N0cmluZyIsIk1hdGgiLCJyYW5kb20iLCJzdWJzdHJpbmciLCJjbGVhckFsbERhdGEiLCJyZW1vdmVJdGVtIiwiZ2VuZXJhdGVPcmRlck51bWJlciIsImRhdGUiLCJ5ZWFyIiwiZ2V0RnVsbFllYXIiLCJzbGljZSIsIm1vbnRoIiwiZ2V0TW9udGgiLCJwYWRTdGFydCIsImRheSIsImdldERhdGUiLCJ0b2RheU9yZGVycyIsImZpbHRlciIsIm9yZGVyIiwic3RhcnRzV2l0aCIsIm9yZGVyQ291bnQiLCJnZXRBSU1vZGVscyIsImRlZmF1bHRBSU1vZGVscyIsInNhdmVBSU1vZGVscyIsIm1vZGVscyIsImdldE1vZGVsQWN0aXZpdGllcyIsImRlZmF1bHRNb2RlbEFjdGl2aXRpZXMiLCJzYXZlTW9kZWxBY3Rpdml0aWVzIiwiYWN0aXZpdGllcyIsImdldFBhZ2VUZW1wbGF0ZXMiLCJkZWZhdWx0UGFnZVRlbXBsYXRlcyIsInNhdmVQYWdlVGVtcGxhdGVzIiwidGVtcGxhdGVzIiwiZ2V0UGFnZVByb2plY3RzIiwiZGVmYXVsdFBhZ2VQcm9qZWN0cyIsInNhdmVQYWdlUHJvamVjdHMiLCJwcm9qZWN0cyIsImdldENvbXBvbmVudExpYnJhcnkiLCJkZWZhdWx0Q29tcG9uZW50TGlicmFyeSIsInNhdmVDb21wb25lbnRMaWJyYXJ5IiwiY29tcG9uZW50cyIsIm5hbWVBciIsIm5hbWVFbiIsIm5hbWVGciIsInByb3BzIiwic3R5bGUiLCJiYWNrZ3JvdW5kQ29sb3IiLCJwb3NpdGlvbiIsIngiLCJ5Iiwid2lkdGgiLCJoZWlnaHQiLCJpc1Zpc2libGUiLCJwcmV2aWV3IiwidGh1bWJuYWlsIiwiaXNBSUdlbmVyYXRlZCIsImlzUHJlbWl1bSIsInRhZ3MiLCJjcmVhdGVkQXQiLCJ1cGRhdGVkQXQiLCJ1c2FnZUNvdW50IiwibWV0YWRhdGEiLCJmb250cyIsImxheW91dCIsInJlc3BvbnNpdmUiLCJ0ZW1wbGF0ZUlkIiwiZ2VuZXJhdGlvbk1vZGUiLCJrZXl3b3JkcyIsImRpcmVjdGlvbiIsImlzUHVibGlzaGVkIiwiY3JlYXRlZEJ5IiwidmVyc2lvbiIsImRlZmF1bHRQcm9wcyIsImRlZmF1bHRTaXplIiwiaXNDdXN0b20iLCJnZXRBSVByb3ZpZGVycyIsImFkZEFJUHJvdmlkZXIiLCJwcm92aWRlciIsInByb3ZpZGVycyIsInB1c2giLCJ1cGRhdGVBSVByb3ZpZGVyIiwidXBkYXRlZFByb3ZpZGVyIiwiaW5kZXgiLCJmaW5kSW5kZXgiLCJkZWxldGVBSVByb3ZpZGVyIiwiZmlsdGVyZWRQcm92aWRlcnMiLCJnZXRBSVByb3ZpZGVyQnlJZCIsImZpbmQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/mockData.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/models/Product.ts":
/*!***********************************!*\
  !*** ./src/lib/models/Product.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProductModel: () => (/* binding */ ProductModel)\n/* harmony export */ });\n/* harmony import */ var _database__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../database */ \"(rsc)/./src/lib/database.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_database__WEBPACK_IMPORTED_MODULE_0__]);\n_database__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\nclass ProductModel {\n    // جلب جميع المنتجات مع الفلترة\n    static async getAll(filters = {}) {\n        let whereConditions = [];\n        let params = [];\n        let paramIndex = 1;\n        // بناء شروط WHERE\n        if (filters.category) {\n            whereConditions.push(`category_id = $${paramIndex}`);\n            params.push(filters.category);\n            paramIndex++;\n        }\n        if (filters.available !== undefined) {\n            whereConditions.push(`is_available = $${paramIndex}`);\n            params.push(filters.available);\n            paramIndex++;\n        }\n        if (filters.published !== undefined) {\n            whereConditions.push(`is_published = $${paramIndex}`);\n            params.push(filters.published);\n            paramIndex++;\n        }\n        if (filters.search) {\n            whereConditions.push(`(name ILIKE $${paramIndex} OR description ILIKE $${paramIndex})`);\n            params.push(`%${filters.search}%`);\n            paramIndex++;\n        }\n        if (filters.minPrice !== undefined) {\n            whereConditions.push(`price >= $${paramIndex}`);\n            params.push(filters.minPrice);\n            paramIndex++;\n        }\n        if (filters.maxPrice !== undefined) {\n            whereConditions.push(`price <= $${paramIndex}`);\n            params.push(filters.maxPrice);\n            paramIndex++;\n        }\n        const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';\n        // ترتيب النتائج\n        const sortBy = filters.sortBy || 'created_at';\n        const sortOrder = filters.sortOrder || 'DESC';\n        const orderClause = `ORDER BY ${sortBy} ${sortOrder}`;\n        // حد النتائج والإزاحة\n        const limit = filters.limit || 50;\n        const offset = filters.offset || 0;\n        const limitClause = `LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;\n        params.push(limit, offset);\n        // استعلام العد الكلي\n        const countQuery = `SELECT COUNT(*) as total FROM products ${whereClause}`;\n        const countResult = await (0,_database__WEBPACK_IMPORTED_MODULE_0__.query)(countQuery, params.slice(0, -2));\n        const total = parseInt(countResult.rows[0].total);\n        // استعلام البيانات\n        const dataQuery = `\n      SELECT \n        id, name, description, category_id, price, rental_price,\n        colors, sizes, images, stock_quantity, is_available, is_published,\n        features, specifications, rating, reviews_count, created_at, updated_at\n      FROM products \n      ${whereClause} \n      ${orderClause} \n      ${limitClause}\n    `;\n        const result = await (0,_database__WEBPACK_IMPORTED_MODULE_0__.query)(dataQuery, params);\n        return {\n            products: result.rows,\n            total\n        };\n    }\n    // جلب منتج واحد بالمعرف\n    static async getById(id) {\n        const result = await (0,_database__WEBPACK_IMPORTED_MODULE_0__.query)('SELECT * FROM products WHERE id = $1', [\n            id\n        ]);\n        return result.rows[0] || null;\n    }\n    // إنشاء منتج جديد\n    static async create(productData) {\n        const result = await (0,_database__WEBPACK_IMPORTED_MODULE_0__.query)(`\n      INSERT INTO products (\n        name, description, category_id, price, rental_price,\n        colors, sizes, images, stock_quantity, is_available, is_published,\n        features, specifications, rating, reviews_count\n      ) VALUES (\n        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15\n      ) RETURNING *\n    `, [\n            productData.name,\n            productData.description,\n            productData.category_id,\n            productData.price,\n            productData.rental_price,\n            productData.colors,\n            productData.sizes,\n            productData.images,\n            productData.stock_quantity,\n            productData.is_available,\n            productData.is_published,\n            productData.features,\n            JSON.stringify(productData.specifications),\n            productData.rating || 0,\n            productData.reviews_count || 0\n        ]);\n        return result.rows[0];\n    }\n    // تحديث منتج\n    static async update(id, updates) {\n        const setClause = [];\n        const params = [];\n        let paramIndex = 1;\n        // بناء جملة SET ديناميكياً\n        Object.entries(updates).forEach(([key, value])=>{\n            if (key !== 'id' && key !== 'created_at' && value !== undefined) {\n                if (key === 'specifications') {\n                    setClause.push(`${key} = $${paramIndex}`);\n                    params.push(JSON.stringify(value));\n                } else {\n                    setClause.push(`${key} = $${paramIndex}`);\n                    params.push(value);\n                }\n                paramIndex++;\n            }\n        });\n        if (setClause.length === 0) {\n            throw new Error('لا توجد حقول للتحديث');\n        }\n        // إضافة updated_at\n        setClause.push(`updated_at = NOW()`);\n        params.push(id);\n        const result = await (0,_database__WEBPACK_IMPORTED_MODULE_0__.query)(`\n      UPDATE products \n      SET ${setClause.join(', ')} \n      WHERE id = $${paramIndex} \n      RETURNING *\n    `, params);\n        return result.rows[0] || null;\n    }\n    // حذف منتج\n    static async delete(id) {\n        const result = await (0,_database__WEBPACK_IMPORTED_MODULE_0__.query)('DELETE FROM products WHERE id = $1', [\n            id\n        ]);\n        return result.rowCount > 0;\n    }\n    // تحديث تقييم المنتج\n    static async updateRating(productId) {\n        await (0,_database__WEBPACK_IMPORTED_MODULE_0__.query)(`\n      UPDATE products \n      SET \n        rating = (\n          SELECT COALESCE(AVG(rating), 0) \n          FROM reviews \n          WHERE product_id = $1\n        ),\n        reviews_count = (\n          SELECT COUNT(*) \n          FROM reviews \n          WHERE product_id = $1\n        ),\n        updated_at = NOW()\n      WHERE id = $1\n    `, [\n            productId\n        ]);\n    }\n    // تحديث المخزون\n    static async updateStock(productId, quantity) {\n        return await (0,_database__WEBPACK_IMPORTED_MODULE_0__.transaction)(async (client)=>{\n            // التحقق من المخزون الحالي\n            const stockResult = await client.query('SELECT stock_quantity FROM products WHERE id = $1 FOR UPDATE', [\n                productId\n            ]);\n            if (stockResult.rows.length === 0) {\n                throw new Error('المنتج غير موجود');\n            }\n            const currentStock = stockResult.rows[0].stock_quantity;\n            const newStock = currentStock + quantity;\n            if (newStock < 0) {\n                throw new Error('المخزون غير كافي');\n            }\n            // تحديث المخزون\n            const updateResult = await client.query('UPDATE products SET stock_quantity = $1, updated_at = NOW() WHERE id = $2', [\n                newStock,\n                productId\n            ]);\n            return updateResult.rowCount > 0;\n        });\n    }\n    // البحث في المنتجات\n    static async search(searchTerm, limit = 20) {\n        const result = await (0,_database__WEBPACK_IMPORTED_MODULE_0__.query)(`\n      SELECT * FROM products \n      WHERE \n        is_published = true \n        AND is_available = true \n        AND (\n          name ILIKE $1 \n          OR description ILIKE $1 \n          OR $2 = ANY(features)\n        )\n      ORDER BY \n        CASE \n          WHEN name ILIKE $1 THEN 1\n          WHEN description ILIKE $1 THEN 2\n          ELSE 3\n        END,\n        rating DESC\n      LIMIT $3\n    `, [\n            `%${searchTerm}%`,\n            searchTerm,\n            limit\n        ]);\n        return result.rows;\n    }\n    // جلب المنتجات الأكثر مبيعاً\n    static async getBestSellers(limit = 10) {\n        const result = await (0,_database__WEBPACK_IMPORTED_MODULE_0__.query)(`\n      SELECT p.*, COALESCE(SUM(oi.quantity), 0) as total_sold\n      FROM products p\n      LEFT JOIN order_items oi ON p.id = oi.product_id\n      LEFT JOIN orders o ON oi.order_id = o.id\n      WHERE p.is_published = true AND p.is_available = true\n        AND (o.status IS NULL OR o.status IN ('confirmed', 'processing', 'shipped', 'delivered'))\n      GROUP BY p.id\n      ORDER BY total_sold DESC, p.rating DESC\n      LIMIT $1\n    `, [\n            limit\n        ]);\n        return result.rows;\n    }\n    // جلب المنتجات الجديدة\n    static async getNewProducts(limit = 10) {\n        const result = await (0,_database__WEBPACK_IMPORTED_MODULE_0__.query)(`\n      SELECT * FROM products\n      WHERE is_published = true AND is_available = true\n      ORDER BY created_at DESC\n      LIMIT $1\n    `, [\n            limit\n        ]);\n        return result.rows;\n    }\n    // جلب المنتجات ذات التقييم العالي\n    static async getTopRated(limit = 10) {\n        const result = await (0,_database__WEBPACK_IMPORTED_MODULE_0__.query)(`\n      SELECT * FROM products\n      WHERE is_published = true AND is_available = true AND rating > 0\n      ORDER BY rating DESC, reviews_count DESC\n      LIMIT $1\n    `, [\n            limit\n        ]);\n        return result.rows;\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/models/Product.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "pg":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = import("pg");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fproducts%2Froute&page=%2Fapi%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Froute.ts&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CGraduation%20Toqs%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CGraduation%20Toqs%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();