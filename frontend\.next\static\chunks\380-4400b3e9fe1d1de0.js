"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[380],{5845:(e,t,r)=>{r.d(t,{i:()=>u});var n,o=r(12115),l=r(52712),i=(n||(n=r.t(o,2)))[" useInsertionEffect ".trim().toString()]||l.N;function u({prop:e,defaultProp:t,onChange:r=()=>{},caller:n}){let[l,u,a]=function({defaultProp:e,onChange:t}){let[r,n]=o.useState(e),l=o.useRef(r),u=o.useRef(t);return i(()=>{u.current=t},[t]),o.useEffect(()=>{l.current!==r&&(u.current?.(r),l.current=r)},[r,l]),[r,n,u]}({defaultProp:t,onChange:r}),c=void 0!==e,s=c?e:l;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==c){let t=c?"controlled":"uncontrolled";console.warn(`${n} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=c},[c,n])}return[s,o.useCallback(t=>{if(c){let r="function"==typeof t?t(e):t;r!==e&&a.current?.(r)}else u(t)},[c,e,u,a])]}Symbol("RADIX:SYNC_STATE")},11275:(e,t,r)=>{r.d(t,{X:()=>l});var n=r(12115),o=r(52712);function l(e){let[t,r]=n.useState(void 0);return(0,o.N)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,o;if(!Array.isArray(t)||!t.length)return;let l=t[0];if("borderBoxSize"in l){let e=l.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,o=t.blockSize}else n=e.offsetWidth,o=e.offsetHeight;r({width:n,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}},37328:(e,t,r)=>{function n(e,t,r){if(!t.has(e))throw TypeError("attempted to "+r+" private field on non-instance");return t.get(e)}function o(e,t){var r=n(e,t,"get");return r.get?r.get.call(e):r.value}function l(e,t,r){var o=n(e,t,"set");if(o.set)o.set.call(e,r);else{if(!o.writable)throw TypeError("attempted to set read only private field");o.value=r}return r}r.d(t,{N:()=>d});var i,u=r(12115),a=r(46081),c=r(6101),s=r(99708),f=r(95155);function d(e){let t=e+"CollectionProvider",[r,n]=(0,a.A)(t),[o,l]=r(t,{collectionRef:{current:null},itemMap:new Map}),i=e=>{let{scope:t,children:r}=e,n=u.useRef(null),l=u.useRef(new Map).current;return(0,f.jsx)(o,{scope:t,itemMap:l,collectionRef:n,children:r})};i.displayName=t;let d=e+"CollectionSlot",p=(0,s.TL)(d),v=u.forwardRef((e,t)=>{let{scope:r,children:n}=e,o=l(d,r),i=(0,c.s)(t,o.collectionRef);return(0,f.jsx)(p,{ref:i,children:n})});v.displayName=d;let m=e+"CollectionItemSlot",w="data-radix-collection-item",h=(0,s.TL)(m),b=u.forwardRef((e,t)=>{let{scope:r,children:n,...o}=e,i=u.useRef(null),a=(0,c.s)(t,i),s=l(m,r);return u.useEffect(()=>(s.itemMap.set(i,{ref:i,...o}),()=>void s.itemMap.delete(i))),(0,f.jsx)(h,{...{[w]:""},ref:a,children:n})});return b.displayName=m,[{Provider:i,Slot:v,ItemSlot:b},function(t){let r=l(e+"CollectionConsumer",t);return u.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(w,"]")));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},n]}var p=new WeakMap;function v(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let r=function(e,t){let r=e.length,n=m(t),o=n>=0?n:r+n;return o<0||o>=r?-1:o}(e,t);return -1===r?void 0:e[r]}function m(e){return e!=e||0===e?0:Math.trunc(e)}i=new WeakMap},61285:(e,t,r)=>{r.d(t,{B:()=>a});var n,o=r(12115),l=r(52712),i=(n||(n=r.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),u=0;function a(e){let[t,r]=o.useState(i());return(0,l.N)(()=>{e||r(e=>e??String(u++))},[e]),e||(t?`radix-${t}`:"")}},89196:(e,t,r)=>{r.d(t,{RG:()=>R,bL:()=>F,q7:()=>M});var n=r(12115),o=r(85185),l=r(37328),i=r(6101),u=r(46081),a=r(61285),c=r(63655),s=r(39033),f=r(5845),d=r(94315),p=r(95155),v="rovingFocusGroup.onEntryFocus",m={bubbles:!1,cancelable:!0},w="RovingFocusGroup",[h,b,g]=(0,l.N)(w),[y,R]=(0,u.A)(w,[g]),[S,A]=y(w),x=n.forwardRef((e,t)=>(0,p.jsx)(h.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(h.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(E,{...e,ref:t})})}));x.displayName=w;var E=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:l,loop:u=!1,dir:a,currentTabStopId:h,defaultCurrentTabStopId:g,onCurrentTabStopIdChange:y,onEntryFocus:R,preventScrollOnEntryFocus:A=!1,...x}=e,E=n.useRef(null),I=(0,i.s)(t,E),C=(0,d.jH)(a),[T,F]=(0,f.i)({prop:h,defaultProp:null!=g?g:null,onChange:y,caller:w}),[M,D]=n.useState(!1),N=(0,s.c)(R),j=b(r),L=n.useRef(!1),[_,G]=n.useState(0);return n.useEffect(()=>{let e=E.current;if(e)return e.addEventListener(v,N),()=>e.removeEventListener(v,N)},[N]),(0,p.jsx)(S,{scope:r,orientation:l,dir:C,loop:u,currentTabStopId:T,onItemFocus:n.useCallback(e=>F(e),[F]),onItemShiftTab:n.useCallback(()=>D(!0),[]),onFocusableItemAdd:n.useCallback(()=>G(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>G(e=>e-1),[]),children:(0,p.jsx)(c.sG.div,{tabIndex:M||0===_?-1:0,"data-orientation":l,...x,ref:I,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{L.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!L.current;if(e.target===e.currentTarget&&t&&!M){let t=new CustomEvent(v,m);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=j().filter(e=>e.focusable);k([e.find(e=>e.active),e.find(e=>e.id===T),...e].filter(Boolean).map(e=>e.ref.current),A)}}L.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>D(!1))})})}),I="RovingFocusGroupItem",C=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:l=!0,active:i=!1,tabStopId:u,children:s,...f}=e,d=(0,a.B)(),v=u||d,m=A(I,r),w=m.currentTabStopId===v,g=b(r),{onFocusableItemAdd:y,onFocusableItemRemove:R,currentTabStopId:S}=m;return n.useEffect(()=>{if(l)return y(),()=>R()},[l,y,R]),(0,p.jsx)(h.ItemSlot,{scope:r,id:v,focusable:l,active:i,children:(0,p.jsx)(c.sG.span,{tabIndex:w?0:-1,"data-orientation":m.orientation,...f,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{l?m.onItemFocus(v):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>m.onItemFocus(v)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void m.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let o=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return T[o]}(e,m.orientation,m.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=g().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=m.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>k(r))}}),children:"function"==typeof s?s({isCurrentTabStop:w,hasTabStop:null!=S}):s})})});C.displayName=I;var T={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function k(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var F=x,M=C}}]);