(()=>{var e={};e.id=7949,e.ids=[7949],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13861:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},13980:(e,s,a)=>{Promise.resolve().then(a.bind(a,34675))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23562:(e,s,a)=>{"use strict";a.d(s,{k:()=>l});var t=a(60687);a(43210);var r=a(25177),i=a(4780);function l({className:e,value:s,...a}){return(0,t.jsx)(r.bL,{"data-slot":"progress",className:(0,i.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",e),...a,children:(0,t.jsx)(r.C1,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:`translateX(-${100-(s||0)}%)`}})})}},23708:(e,s,a)=>{Promise.resolve().then(a.bind(a,89221))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31158:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},33872:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},33873:e=>{"use strict";e.exports=require("path")},34675:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t});let t=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Graduation Toqs\\\\frontend\\\\src\\\\app\\\\dashboard\\\\student\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\student\\page.tsx","default")},55146:(e,s,a)=>{"use strict";a.d(s,{B8:()=>q,UC:()=>z,bL:()=>_,l9:()=>D});var t=a(43210),r=a(70569),i=a(11273),l=a(72942),n=a(46059),c=a(14163),d=a(43),x=a(65551),m=a(96963),o=a(60687),h="Tabs",[u,p]=(0,i.A)(h,[l.RG]),j=(0,l.RG)(),[g,b]=u(h),v=t.forwardRef((e,s)=>{let{__scopeTabs:a,value:t,onValueChange:r,defaultValue:i,orientation:l="horizontal",dir:n,activationMode:u="automatic",...p}=e,j=(0,d.jH)(n),[b,v]=(0,x.i)({prop:t,onChange:r,defaultProp:i??"",caller:h});return(0,o.jsx)(g,{scope:a,baseId:(0,m.B)(),value:b,onValueChange:v,orientation:l,dir:j,activationMode:u,children:(0,o.jsx)(c.sG.div,{dir:j,"data-orientation":l,...p,ref:s})})});v.displayName=h;var N="TabsList",f=t.forwardRef((e,s)=>{let{__scopeTabs:a,loop:t=!0,...r}=e,i=b(N,a),n=j(a);return(0,o.jsx)(l.bL,{asChild:!0,...n,orientation:i.orientation,dir:i.dir,loop:t,children:(0,o.jsx)(c.sG.div,{role:"tablist","aria-orientation":i.orientation,...r,ref:s})})});f.displayName=N;var y="TabsTrigger",w=t.forwardRef((e,s)=>{let{__scopeTabs:a,value:t,disabled:i=!1,...n}=e,d=b(y,a),x=j(a),m=C(d.baseId,t),h=$(d.baseId,t),u=t===d.value;return(0,o.jsx)(l.q7,{asChild:!0,...x,focusable:!i,active:u,children:(0,o.jsx)(c.sG.button,{type:"button",role:"tab","aria-selected":u,"aria-controls":h,"data-state":u?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:m,...n,ref:s,onMouseDown:(0,r.m)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():d.onValueChange(t)}),onKeyDown:(0,r.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&d.onValueChange(t)}),onFocus:(0,r.m)(e.onFocus,()=>{let e="manual"!==d.activationMode;u||i||!e||d.onValueChange(t)})})})});w.displayName=y;var k="TabsContent",A=t.forwardRef((e,s)=>{let{__scopeTabs:a,value:r,forceMount:i,children:l,...d}=e,x=b(k,a),m=C(x.baseId,r),h=$(x.baseId,r),u=r===x.value,p=t.useRef(u);return t.useEffect(()=>{let e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,o.jsx)(n.C,{present:i||u,children:({present:a})=>(0,o.jsx)(c.sG.div,{"data-state":u?"active":"inactive","data-orientation":x.orientation,role:"tabpanel","aria-labelledby":m,hidden:!a,id:h,tabIndex:0,...d,ref:s,style:{...e.style,animationDuration:p.current?"0s":void 0},children:a&&l})})});function C(e,s){return`${e}-trigger-${s}`}function $(e,s){return`${e}-content-${s}`}A.displayName=k;var _=v,q=f,D=w,z=A},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},64398:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},71110:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>x,routeModule:()=>o,tree:()=>d});var t=a(65239),r=a(48088),i=a(88170),l=a.n(i),n=a(30893),c={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);a.d(s,c);let d={children:["",{children:["dashboard",{children:["student",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,34675)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\student\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(a.bind(a,54431)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,54413)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,x=["C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\student\\page.tsx"],m={require:a,loadChunk:()=>Promise.resolve()},o=new t.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/dashboard/student/page",pathname:"/dashboard/student",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},78272:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},79551:e=>{"use strict";e.exports=require("url")},81620:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("share-2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]])},85763:(e,s,a)=>{"use strict";a.d(s,{Xi:()=>c,av:()=>d,j7:()=>n,tU:()=>l});var t=a(60687);a(43210);var r=a(55146),i=a(4780);function l({className:e,...s}){return(0,t.jsx)(r.bL,{"data-slot":"tabs",className:(0,i.cn)("flex flex-col gap-2",e),...s})}function n({className:e,...s}){return(0,t.jsx)(r.B8,{"data-slot":"tabs-list",className:(0,i.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",e),...s})}function c({className:e,...s}){return(0,t.jsx)(r.l9,{"data-slot":"tabs-trigger",className:(0,i.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...s})}function d({className:e,...s}){return(0,t.jsx)(r.UC,{"data-slot":"tabs-content",className:(0,i.cn)("flex-1 outline-none",e),...s})}},85778:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},89221:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>G});var t=a(60687),r=a(43210),i=a(63213),l=a(87801),n=a(44493),c=a(29523),d=a(96834),x=a(23562),m=a(85763),o=a(89667),h=a(97051),u=a(84027),p=a(28561),j=a(67760),g=a(48730),b=a(64398),v=a(19080),N=a(99270),f=a(96474),y=a(27351),w=a(97992),k=a(85778),A=a(13861),C=a(31158),$=a(48340),_=a(33872),q=a(63143),D=a(81620),z=a(88233);function G(){let{user:e,profile:s,loading:a}=(0,i.A)(),[G,P]=(0,r.useState)("overview"),[T,M]=(0,r.useState)([]),[R,Z]=(0,r.useState)([]),[B,L]=(0,r.useState)(!0),S=e=>{switch(e){case"pending":return"bg-yellow-100 text-yellow-800";case"confirmed":return"bg-blue-100 text-blue-800";case"in_production":return"bg-purple-100 text-purple-800";case"shipped":return"bg-orange-100 text-orange-800";case"delivered":return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}},U=e=>{switch(e){case"pending":return"في الانتظار";case"confirmed":return"مؤكد";case"in_production":return"قيد الإنتاج";case"shipped":return"تم الشحن";case"delivered":return"تم التسليم";default:return"غير معروف"}},E=e=>{switch(e){case"pending":return 20;case"confirmed":return 40;case"in_production":return 60;case"shipped":return 80;case"delivered":return 100;default:return 0}};return a||B?(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",children:[(0,t.jsx)(l.V,{}),(0,t.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})})})]}):(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",children:[(0,t.jsx)(l.V,{}),(0,t.jsxs)("main",{className:"container mx-auto px-4 py-8",children:[(0,t.jsx)("div",{className:"mb-8",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white arabic-text",children:["مرحباً، ",s?.full_name||"الطالب","! \uD83C\uDF93"]}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-300 mt-2 arabic-text",children:"إدارة طلباتك وتصاميمك المخصصة"})]}),(0,t.jsxs)("div",{className:"flex gap-3",children:[(0,t.jsxs)(c.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"الإشعارات"]}),(0,t.jsxs)(c.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"الإعدادات"]})]})]})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[(0,t.jsx)(n.Zp,{children:(0,t.jsx)(n.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text",children:"إجمالي الطلبات"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:T.length})]}),(0,t.jsx)(p.A,{className:"h-8 w-8 text-blue-600"})]})})}),(0,t.jsx)(n.Zp,{children:(0,t.jsx)(n.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text",children:"التصاميم المحفوظة"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:R.length})]}),(0,t.jsx)(j.A,{className:"h-8 w-8 text-red-600"})]})})}),(0,t.jsx)(n.Zp,{children:(0,t.jsx)(n.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text",children:"الطلبات النشطة"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:T.filter(e=>"delivered"!==e.status).length})]}),(0,t.jsx)(g.A,{className:"h-8 w-8 text-orange-600"})]})})}),(0,t.jsx)(n.Zp,{children:(0,t.jsx)(n.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text",children:"النقاط المكتسبة"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"1,250"})]}),(0,t.jsx)(b.A,{className:"h-8 w-8 text-yellow-600"})]})})})]}),(0,t.jsxs)(m.tU,{value:G,onValueChange:P,children:[(0,t.jsxs)(m.j7,{className:"grid w-full grid-cols-4",children:[(0,t.jsxs)(m.Xi,{value:"overview",className:"arabic-text",children:[(0,t.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"نظرة عامة"]}),(0,t.jsxs)(m.Xi,{value:"orders",className:"arabic-text",children:[(0,t.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"طلباتي"]}),(0,t.jsxs)(m.Xi,{value:"track-orders",className:"arabic-text",children:[(0,t.jsx)(N.A,{className:"h-4 w-4 mr-2"}),"تتبع الطلبات"]}),(0,t.jsxs)(m.Xi,{value:"designs",className:"arabic-text",children:[(0,t.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"تصاميمي"]}),(0,t.jsxs)(m.Xi,{value:"profile",className:"arabic-text",children:[(0,t.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"الملف الشخصي"]})]}),(0,t.jsxs)(m.av,{value:"overview",className:"space-y-6 mt-6",children:[(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsx)(n.ZB,{className:"arabic-text",children:"الطلبات الأخيرة"}),(0,t.jsx)(n.BT,{className:"arabic-text",children:"آخر الطلبات وحالتها الحالية"})]}),(0,t.jsxs)(n.Wu,{children:[(0,t.jsx)("div",{className:"space-y-4",children:T.slice(0,3).map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center",children:(0,t.jsx)(v.A,{className:"h-6 w-6 text-gray-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("p",{className:"font-medium arabic-text",children:["طلب #",e.id]}),(0,t.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:[e.items.length," عنصر - ",e.total," درهم"]})]})]}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsx)(d.E,{className:S(e.status),children:U(e.status)}),(0,t.jsx)("div",{className:"mt-2 w-32",children:(0,t.jsx)(x.k,{value:E(e.status),className:"h-2"})})]})]},e.id))}),(0,t.jsx)("div",{className:"mt-4",children:(0,t.jsx)(c.$,{variant:"outline",className:"w-full arabic-text",children:"عرض جميع الطلبات"})})]})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsx)(n.ZB,{className:"arabic-text",children:"إجراءات سريعة"})}),(0,t.jsx)(n.Wu,{children:(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,t.jsxs)(c.$,{className:"h-20 flex-col gap-2",variant:"outline",children:[(0,t.jsx)(f.A,{className:"h-6 w-6"}),(0,t.jsx)("span",{className:"arabic-text",children:"طلب جديد"})]}),(0,t.jsxs)(c.$,{className:"h-20 flex-col gap-2",variant:"outline",children:[(0,t.jsx)(y.A,{className:"h-6 w-6"}),(0,t.jsx)("span",{className:"arabic-text",children:"تخصيص زي"})]}),(0,t.jsxs)(c.$,{className:"h-20 flex-col gap-2",variant:"outline",children:[(0,t.jsx)(w.A,{className:"h-6 w-6"}),(0,t.jsx)("span",{className:"arabic-text",children:"تتبع الطلب"})]}),(0,t.jsxs)(c.$,{className:"h-20 flex-col gap-2",variant:"outline",children:[(0,t.jsx)(k.A,{className:"h-6 w-6"}),(0,t.jsx)("span",{className:"arabic-text",children:"الفواتير"})]})]})})]})]}),(0,t.jsxs)(m.av,{value:"orders",className:"space-y-6 mt-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold arabic-text",children:"طلباتي"}),(0,t.jsxs)(c.$,{children:[(0,t.jsx)(f.A,{className:"h-4 w-4 mr-2"}),(0,t.jsx)("span",{className:"arabic-text",children:"طلب جديد"})]})]}),(0,t.jsx)("div",{className:"grid gap-6",children:T.map(e=>(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)(n.ZB,{className:"arabic-text",children:["طلب #",e.id]}),(0,t.jsxs)(n.BT,{className:"arabic-text",children:["تاريخ الطلب: ",new Date(e.created_at).toLocaleDateString("ar-SA")]})]}),(0,t.jsx)(d.E,{className:S(e.status),children:U(e.status)})]})}),(0,t.jsx)(n.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("div",{className:"space-y-3",children:e.items.map(e=>(0,t.jsxs)("div",{className:"flex items-center gap-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-lg"}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("p",{className:"font-medium arabic-text",children:e.name}),(0,t.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:["الكمية: ",e.quantity," \xd7 ",e.price," درهم"]})]})]},e.id))}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,t.jsx)("span",{className:"text-sm font-medium arabic-text",children:"تقدم الطلب"}),(0,t.jsxs)("span",{className:"text-sm text-gray-600",children:[E(e.status),"%"]})]}),(0,t.jsx)(x.k,{value:E(e.status),className:"h-2"})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center pt-3 border-t",children:[(0,t.jsx)("span",{className:"font-medium arabic-text",children:"الإجمالي:"}),(0,t.jsxs)("span",{className:"text-lg font-bold text-blue-600",children:[e.total," درهم"]})]}),(0,t.jsxs)("div",{className:"flex gap-2 pt-3",children:[(0,t.jsxs)(c.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(A.A,{className:"h-4 w-4 mr-2"}),(0,t.jsx)("span",{className:"arabic-text",children:"عرض التفاصيل"})]}),"shipped"===e.status&&(0,t.jsxs)(c.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(w.A,{className:"h-4 w-4 mr-2"}),(0,t.jsx)("span",{className:"arabic-text",children:"تتبع الشحنة"})]}),(0,t.jsxs)(c.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(C.A,{className:"h-4 w-4 mr-2"}),(0,t.jsx)("span",{className:"arabic-text",children:"تحميل الفاتورة"})]})]})]})})]},e.id))})]}),(0,t.jsxs)(m.av,{value:"track-orders",className:"space-y-6 mt-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold arabic-text",children:"تتبع الطلبات"}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(o.p,{placeholder:"أدخل رقم الطلب...",className:"w-64"}),(0,t.jsxs)(c.$,{children:[(0,t.jsx)(N.A,{className:"h-4 w-4 mr-2"}),(0,t.jsx)("span",{className:"arabic-text",children:"بحث"})]})]})]}),(0,t.jsx)("div",{className:"grid gap-6",children:T.map(e=>(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)(n.ZB,{className:"arabic-text",children:["طلب #",e.id]}),(0,t.jsxs)(n.BT,{className:"arabic-text",children:["تاريخ الطلب: ",new Date(e.created_at).toLocaleDateString("ar-SA")]})]}),(0,t.jsx)(d.E,{className:S(e.status),children:U(e.status)})]})}),(0,t.jsx)(n.Wu,{children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h4",{className:"font-medium arabic-text",children:"مراحل الطلب"}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"w-3 h-3 bg-green-500 rounded-full"}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("p",{className:"font-medium arabic-text",children:"تم استلام الطلب"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:new Date(e.created_at).toLocaleDateString("ar-SA")})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:`w-3 h-3 rounded-full ${["processing","shipped","delivered"].includes(e.status)?"bg-green-500":"bg-gray-300"}`}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("p",{className:"font-medium arabic-text",children:"قيد التحضير"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:["processing","shipped","delivered"].includes(e.status)?"مكتمل":"في الانتظار"})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:`w-3 h-3 rounded-full ${["shipped","delivered"].includes(e.status)?"bg-green-500":"bg-gray-300"}`}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("p",{className:"font-medium arabic-text",children:"تم الشحن"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:["shipped","delivered"].includes(e.status)?"مكتمل":"في الانتظار"})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:`w-3 h-3 rounded-full ${"delivered"===e.status?"bg-green-500":"bg-gray-300"}`}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("p",{className:"font-medium arabic-text",children:"تم التسليم"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"delivered"===e.status?"مكتمل":"في الانتظار"})]})]})]})]}),"shipped"===e.status&&(0,t.jsxs)("div",{className:"p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg",children:[(0,t.jsx)("h4",{className:"font-medium arabic-text mb-2",children:"معلومات الشحن"}),(0,t.jsxs)("p",{className:"text-sm arabic-text",children:["رقم التتبع: ",(0,t.jsxs)("span",{className:"font-mono",children:["TRK",e.id,"2024"]})]}),(0,t.jsx)("p",{className:"text-sm arabic-text",children:"الوقت المتوقع للوصول: 2-3 أيام عمل"})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(c.$,{variant:"outline",size:"sm",children:[(0,t.jsx)($.A,{className:"h-4 w-4 mr-2"}),(0,t.jsx)("span",{className:"arabic-text",children:"اتصل بالدعم"})]}),(0,t.jsxs)(c.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(_.A,{className:"h-4 w-4 mr-2"}),(0,t.jsx)("span",{className:"arabic-text",children:"محادثة مباشرة"})]})]})]})})]},e.id))})]}),(0,t.jsxs)(m.av,{value:"designs",className:"space-y-6 mt-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold arabic-text",children:"تصاميمي المحفوظة"}),(0,t.jsxs)(c.$,{children:[(0,t.jsx)(f.A,{className:"h-4 w-4 mr-2"}),(0,t.jsx)("span",{className:"arabic-text",children:"تصميم جديد"})]})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:R.map(e=>(0,t.jsx)(n.Zp,{children:(0,t.jsxs)(n.Wu,{className:"p-0",children:[(0,t.jsx)("div",{className:"aspect-square bg-gray-100 dark:bg-gray-800 rounded-t-lg"}),(0,t.jsxs)("div",{className:"p-4",children:[(0,t.jsx)("h3",{className:"font-medium arabic-text mb-2",children:e.name}),(0,t.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text mb-4",children:["تم الحفظ: ",new Date(e.created_at).toLocaleDateString("ar-SA")]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(c.$,{variant:"outline",size:"sm",className:"flex-1",children:[(0,t.jsx)(A.A,{className:"h-4 w-4 mr-2"}),(0,t.jsx)("span",{className:"arabic-text",children:"عرض"})]}),(0,t.jsxs)(c.$,{variant:"outline",size:"sm",className:"flex-1",children:[(0,t.jsx)(q.A,{className:"h-4 w-4 mr-2"}),(0,t.jsx)("span",{className:"arabic-text",children:"تعديل"})]}),(0,t.jsx)(c.$,{variant:"outline",size:"sm",children:(0,t.jsx)(D.A,{className:"h-4 w-4"})}),(0,t.jsx)(c.$,{variant:"outline",size:"sm",children:(0,t.jsx)(z.A,{className:"h-4 w-4"})})]})]})]})},e.id))})]}),(0,t.jsx)(m.av,{value:"profile",className:"space-y-6 mt-6",children:(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsx)(n.ZB,{className:"arabic-text",children:"الملف الشخصي"}),(0,t.jsx)(n.BT,{className:"arabic-text",children:"إدارة معلوماتك الشخصية وإعدادات الحساب"})]}),(0,t.jsx)(n.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium arabic-text",children:"الاسم الكامل"}),(0,t.jsx)("p",{className:"mt-1 p-2 bg-gray-50 dark:bg-gray-800 rounded arabic-text",children:s?.full_name||"غير محدد"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium arabic-text",children:"البريد الإلكتروني"}),(0,t.jsx)("p",{className:"mt-1 p-2 bg-gray-50 dark:bg-gray-800 rounded",children:s?.email||"غير محدد"})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium arabic-text",children:"رقم الهاتف"}),(0,t.jsx)("p",{className:"mt-1 p-2 bg-gray-50 dark:bg-gray-800 rounded arabic-text",children:s?.phone||"غير محدد"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium arabic-text",children:"اسم المدرسة"}),(0,t.jsx)("p",{className:"mt-1 p-2 bg-gray-50 dark:bg-gray-800 rounded arabic-text",children:s?.school_name||"غير محدد"})]})]}),(0,t.jsx)("div",{className:"pt-4",children:(0,t.jsxs)(c.$,{children:[(0,t.jsx)(q.A,{className:"h-4 w-4 mr-2"}),(0,t.jsx)("span",{className:"arabic-text",children:"تعديل الملف الشخصي"})]})})]})})]})})]})]})]})}},96474:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},97992:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])}};var s=require("../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[4447,8773,6126,3932,7801],()=>a(71110));module.exports=t})();