(()=>{var e={};e.id=7623,e.ids=[7623],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5406:(e,t,n)=>{"use strict";n.r(t),n.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>h,tree:()=>d});var r=n(65239),a=n(48088),l=n(88170),i=n.n(l),s=n(30893),o={};for(let e in s)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>s[e]);n.d(t,o);let d={children:["",{children:["dashboard",{children:["admin",{children:["menu-management",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(n.bind(n,33473)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\admin\\menu-management\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(n.bind(n,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(n.bind(n,94431)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(n.bind(n,54431)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(n.bind(n,54413)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(n.t.bind(n,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(n.t.bind(n,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(n.bind(n,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\admin\\menu-management\\page.tsx"],u={require:n,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/admin/menu-management/page",pathname:"/dashboard/admin/menu-management",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12941:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(62688).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},15079:(e,t,n)=>{"use strict";n.d(t,{bq:()=>u,eb:()=>f,gC:()=>h,l6:()=>d,yv:()=>c});var r=n(60687);n(43210);var a=n(22670),l=n(78272),i=n(13964),s=n(3589),o=n(4780);function d({...e}){return(0,r.jsx)(a.bL,{"data-slot":"select",...e})}function c({...e}){return(0,r.jsx)(a.WT,{"data-slot":"select-value",...e})}function u({className:e,size:t="default",children:n,...i}){return(0,r.jsxs)(a.l9,{"data-slot":"select-trigger","data-size":t,className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...i,children:[n,(0,r.jsx)(a.In,{asChild:!0,children:(0,r.jsx)(l.A,{className:"size-4 opacity-50"})})]})}function h({className:e,children:t,position:n="popper",...l}){return(0,r.jsx)(a.ZL,{children:(0,r.jsxs)(a.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:n,...l,children:[(0,r.jsx)(g,{}),(0,r.jsx)(a.LM,{className:(0,o.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,r.jsx)(p,{})]})})}function f({className:e,children:t,...n}){return(0,r.jsxs)(a.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...n,children:[(0,r.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,r.jsx)(a.VF,{children:(0,r.jsx)(i.A,{className:"size-4"})})}),(0,r.jsx)(a.p4,{children:t})]})}function g({className:e,...t}){return(0,r.jsx)(a.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,r.jsx)(s.A,{className:"size-4"})})}function p({className:e,...t}){return(0,r.jsx)(a.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,r.jsx)(l.A,{className:"size-4"})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22099:(e,t,n)=>{Promise.resolve().then(n.bind(n,33473))},26134:(e,t,n)=>{"use strict";n.d(t,{G$:()=>Y,Hs:()=>w,UC:()=>en,VY:()=>ea,ZL:()=>ee,bL:()=>Z,bm:()=>el,hE:()=>er,hJ:()=>et,l9:()=>Q});var r=n(43210),a=n(70569),l=n(98599),i=n(11273),s=n(96963),o=n(65551),d=n(31355),c=n(32547),u=n(25028),h=n(46059),f=n(14163),g=n(1359),p=n(42247),m=n(63376),x=n(8730),v=n(60687),b="Dialog",[y,w]=(0,i.A)(b),[j,C]=y(b),N=e=>{let{__scopeDialog:t,children:n,open:a,defaultOpen:l,onOpenChange:i,modal:d=!0}=e,c=r.useRef(null),u=r.useRef(null),[h,f]=(0,o.i)({prop:a,defaultProp:l??!1,onChange:i,caller:b});return(0,v.jsx)(j,{scope:t,triggerRef:c,contentRef:u,contentId:(0,s.B)(),titleId:(0,s.B)(),descriptionId:(0,s.B)(),open:h,onOpenChange:f,onOpenToggle:r.useCallback(()=>f(e=>!e),[f]),modal:d,children:n})};N.displayName=b;var D="DialogTrigger",E=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=C(D,n),s=(0,l.s)(t,i.triggerRef);return(0,v.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":J(i.open),...r,ref:s,onClick:(0,a.m)(e.onClick,i.onOpenToggle)})});E.displayName=D;var k="DialogPortal",[_,R]=y(k,{forceMount:void 0}),M=e=>{let{__scopeDialog:t,forceMount:n,children:a,container:l}=e,i=C(k,t);return(0,v.jsx)(_,{scope:t,forceMount:n,children:r.Children.map(a,e=>(0,v.jsx)(h.C,{present:n||i.open,children:(0,v.jsx)(u.Z,{asChild:!0,container:l,children:e})}))})};M.displayName=k;var S="DialogOverlay",A=r.forwardRef((e,t)=>{let n=R(S,e.__scopeDialog),{forceMount:r=n.forceMount,...a}=e,l=C(S,e.__scopeDialog);return l.modal?(0,v.jsx)(h.C,{present:r||l.open,children:(0,v.jsx)(O,{...a,ref:t})}):null});A.displayName=S;var T=(0,x.TL)("DialogOverlay.RemoveScroll"),O=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=C(S,n);return(0,v.jsx)(p.A,{as:T,allowPinchZoom:!0,shards:[a.contentRef],children:(0,v.jsx)(f.sG.div,{"data-state":J(a.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),I="DialogContent",L=r.forwardRef((e,t)=>{let n=R(I,e.__scopeDialog),{forceMount:r=n.forceMount,...a}=e,l=C(I,e.__scopeDialog);return(0,v.jsx)(h.C,{present:r||l.open,children:l.modal?(0,v.jsx)(P,{...a,ref:t}):(0,v.jsx)(z,{...a,ref:t})})});L.displayName=I;var P=r.forwardRef((e,t)=>{let n=C(I,e.__scopeDialog),i=r.useRef(null),s=(0,l.s)(t,n.contentRef,i);return r.useEffect(()=>{let e=i.current;if(e)return(0,m.Eq)(e)},[]),(0,v.jsx)(F,{...e,ref:s,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,a.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),n.triggerRef.current?.focus()}),onPointerDownOutside:(0,a.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,a.m)(e.onFocusOutside,e=>e.preventDefault())})}),z=r.forwardRef((e,t)=>{let n=C(I,e.__scopeDialog),a=r.useRef(!1),l=r.useRef(!1);return(0,v.jsx)(F,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(a.current||n.triggerRef.current?.focus(),t.preventDefault()),a.current=!1,l.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(a.current=!0,"pointerdown"===t.detail.originalEvent.type&&(l.current=!0));let r=t.target;n.triggerRef.current?.contains(r)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&l.current&&t.preventDefault()}})}),F=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:a,onOpenAutoFocus:i,onCloseAutoFocus:s,...o}=e,u=C(I,n),h=r.useRef(null),f=(0,l.s)(t,h);return(0,g.Oh)(),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(c.n,{asChild:!0,loop:!0,trapped:a,onMountAutoFocus:i,onUnmountAutoFocus:s,children:(0,v.jsx)(d.qW,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":J(u.open),...o,ref:f,onDismiss:()=>u.onOpenChange(!1)})}),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(V,{titleId:u.titleId}),(0,v.jsx)(H,{contentRef:h,descriptionId:u.descriptionId})]})]})}),B="DialogTitle",U=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=C(B,n);return(0,v.jsx)(f.sG.h2,{id:a.titleId,...r,ref:t})});U.displayName=B;var q="DialogDescription",G=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=C(q,n);return(0,v.jsx)(f.sG.p,{id:a.descriptionId,...r,ref:t})});G.displayName=q;var W="DialogClose",X=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,l=C(W,n);return(0,v.jsx)(f.sG.button,{type:"button",...r,ref:t,onClick:(0,a.m)(e.onClick,()=>l.onOpenChange(!1))})});function J(e){return e?"open":"closed"}X.displayName=W;var $="DialogTitleWarning",[Y,K]=(0,i.q)($,{contentName:I,titleName:B,docsSlug:"dialog"}),V=({titleId:e})=>{let t=K($),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return r.useEffect(()=>{e&&(document.getElementById(e)||console.error(n))},[n,e]),null},H=({contentRef:e,descriptionId:t})=>{let n=K("DialogDescriptionWarning"),a=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${n.contentName}}.`;return r.useEffect(()=>{let n=e.current?.getAttribute("aria-describedby");t&&n&&(document.getElementById(t)||console.warn(a))},[a,e,t]),null},Z=N,Q=E,ee=M,et=A,en=L,er=U,ea=G,el=X},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33473:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>r});let r=(0,n(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Graduation Toqs\\\\frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\menu-management\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\admin\\menu-management\\page.tsx","default")},33873:e=>{"use strict";e.exports=require("path")},54987:(e,t,n)=>{"use strict";n.d(t,{d:()=>i});var r=n(60687);n(43210);var a=n(90270),l=n(4780);function i({className:e,...t}){return(0,r.jsx)(a.bL,{"data-slot":"switch",className:(0,l.cn)("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:(0,r.jsx)(a.zi,{"data-slot":"switch-thumb",className:(0,l.cn)("bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0")})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63503:(e,t,n)=>{"use strict";n.d(t,{Cf:()=>u,Es:()=>f,L3:()=>g,c7:()=>h,lG:()=>s,rr:()=>p,zM:()=>o});var r=n(60687);n(43210);var a=n(26134),l=n(11860),i=n(4780);function s({...e}){return(0,r.jsx)(a.bL,{"data-slot":"dialog",...e})}function o({...e}){return(0,r.jsx)(a.l9,{"data-slot":"dialog-trigger",...e})}function d({...e}){return(0,r.jsx)(a.ZL,{"data-slot":"dialog-portal",...e})}function c({className:e,...t}){return(0,r.jsx)(a.hJ,{"data-slot":"dialog-overlay",className:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function u({className:e,children:t,showCloseButton:n=!0,...s}){return(0,r.jsxs)(d,{"data-slot":"dialog-portal",children:[(0,r.jsx)(c,{}),(0,r.jsxs)(a.UC,{"data-slot":"dialog-content",className:(0,i.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...s,children:[t,n&&(0,r.jsxs)(a.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,r.jsx)(l.A,{}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function h({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"dialog-header",className:(0,i.cn)("flex flex-col gap-2 text-center sm:text-left",e),...t})}function f({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"dialog-footer",className:(0,i.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...t})}function g({className:e,...t}){return(0,r.jsx)(a.hE,{"data-slot":"dialog-title",className:(0,i.cn)("text-lg leading-none font-semibold",e),...t})}function p({className:e,...t}){return(0,r.jsx)(a.VY,{"data-slot":"dialog-description",className:(0,i.cn)("text-muted-foreground text-sm",e),...t})}},79551:e=>{"use strict";e.exports=require("url")},80013:(e,t,n)=>{"use strict";n.d(t,{J:()=>i});var r=n(60687);n(43210);var a=n(78148),l=n(4780);function i({className:e,...t}){return(0,r.jsx)(a.b,{"data-slot":"label",className:(0,l.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},82347:(e,t,n)=>{Promise.resolve().then(n.bind(n,99122))},85763:(e,t,n)=>{"use strict";n.d(t,{Xi:()=>o,av:()=>d,j7:()=>s,tU:()=>i});var r=n(60687);n(43210);var a=n(55146),l=n(4780);function i({className:e,...t}){return(0,r.jsx)(a.bL,{"data-slot":"tabs",className:(0,l.cn)("flex flex-col gap-2",e),...t})}function s({className:e,...t}){return(0,r.jsx)(a.B8,{"data-slot":"tabs-list",className:(0,l.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",e),...t})}function o({className:e,...t}){return(0,r.jsx)(a.l9,{"data-slot":"tabs-trigger",className:(0,l.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...t})}function d({className:e,...t}){return(0,r.jsx)(a.UC,{"data-slot":"tabs-content",className:(0,l.cn)("flex-1 outline-none",e),...t})}},99122:(e,t,n)=>{"use strict";let r;n.r(t),n.d(t,{default:()=>tW});var a,l,i,s,o,d,c,u,h,f,g=n(60687),p=n(43210),m=n.n(p),x=n(63213),v=n(86748),b=n(87801),y=n(29523),w=n(44493),j=n(96834),C=n(89667),N=n(80013),D=n(15079),E=n(54987),k=n(63503),_=n(85763),R=n(81381),M=n(12597),S=n(13861),A=n(63143),T=n(88233),O=n(47342),I=n(25334),L=n(10022),P=n(28559),z=n(96474),F=n(12941),B=n(52581),U=n(51215);let q="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement;function G(e){let t=Object.prototype.toString.call(e);return"[object Window]"===t||"[object global]"===t}function W(e){return"nodeType"in e}function X(e){var t,n;return e?G(e)?e:W(e)&&null!=(t=null==(n=e.ownerDocument)?void 0:n.defaultView)?t:window:window}function J(e){let{Document:t}=X(e);return e instanceof t}function $(e){return!G(e)&&e instanceof X(e).HTMLElement}function Y(e){return e instanceof X(e).SVGElement}function K(e){return e?G(e)?e.document:W(e)?J(e)?e:$(e)||Y(e)?e.ownerDocument:document:document:document}let V=q?p.useLayoutEffect:p.useEffect;function H(e){let t=(0,p.useRef)(e);return V(()=>{t.current=e}),(0,p.useCallback)(function(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];return null==t.current?void 0:t.current(...n)},[])}function Z(e,t){void 0===t&&(t=[e]);let n=(0,p.useRef)(e);return V(()=>{n.current!==e&&(n.current=e)},t),n}function Q(e,t){let n=(0,p.useRef)();return(0,p.useMemo)(()=>{let t=e(n.current);return n.current=t,t},[...t])}function ee(e){let t=H(e),n=(0,p.useRef)(null),r=(0,p.useCallback)(e=>{e!==n.current&&(null==t||t(e,n.current)),n.current=e},[]);return[n,r]}function et(e){let t=(0,p.useRef)();return(0,p.useEffect)(()=>{t.current=e},[e]),t.current}let en={};function er(e,t){return(0,p.useMemo)(()=>{if(t)return t;let n=null==en[e]?0:en[e]+1;return en[e]=n,e+"-"+n},[e,t])}function ea(e){return function(t){for(var n=arguments.length,r=Array(n>1?n-1:0),a=1;a<n;a++)r[a-1]=arguments[a];return r.reduce((t,n)=>{for(let[r,a]of Object.entries(n)){let n=t[r];null!=n&&(t[r]=n+e*a)}return t},{...t})}}let el=ea(1),ei=ea(-1);function es(e){if(!e)return!1;let{KeyboardEvent:t}=X(e.target);return t&&e instanceof t}function eo(e){if(function(e){if(!e)return!1;let{TouchEvent:t}=X(e.target);return t&&e instanceof t}(e)){if(e.touches&&e.touches.length){let{clientX:t,clientY:n}=e.touches[0];return{x:t,y:n}}else if(e.changedTouches&&e.changedTouches.length){let{clientX:t,clientY:n}=e.changedTouches[0];return{x:t,y:n}}}return"clientX"in e&&"clientY"in e?{x:e.clientX,y:e.clientY}:null}let ed=Object.freeze({Translate:{toString(e){if(!e)return;let{x:t,y:n}=e;return"translate3d("+(t?Math.round(t):0)+"px, "+(n?Math.round(n):0)+"px, 0)"}},Scale:{toString(e){if(!e)return;let{scaleX:t,scaleY:n}=e;return"scaleX("+t+") scaleY("+n+")"}},Transform:{toString(e){if(e)return[ed.Translate.toString(e),ed.Scale.toString(e)].join(" ")}},Transition:{toString(e){let{property:t,duration:n,easing:r}=e;return t+" "+n+"ms "+r}}}),ec="a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]",eu={display:"none"};function eh(e){let{id:t,value:n}=e;return m().createElement("div",{id:t,style:eu},n)}function ef(e){let{id:t,announcement:n,ariaLiveType:r="assertive"}=e;return m().createElement("div",{id:t,style:{position:"fixed",top:0,left:0,width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0 0 0 0)",clipPath:"inset(100%)",whiteSpace:"nowrap"},role:"status","aria-live":r,"aria-atomic":!0},n)}let eg=(0,p.createContext)(null),ep={draggable:"\n    To pick up a draggable item, press the space bar.\n    While dragging, use the arrow keys to move the item.\n    Press space again to drop the item in its new position, or press escape to cancel.\n  "},em={onDragStart(e){let{active:t}=e;return"Picked up draggable item "+t.id+"."},onDragOver(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was moved over droppable area "+n.id+".":"Draggable item "+t.id+" is no longer over a droppable area."},onDragEnd(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was dropped over droppable area "+n.id:"Draggable item "+t.id+" was dropped."},onDragCancel(e){let{active:t}=e;return"Dragging was cancelled. Draggable item "+t.id+" was dropped."}};function ex(e){let{announcements:t=em,container:n,hiddenTextDescribedById:r,screenReaderInstructions:a=ep}=e,{announce:l,announcement:i}=function(){let[e,t]=(0,p.useState)("");return{announce:(0,p.useCallback)(e=>{null!=e&&t(e)},[]),announcement:e}}(),s=er("DndLiveRegion"),[o,d]=(0,p.useState)(!1);(0,p.useEffect)(()=>{d(!0)},[]);var c=(0,p.useMemo)(()=>({onDragStart(e){let{active:n}=e;l(t.onDragStart({active:n}))},onDragMove(e){let{active:n,over:r}=e;t.onDragMove&&l(t.onDragMove({active:n,over:r}))},onDragOver(e){let{active:n,over:r}=e;l(t.onDragOver({active:n,over:r}))},onDragEnd(e){let{active:n,over:r}=e;l(t.onDragEnd({active:n,over:r}))},onDragCancel(e){let{active:n,over:r}=e;l(t.onDragCancel({active:n,over:r}))}}),[l,t]);let u=(0,p.useContext)(eg);if((0,p.useEffect)(()=>{if(!u)throw Error("useDndMonitor must be used within a children of <DndContext>");return u(c)},[c,u]),!o)return null;let h=m().createElement(m().Fragment,null,m().createElement(eh,{id:r,value:a.draggable}),m().createElement(ef,{id:s,announcement:i}));return n?(0,U.createPortal)(h,n):h}function ev(){}function eb(e,t){return(0,p.useMemo)(()=>({sensor:e,options:null!=t?t:{}}),[e,t])}!function(e){e.DragStart="dragStart",e.DragMove="dragMove",e.DragEnd="dragEnd",e.DragCancel="dragCancel",e.DragOver="dragOver",e.RegisterDroppable="registerDroppable",e.SetDroppableDisabled="setDroppableDisabled",e.UnregisterDroppable="unregisterDroppable"}(a||(a={}));let ey=Object.freeze({x:0,y:0});function ew(e,t){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}function ej(e,t){let{data:{value:n}}=e,{data:{value:r}}=t;return n-r}function eC(e,t){let{data:{value:n}}=e,{data:{value:r}}=t;return r-n}function eN(e){let{left:t,top:n,height:r,width:a}=e;return[{x:t,y:n},{x:t+a,y:n},{x:t,y:n+r},{x:t+a,y:n+r}]}function eD(e,t){if(!e||0===e.length)return null;let[n]=e;return t?n[t]:n}function eE(e,t,n){return void 0===t&&(t=e.left),void 0===n&&(n=e.top),{x:t+.5*e.width,y:n+.5*e.height}}let ek=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e,a=eE(t,t.left,t.top),l=[];for(let e of r){let{id:t}=e,r=n.get(t);if(r){let n=ew(eE(r),a);l.push({id:t,data:{droppableContainer:e,value:n}})}}return l.sort(ej)},e_=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e,a=eN(t),l=[];for(let e of r){let{id:t}=e,r=n.get(t);if(r){let n=eN(r),i=Number((a.reduce((e,t,r)=>e+ew(n[r],t),0)/4).toFixed(4));l.push({id:t,data:{droppableContainer:e,value:i}})}}return l.sort(ej)},eR=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e,a=[];for(let e of r){let{id:r}=e,l=n.get(r);if(l){let n=function(e,t){let n=Math.max(t.top,e.top),r=Math.max(t.left,e.left),a=Math.min(t.left+t.width,e.left+e.width),l=Math.min(t.top+t.height,e.top+e.height);if(r<a&&n<l){let i=t.width*t.height,s=e.width*e.height,o=(a-r)*(l-n);return Number((o/(i+s-o)).toFixed(4))}return 0}(l,t);n>0&&a.push({id:r,data:{droppableContainer:e,value:n}})}}return a.sort(eC)};function eM(e,t){return e&&t?{x:e.left-t.left,y:e.top-t.top}:ey}let eS=function(e){return function(t){for(var n=arguments.length,r=Array(n>1?n-1:0),a=1;a<n;a++)r[a-1]=arguments[a];return r.reduce((t,n)=>({...t,top:t.top+e*n.y,bottom:t.bottom+e*n.y,left:t.left+e*n.x,right:t.right+e*n.x}),{...t})}}(1),eA={ignoreTransform:!1};function eT(e,t){void 0===t&&(t=eA);let n=e.getBoundingClientRect();if(t.ignoreTransform){let{transform:t,transformOrigin:r}=X(e).getComputedStyle(e);t&&(n=function(e,t,n){let r=function(e){if(e.startsWith("matrix3d(")){let t=e.slice(9,-1).split(/, /);return{x:+t[12],y:+t[13],scaleX:+t[0],scaleY:+t[5]}}if(e.startsWith("matrix(")){let t=e.slice(7,-1).split(/, /);return{x:+t[4],y:+t[5],scaleX:+t[0],scaleY:+t[3]}}return null}(t);if(!r)return e;let{scaleX:a,scaleY:l,x:i,y:s}=r,o=e.left-i-(1-a)*parseFloat(n),d=e.top-s-(1-l)*parseFloat(n.slice(n.indexOf(" ")+1)),c=a?e.width/a:e.width,u=l?e.height/l:e.height;return{width:c,height:u,top:d,right:o+c,bottom:d+u,left:o}}(n,t,r))}let{top:r,left:a,width:l,height:i,bottom:s,right:o}=n;return{top:r,left:a,width:l,height:i,bottom:s,right:o}}function eO(e){return eT(e,{ignoreTransform:!0})}function eI(e,t){let n=[];return e?function r(a){var l;if(null!=t&&n.length>=t||!a)return n;if(J(a)&&null!=a.scrollingElement&&!n.includes(a.scrollingElement))return n.push(a.scrollingElement),n;if(!$(a)||Y(a)||n.includes(a))return n;let i=X(e).getComputedStyle(a);return(a!==e&&function(e,t){void 0===t&&(t=X(e).getComputedStyle(e));let n=/(auto|scroll|overlay)/;return["overflow","overflowX","overflowY"].some(e=>{let r=t[e];return"string"==typeof r&&n.test(r)})}(a,i)&&n.push(a),void 0===(l=i)&&(l=X(a).getComputedStyle(a)),"fixed"===l.position)?n:r(a.parentNode)}(e):n}function eL(e){let[t]=eI(e,1);return null!=t?t:null}function eP(e){return q&&e?G(e)?e:W(e)?J(e)||e===K(e).scrollingElement?window:$(e)?e:null:null:null}function ez(e){return G(e)?e.scrollX:e.scrollLeft}function eF(e){return G(e)?e.scrollY:e.scrollTop}function eB(e){return{x:ez(e),y:eF(e)}}function eU(e){return!!q&&!!e&&e===document.scrollingElement}function eq(e){let t={x:0,y:0},n=eU(e)?{height:window.innerHeight,width:window.innerWidth}:{height:e.clientHeight,width:e.clientWidth},r={x:e.scrollWidth-n.width,y:e.scrollHeight-n.height},a=e.scrollTop<=t.y,l=e.scrollLeft<=t.x;return{isTop:a,isLeft:l,isBottom:e.scrollTop>=r.y,isRight:e.scrollLeft>=r.x,maxScroll:r,minScroll:t}}!function(e){e[e.Forward=1]="Forward",e[e.Backward=-1]="Backward"}(l||(l={}));let eG={x:.2,y:.2};function eW(e){return e.reduce((e,t)=>el(e,eB(t)),ey)}let eX=[["x",["left","right"],function(e){return e.reduce((e,t)=>e+ez(t),0)}],["y",["top","bottom"],function(e){return e.reduce((e,t)=>e+eF(t),0)}]];class eJ{constructor(e,t){this.rect=void 0,this.width=void 0,this.height=void 0,this.top=void 0,this.bottom=void 0,this.right=void 0,this.left=void 0;let n=eI(t),r=eW(n);for(let[t,a,l]of(this.rect={...e},this.width=e.width,this.height=e.height,eX))for(let e of a)Object.defineProperty(this,e,{get:()=>{let a=l(n),i=r[t]-a;return this.rect[e]+i},enumerable:!0});Object.defineProperty(this,"rect",{enumerable:!1})}}class e${constructor(e){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach(e=>{var t;return null==(t=this.target)?void 0:t.removeEventListener(...e)})},this.target=e}add(e,t,n){var r;null==(r=this.target)||r.addEventListener(e,t,n),this.listeners.push([e,t,n])}}function eY(e,t){let n=Math.abs(e.x),r=Math.abs(e.y);return"number"==typeof t?Math.sqrt(n**2+r**2)>t:"x"in t&&"y"in t?n>t.x&&r>t.y:"x"in t?n>t.x:"y"in t&&r>t.y}function eK(e){e.preventDefault()}function eV(e){e.stopPropagation()}!function(e){e.Click="click",e.DragStart="dragstart",e.Keydown="keydown",e.ContextMenu="contextmenu",e.Resize="resize",e.SelectionChange="selectionchange",e.VisibilityChange="visibilitychange"}(i||(i={})),function(e){e.Space="Space",e.Down="ArrowDown",e.Right="ArrowRight",e.Left="ArrowLeft",e.Up="ArrowUp",e.Esc="Escape",e.Enter="Enter",e.Tab="Tab"}(s||(s={}));let eH={start:[s.Space,s.Enter],cancel:[s.Esc],end:[s.Space,s.Enter,s.Tab]},eZ=(e,t)=>{let{currentCoordinates:n}=t;switch(e.code){case s.Right:return{...n,x:n.x+25};case s.Left:return{...n,x:n.x-25};case s.Down:return{...n,y:n.y+25};case s.Up:return{...n,y:n.y-25}}};class eQ{constructor(e){this.props=void 0,this.autoScrollEnabled=!1,this.referenceCoordinates=void 0,this.listeners=void 0,this.windowListeners=void 0,this.props=e;let{event:{target:t}}=e;this.props=e,this.listeners=new e$(K(t)),this.windowListeners=new e$(X(t)),this.handleKeyDown=this.handleKeyDown.bind(this),this.handleCancel=this.handleCancel.bind(this),this.attach()}attach(){this.handleStart(),this.windowListeners.add(i.Resize,this.handleCancel),this.windowListeners.add(i.VisibilityChange,this.handleCancel),setTimeout(()=>this.listeners.add(i.Keydown,this.handleKeyDown))}handleStart(){let{activeNode:e,onStart:t}=this.props,n=e.node.current;n&&function(e,t){if(void 0===t&&(t=eT),!e)return;let{top:n,left:r,bottom:a,right:l}=t(e);eL(e)&&(a<=0||l<=0||n>=window.innerHeight||r>=window.innerWidth)&&e.scrollIntoView({block:"center",inline:"center"})}(n),t(ey)}handleKeyDown(e){if(es(e)){let{active:t,context:n,options:r}=this.props,{keyboardCodes:a=eH,coordinateGetter:l=eZ,scrollBehavior:i="smooth"}=r,{code:o}=e;if(a.end.includes(o))return void this.handleEnd(e);if(a.cancel.includes(o))return void this.handleCancel(e);let{collisionRect:d}=n.current,c=d?{x:d.left,y:d.top}:ey;this.referenceCoordinates||(this.referenceCoordinates=c);let u=l(e,{active:t,context:n.current,currentCoordinates:c});if(u){let t=ei(u,c),r={x:0,y:0},{scrollableAncestors:a}=n.current;for(let n of a){let a=e.code,{isTop:l,isRight:o,isLeft:d,isBottom:c,maxScroll:h,minScroll:f}=eq(n),g=function(e){if(e===document.scrollingElement){let{innerWidth:e,innerHeight:t}=window;return{top:0,left:0,right:e,bottom:t,width:e,height:t}}let{top:t,left:n,right:r,bottom:a}=e.getBoundingClientRect();return{top:t,left:n,right:r,bottom:a,width:e.clientWidth,height:e.clientHeight}}(n),p={x:Math.min(a===s.Right?g.right-g.width/2:g.right,Math.max(a===s.Right?g.left:g.left+g.width/2,u.x)),y:Math.min(a===s.Down?g.bottom-g.height/2:g.bottom,Math.max(a===s.Down?g.top:g.top+g.height/2,u.y))},m=a===s.Right&&!o||a===s.Left&&!d,x=a===s.Down&&!c||a===s.Up&&!l;if(m&&p.x!==u.x){let e=n.scrollLeft+t.x,l=a===s.Right&&e<=h.x||a===s.Left&&e>=f.x;if(l&&!t.y)return void n.scrollTo({left:e,behavior:i});l?r.x=n.scrollLeft-e:r.x=a===s.Right?n.scrollLeft-h.x:n.scrollLeft-f.x,r.x&&n.scrollBy({left:-r.x,behavior:i});break}if(x&&p.y!==u.y){let e=n.scrollTop+t.y,l=a===s.Down&&e<=h.y||a===s.Up&&e>=f.y;if(l&&!t.x)return void n.scrollTo({top:e,behavior:i});l?r.y=n.scrollTop-e:r.y=a===s.Down?n.scrollTop-h.y:n.scrollTop-f.y,r.y&&n.scrollBy({top:-r.y,behavior:i});break}}this.handleMove(e,el(ei(u,this.referenceCoordinates),r))}}}handleMove(e,t){let{onMove:n}=this.props;e.preventDefault(),n(t)}handleEnd(e){let{onEnd:t}=this.props;e.preventDefault(),this.detach(),t()}handleCancel(e){let{onCancel:t}=this.props;e.preventDefault(),this.detach(),t()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll()}}function e0(e){return!!(e&&"distance"in e)}function e1(e){return!!(e&&"delay"in e)}eQ.activators=[{eventName:"onKeyDown",handler:(e,t,n)=>{let{keyboardCodes:r=eH,onActivation:a}=t,{active:l}=n,{code:i}=e.nativeEvent;if(r.start.includes(i)){let t=l.activatorNode.current;return(!t||e.target===t)&&(e.preventDefault(),null==a||a({event:e.nativeEvent}),!0)}return!1}}];class e4{constructor(e,t,n){var r;void 0===n&&(n=function(e){let{EventTarget:t}=X(e);return e instanceof t?e:K(e)}(e.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=e,this.events=t;let{event:a}=e,{target:l}=a;this.props=e,this.events=t,this.document=K(l),this.documentListeners=new e$(this.document),this.listeners=new e$(n),this.windowListeners=new e$(X(l)),this.initialCoordinates=null!=(r=eo(a))?r:ey,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){let{events:e,props:{options:{activationConstraint:t,bypassActivationConstraint:n}}}=this;if(this.listeners.add(e.move.name,this.handleMove,{passive:!1}),this.listeners.add(e.end.name,this.handleEnd),e.cancel&&this.listeners.add(e.cancel.name,this.handleCancel),this.windowListeners.add(i.Resize,this.handleCancel),this.windowListeners.add(i.DragStart,eK),this.windowListeners.add(i.VisibilityChange,this.handleCancel),this.windowListeners.add(i.ContextMenu,eK),this.documentListeners.add(i.Keydown,this.handleKeydown),t){if(null!=n&&n({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(e1(t)){this.timeoutId=setTimeout(this.handleStart,t.delay),this.handlePending(t);return}if(e0(t))return void this.handlePending(t)}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),null!==this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handlePending(e,t){let{active:n,onPending:r}=this.props;r(n,e,this.initialCoordinates,t)}handleStart(){let{initialCoordinates:e}=this,{onStart:t}=this.props;e&&(this.activated=!0,this.documentListeners.add(i.Click,eV,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(i.SelectionChange,this.removeTextSelection),t(e))}handleMove(e){var t;let{activated:n,initialCoordinates:r,props:a}=this,{onMove:l,options:{activationConstraint:i}}=a;if(!r)return;let s=null!=(t=eo(e))?t:ey,o=ei(r,s);if(!n&&i){if(e0(i)){if(null!=i.tolerance&&eY(o,i.tolerance))return this.handleCancel();if(eY(o,i.distance))return this.handleStart()}return e1(i)&&eY(o,i.tolerance)?this.handleCancel():void this.handlePending(i,o)}e.cancelable&&e.preventDefault(),l(s)}handleEnd(){let{onAbort:e,onEnd:t}=this.props;this.detach(),this.activated||e(this.props.active),t()}handleCancel(){let{onAbort:e,onCancel:t}=this.props;this.detach(),this.activated||e(this.props.active),t()}handleKeydown(e){e.code===s.Esc&&this.handleCancel()}removeTextSelection(){var e;null==(e=this.document.getSelection())||e.removeAllRanges()}}let e2={cancel:{name:"pointercancel"},move:{name:"pointermove"},end:{name:"pointerup"}};class e3 extends e4{constructor(e){let{event:t}=e;super(e,e2,K(t.target))}}e3.activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return!!n.isPrimary&&0===n.button&&(null==r||r({event:n}),!0)}}];let e5={move:{name:"mousemove"},end:{name:"mouseup"}};!function(e){e[e.RightClick=2]="RightClick"}(o||(o={}));class e6 extends e4{constructor(e){super(e,e5,K(e.event.target))}}e6.activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return n.button!==o.RightClick&&(null==r||r({event:n}),!0)}}];let e8={cancel:{name:"touchcancel"},move:{name:"touchmove"},end:{name:"touchend"}};class e7 extends e4{constructor(e){super(e,e8)}static setup(){return window.addEventListener(e8.move.name,e,{capture:!1,passive:!1}),function(){window.removeEventListener(e8.move.name,e)};function e(){}}}e7.activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t,{touches:a}=n;return!(a.length>1)&&(null==r||r({event:n}),!0)}}],function(e){e[e.Pointer=0]="Pointer",e[e.DraggableRect=1]="DraggableRect"}(d||(d={})),function(e){e[e.TreeOrder=0]="TreeOrder",e[e.ReversedTreeOrder=1]="ReversedTreeOrder"}(c||(c={}));let e9={x:{[l.Backward]:!1,[l.Forward]:!1},y:{[l.Backward]:!1,[l.Forward]:!1}};!function(e){e[e.Always=0]="Always",e[e.BeforeDragging=1]="BeforeDragging",e[e.WhileDragging=2]="WhileDragging"}(u||(u={})),(h||(h={})).Optimized="optimized";let te=new Map;function tt(e,t){return Q(n=>e?n||("function"==typeof t?t(e):e):null,[t,e])}function tn(e){let{callback:t,disabled:n}=e,r=H(t),a=(0,p.useMemo)(()=>{if(n||"undefined"==typeof window||void 0===window.ResizeObserver)return;let{ResizeObserver:e}=window;return new e(r)},[n]);return(0,p.useEffect)(()=>()=>null==a?void 0:a.disconnect(),[a]),a}function tr(e){return new eJ(eT(e),e)}function ta(e,t,n){void 0===t&&(t=tr);let[r,a]=(0,p.useState)(null);function l(){a(r=>{if(!e)return null;if(!1===e.isConnected){var a;return null!=(a=null!=r?r:n)?a:null}let l=t(e);return JSON.stringify(r)===JSON.stringify(l)?r:l})}let i=function(e){let{callback:t,disabled:n}=e,r=H(t),a=(0,p.useMemo)(()=>{if(n||"undefined"==typeof window||void 0===window.MutationObserver)return;let{MutationObserver:e}=window;return new e(r)},[r,n]);return(0,p.useEffect)(()=>()=>null==a?void 0:a.disconnect(),[a]),a}({callback(t){if(e)for(let n of t){let{type:t,target:r}=n;if("childList"===t&&r instanceof HTMLElement&&r.contains(e)){l();break}}}}),s=tn({callback:l});return V(()=>{l(),e?(null==s||s.observe(e),null==i||i.observe(document.body,{childList:!0,subtree:!0})):(null==s||s.disconnect(),null==i||i.disconnect())},[e]),r}let tl=[];function ti(e,t){void 0===t&&(t=[]);let n=(0,p.useRef)(null);return(0,p.useEffect)(()=>{n.current=null},t),(0,p.useEffect)(()=>{let t=e!==ey;t&&!n.current&&(n.current=e),!t&&n.current&&(n.current=null)},[e]),n.current?ei(e,n.current):ey}function ts(e){return(0,p.useMemo)(()=>e?function(e){let t=e.innerWidth,n=e.innerHeight;return{top:0,left:0,right:t,bottom:n,width:t,height:n}}(e):null,[e])}let to=[],td=[{sensor:e3,options:{}},{sensor:eQ,options:{}}],tc={current:{}},tu={draggable:{measure:eO},droppable:{measure:eO,strategy:u.WhileDragging,frequency:h.Optimized},dragOverlay:{measure:eT}};class th extends Map{get(e){var t;return null!=e&&null!=(t=super.get(e))?t:void 0}toArray(){return Array.from(this.values())}getEnabled(){return this.toArray().filter(e=>{let{disabled:t}=e;return!t})}getNodeFor(e){var t,n;return null!=(t=null==(n=this.get(e))?void 0:n.node.current)?t:void 0}}let tf={activatorEvent:null,active:null,activeNode:null,activeNodeRect:null,collisions:null,containerNodeRect:null,draggableNodes:new Map,droppableRects:new Map,droppableContainers:new th,over:null,dragOverlay:{nodeRef:{current:null},rect:null,setRef:ev},scrollableAncestors:[],scrollableAncestorRects:[],measuringConfiguration:tu,measureDroppableContainers:ev,windowRect:null,measuringScheduled:!1},tg={activatorEvent:null,activators:[],active:null,activeNodeRect:null,ariaDescribedById:{draggable:""},dispatch:ev,draggableNodes:new Map,over:null,measureDroppableContainers:ev},tp=(0,p.createContext)(tg),tm=(0,p.createContext)(tf);function tx(){return{draggable:{active:null,initialCoordinates:{x:0,y:0},nodes:new Map,translate:{x:0,y:0}},droppable:{containers:new th}}}function tv(e,t){switch(t.type){case a.DragStart:return{...e,draggable:{...e.draggable,initialCoordinates:t.initialCoordinates,active:t.active}};case a.DragMove:if(null==e.draggable.active)return e;return{...e,draggable:{...e.draggable,translate:{x:t.coordinates.x-e.draggable.initialCoordinates.x,y:t.coordinates.y-e.draggable.initialCoordinates.y}}};case a.DragEnd:case a.DragCancel:return{...e,draggable:{...e.draggable,active:null,initialCoordinates:{x:0,y:0},translate:{x:0,y:0}}};case a.RegisterDroppable:{let{element:n}=t,{id:r}=n,a=new th(e.droppable.containers);return a.set(r,n),{...e,droppable:{...e.droppable,containers:a}}}case a.SetDroppableDisabled:{let{id:n,key:r,disabled:a}=t,l=e.droppable.containers.get(n);if(!l||r!==l.key)return e;let i=new th(e.droppable.containers);return i.set(n,{...l,disabled:a}),{...e,droppable:{...e.droppable,containers:i}}}case a.UnregisterDroppable:{let{id:n,key:r}=t,a=e.droppable.containers.get(n);if(!a||r!==a.key)return e;let l=new th(e.droppable.containers);return l.delete(n),{...e,droppable:{...e.droppable,containers:l}}}default:return e}}function tb(e){let{disabled:t}=e,{active:n,activatorEvent:r,draggableNodes:a}=(0,p.useContext)(tp),l=et(r),i=et(null==n?void 0:n.id);return(0,p.useEffect)(()=>{if(!t&&!r&&l&&null!=i){if(!es(l)||document.activeElement===l.target)return;let e=a.get(i);if(!e)return;let{activatorNode:t,node:n}=e;(t.current||n.current)&&requestAnimationFrame(()=>{for(let e of[t.current,n.current]){if(!e)continue;let t=e.matches(ec)?e:e.querySelector(ec);if(t){t.focus();break}}})}},[r,t,a,i,l]),null}let ty=(0,p.createContext)({...ey,scaleX:1,scaleY:1});!function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Initializing=1]="Initializing",e[e.Initialized=2]="Initialized"}(f||(f={}));let tw=(0,p.memo)(function(e){var t,n,r,i,s,o;let{id:h,accessibility:g,autoScroll:x=!0,children:v,sensors:b=td,collisionDetection:y=eR,measuring:w,modifiers:j,...C}=e,[N,D]=(0,p.useReducer)(tv,void 0,tx),[E,k]=function(){let[e]=(0,p.useState)(()=>new Set),t=(0,p.useCallback)(t=>(e.add(t),()=>e.delete(t)),[e]);return[(0,p.useCallback)(t=>{let{type:n,event:r}=t;e.forEach(e=>{var t;return null==(t=e[n])?void 0:t.call(e,r)})},[e]),t]}(),[_,R]=(0,p.useState)(f.Uninitialized),M=_===f.Initialized,{draggable:{active:S,nodes:A,translate:T},droppable:{containers:O}}=N,I=null!=S?A.get(S):null,L=(0,p.useRef)({initial:null,translated:null}),P=(0,p.useMemo)(()=>{var e;return null!=S?{id:S,data:null!=(e=null==I?void 0:I.data)?e:tc,rect:L}:null},[S,I]),z=(0,p.useRef)(null),[F,B]=(0,p.useState)(null),[G,W]=(0,p.useState)(null),J=Z(C,Object.values(C)),Y=er("DndDescribedBy",h),K=(0,p.useMemo)(()=>O.getEnabled(),[O]),H=(0,p.useMemo)(()=>({draggable:{...tu.draggable,...null==w?void 0:w.draggable},droppable:{...tu.droppable,...null==w?void 0:w.droppable},dragOverlay:{...tu.dragOverlay,...null==w?void 0:w.dragOverlay}}),[null==w?void 0:w.draggable,null==w?void 0:w.droppable,null==w?void 0:w.dragOverlay]),{droppableRects:en,measureDroppableContainers:ea,measuringScheduled:ei}=function(e,t){let{dragging:n,dependencies:r,config:a}=t,[l,i]=(0,p.useState)(null),{frequency:s,measure:o,strategy:d}=a,c=(0,p.useRef)(e),h=function(){switch(d){case u.Always:return!1;case u.BeforeDragging:return n;default:return!n}}(),f=Z(h),g=(0,p.useCallback)(function(e){void 0===e&&(e=[]),f.current||i(t=>null===t?e:t.concat(e.filter(e=>!t.includes(e))))},[f]),m=(0,p.useRef)(null),x=Q(t=>{if(h&&!n)return te;if(!t||t===te||c.current!==e||null!=l){let t=new Map;for(let n of e){if(!n)continue;if(l&&l.length>0&&!l.includes(n.id)&&n.rect.current){t.set(n.id,n.rect.current);continue}let e=n.node.current,r=e?new eJ(o(e),e):null;n.rect.current=r,r&&t.set(n.id,r)}return t}return t},[e,l,n,h,o]);return(0,p.useEffect)(()=>{c.current=e},[e]),(0,p.useEffect)(()=>{h||g()},[n,h]),(0,p.useEffect)(()=>{l&&l.length>0&&i(null)},[JSON.stringify(l)]),(0,p.useEffect)(()=>{h||"number"!=typeof s||null!==m.current||(m.current=setTimeout(()=>{g(),m.current=null},s))},[s,h,g,...r]),{droppableRects:x,measureDroppableContainers:g,measuringScheduled:null!=l}}(K,{dragging:M,dependencies:[T.x,T.y],config:H.droppable}),es=function(e,t){let n=null!=t?e.get(t):void 0,r=n?n.node.current:null;return Q(e=>{var n;return null==t?null:null!=(n=null!=r?r:e)?n:null},[r,t])}(A,S),ed=(0,p.useMemo)(()=>G?eo(G):null,[G]),ec=function(){let e=(null==F?void 0:F.autoScrollEnabled)===!1,t="object"==typeof x?!1===x.enabled:!1===x,n=M&&!e&&!t;return"object"==typeof x?{...x,enabled:n}:{enabled:n}}(),eu=tt(es,H.draggable.measure);!function(e){let{activeNode:t,measure:n,initialRect:r,config:a=!0}=e,l=(0,p.useRef)(!1),{x:i,y:s}="boolean"==typeof a?{x:a,y:a}:a;V(()=>{if(!i&&!s||!t){l.current=!1;return}if(l.current||!r)return;let e=null==t?void 0:t.node.current;if(!e||!1===e.isConnected)return;let a=eM(n(e),r);if(i||(a.x=0),s||(a.y=0),l.current=!0,Math.abs(a.x)>0||Math.abs(a.y)>0){let t=eL(e);t&&t.scrollBy({top:a.y,left:a.x})}},[t,i,s,r,n])}({activeNode:null!=S?A.get(S):null,config:ec.layoutShiftCompensation,initialRect:eu,measure:H.draggable.measure});let eh=ta(es,H.draggable.measure,eu),ef=ta(es?es.parentElement:null),ep=(0,p.useRef)({activatorEvent:null,active:null,activeNode:es,collisionRect:null,collisions:null,droppableRects:en,draggableNodes:A,draggingNode:null,draggingNodeRect:null,droppableContainers:O,over:null,scrollableAncestors:[],scrollAdjustedTranslate:null}),em=O.getNodeFor(null==(t=ep.current.over)?void 0:t.id),ev=function(e){let{measure:t}=e,[n,r]=(0,p.useState)(null),a=tn({callback:(0,p.useCallback)(e=>{for(let{target:n}of e)if($(n)){r(e=>{let r=t(n);return e?{...e,width:r.width,height:r.height}:r});break}},[t])}),[l,i]=ee((0,p.useCallback)(e=>{let n=function(e){if(!e)return null;if(e.children.length>1)return e;let t=e.children[0];return $(t)?t:e}(e);null==a||a.disconnect(),n&&(null==a||a.observe(n)),r(n?t(n):null)},[t,a]));return(0,p.useMemo)(()=>({nodeRef:l,rect:n,setRef:i}),[n,l,i])}({measure:H.dragOverlay.measure}),eb=null!=(n=ev.nodeRef.current)?n:es,ew=M?null!=(r=ev.rect)?r:eh:null,ej=!!(ev.nodeRef.current&&ev.rect),eC=function(e){let t=tt(e);return eM(e,t)}(ej?null:eh),eN=ts(eb?X(eb):null),eE=function(e){let t=(0,p.useRef)(e),n=Q(n=>e?n&&n!==tl&&e&&t.current&&e.parentNode===t.current.parentNode?n:eI(e):tl,[e]);return(0,p.useEffect)(()=>{t.current=e},[e]),n}(M?null!=em?em:es:null),ek=function(e,t){void 0===t&&(t=eT);let[n]=e,r=ts(n?X(n):null),[a,l]=(0,p.useState)(to);function i(){l(()=>e.length?e.map(e=>eU(e)?r:new eJ(t(e),e)):to)}let s=tn({callback:i});return V(()=>{null==s||s.disconnect(),i(),e.forEach(e=>null==s?void 0:s.observe(e))},[e]),a}(eE),e_=function(e,t){let{transform:n,...r}=t;return null!=e&&e.length?e.reduce((e,t)=>t({transform:e,...r}),n):n}(j,{transform:{x:T.x-eC.x,y:T.y-eC.y,scaleX:1,scaleY:1},activatorEvent:G,active:P,activeNodeRect:eh,containerNodeRect:ef,draggingNodeRect:ew,over:ep.current.over,overlayNodeRect:ev.rect,scrollableAncestors:eE,scrollableAncestorRects:ek,windowRect:eN}),eA=ed?el(ed,T):null,eO=function(e){let[t,n]=(0,p.useState)(null),r=(0,p.useRef)(e),a=(0,p.useCallback)(e=>{let t=eP(e.target);t&&n(e=>e?(e.set(t,eB(t)),new Map(e)):null)},[]);return(0,p.useEffect)(()=>{let t=r.current;if(e!==t){l(t);let i=e.map(e=>{let t=eP(e);return t?(t.addEventListener("scroll",a,{passive:!0}),[t,eB(t)]):null}).filter(e=>null!=e);n(i.length?new Map(i):null),r.current=e}return()=>{l(e),l(t)};function l(e){e.forEach(e=>{let t=eP(e);null==t||t.removeEventListener("scroll",a)})}},[a,e]),(0,p.useMemo)(()=>e.length?t?Array.from(t.values()).reduce((e,t)=>el(e,t),ey):eW(e):ey,[e,t])}(eE),ez=ti(eO),eF=ti(eO,[eh]),eX=el(e_,ez),e$=ew?eS(ew,e_):null,eY=P&&e$?y({active:P,collisionRect:e$,droppableRects:en,droppableContainers:K,pointerCoordinates:eA}):null,eK=eD(eY,"id"),[eV,eH]=(0,p.useState)(null),eZ=(s=ej?e_:el(e_,eF),o=null!=(i=null==eV?void 0:eV.rect)?i:null,{...s,scaleX:o&&eh?o.width/eh.width:1,scaleY:o&&eh?o.height/eh.height:1}),eQ=(0,p.useRef)(null),e0=(0,p.useCallback)((e,t)=>{let{sensor:n,options:r}=t;if(null==z.current)return;let l=A.get(z.current);if(!l)return;let i=e.nativeEvent,s=new n({active:z.current,activeNode:l,event:i,options:r,context:ep,onAbort(e){if(!A.get(e))return;let{onDragAbort:t}=J.current,n={id:e};null==t||t(n),E({type:"onDragAbort",event:n})},onPending(e,t,n,r){if(!A.get(e))return;let{onDragPending:a}=J.current,l={id:e,constraint:t,initialCoordinates:n,offset:r};null==a||a(l),E({type:"onDragPending",event:l})},onStart(e){let t=z.current;if(null==t)return;let n=A.get(t);if(!n)return;let{onDragStart:r}=J.current,l={activatorEvent:i,active:{id:t,data:n.data,rect:L}};(0,U.unstable_batchedUpdates)(()=>{null==r||r(l),R(f.Initializing),D({type:a.DragStart,initialCoordinates:e,active:t}),E({type:"onDragStart",event:l}),B(eQ.current),W(i)})},onMove(e){D({type:a.DragMove,coordinates:e})},onEnd:o(a.DragEnd),onCancel:o(a.DragCancel)});function o(e){return async function(){let{active:t,collisions:n,over:r,scrollAdjustedTranslate:l}=ep.current,s=null;if(t&&l){let{cancelDrop:o}=J.current;s={activatorEvent:i,active:t,collisions:n,delta:l,over:r},e===a.DragEnd&&"function"==typeof o&&await Promise.resolve(o(s))&&(e=a.DragCancel)}z.current=null,(0,U.unstable_batchedUpdates)(()=>{D({type:e}),R(f.Uninitialized),eH(null),B(null),W(null),eQ.current=null;let t=e===a.DragEnd?"onDragEnd":"onDragCancel";if(s){let e=J.current[t];null==e||e(s),E({type:t,event:s})}})}}eQ.current=s},[A]),e1=(0,p.useCallback)((e,t)=>(n,r)=>{let a=n.nativeEvent,l=A.get(r);null!==z.current||!l||a.dndKit||a.defaultPrevented||!0===e(n,t.options,{active:l})&&(a.dndKit={capturedBy:t.sensor},z.current=r,e0(n,t))},[A,e0]),e4=(0,p.useMemo)(()=>b.reduce((e,t)=>{let{sensor:n}=t;return[...e,...n.activators.map(e=>({eventName:e.eventName,handler:e1(e.handler,t)}))]},[]),[b,e1]);(0,p.useEffect)(()=>{if(!q)return;let e=b.map(e=>{let{sensor:t}=e;return null==t.setup?void 0:t.setup()});return()=>{for(let t of e)null==t||t()}},b.map(e=>{let{sensor:t}=e;return t})),V(()=>{eh&&_===f.Initializing&&R(f.Initialized)},[eh,_]),(0,p.useEffect)(()=>{let{onDragMove:e}=J.current,{active:t,activatorEvent:n,collisions:r,over:a}=ep.current;if(!t||!n)return;let l={active:t,activatorEvent:n,collisions:r,delta:{x:eX.x,y:eX.y},over:a};(0,U.unstable_batchedUpdates)(()=>{null==e||e(l),E({type:"onDragMove",event:l})})},[eX.x,eX.y]),(0,p.useEffect)(()=>{let{active:e,activatorEvent:t,collisions:n,droppableContainers:r,scrollAdjustedTranslate:a}=ep.current;if(!e||null==z.current||!t||!a)return;let{onDragOver:l}=J.current,i=r.get(eK),s=i&&i.rect.current?{id:i.id,rect:i.rect.current,data:i.data,disabled:i.disabled}:null,o={active:e,activatorEvent:t,collisions:n,delta:{x:a.x,y:a.y},over:s};(0,U.unstable_batchedUpdates)(()=>{eH(s),null==l||l(o),E({type:"onDragOver",event:o})})},[eK]),V(()=>{ep.current={activatorEvent:G,active:P,activeNode:es,collisionRect:e$,collisions:eY,droppableRects:en,draggableNodes:A,draggingNode:eb,draggingNodeRect:ew,droppableContainers:O,over:eV,scrollableAncestors:eE,scrollAdjustedTranslate:eX},L.current={initial:ew,translated:e$}},[P,es,eY,e$,A,eb,ew,en,O,eV,eE,eX]),function(e){let{acceleration:t,activator:n=d.Pointer,canScroll:r,draggingRect:a,enabled:i,interval:s=5,order:o=c.TreeOrder,pointerCoordinates:u,scrollableAncestors:h,scrollableAncestorRects:f,delta:g,threshold:m}=e,x=function(e){let{delta:t,disabled:n}=e,r=et(t);return Q(e=>{if(n||!r||!e)return e9;let a={x:Math.sign(t.x-r.x),y:Math.sign(t.y-r.y)};return{x:{[l.Backward]:e.x[l.Backward]||-1===a.x,[l.Forward]:e.x[l.Forward]||1===a.x},y:{[l.Backward]:e.y[l.Backward]||-1===a.y,[l.Forward]:e.y[l.Forward]||1===a.y}}},[n,t,r])}({delta:g,disabled:!i}),[v,b]=function(){let e=(0,p.useRef)(null);return[(0,p.useCallback)((t,n)=>{e.current=setInterval(t,n)},[]),(0,p.useCallback)(()=>{null!==e.current&&(clearInterval(e.current),e.current=null)},[])]}(),y=(0,p.useRef)({x:0,y:0}),w=(0,p.useRef)({x:0,y:0}),j=(0,p.useMemo)(()=>{switch(n){case d.Pointer:return u?{top:u.y,bottom:u.y,left:u.x,right:u.x}:null;case d.DraggableRect:return a}},[n,a,u]),C=(0,p.useRef)(null),N=(0,p.useCallback)(()=>{let e=C.current;if(!e)return;let t=y.current.x*w.current.x,n=y.current.y*w.current.y;e.scrollBy(t,n)},[]),D=(0,p.useMemo)(()=>o===c.TreeOrder?[...h].reverse():h,[o,h]);(0,p.useEffect)(()=>{if(!i||!h.length||!j)return void b();for(let e of D){if((null==r?void 0:r(e))===!1)continue;let n=f[h.indexOf(e)];if(!n)continue;let{direction:a,speed:i}=function(e,t,n,r,a){let{top:i,left:s,right:o,bottom:d}=n;void 0===r&&(r=10),void 0===a&&(a=eG);let{isTop:c,isBottom:u,isLeft:h,isRight:f}=eq(e),g={x:0,y:0},p={x:0,y:0},m={height:t.height*a.y,width:t.width*a.x};return!c&&i<=t.top+m.height?(g.y=l.Backward,p.y=r*Math.abs((t.top+m.height-i)/m.height)):!u&&d>=t.bottom-m.height&&(g.y=l.Forward,p.y=r*Math.abs((t.bottom-m.height-d)/m.height)),!f&&o>=t.right-m.width?(g.x=l.Forward,p.x=r*Math.abs((t.right-m.width-o)/m.width)):!h&&s<=t.left+m.width&&(g.x=l.Backward,p.x=r*Math.abs((t.left+m.width-s)/m.width)),{direction:g,speed:p}}(e,n,j,t,m);for(let e of["x","y"])x[e][a[e]]||(i[e]=0,a[e]=0);if(i.x>0||i.y>0){b(),C.current=e,v(N,s),y.current=i,w.current=a;return}}y.current={x:0,y:0},w.current={x:0,y:0},b()},[t,N,r,b,i,s,JSON.stringify(j),JSON.stringify(x),v,h,D,f,JSON.stringify(m)])}({...ec,delta:T,draggingRect:e$,pointerCoordinates:eA,scrollableAncestors:eE,scrollableAncestorRects:ek});let e2=(0,p.useMemo)(()=>({active:P,activeNode:es,activeNodeRect:eh,activatorEvent:G,collisions:eY,containerNodeRect:ef,dragOverlay:ev,draggableNodes:A,droppableContainers:O,droppableRects:en,over:eV,measureDroppableContainers:ea,scrollableAncestors:eE,scrollableAncestorRects:ek,measuringConfiguration:H,measuringScheduled:ei,windowRect:eN}),[P,es,eh,G,eY,ef,ev,A,O,en,eV,ea,eE,ek,H,ei,eN]),e3=(0,p.useMemo)(()=>({activatorEvent:G,activators:e4,active:P,activeNodeRect:eh,ariaDescribedById:{draggable:Y},dispatch:D,draggableNodes:A,over:eV,measureDroppableContainers:ea}),[G,e4,P,eh,D,Y,A,eV,ea]);return m().createElement(eg.Provider,{value:k},m().createElement(tp.Provider,{value:e3},m().createElement(tm.Provider,{value:e2},m().createElement(ty.Provider,{value:eZ},v)),m().createElement(tb,{disabled:(null==g?void 0:g.restoreFocus)===!1})),m().createElement(ex,{...g,hiddenTextDescribedById:Y}))}),tj=(0,p.createContext)(null),tC="button",tN={timeout:25};r={styles:{active:{opacity:"0"}}},e=>{let{active:t,dragOverlay:n}=e,a={},{styles:l,className:i}=r;if(null!=l&&l.active)for(let[e,n]of Object.entries(l.active))void 0!==n&&(a[e]=t.node.style.getPropertyValue(e),t.node.style.setProperty(e,n));if(null!=l&&l.dragOverlay)for(let[e,t]of Object.entries(l.dragOverlay))void 0!==t&&n.node.style.setProperty(e,t);return null!=i&&i.active&&t.node.classList.add(i.active),null!=i&&i.dragOverlay&&n.node.classList.add(i.dragOverlay),function(){for(let[e,n]of Object.entries(a))t.node.style.setProperty(e,n);null!=i&&i.active&&t.node.classList.remove(i.active)}};function tD(e,t,n){let r=e.slice();return r.splice(n<0?r.length+n:n,0,r.splice(t,1)[0]),r}function tE(e){return null!==e&&e>=0}let tk=e=>{let{rects:t,activeIndex:n,overIndex:r,index:a}=e,l=tD(t,r,n),i=t[a],s=l[a];return s&&i?{x:s.left-i.left,y:s.top-i.top,scaleX:s.width/i.width,scaleY:s.height/i.height}:null},t_={scaleX:1,scaleY:1},tR=e=>{var t;let{activeIndex:n,activeNodeRect:r,index:a,rects:l,overIndex:i}=e,s=null!=(t=l[n])?t:r;if(!s)return null;if(a===n){let e=l[i];return e?{x:0,y:n<i?e.top+e.height-(s.top+s.height):e.top-s.top,...t_}:null}let o=function(e,t,n){let r=e[t],a=e[t-1],l=e[t+1];return r?n<t?a?r.top-(a.top+a.height):l?l.top-(r.top+r.height):0:l?l.top-(r.top+r.height):a?r.top-(a.top+a.height):0:0}(l,a,n);return a>n&&a<=i?{x:0,y:-s.height-o,...t_}:a<n&&a>=i?{x:0,y:s.height+o,...t_}:{x:0,y:0,...t_}},tM="Sortable",tS=m().createContext({activeIndex:-1,containerId:tM,disableTransforms:!1,items:[],overIndex:-1,useDragOverlay:!1,sortedRects:[],strategy:tk,disabled:{draggable:!1,droppable:!1}});function tA(e){let{children:t,id:n,items:r,strategy:a=tk,disabled:l=!1}=e,{active:i,dragOverlay:s,droppableRects:o,over:d,measureDroppableContainers:c}=(0,p.useContext)(tm),u=er(tM,n),h=null!==s.rect,f=(0,p.useMemo)(()=>r.map(e=>"object"==typeof e&&"id"in e?e.id:e),[r]),g=null!=i,x=i?f.indexOf(i.id):-1,v=d?f.indexOf(d.id):-1,b=(0,p.useRef)(f),y=!function(e,t){if(e===t)return!0;if(e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}(f,b.current),w=-1!==v&&-1===x||y,j="boolean"==typeof l?{draggable:l,droppable:l}:l;V(()=>{y&&g&&c(f)},[y,f,g,c]),(0,p.useEffect)(()=>{b.current=f},[f]);let C=(0,p.useMemo)(()=>({activeIndex:x,containerId:u,disabled:j,disableTransforms:w,items:f,overIndex:v,useDragOverlay:h,sortedRects:f.reduce((e,t,n)=>{let r=o.get(t);return r&&(e[n]=r),e},Array(f.length)),strategy:a}),[x,u,j.draggable,j.droppable,w,f,v,o,h,a]);return m().createElement(tS.Provider,{value:C},t)}let tT=e=>{let{id:t,items:n,activeIndex:r,overIndex:a}=e;return tD(n,r,a).indexOf(t)},tO=e=>{let{containerId:t,isSorting:n,wasDragging:r,index:a,items:l,newIndex:i,previousItems:s,previousContainerId:o,transition:d}=e;return!!d&&!!r&&(s===l||a!==i)&&(!!n||i!==a&&t===o)},tI={duration:200,easing:"ease"},tL="transform",tP=ed.Transition.toString({property:tL,duration:0,easing:"linear"}),tz={roleDescription:"sortable"};function tF(e){if(!e)return!1;let t=e.data.current;return!!t&&"sortable"in t&&"object"==typeof t.sortable&&"containerId"in t.sortable&&"items"in t.sortable&&"index"in t.sortable}let tB=[s.Down,s.Right,s.Up,s.Left],tU=(e,t)=>{let{context:{active:n,collisionRect:r,droppableRects:a,droppableContainers:l,over:i,scrollableAncestors:o}}=t;if(tB.includes(e.code)){if(e.preventDefault(),!n||!r)return;let t=[];l.getEnabled().forEach(n=>{if(!n||null!=n&&n.disabled)return;let l=a.get(n.id);if(l)switch(e.code){case s.Down:r.top<l.top&&t.push(n);break;case s.Up:r.top>l.top&&t.push(n);break;case s.Left:r.left>l.left&&t.push(n);break;case s.Right:r.left<l.left&&t.push(n)}});let d=e_({active:n,collisionRect:r,droppableRects:a,droppableContainers:t,pointerCoordinates:null}),c=eD(d,"id");if(c===(null==i?void 0:i.id)&&d.length>1&&(c=d[1].id),null!=c){let e=l.get(n.id),t=l.get(c),i=t?a.get(t.id):null,s=null==t?void 0:t.node.current;if(s&&i&&e&&t){let n=eI(s).some((e,t)=>o[t]!==e),a=tq(e,t),l=function(e,t){return!!tF(e)&&!!tF(t)&&!!tq(e,t)&&e.data.current.sortable.index<t.data.current.sortable.index}(e,t),d=n||!a?{x:0,y:0}:{x:l?r.width-i.width:0,y:l?r.height-i.height:0},c={x:i.left,y:i.top};return d.x&&d.y?c:ei(c,d)}}}};function tq(e,t){return!!tF(e)&&!!tF(t)&&e.data.current.sortable.containerId===t.data.current.sortable.containerId}function tG({item:e,onEdit:t,onDelete:n,onToggleStatus:r,getTargetIcon:l,childItems:i}){let{attributes:s,listeners:o,setNodeRef:d,transform:c,transition:u,isDragging:h}=function(e){var t,n,r,l;let{animateLayoutChanges:i=tO,attributes:s,disabled:o,data:d,getNewIndex:c=tT,id:u,strategy:h,resizeObserverConfig:f,transition:g=tI}=e,{items:m,containerId:x,activeIndex:v,disabled:b,disableTransforms:y,sortedRects:w,overIndex:j,useDragOverlay:C,strategy:N}=(0,p.useContext)(tS),D=(t=o,n=b,"boolean"==typeof t?{draggable:t,droppable:!1}:{draggable:null!=(r=null==t?void 0:t.draggable)?r:n.draggable,droppable:null!=(l=null==t?void 0:t.droppable)?l:n.droppable}),E=m.indexOf(u),k=(0,p.useMemo)(()=>({sortable:{containerId:x,index:E,items:m},...d}),[x,d,E,m]),_=(0,p.useMemo)(()=>m.slice(m.indexOf(u)),[m,u]),{rect:R,node:M,isOver:S,setNodeRef:A}=function(e){let{data:t,disabled:n=!1,id:r,resizeObserverConfig:l}=e,i=er("Droppable"),{active:s,dispatch:o,over:d,measureDroppableContainers:c}=(0,p.useContext)(tp),u=(0,p.useRef)({disabled:n}),h=(0,p.useRef)(!1),f=(0,p.useRef)(null),g=(0,p.useRef)(null),{disabled:m,updateMeasurementsFor:x,timeout:v}={...tN,...l},b=Z(null!=x?x:r),y=tn({callback:(0,p.useCallback)(()=>{if(!h.current){h.current=!0;return}null!=g.current&&clearTimeout(g.current),g.current=setTimeout(()=>{c(Array.isArray(b.current)?b.current:[b.current]),g.current=null},v)},[v]),disabled:m||!s}),[w,j]=ee((0,p.useCallback)((e,t)=>{y&&(t&&(y.unobserve(t),h.current=!1),e&&y.observe(e))},[y])),C=Z(t);return(0,p.useEffect)(()=>{y&&w.current&&(y.disconnect(),h.current=!1,y.observe(w.current))},[w,y]),(0,p.useEffect)(()=>(o({type:a.RegisterDroppable,element:{id:r,key:i,disabled:n,node:w,rect:f,data:C}}),()=>o({type:a.UnregisterDroppable,key:i,id:r})),[r]),(0,p.useEffect)(()=>{n!==u.current.disabled&&(o({type:a.SetDroppableDisabled,id:r,key:i,disabled:n}),u.current.disabled=n)},[r,i,n,o]),{active:s,rect:f,isOver:(null==d?void 0:d.id)===r,node:w,over:d,setNodeRef:j}}({id:u,data:k,disabled:D.droppable,resizeObserverConfig:{updateMeasurementsFor:_,...f}}),{active:T,activatorEvent:O,activeNodeRect:I,attributes:L,setNodeRef:P,listeners:z,isDragging:F,over:B,setActivatorNodeRef:U,transform:q}=function(e){let{id:t,data:n,disabled:r=!1,attributes:a}=e,l=er("Draggable"),{activators:i,activatorEvent:s,active:o,activeNodeRect:d,ariaDescribedById:c,draggableNodes:u,over:h}=(0,p.useContext)(tp),{role:f=tC,roleDescription:g="draggable",tabIndex:m=0}=null!=a?a:{},x=(null==o?void 0:o.id)===t,v=(0,p.useContext)(x?ty:tj),[b,y]=ee(),[w,j]=ee(),C=(0,p.useMemo)(()=>i.reduce((e,n)=>{let{eventName:r,handler:a}=n;return e[r]=e=>{a(e,t)},e},{}),[i,t]),N=Z(n);return V(()=>(u.set(t,{id:t,key:l,node:b,activatorNode:w,data:N}),()=>{let e=u.get(t);e&&e.key===l&&u.delete(t)}),[u,t]),{active:o,activatorEvent:s,activeNodeRect:d,attributes:(0,p.useMemo)(()=>({role:f,tabIndex:m,"aria-disabled":r,"aria-pressed":!!x&&f===tC||void 0,"aria-roledescription":g,"aria-describedby":c.draggable}),[r,f,m,x,g,c.draggable]),isDragging:x,listeners:r?void 0:C,node:b,over:h,setNodeRef:y,setActivatorNodeRef:j,transform:v}}({id:u,data:k,attributes:{...tz,...s},disabled:D.draggable}),G=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,p.useMemo)(()=>e=>{t.forEach(t=>t(e))},t)}(A,P),W=!!T,X=W&&!y&&tE(v)&&tE(j),J=!C&&F,$=J&&X?q:null,Y=X?null!=$?$:(null!=h?h:N)({rects:w,activeNodeRect:I,activeIndex:v,overIndex:j,index:E}):null,K=tE(v)&&tE(j)?c({id:u,items:m,activeIndex:v,overIndex:j}):E,H=null==T?void 0:T.id,Q=(0,p.useRef)({activeId:H,items:m,newIndex:K,containerId:x}),et=m!==Q.current.items,en=i({active:T,containerId:x,isDragging:F,isSorting:W,id:u,index:E,items:m,newIndex:Q.current.newIndex,previousItems:Q.current.items,previousContainerId:Q.current.containerId,transition:g,wasDragging:null!=Q.current.activeId}),ea=function(e){let{disabled:t,index:n,node:r,rect:a}=e,[l,i]=(0,p.useState)(null),s=(0,p.useRef)(n);return V(()=>{if(!t&&n!==s.current&&r.current){let e=a.current;if(e){let t=eT(r.current,{ignoreTransform:!0}),n={x:e.left-t.left,y:e.top-t.top,scaleX:e.width/t.width,scaleY:e.height/t.height};(n.x||n.y)&&i(n)}}n!==s.current&&(s.current=n)},[t,n,r,a]),(0,p.useEffect)(()=>{l&&i(null)},[l]),l}({disabled:!en,index:E,node:M,rect:R});return(0,p.useEffect)(()=>{W&&Q.current.newIndex!==K&&(Q.current.newIndex=K),x!==Q.current.containerId&&(Q.current.containerId=x),m!==Q.current.items&&(Q.current.items=m)},[W,K,x,m]),(0,p.useEffect)(()=>{if(H===Q.current.activeId)return;if(null!=H&&null==Q.current.activeId){Q.current.activeId=H;return}let e=setTimeout(()=>{Q.current.activeId=H},50);return()=>clearTimeout(e)},[H]),{active:T,activeIndex:v,attributes:L,data:k,rect:R,index:E,newIndex:K,items:m,isOver:S,isSorting:W,isDragging:F,listeners:z,node:M,overIndex:j,over:B,setNodeRef:G,setActivatorNodeRef:U,setDroppableNodeRef:A,setDraggableNodeRef:P,transform:null!=ea?ea:Y,transition:ea||et&&Q.current.newIndex===E?tP:(!J||es(O))&&g&&(W||en)?ed.Transition.toString({...g,property:tL}):void 0}}({id:e.id}),f={transform:ed.Transform.toString(c),transition:u,opacity:h?.5:1};return(0,g.jsxs)("div",{ref:d,style:f,className:"space-y-2",children:[(0,g.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg bg-white dark:bg-gray-800",children:[(0,g.jsxs)("div",{className:"flex items-center gap-4",children:[(0,g.jsx)("div",{...s,...o,className:"cursor-grab active:cursor-grabbing",children:(0,g.jsx)(R.A,{className:"h-4 w-4 text-gray-400"})}),(0,g.jsxs)("div",{className:"flex items-center gap-2",children:[l(e.target_type),(0,g.jsx)("span",{className:"font-medium arabic-text",children:e.title_ar}),!e.is_active&&(0,g.jsx)(j.E,{variant:"secondary",className:"arabic-text",children:"غير مفعل"})]})]}),(0,g.jsxs)("div",{className:"flex items-center gap-2",children:[(0,g.jsx)(y.$,{variant:"ghost",size:"sm",onClick:()=>r(e),children:e.is_active?(0,g.jsx)(M.A,{className:"h-4 w-4"}):(0,g.jsx)(S.A,{className:"h-4 w-4"})}),(0,g.jsx)(y.$,{variant:"ghost",size:"sm",onClick:()=>t(e),children:(0,g.jsx)(A.A,{className:"h-4 w-4"})}),(0,g.jsx)(y.$,{variant:"ghost",size:"sm",onClick:()=>n(e.id),children:(0,g.jsx)(T.A,{className:"h-4 w-4"})})]})]}),i.map(e=>(0,g.jsxs)("div",{className:"mr-8 flex items-center justify-between p-3 border rounded-lg bg-gray-50 dark:bg-gray-700",children:[(0,g.jsxs)("div",{className:"flex items-center gap-2",children:[l(e.target_type),(0,g.jsx)("span",{className:"arabic-text",children:e.title_ar}),!e.is_active&&(0,g.jsx)(j.E,{variant:"secondary",className:"arabic-text",children:"غير مفعل"})]}),(0,g.jsxs)("div",{className:"flex items-center gap-2",children:[(0,g.jsx)(y.$,{variant:"ghost",size:"sm",onClick:()=>r(e),children:e.is_active?(0,g.jsx)(M.A,{className:"h-4 w-4"}):(0,g.jsx)(S.A,{className:"h-4 w-4"})}),(0,g.jsx)(y.$,{variant:"ghost",size:"sm",onClick:()=>t(e),children:(0,g.jsx)(A.A,{className:"h-4 w-4"})}),(0,g.jsx)(y.$,{variant:"ghost",size:"sm",onClick:()=>n(e.id),children:(0,g.jsx)(T.A,{className:"h-4 w-4"})})]})]},e.id))]})}function tW(){let{user:e,profile:t}=(0,x.A)(),{menuItems:n,loading:r,addMenuItem:a,updateMenuItem:l,deleteMenuItem:i,toggleItemStatus:s,fetchMenuItems:o}=(0,v.b)(),[d,c]=(0,p.useState)(!1),[u,h]=(0,p.useState)(null),[f,m]=(0,p.useState)({title_ar:"",title_en:"",title_fr:"",slug:"",icon:"",parent_id:"",target_type:"internal",target_value:"",is_active:!0}),j=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,p.useMemo)(()=>[...t].filter(e=>null!=e),[...t])}(eb(e3),eb(eQ,{coordinateGetter:tU})),R=()=>{m({title_ar:"",title_en:"",title_fr:"",slug:"",icon:"",parent_id:"",target_type:"internal",target_value:"",is_active:!0}),h(null)},A=e=>{h(e),m({title_ar:e.title_ar,title_en:e.title_en||"",title_fr:e.title_fr||"",slug:e.slug,icon:e.icon||"",parent_id:e.parent_id||"",target_type:e.target_type,target_value:e.target_value,is_active:e.is_active}),c(!0)},T=async()=>{let e={...f,parent_id:"none"===f.parent_id?null:f.parent_id||null,order_index:n.length+1},t=!1;(u?await l(u.id,e):await a(e))&&(c(!1),R())},U=async e=>{confirm("هل أنت متأكد من حذف هذا العنصر؟")&&await i(e)},q=async e=>{await s(e.id)},G=async e=>{try{let t=n.map(async t=>t.is_active===e||await l(t.id,{is_active:e}));await Promise.all(t),B.o.success(e?"تم تفعيل جميع عناصر القائمة":"تم إلغاء تفعيل جميع عناصر القائمة")}catch(e){console.error("Error toggling all items:",e),B.o.error("خطأ في تحديث عناصر القائمة")}},W=async e=>{let{active:t,over:r}=e;if(!r||t.id===r.id)return;let a=X.findIndex(e=>e.id===t.id),l=X.findIndex(e=>e.id===r.id);if(-1===a||-1===l)return;let i=tD(X,a,l),s=n.filter(e=>e.parent_id),o=[...i.map((e,t)=>({...e,order_index:t+1})),...s];await reorderMenuItems(o)},X=n.filter(e=>!e.parent_id),J=e=>n.filter(t=>t.parent_id===e),$=e=>{switch(e){case"internal":default:return(0,g.jsx)(O.A,{className:"h-4 w-4"});case"external":return(0,g.jsx)(I.A,{className:"h-4 w-4"});case"page":return(0,g.jsx)(L.A,{className:"h-4 w-4"})}};return e&&t&&"admin"===t.role?(0,g.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",children:[(0,g.jsx)(b.V,{}),(0,g.jsxs)("main",{className:"container mx-auto px-4 py-8",children:[(0,g.jsxs)("div",{className:"mb-8",children:[(0,g.jsx)("div",{className:"flex items-center gap-4 mb-4",children:(0,g.jsx)(y.$,{variant:"outline",size:"sm",asChild:!0,children:(0,g.jsxs)("a",{href:"/dashboard/admin",children:[(0,g.jsx)(P.A,{className:"h-4 w-4 mr-2"}),"العودة للوحة التحكم"]})})}),(0,g.jsxs)("div",{className:"flex items-center justify-between",children:[(0,g.jsxs)("div",{children:[(0,g.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white arabic-text",children:"إدارة القائمة الرئيسية \uD83D\uDDC2️"}),(0,g.jsx)("p",{className:"text-gray-600 dark:text-gray-300 mt-2 arabic-text",children:"تحكم في عناصر القائمة الرئيسية وترتيبها"})]}),(0,g.jsxs)("div",{className:"flex gap-3",children:[(0,g.jsx)(k.lG,{open:d,onOpenChange:c,children:(0,g.jsx)(k.zM,{asChild:!0,children:(0,g.jsxs)(y.$,{onClick:R,children:[(0,g.jsx)(z.A,{className:"h-4 w-4 mr-2"}),"إضافة عنصر جديد"]})})}),(0,g.jsxs)(y.$,{variant:"outline",onClick:()=>G(!0),className:"text-green-600 hover:text-green-700",children:[(0,g.jsx)(S.A,{className:"h-4 w-4 mr-2"}),"تفعيل الكل"]}),(0,g.jsxs)(y.$,{variant:"outline",onClick:()=>G(!1),className:"text-red-600 hover:text-red-700",children:[(0,g.jsx)(M.A,{className:"h-4 w-4 mr-2"}),"إلغاء تفعيل الكل"]})]}),(0,g.jsx)(k.lG,{open:d,onOpenChange:c,children:(0,g.jsxs)(k.Cf,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,g.jsxs)(k.c7,{children:[(0,g.jsx)(k.L3,{className:"arabic-text",children:u?"تحرير عنصر القائمة":"إضافة عنصر قائمة جديد"}),(0,g.jsx)(k.rr,{className:"arabic-text",children:"املأ البيانات أدناه لإنشاء أو تحديث عنصر القائمة"})]}),(0,g.jsxs)(_.tU,{defaultValue:"basic",className:"w-full",children:[(0,g.jsxs)(_.j7,{className:"grid w-full grid-cols-2",children:[(0,g.jsx)(_.Xi,{value:"basic",className:"arabic-text",children:"البيانات الأساسية"}),(0,g.jsx)(_.Xi,{value:"translations",className:"arabic-text",children:"الترجمات"})]}),(0,g.jsxs)(_.av,{value:"basic",className:"space-y-4",children:[(0,g.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,g.jsxs)("div",{children:[(0,g.jsx)(N.J,{htmlFor:"title_ar",className:"arabic-text",children:"العنوان بالعربية *"}),(0,g.jsx)(C.p,{id:"title_ar",value:f.title_ar,onChange:e=>m({...f,title_ar:e.target.value}),placeholder:"مثال: الرئيسية",className:"arabic-text"})]}),(0,g.jsxs)("div",{children:[(0,g.jsx)(N.J,{htmlFor:"slug",className:"arabic-text",children:"الرابط المختصر *"}),(0,g.jsx)(C.p,{id:"slug",value:f.slug,onChange:e=>m({...f,slug:e.target.value}),placeholder:"مثال: home"})]})]}),(0,g.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,g.jsxs)("div",{children:[(0,g.jsx)(N.J,{htmlFor:"target_type",className:"arabic-text",children:"نوع الهدف *"}),(0,g.jsxs)(D.l6,{value:f.target_type,onValueChange:e=>m({...f,target_type:e}),children:[(0,g.jsx)(D.bq,{children:(0,g.jsx)(D.yv,{})}),(0,g.jsxs)(D.gC,{children:[(0,g.jsx)(D.eb,{value:"internal",children:"رابط داخلي"}),(0,g.jsx)(D.eb,{value:"external",children:"رابط خارجي"}),(0,g.jsx)(D.eb,{value:"page",children:"صفحة ديناميكية"})]})]})]}),(0,g.jsxs)("div",{children:[(0,g.jsx)(N.J,{htmlFor:"target_value",className:"arabic-text",children:"قيمة الهدف *"}),(0,g.jsx)(C.p,{id:"target_value",value:f.target_value,onChange:e=>m({...f,target_value:e.target.value}),placeholder:"internal"===f.target_type?"/catalog":"external"===f.target_type?"https://example.com":"معرف الصفحة"})]})]}),(0,g.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,g.jsxs)("div",{children:[(0,g.jsx)(N.J,{htmlFor:"icon",className:"arabic-text",children:"الأيقونة (Lucide)"}),(0,g.jsx)(C.p,{id:"icon",value:f.icon,onChange:e=>m({...f,icon:e.target.value}),placeholder:"مثال: Home"})]}),(0,g.jsxs)("div",{children:[(0,g.jsx)(N.J,{htmlFor:"parent_id",className:"arabic-text",children:"العنصر الأب"}),(0,g.jsxs)(D.l6,{value:f.parent_id,onValueChange:e=>m({...f,parent_id:e}),children:[(0,g.jsx)(D.bq,{children:(0,g.jsx)(D.yv,{placeholder:"اختر العنصر الأب (اختياري)"})}),(0,g.jsxs)(D.gC,{children:[(0,g.jsx)(D.eb,{value:"none",children:"بدون عنصر أب"}),X.map(e=>(0,g.jsx)(D.eb,{value:e.id,children:e.title_ar},e.id))]})]})]})]}),(0,g.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,g.jsx)(E.d,{id:"is_active",checked:f.is_active,onCheckedChange:e=>m({...f,is_active:e})}),(0,g.jsx)(N.J,{htmlFor:"is_active",className:"arabic-text",children:"مفعل"})]})]}),(0,g.jsxs)(_.av,{value:"translations",className:"space-y-4",children:[(0,g.jsxs)("div",{children:[(0,g.jsx)(N.J,{htmlFor:"title_en",className:"arabic-text",children:"العنوان بالإنجليزية"}),(0,g.jsx)(C.p,{id:"title_en",value:f.title_en,onChange:e=>m({...f,title_en:e.target.value}),placeholder:"Example: Home"})]}),(0,g.jsxs)("div",{children:[(0,g.jsx)(N.J,{htmlFor:"title_fr",className:"arabic-text",children:"العنوان بالفرنسية"}),(0,g.jsx)(C.p,{id:"title_fr",value:f.title_fr,onChange:e=>m({...f,title_fr:e.target.value}),placeholder:"Exemple: Accueil"})]})]})]}),(0,g.jsxs)(k.Es,{children:[(0,g.jsx)(y.$,{variant:"outline",onClick:()=>c(!1),children:"إلغاء"}),(0,g.jsx)(y.$,{onClick:T,children:u?"تحديث":"إضافة"})]})]})})]})]}),(0,g.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[(0,g.jsx)(w.Zp,{children:(0,g.jsx)(w.Wu,{className:"p-6",children:(0,g.jsxs)("div",{className:"flex items-center justify-between",children:[(0,g.jsxs)("div",{children:[(0,g.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text",children:"إجمالي العناصر"}),(0,g.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:n.length})]}),(0,g.jsx)(F.A,{className:"h-8 w-8 text-blue-600"})]})})}),(0,g.jsx)(w.Zp,{children:(0,g.jsx)(w.Wu,{className:"p-6",children:(0,g.jsxs)("div",{className:"flex items-center justify-between",children:[(0,g.jsxs)("div",{children:[(0,g.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text",children:"العناصر المفعلة"}),(0,g.jsx)("p",{className:"text-2xl font-bold text-green-600",children:n.filter(e=>e.is_active).length})]}),(0,g.jsx)(S.A,{className:"h-8 w-8 text-green-600"})]})})}),(0,g.jsx)(w.Zp,{children:(0,g.jsx)(w.Wu,{className:"p-6",children:(0,g.jsxs)("div",{className:"flex items-center justify-between",children:[(0,g.jsxs)("div",{children:[(0,g.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text",children:"العناصر المعطلة"}),(0,g.jsx)("p",{className:"text-2xl font-bold text-red-600",children:n.filter(e=>!e.is_active).length})]}),(0,g.jsx)(M.A,{className:"h-8 w-8 text-red-600"})]})})}),(0,g.jsx)(w.Zp,{children:(0,g.jsx)(w.Wu,{className:"p-6",children:(0,g.jsxs)("div",{className:"flex items-center justify-between",children:[(0,g.jsxs)("div",{children:[(0,g.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text",children:"القوائم الفرعية"}),(0,g.jsx)("p",{className:"text-2xl font-bold text-purple-600",children:n.filter(e=>e.parent_id).length})]}),(0,g.jsx)(P.A,{className:"h-8 w-8 text-purple-600"})]})})})]}),(0,g.jsxs)(w.Zp,{children:[(0,g.jsxs)(w.aR,{children:[(0,g.jsx)(w.ZB,{className:"arabic-text",children:"عناصر القائمة الحالية"}),(0,g.jsx)(w.BT,{className:"arabic-text",children:"إدارة وترتيب عناصر القائمة الرئيسية"})]}),(0,g.jsx)(w.Wu,{children:r?(0,g.jsxs)("div",{className:"text-center py-8",children:[(0,g.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,g.jsx)("p",{className:"mt-2 text-gray-600 arabic-text",children:"جاري التحميل..."})]}):0===n.length?(0,g.jsxs)("div",{className:"text-center py-8",children:[(0,g.jsx)(F.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,g.jsx)("p",{className:"text-gray-600 arabic-text",children:"لا توجد عناصر قائمة"})]}):(0,g.jsx)(tw,{sensors:j,collisionDetection:ek,onDragEnd:W,children:(0,g.jsx)(tA,{items:X.map(e=>e.id),strategy:tR,children:(0,g.jsx)("div",{className:"space-y-2",children:X.map(e=>(0,g.jsx)(tG,{item:e,onEdit:A,onDelete:U,onToggleStatus:q,getTargetIcon:$,childItems:J(e.id)},e.id))})})})})]})]})]}):null}}};var t=require("../../../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),r=t.X(0,[4447,8773,4097,3050,3932,7801],()=>n(5406));module.exports=r})();