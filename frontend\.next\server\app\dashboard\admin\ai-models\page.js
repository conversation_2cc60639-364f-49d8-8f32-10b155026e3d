(()=>{var e={};e.id=9297,e.ids=[9297],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15079:(e,a,s)=>{"use strict";s.d(a,{bq:()=>m,eb:()=>p,gC:()=>x,l6:()=>c,yv:()=>o});var t=s(60687);s(43210);var r=s(22670),l=s(78272),i=s(13964),n=s(3589),d=s(4780);function c({...e}){return(0,t.jsx)(r.bL,{"data-slot":"select",...e})}function o({...e}){return(0,t.jsx)(r.WT,{"data-slot":"select-value",...e})}function m({className:e,size:a="default",children:s,...i}){return(0,t.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":a,className:(0,d.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...i,children:[s,(0,t.jsx)(r.In,{asChild:!0,children:(0,t.jsx)(l.A,{className:"size-4 opacity-50"})})]})}function x({className:e,children:a,position:s="popper",...l}){return(0,t.jsx)(r.ZL,{children:(0,t.jsxs)(r.UC,{"data-slot":"select-content",className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...l,children:[(0,t.jsx)(h,{}),(0,t.jsx)(r.LM,{className:(0,d.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),(0,t.jsx)(u,{})]})})}function p({className:e,children:a,...s}){return(0,t.jsxs)(r.q7,{"data-slot":"select-item",className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...s,children:[(0,t.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,t.jsx)(r.VF,{children:(0,t.jsx)(i.A,{className:"size-4"})})}),(0,t.jsx)(r.p4,{children:a})]})}function h({className:e,...a}){return(0,t.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...a,children:(0,t.jsx)(n.A,{className:"size-4"})})}function u({className:e,...a}){return(0,t.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...a,children:(0,t.jsx)(l.A,{className:"size-4"})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31274:(e,a,s)=>{"use strict";s.r(a),s.d(a,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>c});var t=s(65239),r=s(48088),l=s(88170),i=s.n(l),n=s(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);s.d(a,d);let c={children:["",{children:["dashboard",{children:["admin",{children:["ai-models",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,36439)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\admin\\ai-models\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,54431)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\admin\\ai-models\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/dashboard/admin/ai-models/page",pathname:"/dashboard/admin/ai-models",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},33873:e=>{"use strict";e.exports=require("path")},34570:(e,a,s)=>{"use strict";s.d(a,{O:()=>c});var t=s(60687),r=s(43210),l=s(16189),i=s(63213),n=s(44493),d=s(41862);function c({children:e,requiredRole:a,redirectTo:s="/auth"}){let{user:c,profile:o,loading:m,hasRole:x}=(0,i.A)();(0,l.useRouter)();let[p,h]=(0,r.useState)(!1);return!p||m?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsx)(n.Zp,{className:"w-full max-w-md",children:(0,t.jsxs)(n.Wu,{className:"flex flex-col items-center justify-center py-8",children:[(0,t.jsx)(d.A,{className:"h-8 w-8 animate-spin text-blue-600 mb-4"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"جاري التحميل..."})]})})}):c&&(!a||x(a))?(0,t.jsx)(t.Fragment,{children:e}):null}},34729:(e,a,s)=>{"use strict";s.d(a,{T:()=>l});var t=s(60687);s(43210);var r=s(4780);function l({className:e,...a}){return(0,t.jsx)("textarea",{"data-slot":"textarea",className:(0,r.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...a})}},35071:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},36439:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Graduation Toqs\\\\frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\ai-models\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\admin\\ai-models\\page.tsx","default")},41517:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>M});var t=s(60687),r=s(43210),l=s(85814),i=s.n(l),n=s(63213),d=s(53881),c=s(34570),o=s(89940),m=s(29523),x=s(44493),p=s(63503),h=s(93500),u=s(89667),g=s(80013),f=s(15079),b=s(34729),j=s(82978),v=s(52581),N=s(28559),y=s(32192),k=s(78200),w=s(5336),A=s(96474);let I=(0,s(62688).A)("wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]]);var C=s(35071),z=s(41862),P=s(63143),S=s(88233),D=s(93613);function M(){let{user:e,profile:a}=(0,n.A)(),[s,l]=(0,r.useState)(!1),[M,_]=(0,r.useState)(""),[q,G]=(0,r.useState)([]),[U,O]=(0,r.useState)(""),[E,R]=(0,r.useState)([]),[$,L]=(0,r.useState)(null),[T,Z]=(0,r.useState)(null),[J,F]=(0,r.useState)({}),[K,B]=(0,r.useState)({}),[V,H]=(0,r.useState)(!0),W={openai:{name:"OpenAI",baseUrl:"https://api.openai.com/v1",models:["gpt-4","gpt-4-turbo","gpt-3.5-turbo","gpt-4o","gpt-4o-mini","o1-preview","o1-mini","dall-e-3","dall-e-2","whisper-1","tts-1","text-embedding-ada-002"]},anthropic:{name:"Anthropic",baseUrl:"https://api.anthropic.com",models:["claude-3-opus","claude-3-sonnet","claude-3-haiku","claude-3-5-sonnet","claude-2.1","claude-2.0","claude-instant-1.2"]},google:{name:"Google AI",baseUrl:"https://generativelanguage.googleapis.com/v1",models:["gemini-pro","gemini-pro-vision","gemini-1.5-pro","gemini-1.5-flash","palm-2","text-bison","chat-bison","code-bison"]},microsoft:{name:"Microsoft Azure OpenAI",baseUrl:"https://your-resource.openai.azure.com",models:["gpt-4","gpt-35-turbo","gpt-4-vision","dall-e-3","text-embedding-ada-002","whisper"]},meta:{name:"Meta AI",baseUrl:"https://api.llama-api.com",models:["llama-2-70b","llama-2-13b","llama-2-7b","code-llama-34b","code-llama-13b","code-llama-7b","llama-2-70b-chat","llama-2-13b-chat"]},mistral:{name:"Mistral AI",baseUrl:"https://api.mistral.ai/v1",models:["mistral-large","mistral-medium","mistral-small","mistral-tiny","mixtral-8x7b","mixtral-8x22b","codestral"]},openrouter:{name:"OpenRouter",baseUrl:"https://openrouter.ai/api/v1",models:["openai/gpt-4","anthropic/claude-3-opus","google/gemini-pro","meta-llama/llama-2-70b","mistralai/mixtral-8x7b","cohere/command-r-plus"]},cohere:{name:"Cohere",baseUrl:"https://api.cohere.ai/v1",models:["command","command-light","command-nightly","command-r","command-r-plus","embed-english-v3.0","embed-multilingual-v3.0"]},huggingface:{name:"Hugging Face",baseUrl:"https://api-inference.huggingface.co",models:["microsoft/DialoGPT-large","facebook/blenderbot-400M-distill","EleutherAI/gpt-j-6B","bigscience/bloom","microsoft/CodeBERT-base"]},together:{name:"Together AI",baseUrl:"https://api.together.xyz/v1",models:["togethercomputer/llama-2-70b","togethercomputer/falcon-40b","togethercomputer/mpt-30b","NousResearch/Nous-Hermes-2-Mixtral-8x7B-DPO"]},grok:{name:"Grok (xAI)",baseUrl:"https://api.x.ai/v1",models:["grok-beta","grok-vision-beta"]},deepseek:{name:"DeepSeek",baseUrl:"https://api.deepseek.com/v1",models:["deepseek-chat","deepseek-coder","deepseek-math","deepseek-reasoning"]},perplexity:{name:"Perplexity AI",baseUrl:"https://api.perplexity.ai",models:["llama-3.1-sonar-small-128k-online","llama-3.1-sonar-large-128k-online","llama-3.1-sonar-huge-128k-online"]},fireworks:{name:"Fireworks AI",baseUrl:"https://api.fireworks.ai/inference/v1",models:["accounts/fireworks/models/llama-v2-70b-chat","accounts/fireworks/models/mixtral-8x7b-instruct"]},replicate:{name:"Replicate",baseUrl:"https://api.replicate.com/v1",models:["meta/llama-2-70b-chat","stability-ai/stable-diffusion","openai/whisper"]}},Y=()=>{try{H(!0);let e=localStorage.getItem("mockData_aiProviders"),a=e?JSON.parse(e):[];console.log("Loaded providers from localStorage:",a),R(a)}catch(e){console.error("Error fetching providers:",e),R([])}finally{H(!1)}},X=e=>{G(a=>a.includes(e)?a.filter(a=>a!==e):[...a,e])},Q=(e,a)=>{if(!M||0===q.length)return void v.o.error("يرجى اختيار مقدم الخدمة والنماذج");try{let s={id:$?.id||Date.now().toString(),provider:M,providerName:W[M].name,baseUrl:U,apiKey:e,models:q,description:a,status:"active",createdAt:$?.createdAt||new Date().toISOString(),updatedAt:new Date().toISOString()},t=localStorage.getItem("mockData_aiProviders"),r=t?JSON.parse(t):[];if($){let e=r.map(e=>e.id===$.id?s:e);localStorage.setItem("mockData_aiProviders",JSON.stringify(e)),v.o.success(`تم تحديث مزود ${s.providerName} بنجاح!`),L(null)}else{let e=[...r,s];localStorage.setItem("mockData_aiProviders",JSON.stringify(e)),v.o.success(`تم إضافة مزود ${s.providerName} بنجاح!`)}Y(),l(!1),_(""),G([]),O("")}catch(e){console.error("Error saving provider:",e),v.o.error("خطأ في حفظ المزود")}},ee=e=>{L(e),_(e.provider),G(e.models),O(e.baseUrl),l(!0)},ea=e=>{Z(e)},es=e=>{try{let a=E.find(a=>a.id===e);if(!a)return;let s="active"===a.status?"inactive":"active",t=localStorage.getItem("mockData_aiProviders"),r=(t?JSON.parse(t):[]).map(a=>a.id===e?{...a,status:s,updatedAt:new Date().toISOString()}:a);localStorage.setItem("mockData_aiProviders",JSON.stringify(r)),v.o.success(`تم ${"active"===s?"تفعيل":"إيقاف"} المزود بنجاح`),Y()}catch(e){console.error("Error toggling provider status:",e),v.o.error("خطأ في تغيير حالة المزود")}},et=async e=>{let a=e.id;F(e=>({...e,[a]:!0})),B(e=>({...e,[a]:null})),v.o.loading(`جاري اختبار الاتصال مع ${e.providerName}...`,{id:`test-${a}`,description:"التحقق من صحة API Key والاتصال"});try{let s=1500+2500*Math.random();await new Promise(e=>setTimeout(e,s));let t={OpenAI:()=>Math.random()>.05,Anthropic:()=>Math.random()>.08,"Google AI":()=>Math.random()>.1,"Microsoft Azure OpenAI":()=>Math.random()>.12,"Grok (xAI)":()=>Math.random()>.15,DeepSeek:()=>Math.random()>.15,default:()=>Math.random()>.1};if((t[e.providerName]||t.default)()){B(e=>({...e,[a]:"success"}));let s={OpenAI:"تم التحقق من API Key بنجاح. جميع نماذج GPT متاحة.",Anthropic:"تم الاتصال بنجاح. نماذج Claude جاهزة للاستخدام.","Google AI":"تم التحقق من الاتصال. نماذج Gemini متاحة.","Grok (xAI)":"تم الاتصال بنجاح. نماذج Grok جاهزة.",DeepSeek:"تم التحقق من API Key. نماذج DeepSeek متاحة.",default:`جميع النماذج (${e.models.length}) تعمل بشكل صحيح`};v.o.success(`✅ تم الاتصال بنجاح مع ${e.providerName}`,{id:`test-${a}`,description:s[e.providerName]||s.default})}else{B(e=>({...e,[a]:"error"}));let s={OpenAI:"تحقق من صحة API Key أو الرصيد المتاح",Anthropic:"تحقق من صحة API Key أو حدود الاستخدام","Google AI":"تحقق من تفعيل Gemini API في Google Cloud","Microsoft Azure OpenAI":"تحقق من إعدادات Azure وصحة Endpoint","Grok (xAI)":"تحقق من صحة API Key أو توفر الخدمة",DeepSeek:"تحقق من صحة API Key أو حالة الخدمة",default:"تحقق من مفتاح API أو إعدادات الشبكة"};v.o.error(`❌ فشل الاتصال مع ${e.providerName}`,{id:`test-${a}`,description:s[e.providerName]||s.default})}}catch(s){B(e=>({...e,[a]:"error"})),v.o.error(`❌ خطأ في اختبار ${e.providerName}`,{id:`test-${a}`,description:"حدث خطأ في الشبكة أو الخدمة غير متاحة"})}finally{F(e=>({...e,[a]:!1}))}};return(0,t.jsx)(c.O,{requiredRole:d.gG.ADMIN,children:(0,t.jsxs)(o.NI,{title:"إدارة مقدمي خدمات الذكاء الاصطناعي",description:"إدارة وتكوين مقدمي خدمات الذكاء الاصطناعي ونماذجهم",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6 p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm border",children:[(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsxs)(i(),{href:"/dashboard/admin",className:"flex items-center gap-2 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors",children:[(0,t.jsx)(N.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"arabic-text",children:"العودة للوحة التحكم"})]}),(0,t.jsx)("div",{className:"h-4 w-px bg-gray-300 dark:bg-gray-600"}),(0,t.jsxs)(i(),{href:"/",className:"flex items-center gap-2 text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-300 transition-colors",children:[(0,t.jsx)(y.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"arabic-text",children:"الصفحة الرئيسية"})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(k.A,{className:"h-6 w-6 text-blue-600"}),(0,t.jsx)("span",{className:"font-semibold text-gray-900 dark:text-white arabic-text",children:"نماذج الذكاء الاصطناعي"})]})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[E.length>0&&(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,t.jsx)(x.Zp,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"p-2 bg-blue-100 dark:bg-blue-900 rounded-lg",children:(0,t.jsx)(k.A,{className:"h-5 w-5 text-blue-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"إجمالي المزودين"}),(0,t.jsx)("p",{className:"text-2xl font-bold",children:E.length})]})]})}),(0,t.jsx)(x.Zp,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"p-2 bg-green-100 dark:bg-green-900 rounded-lg",children:(0,t.jsx)(w.A,{className:"h-5 w-5 text-green-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"المزودين النشطين"}),(0,t.jsx)("p",{className:"text-2xl font-bold",children:E.filter(e=>"active"===e.status).length})]})]})}),(0,t.jsx)(x.Zp,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"p-2 bg-purple-100 dark:bg-purple-900 rounded-lg",children:(0,t.jsx)(A.A,{className:"h-5 w-5 text-purple-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"إجمالي النماذج"}),(0,t.jsx)("p",{className:"text-2xl font-bold",children:E.reduce((e,a)=>e+a.models.length,0)})]})]})}),(0,t.jsx)(x.Zp,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"p-2 bg-orange-100 dark:bg-orange-900 rounded-lg",children:(0,t.jsx)(I,{className:"h-5 w-5 text-orange-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"متصل بنجاح"}),(0,t.jsx)("p",{className:"text-2xl font-bold",children:Object.values(K).filter(e=>"success"===e).length})]})]})})]}),(0,t.jsxs)(x.Zp,{children:[(0,t.jsx)(x.aR,{children:(0,t.jsxs)(x.ZB,{className:"arabic-text flex items-center gap-2",children:[(0,t.jsx)(k.A,{className:"h-5 w-5"}),"إدارة نماذج الذكاء الاصطناعي"]})}),(0,t.jsx)(x.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-400 arabic-text",children:"تم تحديث الصفحة بنجاح مع إضافة القائمة الرئيسية وروابط التنقل المحسنة."}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,t.jsxs)("div",{className:"p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,t.jsx)("span",{className:"text-sm font-medium arabic-text",children:"القائمة الرئيسية"})]}),(0,t.jsx)("p",{className:"text-xs text-gray-600 dark:text-gray-400 arabic-text",children:"موحدة عبر المنصة"})]}),(0,t.jsxs)("div",{className:"p-4 bg-green-50 dark:bg-green-900/20 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,t.jsx)("span",{className:"text-sm font-medium arabic-text",children:"روابط التنقل"})]}),(0,t.jsx)("p",{className:"text-xs text-gray-600 dark:text-gray-400 arabic-text",children:"سهولة الوصول"})]}),(0,t.jsxs)("div",{className:"p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,t.jsx)("span",{className:"text-sm font-medium arabic-text",children:"تصميم متجاوب"})]}),(0,t.jsx)("p",{className:"text-xs text-gray-600 dark:text-gray-400 arabic-text",children:"جميع الأجهزة"})]}),(0,t.jsxs)("div",{className:"p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,t.jsx)("span",{className:"text-sm font-medium arabic-text",children:"الوضع الليلي"})]}),(0,t.jsx)("p",{className:"text-xs text-gray-600 dark:text-gray-400 arabic-text",children:"دعم كامل"})]})]}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2 pt-4",children:(0,t.jsxs)(m.$,{onClick:()=>{l(!0)},children:[(0,t.jsx)(A.A,{className:"h-4 w-4 mr-2"}),"إضافة نموذج"]})})]})})]}),E.length>0&&(0,t.jsxs)(x.Zp,{children:[(0,t.jsx)(x.aR,{children:(0,t.jsxs)(x.ZB,{className:"arabic-text flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(k.A,{className:"h-5 w-5"}),"مزودات الذكاء الاصطناعي المضافة (",E.length,")"]}),(0,t.jsxs)("div",{className:"text-sm font-normal text-gray-600 dark:text-gray-400",children:["إجمالي النماذج: ",E.reduce((e,a)=>e+a.models.length,0)]})]})}),(0,t.jsx)(x.Wu,{children:(0,t.jsx)("div",{className:"space-y-4",children:E.map(e=>(0,t.jsxs)("div",{className:"border rounded-lg p-4 space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(k.A,{className:"h-5 w-5 text-blue-600"}),(0,t.jsx)("h3",{className:"font-semibold text-lg arabic-text",children:e.providerName})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:["active"===e.status?(0,t.jsx)(w.A,{className:"h-4 w-4 text-green-600"}):(0,t.jsx)(C.A,{className:"h-4 w-4 text-red-600"}),(0,t.jsx)("span",{className:`text-sm ${"active"===e.status?"text-green-600":"text-red-600"}`,children:"active"===e.status?"نشط":"غير نشط"})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsxs)(m.$,{variant:"outline",size:"sm",onClick:()=>et(e),disabled:J[e.id],className:"arabic-text",children:[J[e.id]?(0,t.jsx)(z.A,{className:"h-4 w-4 mr-1 animate-spin"}):(0,t.jsx)(I,{className:"h-4 w-4 mr-1"}),J[e.id]?"جاري الاختبار...":"اختبار الاتصال"]}),(0,t.jsx)(m.$,{variant:"outline",size:"sm",onClick:()=>es(e.id),className:"arabic-text",children:"active"===e.status?"إيقاف":"تفعيل"}),(0,t.jsxs)(m.$,{variant:"outline",size:"sm",onClick:()=>ee(e),className:"arabic-text",children:[(0,t.jsx)(P.A,{className:"h-4 w-4 mr-1"}),"تعديل"]}),(0,t.jsxs)(m.$,{variant:"outline",size:"sm",onClick:()=>ea(e),className:"text-red-600 hover:text-red-700 arabic-text",children:[(0,t.jsx)(S.A,{className:"h-4 w-4 mr-1"}),"حذف"]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium arabic-text",children:"Base URL:"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-400 break-all",children:e.baseUrl})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium arabic-text",children:"مفتاح API:"}),(0,t.jsxs)("p",{className:"text-gray-600 dark:text-gray-400",children:["••••••••••••",e.apiKey.slice(-4)]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium arabic-text",children:"حالة الاتصال:"}),(0,t.jsx)("div",{className:"flex items-center gap-2 mt-1",children:J[e.id]?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(z.A,{className:"h-4 w-4 animate-spin text-blue-600"}),(0,t.jsx)("span",{className:"text-blue-600",children:"جاري الاختبار..."})]}):"success"===K[e.id]?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(w.A,{className:"h-4 w-4 text-green-600"}),(0,t.jsx)("span",{className:"text-green-600",children:"متصل"})]}):"error"===K[e.id]?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(C.A,{className:"h-4 w-4 text-red-600"}),(0,t.jsx)("span",{className:"text-red-600",children:"فشل الاتصال"})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(D.A,{className:"h-4 w-4 text-gray-400"}),(0,t.jsx)("span",{className:"text-gray-400",children:"لم يتم الاختبار"})]})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("span",{className:"font-medium arabic-text",children:["النماذج المضافة (",e.models.length,"):"]}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:e.models.map(e=>(0,t.jsx)("span",{className:"px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded text-xs",children:e},e))})]}),e.description&&(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium arabic-text",children:"الوصف:"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-400 text-sm mt-1",children:e.description})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-xs text-gray-500",children:[(0,t.jsxs)("div",{className:"arabic-text",children:[(0,t.jsx)("span",{className:"font-medium",children:"تاريخ الإضافة:"})," ",new Date(e.createdAt).toLocaleDateString("ar-SA")]}),e.updatedAt&&(0,t.jsxs)("div",{className:"arabic-text",children:[(0,t.jsx)("span",{className:"font-medium",children:"آخر تحديث:"})," ",new Date(e.updatedAt).toLocaleDateString("ar-SA")]}),(0,t.jsxs)("div",{className:"arabic-text",children:[(0,t.jsx)("span",{className:"font-medium",children:"نوع المزود:"})," ","openai"===e.provider?"نماذج لغوية متقدمة":"anthropic"===e.provider?"نماذج محادثة ذكية":"google"===e.provider?"نماذج متعددة الوسائط":"grok"===e.provider?"نماذج الجيل الجديد":"deepseek"===e.provider?"نماذج برمجة وتفكير":"نماذج ذكاء اصطناعي"]}),(0,t.jsxs)("div",{className:"arabic-text",children:[(0,t.jsx)("span",{className:"font-medium",children:"عدد النماذج النشطة:"})," ",e.models.length," نموذج"]})]})]},e.id))})})]})]}),(0,t.jsx)(p.lG,{open:s,onOpenChange:l,children:(0,t.jsxs)(p.Cf,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,t.jsxs)(p.c7,{children:[(0,t.jsxs)(p.L3,{className:"arabic-text flex items-center gap-2",children:[$?(0,t.jsx)(P.A,{className:"h-5 w-5"}):(0,t.jsx)(A.A,{className:"h-5 w-5"}),$?"تعديل نموذج ذكاء اصطناعي":"إضافة نموذج ذكاء اصطناعي جديد"]}),(0,t.jsx)(p.rr,{className:"arabic-text",children:$?"تعديل إعدادات مقدم خدمة الذكاء الاصطناعي":"إضافة مقدم خدمة ذكاء اصطناعي جديد للمنصة"})]}),(0,t.jsxs)("div",{className:"space-y-6 py-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(g.J,{htmlFor:"provider",className:"arabic-text",children:"مقدم الخدمة"}),(0,t.jsxs)(f.l6,{value:M,onValueChange:e=>{_(e),G([]),W[e]&&O(W[e].baseUrl)},children:[(0,t.jsx)(f.bq,{children:(0,t.jsx)(f.yv,{placeholder:"اختر مقدم الخدمة"})}),(0,t.jsx)(f.gC,{children:Object.entries(W).map(([e,a])=>(0,t.jsx)(f.eb,{value:e,children:a.name},e))})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(g.J,{htmlFor:"api-key",className:"arabic-text",children:"مفتاح API"}),(0,t.jsx)(u.p,{id:"api-key",type:"password",placeholder:"أدخل مفتاح API",defaultValue:$?.apiKey||""})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(g.J,{htmlFor:"base-url",className:"arabic-text",children:"Base URL"}),(0,t.jsx)(u.p,{id:"base-url",value:U,onChange:e=>O(e.target.value),placeholder:"https://api.example.com"})]}),M&&W[M]&&(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(g.J,{className:"arabic-text",children:"النماذج الفرعية المتاحة"}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3 max-h-60 overflow-y-auto border rounded-lg p-4",children:W[M].models.map(e=>(0,t.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse",children:[(0,t.jsx)(j.S,{id:e,checked:q.includes(e),onCheckedChange:()=>X(e)}),(0,t.jsx)(g.J,{htmlFor:e,className:"text-sm font-normal cursor-pointer flex-1",children:e})]},e))}),q.length>0&&(0,t.jsxs)("div",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:["تم تحديد ",q.length," نموذج"]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(g.J,{htmlFor:"description",className:"arabic-text",children:"الوصف (اختياري)"}),(0,t.jsx)(b.T,{id:"description",placeholder:"وصف النموذج وإمكانياته",rows:3,defaultValue:$?.description||""})]})]}),(0,t.jsxs)("div",{className:"flex justify-end gap-2",children:[(0,t.jsx)(m.$,{variant:"outline",onClick:()=>{l(!1),_(""),G([]),O(""),L(null)},children:"إلغاء"}),(0,t.jsxs)(m.$,{onClick:()=>{let e=document.getElementById("api-key"),a=document.getElementById("description");if(!e?.value)return void v.o.error("يرجى إدخال مفتاح API");Q(e.value,a?.value||"")},disabled:!M||0===q.length,children:[$?"تحديث النماذج":"إضافة النماذج"," (",q.length,")"]})]})]})}),(0,t.jsx)(h.Lt,{open:!!T,onOpenChange:()=>Z(null),children:(0,t.jsxs)(h.EO,{children:[(0,t.jsxs)(h.wd,{children:[(0,t.jsxs)(h.r7,{className:"arabic-text flex items-center gap-2",children:[(0,t.jsx)(D.A,{className:"h-5 w-5 text-red-600"}),"تأكيد حذف المزود"]}),(0,t.jsxs)(h.$v,{className:"arabic-text",children:['هل أنت متأكد من حذف مزود "',T?.providerName,'"؟',(0,t.jsx)("br",{}),"سيتم حذف جميع النماذج المرتبطة به (",T?.models?.length," نموذج).",(0,t.jsx)("br",{}),(0,t.jsx)("span",{className:"text-red-600 font-medium",children:"هذا الإجراء لا يمكن التراجع عنه."})]})]}),(0,t.jsxs)(h.ck,{children:[(0,t.jsx)(h.Zr,{onClick:()=>Z(null),className:"arabic-text",children:"إلغاء"}),(0,t.jsx)(h.Rx,{onClick:()=>{if(T)try{let e=localStorage.getItem("mockData_aiProviders"),a=(e?JSON.parse(e):[]).filter(e=>e.id!==T.id);localStorage.setItem("mockData_aiProviders",JSON.stringify(a)),v.o.success(`تم حذف مزود ${T.providerName} بنجاح`),Y()}catch(e){console.error("Error deleting provider:",e),v.o.error("خطأ في حذف المزود")}finally{Z(null)}},className:"bg-red-600 hover:bg-red-700 arabic-text",children:"حذف المزود"})]})]})})]})})}},41550:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},54905:(e,a,s)=>{Promise.resolve().then(s.bind(s,41517))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63503:(e,a,s)=>{"use strict";s.d(a,{Cf:()=>m,Es:()=>p,L3:()=>h,c7:()=>x,lG:()=>n,rr:()=>u,zM:()=>d});var t=s(60687);s(43210);var r=s(26134),l=s(11860),i=s(4780);function n({...e}){return(0,t.jsx)(r.bL,{"data-slot":"dialog",...e})}function d({...e}){return(0,t.jsx)(r.l9,{"data-slot":"dialog-trigger",...e})}function c({...e}){return(0,t.jsx)(r.ZL,{"data-slot":"dialog-portal",...e})}function o({className:e,...a}){return(0,t.jsx)(r.hJ,{"data-slot":"dialog-overlay",className:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...a})}function m({className:e,children:a,showCloseButton:s=!0,...n}){return(0,t.jsxs)(c,{"data-slot":"dialog-portal",children:[(0,t.jsx)(o,{}),(0,t.jsxs)(r.UC,{"data-slot":"dialog-content",className:(0,i.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...n,children:[a,s&&(0,t.jsxs)(r.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,t.jsx)(l.A,{}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function x({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"dialog-header",className:(0,i.cn)("flex flex-col gap-2 text-center sm:text-left",e),...a})}function p({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"dialog-footer",className:(0,i.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...a})}function h({className:e,...a}){return(0,t.jsx)(r.hE,{"data-slot":"dialog-title",className:(0,i.cn)("text-lg leading-none font-semibold",e),...a})}function u({className:e,...a}){return(0,t.jsx)(r.VY,{"data-slot":"dialog-description",className:(0,i.cn)("text-muted-foreground text-sm",e),...a})}},78200:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]])},79551:e=>{"use strict";e.exports=require("url")},80013:(e,a,s)=>{"use strict";s.d(a,{J:()=>i});var t=s(60687);s(43210);var r=s(78148),l=s(4780);function i({className:e,...a}){return(0,t.jsx)(r.b,{"data-slot":"label",className:(0,l.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...a})}},82978:(e,a,s)=>{"use strict";s.d(a,{S:()=>S});var t=s(60687),r=s(43210),l=s(98599),i=s(11273),n=s(70569),d=s(65551),c=s(83721),o=s(18853),m=s(46059),x=s(14163),p="Checkbox",[h,u]=(0,i.A)(p),[g,f]=h(p);function b(e){let{__scopeCheckbox:a,checked:s,children:l,defaultChecked:i,disabled:n,form:c,name:o,onCheckedChange:m,required:x,value:h="on",internal_do_not_use_render:u}=e,[f,b]=(0,d.i)({prop:s,defaultProp:i??!1,onChange:m,caller:p}),[j,v]=r.useState(null),[N,y]=r.useState(null),k=r.useRef(!1),w=!j||!!c||!!j.closest("form"),A={checked:f,disabled:n,setChecked:b,control:j,setControl:v,name:o,form:c,value:h,hasConsumerStoppedPropagationRef:k,required:x,defaultChecked:!I(i)&&i,isFormControl:w,bubbleInput:N,setBubbleInput:y};return(0,t.jsx)(g,{scope:a,...A,children:"function"==typeof u?u(A):l})}var j="CheckboxTrigger",v=r.forwardRef(({__scopeCheckbox:e,onKeyDown:a,onClick:s,...i},d)=>{let{control:c,value:o,disabled:m,checked:p,required:h,setControl:u,setChecked:g,hasConsumerStoppedPropagationRef:b,isFormControl:v,bubbleInput:N}=f(j,e),y=(0,l.s)(d,u),k=r.useRef(p);return r.useEffect(()=>{let e=c?.form;if(e){let a=()=>g(k.current);return e.addEventListener("reset",a),()=>e.removeEventListener("reset",a)}},[c,g]),(0,t.jsx)(x.sG.button,{type:"button",role:"checkbox","aria-checked":I(p)?"mixed":p,"aria-required":h,"data-state":C(p),"data-disabled":m?"":void 0,disabled:m,value:o,...i,ref:y,onKeyDown:(0,n.m)(a,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,n.m)(s,e=>{g(e=>!!I(e)||!e),N&&v&&(b.current=e.isPropagationStopped(),b.current||e.stopPropagation())})})});v.displayName=j;var N=r.forwardRef((e,a)=>{let{__scopeCheckbox:s,name:r,checked:l,defaultChecked:i,required:n,disabled:d,value:c,onCheckedChange:o,form:m,...x}=e;return(0,t.jsx)(b,{__scopeCheckbox:s,checked:l,defaultChecked:i,disabled:d,required:n,onCheckedChange:o,name:r,form:m,value:c,internal_do_not_use_render:({isFormControl:e})=>(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(v,{...x,ref:a,__scopeCheckbox:s}),e&&(0,t.jsx)(A,{__scopeCheckbox:s})]})})});N.displayName=p;var y="CheckboxIndicator",k=r.forwardRef((e,a)=>{let{__scopeCheckbox:s,forceMount:r,...l}=e,i=f(y,s);return(0,t.jsx)(m.C,{present:r||I(i.checked)||!0===i.checked,children:(0,t.jsx)(x.sG.span,{"data-state":C(i.checked),"data-disabled":i.disabled?"":void 0,...l,ref:a,style:{pointerEvents:"none",...e.style}})})});k.displayName=y;var w="CheckboxBubbleInput",A=r.forwardRef(({__scopeCheckbox:e,...a},s)=>{let{control:i,hasConsumerStoppedPropagationRef:n,checked:d,defaultChecked:m,required:p,disabled:h,name:u,value:g,form:b,bubbleInput:j,setBubbleInput:v}=f(w,e),N=(0,l.s)(s,v),y=(0,c.Z)(d),k=(0,o.X)(i);r.useEffect(()=>{if(!j)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,a=!n.current;if(y!==d&&e){let s=new Event("click",{bubbles:a});j.indeterminate=I(d),e.call(j,!I(d)&&d),j.dispatchEvent(s)}},[j,y,d,n]);let A=r.useRef(!I(d)&&d);return(0,t.jsx)(x.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:m??A.current,required:p,disabled:h,name:u,value:g,form:b,...a,tabIndex:-1,ref:N,style:{...a.style,...k,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function I(e){return"indeterminate"===e}function C(e){return I(e)?"indeterminate":e?"checked":"unchecked"}A.displayName=w;var z=s(13964),P=s(4780);function S({className:e,...a}){return(0,t.jsx)(N,{"data-slot":"checkbox",className:(0,P.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...a,children:(0,t.jsx)(k,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,t.jsx)(z.A,{className:"size-3.5"})})})}},84281:(e,a,s)=>{Promise.resolve().then(s.bind(s,36439))},89940:(e,a,s)=>{"use strict";s.d(a,{NI:()=>j,Mx:()=>b});var t=s(60687),r=s(87801),l=s(85814),i=s.n(l),n=s(8520),d=s(19526),c=s(72575),o=s(66232);let m=(0,s(62688).A)("linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]]);var x=s(27351),p=s(41550),h=s(48340),u=s(97992),g=s(67760);function f(){let{t:e}=(0,n.B)(),a=new Date().getFullYear(),s=[{href:"#",icon:d.A,label:"Facebook"},{href:"#",icon:c.A,label:"Twitter"},{href:"#",icon:o.A,label:"Instagram"},{href:"#",icon:m,label:"LinkedIn"}];return(0,t.jsx)("footer",{className:"bg-gray-900 text-white mt-16",children:(0,t.jsxs)("div",{className:"container mx-auto px-4 py-12",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(x.A,{className:"h-8 w-8 text-blue-400"}),(0,t.jsx)("span",{className:"text-xl font-bold",children:"Graduation Toqs"})]}),(0,t.jsx)("p",{className:"text-gray-300 arabic-text leading-relaxed",children:"أول منصة مغربية متخصصة في تأجير وبيع أزياء التخرج مع ميزات التخصيص والذكاء الاصطناعي"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[(0,t.jsx)(p.A,{className:"h-4 w-4 text-blue-400"}),(0,t.jsx)("span",{children:"<EMAIL>"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[(0,t.jsx)(h.A,{className:"h-4 w-4 text-blue-400"}),(0,t.jsx)("span",{children:"+212 6 12 34 56 78"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[(0,t.jsx)(u.A,{className:"h-4 w-4 text-blue-400"}),(0,t.jsx)("span",{className:"arabic-text",children:"بني ملال، المغرب"})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-4 arabic-text",children:"الشركة"}),(0,t.jsx)("ul",{className:"space-y-2",children:[{href:"/about",label:"من نحن"},{href:"/contact",label:"تواصل معنا"},{href:"/support",label:"الدعم الفني"},{href:"/privacy",label:"سياسة الخصوصية"}].map(e=>(0,t.jsx)("li",{children:(0,t.jsx)(i(),{href:e.href,className:"text-gray-300 hover:text-blue-400 transition-colors arabic-text",children:e.label})},e.href))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-4 arabic-text",children:"الخدمات"}),(0,t.jsx)("ul",{className:"space-y-2",children:[{href:"/catalog",label:"الكتالوج"},{href:"/customize",label:"التخصيص"},{href:"/track-order",label:"تتبع الطلب"},{href:"/size-guide",label:"دليل المقاسات"}].map(e=>(0,t.jsx)("li",{children:(0,t.jsx)(i(),{href:e.href,className:"text-gray-300 hover:text-blue-400 transition-colors arabic-text",children:e.label})},e.href))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-4 arabic-text",children:"الدعم"}),(0,t.jsx)("ul",{className:"space-y-2",children:[{href:"/faq",label:"الأسئلة الشائعة"},{href:"/terms-conditions",label:"الشروط والأحكام"},{href:"/privacy-policy",label:"سياسة الخصوصية"},{href:"/support",label:"الدعم الفني"}].map(e=>(0,t.jsx)("li",{children:(0,t.jsx)(i(),{href:e.href,className:"text-gray-300 hover:text-blue-400 transition-colors arabic-text",children:e.label})},e.href))})]})]}),(0,t.jsx)("div",{className:"border-t border-gray-800 mt-8 pt-8",children:(0,t.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center gap-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)("span",{className:"text-gray-400 arabic-text",children:"تابعنا على:"}),s.map(e=>{let a=e.icon;return(0,t.jsx)("a",{href:e.href,className:"text-gray-400 hover:text-blue-400 transition-colors","aria-label":e.label,children:(0,t.jsx)(a,{className:"h-5 w-5"})},e.label)})]}),(0,t.jsxs)("div",{className:"text-center md:text-right",children:[(0,t.jsxs)("p",{className:"text-gray-400 text-sm arabic-text",children:["\xa9 ",a," Graduation Toqs. جميع الحقوق محفوظة"]}),(0,t.jsxs)("p",{className:"text-gray-500 text-xs mt-1 flex items-center justify-center md:justify-end gap-1",children:[(0,t.jsx)("span",{className:"arabic-text",children:"صُنع بـ"}),(0,t.jsx)(g.A,{className:"h-3 w-3 text-red-500"}),(0,t.jsx)("span",{className:"arabic-text",children:"في المغرب"})]})]})]})})]})})}function b({children:e,className:a="",showFooter:s=!0,containerClassName:l="container mx-auto px-4 py-8"}){return(0,t.jsxs)("div",{className:`min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 ${a}`,children:[(0,t.jsx)(r.V,{}),(0,t.jsx)("main",{className:l,children:e}),s&&(0,t.jsx)(f,{})]})}function j({children:e,className:a="",title:s,description:l}){return(0,t.jsxs)("div",{className:`min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 ${a}`,children:[(0,t.jsx)(r.V,{}),(0,t.jsxs)("main",{className:"container mx-auto px-4 py-8",children:[(s||l)&&(0,t.jsxs)("div",{className:"mb-8",children:[s&&(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white arabic-text mb-2",children:s}),l&&(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-300 arabic-text",children:l})]}),e]})]})}},93500:(e,a,s)=>{"use strict";s.d(a,{$v:()=>u,EO:()=>m,Lt:()=>d,Rx:()=>g,Zr:()=>f,ck:()=>p,r7:()=>h,wd:()=>x});var t=s(60687),r=s(43210),l=s(97895),i=s(4780),n=s(29523);let d=l.bL;l.l9;let c=l.ZL,o=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)(l.hJ,{className:(0,i.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...a,ref:s}));o.displayName=l.hJ.displayName;let m=r.forwardRef(({className:e,...a},s)=>(0,t.jsxs)(c,{children:[(0,t.jsx)(o,{}),(0,t.jsx)(l.UC,{ref:s,className:(0,i.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...a})]}));m.displayName=l.UC.displayName;let x=({className:e,...a})=>(0,t.jsx)("div",{className:(0,i.cn)("flex flex-col space-y-2 text-center sm:text-left",e),...a});x.displayName="AlertDialogHeader";let p=({className:e,...a})=>(0,t.jsx)("div",{className:(0,i.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...a});p.displayName="AlertDialogFooter";let h=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)(l.hE,{ref:s,className:(0,i.cn)("text-lg font-semibold",e),...a}));h.displayName=l.hE.displayName;let u=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)(l.VY,{ref:s,className:(0,i.cn)("text-sm text-muted-foreground",e),...a}));u.displayName=l.VY.displayName;let g=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)(l.rc,{ref:s,className:(0,i.cn)((0,n.r)(),e),...a}));g.displayName=l.rc.displayName;let f=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)(l.ZD,{ref:s,className:(0,i.cn)((0,n.r)({variant:"outline"}),"mt-2 sm:mt-0",e),...a}));f.displayName=l.ZD.displayName},97992:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])}};var a=require("../../../../webpack-runtime.js");a.C(e);var s=e=>a(a.s=e),t=a.X(0,[4447,8773,4097,2762,5068,3932,7801],()=>s(31274));module.exports=t})();