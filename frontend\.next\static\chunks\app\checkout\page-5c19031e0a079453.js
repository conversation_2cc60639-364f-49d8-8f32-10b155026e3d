(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8279],{47262:(e,a,s)=>{"use strict";s.d(a,{S:()=>n});var t=s(95155);s(12115);var i=s(76981),r=s(5196),l=s(59434);function n(e){let{className:a,...s}=e;return(0,t.jsx)(i.bL,{"data-slot":"checkbox",className:(0,l.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",a),...s,children:(0,t.jsx)(i.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,t.jsx)(r.A,{className:"size-3.5"})})})}},53416:(e,a,s)=>{"use strict";s.d(a,{B:()=>i});let t={fields:[{id:"fullName",name:"fullName",label:"الاسم الكامل",type:"text",required:!0,enabled:!0,placeholder:"أدخل اسمك الكامل",order:1,section:"personal",validation:{minLength:2,maxLength:100}},{id:"email",name:"email",label:"البريد الإلكتروني",type:"email",required:!0,enabled:!0,placeholder:"<EMAIL>",order:2,section:"personal"},{id:"phone",name:"phone",label:"رقم الهاتف",type:"tel",required:!0,enabled:!0,placeholder:"+971-XX-XXX-XXXX",order:3,section:"personal"},{id:"address",name:"address",label:"العنوان",type:"textarea",required:!0,enabled:!0,placeholder:"أدخل عنوانك الكامل",order:4,section:"shipping"},{id:"city",name:"city",label:"المدينة",type:"text",required:!0,enabled:!0,placeholder:"اسم المدينة",order:5,section:"shipping"},{id:"state",name:"state",label:"الإمارة/المنطقة",type:"select",required:!0,enabled:!0,order:6,section:"shipping",options:["أبوظبي","دبي","الشارقة","عجمان","أم القيوين","رأس الخيمة","الفجيرة"]},{id:"zipCode",name:"zipCode",label:"الرمز البريدي",type:"text",required:!1,enabled:!0,placeholder:"12345",order:7,section:"shipping"},{id:"specialInstructions",name:"specialInstructions",label:"تعليمات خاصة",type:"textarea",required:!1,enabled:!0,placeholder:"أي تعليمات خاصة للتوصيل...",order:8,section:"shipping"}],paymentMethods:[{id:"card",name:"بطاقة ائتمان/خصم",description:"Visa, Mastercard, American Express",icon:"CreditCard",enabled:!0,order:1,config:{requiresCard:!0,additionalFields:[{id:"cardNumber",name:"cardNumber",label:"رقم البطاقة",type:"text",required:!0,enabled:!0,placeholder:"1234 5678 9012 3456",order:1,section:"billing"},{id:"expiryDate",name:"expiryDate",label:"تاريخ الانتهاء",type:"text",required:!0,enabled:!0,placeholder:"MM/YY",order:2,section:"billing"},{id:"cvv",name:"cvv",label:"رمز الأمان",type:"text",required:!0,enabled:!0,placeholder:"123",order:3,section:"billing"}]}},{id:"cash",name:"الدفع عند الاستلام",description:"ادفع نقداً عند وصول الطلب",icon:"Banknote",enabled:!0,order:2},{id:"bank_transfer",name:"تحويل بنكي",description:"تحويل مباشر إلى حساب البنك",icon:"Building2",enabled:!0,order:3},{id:"digital_wallet",name:"المحفظة الرقمية",description:"Apple Pay, Google Pay, Samsung Pay",icon:"Smartphone",enabled:!1,order:4}],deliveryOptions:[{id:"standard",name:"التوصيل العادي",description:"3-5 أيام عمل",price:25,estimatedDays:"3-5 أيام",enabled:!0,order:1,icon:"Truck"},{id:"express",name:"التوصيل السريع",description:"1-2 أيام عمل",price:50,estimatedDays:"1-2 أيام",enabled:!0,order:2,icon:"Zap",restrictions:{minOrderValue:100}},{id:"same_day",name:"التوصيل في نفس اليوم",description:"خلال 6 ساعات",price:100,estimatedDays:"6 ساعات",enabled:!0,order:3,icon:"Clock",restrictions:{minOrderValue:200,availableRegions:["دبي","أبوظبي"]}},{id:"pickup",name:"الاستلام من المتجر",description:"استلم طلبك من فرعنا",price:0,estimatedDays:"فوري",enabled:!1,order:4,icon:"Store"}],general:{requireTermsAcceptance:!0,allowGuestCheckout:!0,showOrderSummary:!0,enableSpecialInstructions:!0,defaultCountry:"الإمارات العربية المتحدة",currency:"AED",taxRate:.05}};class i{static getStorageKey(){return"checkoutSettings"}static getSettings(){let e=localStorage.getItem(this.getStorageKey());return e?JSON.parse(e):t}static saveSettings(e){localStorage.setItem(this.getStorageKey(),JSON.stringify(e))}static resetToDefaults(){this.saveSettings(t)}static addField(e){let a=this.getSettings();a.fields.push(e),this.saveSettings(a)}static updateField(e,a){let s=this.getSettings(),t=s.fields.findIndex(a=>a.id===e);-1!==t&&(s.fields[t]={...s.fields[t],...a},this.saveSettings(s))}static removeField(e){let a=this.getSettings();a.fields=a.fields.filter(a=>a.id!==e),this.saveSettings(a)}static updatePaymentMethod(e,a){let s=this.getSettings(),t=s.paymentMethods.findIndex(a=>a.id===e);-1!==t&&(s.paymentMethods[t]={...s.paymentMethods[t],...a},this.saveSettings(s))}static updateDeliveryOption(e,a){let s=this.getSettings(),t=s.deliveryOptions.findIndex(a=>a.id===e);-1!==t&&(s.deliveryOptions[t]={...s.deliveryOptions[t],...a},this.saveSettings(s))}}},59409:(e,a,s)=>{"use strict";s.d(a,{bq:()=>x,eb:()=>u,gC:()=>m,l6:()=>c,yv:()=>o});var t=s(95155);s(12115);var i=s(31992),r=s(66474),l=s(5196),n=s(47863),d=s(59434);function c(e){let{...a}=e;return(0,t.jsx)(i.bL,{"data-slot":"select",...a})}function o(e){let{...a}=e;return(0,t.jsx)(i.WT,{"data-slot":"select-value",...a})}function x(e){let{className:a,size:s="default",children:l,...n}=e;return(0,t.jsxs)(i.l9,{"data-slot":"select-trigger","data-size":s,className:(0,d.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...n,children:[l,(0,t.jsx)(i.In,{asChild:!0,children:(0,t.jsx)(r.A,{className:"size-4 opacity-50"})})]})}function m(e){let{className:a,children:s,position:r="popper",...l}=e;return(0,t.jsx)(i.ZL,{children:(0,t.jsxs)(i.UC,{"data-slot":"select-content",className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:r,...l,children:[(0,t.jsx)(h,{}),(0,t.jsx)(i.LM,{className:(0,d.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:s}),(0,t.jsx)(p,{})]})})}function u(e){let{className:a,children:s,...r}=e;return(0,t.jsxs)(i.q7,{"data-slot":"select-item",className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",a),...r,children:[(0,t.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,t.jsx)(i.VF,{children:(0,t.jsx)(l.A,{className:"size-4"})})}),(0,t.jsx)(i.p4,{children:s})]})}function h(e){let{className:a,...s}=e;return(0,t.jsx)(i.PP,{"data-slot":"select-scroll-up-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",a),...s,children:(0,t.jsx)(n.A,{className:"size-4"})})}function p(e){let{className:a,...s}=e;return(0,t.jsx)(i.wn,{"data-slot":"select-scroll-down-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",a),...s,children:(0,t.jsx)(r.A,{className:"size-4"})})}},62523:(e,a,s)=>{"use strict";s.d(a,{p:()=>r});var t=s(95155);s(12115);var i=s(59434);function r(e){let{className:a,type:s,...r}=e;return(0,t.jsx)("input",{type:s,"data-slot":"input",className:(0,i.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",a),...r})}},78157:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>O});var t=s(95155),i=s(12115),r=s(40283),l=s(5323),n=s(86566),d=s(53416),c=s(62523),o=s(85057),x=s(88539),m=s(59409),u=s(47262);function h(e){let{field:a,value:s,onChange:i,error:r}=e;return a.enabled?(0,t.jsxs)("div",{className:"space-y-2",children:["checkbox"!==a.type&&(0,t.jsxs)(o.J,{htmlFor:a.id,className:"arabic-text",children:[a.label,a.required&&(0,t.jsx)("span",{className:"text-red-500 mr-1",children:"*"})]}),(()=>{switch(a.type){case"text":case"email":case"tel":return(0,t.jsx)(c.p,{type:a.type,id:a.id,value:s||"",onChange:e=>i(e.target.value),placeholder:a.placeholder,required:a.required,className:r?"border-red-500":""});case"textarea":return(0,t.jsx)(x.T,{id:a.id,value:s||"",onChange:e=>i(e.target.value),placeholder:a.placeholder,required:a.required,className:r?"border-red-500":"",rows:3});case"select":var e;return(0,t.jsxs)(m.l6,{value:s||"",onValueChange:i,children:[(0,t.jsx)(m.bq,{className:r?"border-red-500":"",children:(0,t.jsx)(m.yv,{placeholder:a.placeholder||"اختر ".concat(a.label)})}),(0,t.jsx)(m.gC,{children:null==(e=a.options)?void 0:e.map(e=>(0,t.jsx)(m.eb,{value:e,children:e},e))})]});case"checkbox":return(0,t.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse",children:[(0,t.jsx)(u.S,{id:a.id,checked:s||!1,onCheckedChange:i}),(0,t.jsx)(o.J,{htmlFor:a.id,className:"text-sm arabic-text",children:a.placeholder||a.label})]});default:return null}})(),r&&(0,t.jsx)("p",{className:"text-sm text-red-500 arabic-text",children:r})]}):null}var p=s(66695),b=s(30285),g=s(22346),j=s(54059),f=s(9428),v=s(59434);function y(e){let{className:a,...s}=e;return(0,t.jsx)(j.bL,{"data-slot":"radio-group",className:(0,v.cn)("grid gap-3",a),...s})}function N(e){let{className:a,...s}=e;return(0,t.jsx)(j.q7,{"data-slot":"radio-group-item",className:(0,v.cn)("border-input text-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 aspect-square size-4 shrink-0 rounded-full border shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",a),...s,children:(0,t.jsx)(j.C1,{"data-slot":"radio-group-indicator",className:"relative flex items-center justify-center",children:(0,t.jsx)(f.A,{className:"fill-primary absolute top-1/2 left-1/2 size-2 -translate-x-1/2 -translate-y-1/2"})})})}var w=s(4516),k=s(29799),C=s(81586),S=s(40646),A=s(35169),q=s(71007),z=s(92138),_=s(75525);function O(){let{user:e,profile:a}=(0,r.A)(),{cartItems:s,getCartTotal:c}=(0,l._)(),[m,j]=(0,i.useState)(null),[f,v]=(0,i.useState)(1),[O,T]=(0,i.useState)({}),[D,M]=(0,i.useState)(""),[B,F]=(0,i.useState)(""),[Z,V]=(0,i.useState)(""),[I,J]=(0,i.useState)(!1),[P,X]=(0,i.useState)(!1),R={items:[{name:"فستان تخرج كلاسيكي",quantity:1,price:450},{name:"قبعة التخرج",quantity:1,price:75}],subtotal:525,tax:26.25,shipping:25,total:576.25};(0,i.useEffect)(()=>{(async()=>{try{let e=await d.B.getSettings();if(j(e),e.deliveryOptions.length>0){let a=e.deliveryOptions.find(e=>e.enabled);a&&M(a.id)}if(e.paymentMethods.length>0){let a=e.paymentMethods.find(e=>e.enabled);a&&F(a.id)}}catch(e){console.error("Error loading checkout settings:",e)}})()},[]);let $=(e,a)=>{T(s=>({...s,[e]:a}))},E=()=>{if(!m)return!1;for(let e of m.fields.filter(e=>e.required&&e.enabled))if(!O[e.id]||""===O[e.id].toString().trim())return alert("الرجاء إدخال ".concat(e.label)),!1;return D?B?!m.general.requireTermsAcceptance||!!I||(alert("يرجى الموافقة على الشروط والأحكام"),!1):(alert("الرجاء اختيار طريقة الدفع"),!1):(alert("الرجاء اختيار طريقة التوصيل"),!1)},L=async()=>{E()&&(X(!0),setTimeout(()=>{if(X(!1),"bank_transfer"===B){let e="ORD-"+Date.now(),a=R.total;window.location.href="/payment/bank-transfer?order_id=".concat(e,"&amount=").concat(a)}else window.location.href="/order-confirmation"},2e3))},W=[{id:1,name:"معلومات الشحن",icon:"MapPin"},{id:2,name:"طريقة التوصيل",icon:"Truck"},{id:3,name:"طريقة الدفع",icon:"CreditCard"},{id:4,name:"مراجعة الطلب",icon:"CheckCircle"}],K=e=>{switch(e){case"MapPin":return(0,t.jsx)(w.A,{className:"h-4 w-4"});case"Truck":return(0,t.jsx)(k.A,{className:"h-4 w-4"});case"CreditCard":return(0,t.jsx)(C.A,{className:"h-4 w-4"});default:return(0,t.jsx)(S.A,{className:"h-4 w-4"})}};return m?(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",children:[(0,t.jsx)(n.V,{}),(0,t.jsxs)("main",{className:"container mx-auto px-4 py-8",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)(b.$,{variant:"outline",size:"sm",asChild:!0,className:"mb-4",children:(0,t.jsxs)("a",{href:"/cart",children:[(0,t.jsx)(A.A,{className:"h-4 w-4 mr-2"}),"العودة للسلة"]})}),(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white arabic-text",children:"إتمام الطلب \uD83D\uDCB3"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-300 mt-2 arabic-text",children:"أكمل معلوماتك لإتمام عملية الشراء"})]}),(0,t.jsx)("div",{className:"mb-8",children:(0,t.jsx)("div",{className:"flex items-center justify-between",children:W.map((e,a)=>(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex items-center justify-center w-10 h-10 rounded-full border-2 ".concat(f>=e.id?"bg-blue-600 border-blue-600 text-white":"border-gray-300 text-gray-400"),children:f>e.id?(0,t.jsx)(S.A,{className:"h-5 w-5"}):K(e.icon)}),(0,t.jsx)("span",{className:"ml-3 text-sm font-medium arabic-text ".concat(f>=e.id?"text-blue-600":"text-gray-500"),children:e.name}),a<W.length-1&&(0,t.jsx)("div",{className:"w-16 h-0.5 mx-4 ".concat(f>e.id?"bg-blue-600":"bg-gray-300")})]},e.id))})}),(0,t.jsxs)("div",{className:"grid lg:grid-cols-3 gap-8",children:[(0,t.jsxs)("div",{className:"lg:col-span-2",children:[1===f&&(0,t.jsxs)(p.Zp,{children:[(0,t.jsxs)(p.aR,{children:[(0,t.jsxs)(p.ZB,{className:"flex items-center gap-2 arabic-text",children:[(0,t.jsx)(q.A,{className:"h-5 w-5"}),"معلومات العميل"]}),(0,t.jsx)(p.BT,{className:"arabic-text",children:"أدخل معلوماتك الشخصية ومعلومات التوصيل"})]}),(0,t.jsxs)(p.Wu,{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white arabic-text mb-4",children:"المعلومات الشخصية"}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:m.fields.filter(e=>"personal"===e.section&&e.enabled).sort((e,a)=>e.order-a.order).map(e=>(0,t.jsx)(h,{field:e,value:O[e.id],onChange:a=>$(e.id,a)},e.id))})]}),(0,t.jsx)(g.w,{}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white arabic-text mb-4",children:"معلومات التوصيل"}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:m.fields.filter(e=>"shipping"===e.section&&e.enabled).sort((e,a)=>e.order-a.order).map(e=>(0,t.jsx)(h,{field:e,value:O[e.id],onChange:a=>$(e.id,a)},e.id))})]}),(0,t.jsx)("div",{className:"flex justify-end",children:(0,t.jsxs)(b.$,{onClick:()=>v(2),className:"arabic-text",children:["التالي",(0,t.jsx)(A.A,{className:"h-4 w-4 mr-2"})]})})]})]}),2===f&&(0,t.jsxs)(p.Zp,{children:[(0,t.jsxs)(p.aR,{children:[(0,t.jsxs)(p.ZB,{className:"flex items-center gap-2 arabic-text",children:[(0,t.jsx)(k.A,{className:"h-5 w-5"}),"طريقة التوصيل"]}),(0,t.jsx)(p.BT,{className:"arabic-text",children:"اختر طريقة التوصيل المناسبة لك"})]}),(0,t.jsxs)(p.Wu,{children:[(0,t.jsx)(y,{value:D,onValueChange:M,children:(0,t.jsx)("div",{className:"space-y-4",children:m.deliveryOptions.filter(e=>e.enabled).sort((e,a)=>e.order-a.order).map(e=>{var a;return(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(N,{value:e.id,id:e.id}),(0,t.jsx)(o.J,{htmlFor:e.id,className:"flex-1 cursor-pointer",children:(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(k.A,{className:"h-5 w-5 text-blue-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium arabic-text",children:e.name}),(0,t.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:[e.description," - ",e.estimatedDays]}),(null==(a=e.restrictions)?void 0:a.minOrderValue)&&(0,t.jsxs)("p",{className:"text-xs text-orange-600 arabic-text",children:["الحد الأدنى للطلب: ",e.restrictions.minOrderValue," درهم"]})]})]}),(0,t.jsx)("span",{className:"font-bold text-green-600",children:0===e.price?"مجاني":"".concat(e.price," درهم")})]})})]},e.id)})})}),m.general.enableSpecialInstructions&&(0,t.jsxs)("div",{className:"mt-6",children:[(0,t.jsx)(o.J,{htmlFor:"instructions",className:"arabic-text",children:"تعليمات خاصة (اختياري)"}),(0,t.jsx)(x.T,{id:"instructions",value:Z,onChange:e=>V(e.target.value),placeholder:"أي تعليمات خاصة للتوصيل...",className:"arabic-text"})]}),(0,t.jsxs)("div",{className:"flex justify-between mt-6",children:[(0,t.jsxs)(b.$,{variant:"outline",onClick:()=>v(1),className:"arabic-text",children:[(0,t.jsx)(z.A,{className:"h-4 w-4 ml-2"}),"السابق"]}),(0,t.jsxs)(b.$,{onClick:()=>v(3),className:"arabic-text",children:["التالي",(0,t.jsx)(A.A,{className:"h-4 w-4 mr-2"})]})]})]})]}),3===f&&(0,t.jsxs)(p.Zp,{children:[(0,t.jsxs)(p.aR,{children:[(0,t.jsxs)(p.ZB,{className:"flex items-center gap-2 arabic-text",children:[(0,t.jsx)(C.A,{className:"h-5 w-5"}),"طريقة الدفع"]}),(0,t.jsx)(p.BT,{className:"arabic-text",children:"اختر طريقة الدفع المفضلة لديك"})]}),(0,t.jsxs)(p.Wu,{children:[(0,t.jsx)(y,{value:B,onValueChange:F,children:(0,t.jsx)("div",{className:"space-y-4",children:m.paymentMethods.filter(e=>e.enabled).sort((e,a)=>e.order-a.order).map(e=>(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(N,{value:e.id,id:e.id}),(0,t.jsx)(o.J,{htmlFor:e.id,className:"flex-1 cursor-pointer",children:(0,t.jsxs)("div",{className:"flex items-center gap-3 p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800",children:[(0,t.jsx)(C.A,{className:"h-5 w-5 text-blue-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium arabic-text",children:e.name}),(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:e.description})]})]})})]},e.id))})}),(()=>{var e;let a=m.paymentMethods.find(e=>e.id===B);return(null==a||null==(e=a.config)?void 0:e.additionalFields)?(0,t.jsxs)("div",{className:"mt-6 space-y-4 p-4 border rounded-lg bg-gray-50 dark:bg-gray-800",children:[(0,t.jsx)("h3",{className:"font-medium arabic-text",children:"معلومات الدفع"}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:a.config.additionalFields.map(e=>(0,t.jsx)(h,{field:e,value:O[e.id],onChange:a=>$(e.id,a)},e.id))})]}):null})(),(0,t.jsxs)("div",{className:"flex justify-between mt-6",children:[(0,t.jsxs)(b.$,{variant:"outline",onClick:()=>v(2),className:"arabic-text",children:[(0,t.jsx)(z.A,{className:"h-4 w-4 ml-2"}),"السابق"]}),(0,t.jsxs)(b.$,{onClick:()=>v(4),className:"arabic-text",children:["التالي",(0,t.jsx)(A.A,{className:"h-4 w-4 mr-2"})]})]})]})]}),4===f&&(0,t.jsxs)(p.Zp,{children:[(0,t.jsxs)(p.aR,{children:[(0,t.jsxs)(p.ZB,{className:"flex items-center gap-2 arabic-text",children:[(0,t.jsx)(S.A,{className:"h-5 w-5"}),"مراجعة الطلب"]}),(0,t.jsx)(p.BT,{className:"arabic-text",children:"راجع تفاصيل طلبك قبل التأكيد"})]}),(0,t.jsxs)(p.Wu,{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium mb-3 arabic-text",children:"المنتجات"}),(0,t.jsx)("div",{className:"space-y-2",children:R.items.map((e,a)=>(0,t.jsxs)("div",{className:"flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg",children:[(0,t.jsxs)("span",{className:"arabic-text",children:[e.name," \xd7 ",e.quantity]}),(0,t.jsxs)("span",{children:[e.price," درهم"]})]},a))})]}),m.general.requireTermsAcceptance&&(0,t.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse",children:[(0,t.jsx)(u.S,{id:"terms",checked:I,onCheckedChange:J}),(0,t.jsxs)(o.J,{htmlFor:"terms",className:"text-sm arabic-text",children:["أوافق على ",(0,t.jsx)("a",{href:"/terms",className:"text-blue-600 hover:underline",children:"الشروط والأحكام"})," و",(0,t.jsx)("a",{href:"/privacy",className:"text-blue-600 hover:underline",children:"سياسة الخصوصية"})]})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsxs)(b.$,{variant:"outline",onClick:()=>v(3),className:"arabic-text",children:[(0,t.jsx)(z.A,{className:"h-4 w-4 ml-2"}),"السابق"]}),(0,t.jsxs)(b.$,{onClick:L,disabled:m.general.requireTermsAcceptance&&!I||P,className:"arabic-text",children:[P?"جاري المعالجة...":"تأكيد الطلب",(0,t.jsx)(S.A,{className:"h-4 w-4 mr-2"})]})]})]})]})]}),m.general.showOrderSummary&&(0,t.jsx)("div",{className:"lg:col-span-1",children:(0,t.jsxs)(p.Zp,{className:"sticky top-24",children:[(0,t.jsx)(p.aR,{children:(0,t.jsx)(p.ZB,{className:"arabic-text",children:"ملخص الطلب"})}),(0,t.jsxs)(p.Wu,{className:"space-y-4",children:[(0,t.jsx)("div",{className:"space-y-2",children:R.items.map((e,a)=>(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,t.jsxs)("span",{className:"arabic-text",children:[e.name," \xd7 ",e.quantity]}),(0,t.jsxs)("span",{children:[e.price," درهم"]})]},a))}),(0,t.jsx)(g.w,{}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"arabic-text",children:"المجموع الفرعي:"}),(0,t.jsxs)("span",{children:[R.subtotal," درهم"]})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"arabic-text",children:"الضريبة:"}),(0,t.jsxs)("span",{children:[R.tax," درهم"]})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"arabic-text",children:"الشحن:"}),(0,t.jsxs)("span",{children:[R.shipping," درهم"]})]})]}),(0,t.jsx)(g.w,{}),(0,t.jsxs)("div",{className:"flex justify-between font-bold text-lg",children:[(0,t.jsx)("span",{className:"arabic-text",children:"الإجمالي:"}),(0,t.jsxs)("span",{children:[R.total," درهم"]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400",children:[(0,t.jsx)(_.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"arabic-text",children:"دفع آمن ومضمون"})]})]})]})})]})]})]}):(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",children:[(0,t.jsx)(n.V,{}),(0,t.jsx)("main",{className:"container mx-auto px-4 py-8",children:(0,t.jsxs)("div",{className:"text-center py-16",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"}),(0,t.jsx)("p",{className:"mt-4 text-gray-600 dark:text-gray-400",children:"جاري تحميل صفحة الدفع..."})]})})]})}},81093:(e,a,s)=>{Promise.resolve().then(s.bind(s,78157))},85057:(e,a,s)=>{"use strict";s.d(a,{J:()=>l});var t=s(95155);s(12115);var i=s(40968),r=s(59434);function l(e){let{className:a,...s}=e;return(0,t.jsx)(i.b,{"data-slot":"label",className:(0,r.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...s})}},88539:(e,a,s)=>{"use strict";s.d(a,{T:()=>r});var t=s(95155);s(12115);var i=s(59434);function r(e){let{className:a,...s}=e;return(0,t.jsx)("textarea",{"data-slot":"textarea",className:(0,i.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),...s})}}},e=>{var a=a=>e(e.s=a);e.O(0,[7598,5486,380,2433,6874,8698,6671,7889,9221,4358,7277,2632,3898,6566,8441,1684,7358],()=>a(81093)),_N_E=e.O()}]);