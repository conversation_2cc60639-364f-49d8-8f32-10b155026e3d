(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[159],{17313:(e,a,r)=>{"use strict";r.d(a,{Xi:()=>l,av:()=>c,j7:()=>d,tU:()=>n});var t=r(95155);r(12115);var s=r(60704),i=r(59434);function n(e){let{className:a,...r}=e;return(0,t.jsx)(s.bL,{"data-slot":"tabs",className:(0,i.cn)("flex flex-col gap-2",a),...r})}function d(e){let{className:a,...r}=e;return(0,t.jsx)(s.B8,{"data-slot":"tabs-list",className:(0,i.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",a),...r})}function l(e){let{className:a,...r}=e;return(0,t.jsx)(s.l9,{"data-slot":"tabs-trigger",className:(0,i.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...r})}function c(e){let{className:a,...r}=e;return(0,t.jsx)(s.UC,{"data-slot":"tabs-content",className:(0,i.cn)("flex-1 outline-none",a),...r})}},22346:(e,a,r)=>{"use strict";r.d(a,{w:()=>n});var t=r(95155);r(12115);var s=r(87489),i=r(59434);function n(e){let{className:a,orientation:r="horizontal",decorative:n=!0,...d}=e;return(0,t.jsx)(s.b,{"data-slot":"separator",decorative:n,orientation:r,className:(0,i.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",a),...d})}},26126:(e,a,r)=>{"use strict";r.d(a,{E:()=>l});var t=r(95155);r(12115);var s=r(99708),i=r(74466),n=r(59434);let d=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:a,variant:r,asChild:i=!1,...l}=e,c=i?s.DX:"span";return(0,t.jsx)(c,{"data-slot":"badge",className:(0,n.cn)(d({variant:r}),a),...l})}},30285:(e,a,r)=>{"use strict";r.d(a,{$:()=>l,r:()=>d});var t=r(95155);r(12115);var s=r(99708),i=r(74466),n=r(59434);let d=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:a,variant:r,size:i,asChild:l=!1,...c}=e,o=l?s.DX:"button";return(0,t.jsx)(o,{"data-slot":"button",className:(0,n.cn)(d({variant:r,size:i,className:a})),...c})}},40283:(e,a,r)=>{"use strict";r.d(a,{A:()=>l,AuthProvider:()=>d});var t=r(95155),s=r(12115),i=r(59385);let n=(0,s.createContext)(void 0);function d(e){let{children:a}=e,[r,d]=(0,s.useState)(null),[l,c]=(0,s.useState)(null),[o,u]=(0,s.useState)(!0),[m,x]=(0,s.useState)(!1);(0,s.useEffect)(()=>{x(!0)},[]),(0,s.useEffect)(()=>{m&&(async()=>{try{let e=localStorage.getItem("mockUser"),a=localStorage.getItem("mockProfile");if(e&&a){let r=JSON.parse(e),t=JSON.parse(a);if(r&&t&&r.id&&t.id){let e=localStorage.getItem("sessionTimestamp"),a=Date.now(),s=e?a-parseInt(e):0;e&&s<864e5?(d(r),c(t),console.log("User data loaded from localStorage:",{userData:r,profileData:t})):(console.log("Session expired, clearing user data"),localStorage.removeItem("mockUser"),localStorage.removeItem("mockProfile"),localStorage.removeItem("sessionTimestamp"))}else localStorage.removeItem("mockUser"),localStorage.removeItem("mockProfile"),localStorage.removeItem("sessionTimestamp")}}catch(e){console.error("Error loading user from localStorage:",e),localStorage.removeItem("mockUser"),localStorage.removeItem("mockProfile")}finally{u(!1)}})()},[m]),(0,s.useEffect)(()=>{if(!r||!l)return;let e=()=>{try{localStorage.setItem("sessionTimestamp",Date.now().toString())}catch(e){console.error("Error refreshing session:",e)}},a=["click","keypress","scroll","mousemove"];return a.forEach(a=>{document.addEventListener(a,e,{passive:!0})}),()=>{a.forEach(a=>{document.removeEventListener(a,e)})}},[r,l]);let h=async(e,a,r)=>(console.log("Sign up:",e,r),{data:{user:{id:"1",email:e}},error:null}),g=async(e,a)=>{console.log("Sign in:",e);let r={id:"1",email:e},t=i.gG.STUDENT;e.includes("admin")?t=i.gG.ADMIN:e.includes("school")?t=i.gG.SCHOOL:e.includes("delivery")&&(t=i.gG.DELIVERY);let s={id:"1",email:e,full_name:e.split("@")[0]||"مستخدم",role:t,created_at:new Date().toISOString(),updated_at:new Date().toISOString()};d(r),c(s);try{localStorage.setItem("mockUser",JSON.stringify(r)),localStorage.setItem("mockProfile",JSON.stringify(s)),localStorage.setItem("sessionTimestamp",Date.now().toString()),console.log("User data saved to localStorage:",{mockUser:r,mockProfile:s})}catch(e){console.error("Error saving user data to localStorage:",e)}return setTimeout(()=>{t===i.gG.ADMIN?window.location.href="/dashboard/admin":t===i.gG.SCHOOL?window.location.href="/dashboard/school":t===i.gG.DELIVERY?window.location.href="/dashboard/delivery":window.location.href="/dashboard/student"},100),{data:{user:r},error:null}},f=async()=>{try{return d(null),c(null),localStorage.removeItem("mockUser"),localStorage.removeItem("mockProfile"),localStorage.removeItem("sessionTimestamp"),console.log("User data cleared from localStorage"),{error:null}}catch(e){return console.error("Error during sign out:",e),{error:"فشل في تسجيل الخروج"}}},v=async e=>{if(!r)return{data:null,error:"No user logged in"};let a={...l,...e};return c(a),{data:a,error:null}};return(0,t.jsx)(n.Provider,{value:{user:r,profile:l,loading:o,signUp:h,signIn:g,signOut:f,updateProfile:v,hasRole:e=>!!l&&(0,i.ly)(l.role,e)},children:a})}function l(){let e=(0,s.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},43269:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>I});var t=r(95155),s=r(12115),i=r(40283),n=r(66695),d=r(30285),l=r(62523),c=r(85057),o=r(80333),u=r(88539),m=r(26126),x=r(22346),h=r(17313),g=r(81586),f=r(48136),v=r(29799),b=r(381),p=r(35169),j=r(13717),y=r(84616),N=r(62525),w=r(4229),k=r(56671),_=r(6874),S=r.n(_);let A=[{id:"1",name:"الدفع عند الاستلام",type:"cash_on_delivery",enabled:!0,configuration:{max_amount:2e3,available_cities:["بني ملال","خنيفرة","الفقيه بن صالح"]},fees:{fixed_fee:0,percentage_fee:0},description:"الدفع نقداً عند استلام الطلب"},{id:"2",name:"التحويل البنكي",type:"bank_transfer",enabled:!0,configuration:{processing_time:"2-4 ساعات عمل",requires_proof:!0},fees:{fixed_fee:0,percentage_fee:0},description:"تحويل مباشر إلى الحساب البنكي"},{id:"3",name:"البطاقة الائتمانية",type:"credit_card",enabled:!1,configuration:{supported_cards:["Visa","Mastercard"],requires_3ds:!0},fees:{fixed_fee:5,percentage_fee:2.5},description:"الدفع بالبطاقة الائتمانية (قريباً)"}],E=[{id:"1",bank_name:"البنك الشعبي المغربي",account_holder:"منصة أزياء التخرج المغربية",account_number:"****************",rib:"236 000 ********** 12",swift:"BMCEMAMC",branch:"بني ملال",enabled:!0},{id:"2",bank_name:"بنك المغرب",account_holder:"منصة أزياء التخرج المغربية",account_number:"011000**********",rib:"011 000 ********** 34",swift:"BMCEMAMC",branch:"خنيفرة",enabled:!0}];function I(){let{user:e,profile:a}=(0,i.A)(),[r,_]=(0,s.useState)(A),[I,C]=(0,s.useState)(E),[D,z]=(0,s.useState)(!1),[J,O]=(0,s.useState)(null),[T,U]=(0,s.useState)(null);(0,s.useEffect)(()=>{if(!e||!a||"admin"!==a.role){window.location.href="/dashboard";return}},[e,a]);let V=async e=>{_(a=>a.map(a=>a.id===e?{...a,enabled:!a.enabled}:a)),k.o.success("تم تحديث حالة طريقة الدفع")},F=async e=>{C(a=>a.map(a=>a.id===e?{...a,enabled:!a.enabled}:a)),k.o.success("تم تحديث حالة الحساب البنكي")},P=async()=>{z(!0);try{await new Promise(e=>setTimeout(e,1e3)),k.o.success("تم حفظ الإعدادات بنجاح")}catch(e){k.o.error("حدث خطأ أثناء حفظ الإعدادات")}finally{z(!1)}},M=e=>{switch(e){case"credit_card":return(0,t.jsx)(g.A,{className:"h-5 w-5"});case"bank_transfer":return(0,t.jsx)(f.A,{className:"h-5 w-5"});case"cash_on_delivery":return(0,t.jsx)(v.A,{className:"h-5 w-5"});default:return(0,t.jsx)(b.A,{className:"h-5 w-5"})}};return(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",children:[(0,t.jsx)("nav",{className:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700",children:(0,t.jsx)("div",{className:"container mx-auto px-4",children:(0,t.jsx)("div",{className:"flex items-center justify-between h-16",children:(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)(d.$,{variant:"outline",size:"sm",asChild:!0,children:(0,t.jsxs)(S(),{href:"/dashboard/admin",children:[(0,t.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"العودة للوحة التحكم"]})}),(0,t.jsx)("div",{className:"h-6 w-px bg-gray-300 dark:bg-gray-600"}),(0,t.jsx)(d.$,{variant:"ghost",size:"sm",asChild:!0,children:(0,t.jsx)(S(),{href:"/",children:"الصفحة الرئيسية"})})]})})})}),(0,t.jsxs)("main",{className:"container mx-auto px-4 py-8",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-2 arabic-text",children:"إدارة طرق الدفع \uD83D\uDCB3"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-400 arabic-text",children:"إدارة وتكوين طرق الدفع المتاحة للعملاء"})]}),(0,t.jsxs)(h.tU,{defaultValue:"methods",className:"space-y-6",children:[(0,t.jsxs)(h.j7,{className:"grid w-full grid-cols-3",children:[(0,t.jsxs)(h.Xi,{value:"methods",className:"arabic-text",children:[(0,t.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"طرق الدفع"]}),(0,t.jsxs)(h.Xi,{value:"bank-accounts",className:"arabic-text",children:[(0,t.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"الحسابات البنكية"]}),(0,t.jsxs)(h.Xi,{value:"settings",className:"arabic-text",children:[(0,t.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"الإعدادات العامة"]})]}),(0,t.jsx)(h.av,{value:"methods",className:"space-y-6",children:(0,t.jsx)("div",{className:"grid gap-6",children:r.map(e=>(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[M(e.type),(0,t.jsxs)("div",{children:[(0,t.jsx)(n.ZB,{className:"arabic-text",children:e.name}),(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:e.description})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(m.E,{variant:e.enabled?"default":"secondary",children:e.enabled?"مفعل":"معطل"}),(0,t.jsx)(o.d,{checked:e.enabled,onCheckedChange:()=>V(e.id)})]})]})}),(0,t.jsxs)(n.Wu,{children:[(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium mb-2 arabic-text",children:"الرسوم:"}),(0,t.jsxs)("div",{className:"space-y-1 text-sm",children:[(0,t.jsxs)("p",{children:["رسوم ثابتة: ",e.fees.fixed_fee," Dhs"]}),(0,t.jsxs)("p",{children:["رسوم نسبية: ",e.fees.percentage_fee,"%"]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium mb-2 arabic-text",children:"التكوين:"}),(0,t.jsx)("div",{className:"space-y-1 text-sm text-gray-600 dark:text-gray-400",children:Object.entries(e.configuration).map(e=>{let[a,r]=e;return(0,t.jsxs)("p",{children:[a,": ",Array.isArray(r)?r.join(", "):String(r)]},a)})})]})]}),(0,t.jsx)("div",{className:"flex gap-2 mt-4",children:(0,t.jsxs)(d.$,{size:"sm",variant:"outline",onClick:()=>O(e),children:[(0,t.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"تعديل"]})})]})]},e.id))})}),(0,t.jsxs)(h.av,{value:"bank-accounts",className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("h3",{className:"text-xl font-semibold arabic-text",children:"الحسابات البنكية"}),(0,t.jsxs)(d.$,{onClick:()=>U({id:"",bank_name:"",account_holder:"",account_number:"",rib:"",swift:"",branch:"",enabled:!0}),children:[(0,t.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"إضافة حساب جديد"]})]}),(0,t.jsx)("div",{className:"grid gap-6",children:I.map(e=>(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(f.A,{className:"h-5 w-5 text-blue-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)(n.ZB,{className:"arabic-text",children:e.bank_name}),(0,t.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:["الفرع: ",e.branch]})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(m.E,{variant:e.enabled?"default":"secondary",children:e.enabled?"مفعل":"معطل"}),(0,t.jsx)(o.d,{checked:e.enabled,onCheckedChange:()=>F(e.id)})]})]})}),(0,t.jsxs)(n.Wu,{children:[(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(c.J,{className:"text-sm font-medium",children:"صاحب الحساب:"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:e.account_holder})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(c.J,{className:"text-sm font-medium",children:"رقم الحساب:"}),(0,t.jsx)("p",{className:"text-sm font-mono text-gray-600 dark:text-gray-400",children:e.account_number})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(c.J,{className:"text-sm font-medium",children:"RIB:"}),(0,t.jsx)("p",{className:"text-sm font-mono text-gray-600 dark:text-gray-400",children:e.rib})]}),e.swift&&(0,t.jsxs)("div",{children:[(0,t.jsx)(c.J,{className:"text-sm font-medium",children:"SWIFT:"}),(0,t.jsx)("p",{className:"text-sm font-mono text-gray-600 dark:text-gray-400",children:e.swift})]})]})]}),(0,t.jsxs)("div",{className:"flex gap-2 mt-4",children:[(0,t.jsxs)(d.$,{size:"sm",variant:"outline",onClick:()=>U(e),children:[(0,t.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"تعديل"]}),(0,t.jsxs)(d.$,{size:"sm",variant:"outline",className:"text-red-600 hover:text-red-700",children:[(0,t.jsx)(N.A,{className:"h-4 w-4 mr-2"}),"حذف"]})]})]})]},e.id))})]}),(0,t.jsx)(h.av,{value:"settings",className:"space-y-6",children:(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsx)(n.ZB,{className:"arabic-text",children:"الإعدادات العامة للدفع"})}),(0,t.jsxs)(n.Wu,{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(c.J,{htmlFor:"default_currency",className:"arabic-text",children:"العملة الافتراضية"}),(0,t.jsx)(l.p,{id:"default_currency",value:"MAD",readOnly:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(c.J,{htmlFor:"tax_rate",className:"arabic-text",children:"معدل الضريبة (%)"}),(0,t.jsx)(l.p,{id:"tax_rate",type:"number",defaultValue:"20"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(c.J,{htmlFor:"min_order_amount",className:"arabic-text",children:"الحد الأدنى للطلب (Dhs)"}),(0,t.jsx)(l.p,{id:"min_order_amount",type:"number",defaultValue:"50"})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(c.J,{htmlFor:"max_cod_amount",className:"arabic-text",children:"الحد الأقصى للدفع عند الاستلام (Dhs)"}),(0,t.jsx)(l.p,{id:"max_cod_amount",type:"number",defaultValue:"2000"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(c.J,{htmlFor:"free_shipping_threshold",className:"arabic-text",children:"الحد الأدنى للشحن المجاني (Dhs)"}),(0,t.jsx)(l.p,{id:"free_shipping_threshold",type:"number",defaultValue:"500"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(c.J,{htmlFor:"payment_timeout",className:"arabic-text",children:"مهلة انتظار الدفع (دقائق)"}),(0,t.jsx)(l.p,{id:"payment_timeout",type:"number",defaultValue:"30"})]})]})]}),(0,t.jsx)(x.w,{}),(0,t.jsxs)("div",{children:[(0,t.jsx)(c.J,{htmlFor:"payment_instructions",className:"arabic-text",children:"تعليمات الدفع للعملاء"}),(0,t.jsx)(u.T,{id:"payment_instructions",placeholder:"أدخل التعليمات التي ستظهر للعملاء أثناء عملية الدفع...",className:"mt-2 arabic-text",rows:4,defaultValue:"يرجى التأكد من صحة المعلومات قبل إتمام عملية الدفع. في حالة وجود أي مشاكل، يرجى التواصل مع فريق الدعم."})]}),(0,t.jsx)("div",{className:"flex justify-end",children:(0,t.jsx)(d.$,{onClick:P,disabled:D,children:D?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(b.A,{className:"h-4 w-4 mr-2 animate-spin"}),"جاري الحفظ..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(w.A,{className:"h-4 w-4 mr-2"}),"حفظ الإعدادات"]})})})]})]})})]})]})]})}},59385:(e,a,r)=>{"use strict";r.d(a,{gG:()=>t,ly:()=>s});var t=function(e){return e.STUDENT="student",e.SCHOOL="school",e.ADMIN="admin",e.DELIVERY="delivery",e}({});function s(e,a){let r={admin:4,school:3,delivery:2,student:1};return r[e]>=r[a]}},59434:(e,a,r)=>{"use strict";r.d(a,{cn:()=>i});var t=r(52596),s=r(39688);function i(){for(var e=arguments.length,a=Array(e),r=0;r<e;r++)a[r]=arguments[r];return(0,s.QP)((0,t.$)(a))}},62523:(e,a,r)=>{"use strict";r.d(a,{p:()=>i});var t=r(95155);r(12115);var s=r(59434);function i(e){let{className:a,type:r,...i}=e;return(0,t.jsx)("input",{type:r,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",a),...i})}},66695:(e,a,r)=>{"use strict";r.d(a,{BT:()=>l,Wu:()=>c,ZB:()=>d,Zp:()=>i,aR:()=>n});var t=r(95155);r(12115);var s=r(59434);function i(e){let{className:a,...r}=e;return(0,t.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...r})}function n(e){let{className:a,...r}=e;return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...r})}function d(e){let{className:a,...r}=e;return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",a),...r})}function l(e){let{className:a,...r}=e;return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",a),...r})}function c(e){let{className:a,...r}=e;return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",a),...r})}},80333:(e,a,r)=>{"use strict";r.d(a,{d:()=>n});var t=r(95155);r(12115);var s=r(4884),i=r(59434);function n(e){let{className:a,...r}=e;return(0,t.jsx)(s.bL,{"data-slot":"switch",className:(0,i.cn)("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",a),...r,children:(0,t.jsx)(s.zi,{"data-slot":"switch-thumb",className:(0,i.cn)("bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0")})})}},81909:(e,a,r)=>{Promise.resolve().then(r.bind(r,43269))},85057:(e,a,r)=>{"use strict";r.d(a,{J:()=>n});var t=r(95155);r(12115);var s=r(40968),i=r(59434);function n(e){let{className:a,...r}=e;return(0,t.jsx)(s.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...r})}},88539:(e,a,r)=>{"use strict";r.d(a,{T:()=>i});var t=r(95155);r(12115);var s=r(59434);function i(e){let{className:a,...r}=e;return(0,t.jsx)("textarea",{"data-slot":"textarea",className:(0,s.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),...r})}}},e=>{var a=a=>e(e.s=a);e.O(0,[7598,5486,380,6874,6671,9645,8441,1684,7358],()=>a(81909)),_N_E=e.O()}]);