(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5105],{17853:(e,r,t)=>{Promise.resolve().then(t.bind(t,44879))},35695:(e,r,t)=>{"use strict";var a=t(18999);t.o(a,"useParams")&&t.d(r,{useParams:function(){return a.useParams}}),t.o(a,"usePathname")&&t.d(r,{usePathname:function(){return a.usePathname}}),t.o(a,"useRouter")&&t.d(r,{useRouter:function(){return a.useRouter}}),t.o(a,"useSearchParams")&&t.d(r,{useSearchParams:function(){return a.useSearchParams}})},40283:(e,r,t)=>{"use strict";t.d(r,{A:()=>c,AuthProvider:()=>n});var a=t(95155),o=t(12115),s=t(59385);let l=(0,o.createContext)(void 0);function n(e){let{children:r}=e,[t,n]=(0,o.useState)(null),[c,i]=(0,o.useState)(null),[d,u]=(0,o.useState)(!0),[m,f]=(0,o.useState)(!1);(0,o.useEffect)(()=>{f(!0)},[]),(0,o.useEffect)(()=>{m&&(async()=>{try{let e=localStorage.getItem("mockUser"),r=localStorage.getItem("mockProfile");if(e&&r){let t=JSON.parse(e),a=JSON.parse(r);if(t&&a&&t.id&&a.id){let e=localStorage.getItem("sessionTimestamp"),r=Date.now(),o=e?r-parseInt(e):0;e&&o<864e5?(n(t),i(a),console.log("User data loaded from localStorage:",{userData:t,profileData:a})):(console.log("Session expired, clearing user data"),localStorage.removeItem("mockUser"),localStorage.removeItem("mockProfile"),localStorage.removeItem("sessionTimestamp"))}else localStorage.removeItem("mockUser"),localStorage.removeItem("mockProfile"),localStorage.removeItem("sessionTimestamp")}}catch(e){console.error("Error loading user from localStorage:",e),localStorage.removeItem("mockUser"),localStorage.removeItem("mockProfile")}finally{u(!1)}})()},[m]),(0,o.useEffect)(()=>{if(!t||!c)return;let e=()=>{try{localStorage.setItem("sessionTimestamp",Date.now().toString())}catch(e){console.error("Error refreshing session:",e)}},r=["click","keypress","scroll","mousemove"];return r.forEach(r=>{document.addEventListener(r,e,{passive:!0})}),()=>{r.forEach(r=>{document.removeEventListener(r,e)})}},[t,c]);let g=async(e,r,t)=>(console.log("Sign up:",e,t),{data:{user:{id:"1",email:e}},error:null}),h=async(e,r)=>{console.log("Sign in:",e);let t={id:"1",email:e},a=s.gG.STUDENT;e.includes("admin")?a=s.gG.ADMIN:e.includes("school")?a=s.gG.SCHOOL:e.includes("delivery")&&(a=s.gG.DELIVERY);let o={id:"1",email:e,full_name:e.split("@")[0]||"مستخدم",role:a,created_at:new Date().toISOString(),updated_at:new Date().toISOString()};n(t),i(o);try{localStorage.setItem("mockUser",JSON.stringify(t)),localStorage.setItem("mockProfile",JSON.stringify(o)),localStorage.setItem("sessionTimestamp",Date.now().toString()),console.log("User data saved to localStorage:",{mockUser:t,mockProfile:o})}catch(e){console.error("Error saving user data to localStorage:",e)}return setTimeout(()=>{a===s.gG.ADMIN?window.location.href="/dashboard/admin":a===s.gG.SCHOOL?window.location.href="/dashboard/school":a===s.gG.DELIVERY?window.location.href="/dashboard/delivery":window.location.href="/dashboard/student"},100),{data:{user:t},error:null}},v=async()=>{try{return n(null),i(null),localStorage.removeItem("mockUser"),localStorage.removeItem("mockProfile"),localStorage.removeItem("sessionTimestamp"),console.log("User data cleared from localStorage"),{error:null}}catch(e){return console.error("Error during sign out:",e),{error:"فشل في تسجيل الخروج"}}},S=async e=>{if(!t)return{data:null,error:"No user logged in"};let r={...c,...e};return i(r),{data:r,error:null}};return(0,a.jsx)(l.Provider,{value:{user:t,profile:c,loading:d,signUp:g,signIn:h,signOut:v,updateProfile:S,hasRole:e=>!!c&&(0,s.ly)(c.role,e)},children:r})}function c(){let e=(0,o.useContext)(l);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},44879:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>u});var a=t(95155),o=t(12115),s=t(45876),l=t(40283),n=t(59385),c=t(66695),i=t(51154);function d(){let{profile:e}=(0,l.A)();return(0,o.useEffect)(()=>{if(e)switch(e.role){case n.gG.ADMIN:window.location.href="/dashboard/admin";break;case n.gG.SCHOOL:window.location.href="/dashboard/school";break;case n.gG.DELIVERY:window.location.href="/dashboard/delivery";break;case n.gG.STUDENT:default:window.location.href="/dashboard/student"}},[e]),(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",children:(0,a.jsx)(c.Zp,{className:"w-full max-w-md",children:(0,a.jsxs)(c.Wu,{className:"flex flex-col items-center justify-center py-8",children:[(0,a.jsx)(i.A,{className:"h-8 w-8 animate-spin text-blue-600 mb-4"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-300 text-center",children:"جاري إعادة التوجيه إلى لوحة التحكم المناسبة..."})]})})})}function u(){return(0,a.jsx)(s.O,{children:(0,a.jsx)(d,{})})}},45876:(e,r,t)=>{"use strict";t.d(r,{O:()=>i});var a=t(95155),o=t(12115),s=t(35695),l=t(40283),n=t(66695),c=t(51154);function i(e){let{children:r,requiredRole:t,redirectTo:i="/auth"}=e,{user:d,profile:u,loading:m,hasRole:f}=(0,l.A)(),g=(0,s.useRouter)(),[h,v]=(0,o.useState)(!1);return((0,o.useEffect)(()=>{v(!0)},[]),(0,o.useEffect)(()=>{if(h&&!m){if(!d)return void g.push(i);if(t&&!f(t))return void g.push("/unauthorized")}},[h,d,u,m,t,f,g,i]),!h||m)?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsx)(n.Zp,{className:"w-full max-w-md",children:(0,a.jsxs)(n.Wu,{className:"flex flex-col items-center justify-center py-8",children:[(0,a.jsx)(c.A,{className:"h-8 w-8 animate-spin text-blue-600 mb-4"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"جاري التحميل..."})]})})}):d&&(!t||f(t))?(0,a.jsx)(a.Fragment,{children:r}):null}},51154:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(19946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},59385:(e,r,t)=>{"use strict";t.d(r,{gG:()=>a,ly:()=>o});var a=function(e){return e.STUDENT="student",e.SCHOOL="school",e.ADMIN="admin",e.DELIVERY="delivery",e}({});function o(e,r){let t={admin:4,school:3,delivery:2,student:1};return t[e]>=t[r]}},59434:(e,r,t)=>{"use strict";t.d(r,{cn:()=>s});var a=t(52596),o=t(39688);function s(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,o.QP)((0,a.$)(r))}},66695:(e,r,t)=>{"use strict";t.d(r,{BT:()=>c,Wu:()=>i,ZB:()=>n,Zp:()=>s,aR:()=>l});var a=t(95155);t(12115);var o=t(59434);function s(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,o.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",r),...t})}function l(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,o.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",r),...t})}function n(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,o.cn)("leading-none font-semibold",r),...t})}function c(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,o.cn)("text-muted-foreground text-sm",r),...t})}function i(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,o.cn)("px-6",r),...t})}}},e=>{var r=r=>e(e.s=r);e.O(0,[7598,8441,1684,7358],()=>r(17853)),_N_E=e.O()}]);