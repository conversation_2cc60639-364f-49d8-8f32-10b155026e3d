"use strict";(()=>{var e={};e.id=6865,e.ids=[6865],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},93249:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>f,routeModule:()=>g,serverHooks:()=>j,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{DELETE:()=>c,GET:()=>u,PUT:()=>d});var o=t(96559),n=t(48088),a=t(37719),i=t(32190),p=t(38561);async function u(e,{params:r}){try{let e=p.CN.getPageProjects().find(e=>e.id===r.id);if(!e)return i.NextResponse.json({error:"المشروع غير موجود"},{status:404});return i.NextResponse.json({project:e})}catch(e){return console.error("Error fetching page project:",e),i.NextResponse.json({error:"خطأ في جلب المشروع"},{status:500})}}async function d(e,{params:r}){try{let t=await e.json(),s=p.CN.getPageProjects(),o=s.findIndex(e=>e.id===r.id);if(-1===o)return i.NextResponse.json({error:"المشروع غير موجود"},{status:404});let n={...s[o],...t,id:r.id,updatedAt:new Date().toISOString()};return s[o]=n,p.CN.savePageProjects(s),i.NextResponse.json({message:"تم تحديث المشروع بنجاح",project:n})}catch(e){return console.error("Error updating page project:",e),i.NextResponse.json({error:"خطأ في تحديث المشروع"},{status:500})}}async function c(e,{params:r}){try{let e=p.CN.getPageProjects(),t=e.findIndex(e=>e.id===r.id);if(-1===t)return i.NextResponse.json({error:"المشروع غير موجود"},{status:404});if(e[t].isPublished)return i.NextResponse.json({error:"لا يمكن حذف المشروع المنشور. يرجى إلغاء نشره أولاً.",suggestion:"unpublish_first"},{status:400});let s=e.splice(t,1)[0];return p.CN.savePageProjects(e),i.NextResponse.json({message:`تم حذف المشروع "${s.name}" بنجاح`,project:s})}catch(e){return console.error("Error deleting page project:",e),i.NextResponse.json({error:"خطأ في حذف المشروع"},{status:500})}}let g=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/page-builder/[id]/route",pathname:"/api/page-builder/[id]",filename:"route",bundlePath:"app/api/page-builder/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\page-builder\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:x,serverHooks:j}=g;function f(){return(0,a.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:x})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,8554],()=>t(93249));module.exports=s})();