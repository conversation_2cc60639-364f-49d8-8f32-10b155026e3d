(()=>{var e={};e.id=5006,e.ids=[5006],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6211:(e,a,s)=>{"use strict";s.d(a,{A0:()=>n,BF:()=>c,Hj:()=>d,XI:()=>l,nA:()=>x,nd:()=>o});var t=s(60687),r=s(43210),i=s(4780);let l=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)("div",{className:"relative w-full overflow-auto",children:(0,t.jsx)("table",{ref:s,className:(0,i.cn)("w-full caption-bottom text-sm",e),...a})}));l.displayName="Table";let n=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)("thead",{ref:s,className:(0,i.cn)("[&_tr]:border-b",e),...a}));n.displayName="TableHeader";let c=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)("tbody",{ref:s,className:(0,i.cn)("[&_tr:last-child]:border-0",e),...a}));c.displayName="TableBody",r.forwardRef(({className:e,...a},s)=>(0,t.jsx)("tfoot",{ref:s,className:(0,i.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...a})).displayName="TableFooter";let d=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)("tr",{ref:s,className:(0,i.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...a}));d.displayName="TableRow";let o=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)("th",{ref:s,className:(0,i.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...a}));o.displayName="TableHead";let x=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)("td",{ref:s,className:(0,i.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...a}));x.displayName="TableCell",r.forwardRef(({className:e,...a},s)=>(0,t.jsx)("caption",{ref:s,className:(0,i.cn)("mt-4 text-sm text-muted-foreground",e),...a})).displayName="TableCaption"},8819:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15116:(e,a,s)=>{"use strict";s.r(a),s.d(a,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>h,tree:()=>d});var t=s(65239),r=s(48088),i=s(88170),l=s.n(i),n=s(30893),c={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);s.d(a,c);let d={children:["",{children:["dashboard",{children:["admin",{children:["products",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,57802)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\admin\\products\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,54431)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\admin\\products\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/dashboard/admin/products/page",pathname:"/dashboard/admin/products",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23562:(e,a,s)=>{"use strict";s.d(a,{k:()=>l});var t=s(60687);s(43210);var r=s(25177),i=s(4780);function l({className:e,value:a,...s}){return(0,t.jsx)(r.bL,{"data-slot":"progress",className:(0,i.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",e),...s,children:(0,t.jsx)(r.C1,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:`translateX(-${100-(a||0)}%)`}})})}},25541:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},37360:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},47158:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("wand-sparkles",[["path",{d:"m21.64 3.64-1.28-1.28a1.21 1.21 0 0 0-1.72 0L2.36 18.64a1.21 1.21 0 0 0 0 1.72l1.28 1.28a1.2 1.2 0 0 0 1.72 0L21.64 5.36a1.2 1.2 0 0 0 0-1.72",key:"ul74o6"}],["path",{d:"m14 7 3 3",key:"1r5n42"}],["path",{d:"M5 6v4",key:"ilb8ba"}],["path",{d:"M19 14v4",key:"blhpug"}],["path",{d:"M10 2v2",key:"7u0qdc"}],["path",{d:"M7 8H3",key:"zfb6yr"}],["path",{d:"M21 16h-4",key:"1cnmox"}],["path",{d:"M11 3H9",key:"1obp7u"}]])},48006:(e,a,s)=>{Promise.resolve().then(s.bind(s,54370))},54370:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>eX});var t=s(60687),r=s(43210),i=s(63213),l=s(87801),n=s(44493),c=s(29523),d=s(96834),o=s(89667),x=s(80013),h=s(15079),m=s(63503),p=s(85763),u=s(6211),j=s(21342),g=s(34729),b=s(70569),f=s(98599),v=s(11273),y=s(31355),N=s(96963),w=s(55509),k=(s(25028),s(46059)),C=s(14163),_=s(8730),A=s(65551),T=s(69024),[E,z]=(0,v.A)("Tooltip",[w.Bk]),$=(0,w.Bk)(),S="TooltipProvider",M="tooltip.open",[q,P]=E(S),R=e=>{let{__scopeTooltip:a,delayDuration:s=700,skipDelayDuration:i=300,disableHoverableContent:l=!1,children:n}=e,c=r.useRef(!0),d=r.useRef(!1),o=r.useRef(0);return r.useEffect(()=>{let e=o.current;return()=>window.clearTimeout(e)},[]),(0,t.jsx)(q,{scope:a,isOpenDelayedRef:c,delayDuration:s,onOpen:r.useCallback(()=>{window.clearTimeout(o.current),c.current=!1},[]),onClose:r.useCallback(()=>{window.clearTimeout(o.current),o.current=window.setTimeout(()=>c.current=!0,i)},[i]),isPointerInTransitRef:d,onPointerInTransitChange:r.useCallback(e=>{d.current=e},[]),disableHoverableContent:l,children:n})};R.displayName=S;var D="Tooltip",[F,L]=E(D),O=e=>{let{__scopeTooltip:a,children:s,open:i,defaultOpen:l,onOpenChange:n,disableHoverableContent:c,delayDuration:d}=e,o=P(D,e.__scopeTooltip),x=$(a),[h,m]=r.useState(null),p=(0,N.B)(),u=r.useRef(0),j=c??o.disableHoverableContent,g=d??o.delayDuration,b=r.useRef(!1),[f,v]=(0,A.i)({prop:i,defaultProp:l??!1,onChange:e=>{e?(o.onOpen(),document.dispatchEvent(new CustomEvent(M))):o.onClose(),n?.(e)},caller:D}),y=r.useMemo(()=>f?b.current?"delayed-open":"instant-open":"closed",[f]),k=r.useCallback(()=>{window.clearTimeout(u.current),u.current=0,b.current=!1,v(!0)},[v]),C=r.useCallback(()=>{window.clearTimeout(u.current),u.current=0,v(!1)},[v]),_=r.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>{b.current=!0,v(!0),u.current=0},g)},[g,v]);return r.useEffect(()=>()=>{u.current&&(window.clearTimeout(u.current),u.current=0)},[]),(0,t.jsx)(w.bL,{...x,children:(0,t.jsx)(F,{scope:a,contentId:p,open:f,stateAttribute:y,trigger:h,onTriggerChange:m,onTriggerEnter:r.useCallback(()=>{o.isOpenDelayedRef.current?_():k()},[o.isOpenDelayedRef,_,k]),onTriggerLeave:r.useCallback(()=>{j?C():(window.clearTimeout(u.current),u.current=0)},[C,j]),onOpen:k,onClose:C,disableHoverableContent:j,children:s})})};O.displayName=D;var U="TooltipTrigger",I=r.forwardRef((e,a)=>{let{__scopeTooltip:s,...i}=e,l=L(U,s),n=P(U,s),c=$(s),d=r.useRef(null),o=(0,f.s)(a,d,l.onTriggerChange),x=r.useRef(!1),h=r.useRef(!1),m=r.useCallback(()=>x.current=!1,[]);return r.useEffect(()=>()=>document.removeEventListener("pointerup",m),[m]),(0,t.jsx)(w.Mz,{asChild:!0,...c,children:(0,t.jsx)(C.sG.button,{"aria-describedby":l.open?l.contentId:void 0,"data-state":l.stateAttribute,...i,ref:o,onPointerMove:(0,b.m)(e.onPointerMove,e=>{"touch"!==e.pointerType&&(h.current||n.isPointerInTransitRef.current||(l.onTriggerEnter(),h.current=!0))}),onPointerLeave:(0,b.m)(e.onPointerLeave,()=>{l.onTriggerLeave(),h.current=!1}),onPointerDown:(0,b.m)(e.onPointerDown,()=>{l.open&&l.onClose(),x.current=!0,document.addEventListener("pointerup",m,{once:!0})}),onFocus:(0,b.m)(e.onFocus,()=>{x.current||l.onOpen()}),onBlur:(0,b.m)(e.onBlur,l.onClose),onClick:(0,b.m)(e.onClick,l.onClose)})})});I.displayName=U;var[B,Z]=E("TooltipPortal",{forceMount:void 0}),G="TooltipContent",W=r.forwardRef((e,a)=>{let s=Z(G,e.__scopeTooltip),{forceMount:r=s.forceMount,side:i="top",...l}=e,n=L(G,e.__scopeTooltip);return(0,t.jsx)(k.C,{present:r||n.open,children:n.disableHoverableContent?(0,t.jsx)(K,{side:i,...l,ref:a}):(0,t.jsx)(X,{side:i,...l,ref:a})})}),X=r.forwardRef((e,a)=>{let s=L(G,e.__scopeTooltip),i=P(G,e.__scopeTooltip),l=r.useRef(null),n=(0,f.s)(a,l),[c,d]=r.useState(null),{trigger:o,onClose:x}=s,h=l.current,{onPointerInTransitChange:m}=i,p=r.useCallback(()=>{d(null),m(!1)},[m]),u=r.useCallback((e,a)=>{let s=e.currentTarget,t={x:e.clientX,y:e.clientY},r=function(e,a){let s=Math.abs(a.top-e.y),t=Math.abs(a.bottom-e.y),r=Math.abs(a.right-e.x),i=Math.abs(a.left-e.x);switch(Math.min(s,t,r,i)){case i:return"left";case r:return"right";case s:return"top";case t:return"bottom";default:throw Error("unreachable")}}(t,s.getBoundingClientRect());d(function(e){let a=e.slice();return a.sort((e,a)=>e.x<a.x?-1:e.x>a.x?1:e.y<a.y?-1:1*!!(e.y>a.y)),function(e){if(e.length<=1)return e.slice();let a=[];for(let s=0;s<e.length;s++){let t=e[s];for(;a.length>=2;){let e=a[a.length-1],s=a[a.length-2];if((e.x-s.x)*(t.y-s.y)>=(e.y-s.y)*(t.x-s.x))a.pop();else break}a.push(t)}a.pop();let s=[];for(let a=e.length-1;a>=0;a--){let t=e[a];for(;s.length>=2;){let e=s[s.length-1],a=s[s.length-2];if((e.x-a.x)*(t.y-a.y)>=(e.y-a.y)*(t.x-a.x))s.pop();else break}s.push(t)}return(s.pop(),1===a.length&&1===s.length&&a[0].x===s[0].x&&a[0].y===s[0].y)?a:a.concat(s)}(a)}([...function(e,a,s=5){let t=[];switch(a){case"top":t.push({x:e.x-s,y:e.y+s},{x:e.x+s,y:e.y+s});break;case"bottom":t.push({x:e.x-s,y:e.y-s},{x:e.x+s,y:e.y-s});break;case"left":t.push({x:e.x+s,y:e.y-s},{x:e.x+s,y:e.y+s});break;case"right":t.push({x:e.x-s,y:e.y-s},{x:e.x-s,y:e.y+s})}return t}(t,r),...function(e){let{top:a,right:s,bottom:t,left:r}=e;return[{x:r,y:a},{x:s,y:a},{x:s,y:t},{x:r,y:t}]}(a.getBoundingClientRect())])),m(!0)},[m]);return r.useEffect(()=>()=>p(),[p]),r.useEffect(()=>{if(o&&h){let e=e=>u(e,h),a=e=>u(e,o);return o.addEventListener("pointerleave",e),h.addEventListener("pointerleave",a),()=>{o.removeEventListener("pointerleave",e),h.removeEventListener("pointerleave",a)}}},[o,h,u,p]),r.useEffect(()=>{if(c){let e=e=>{let a=e.target,s={x:e.clientX,y:e.clientY},t=o?.contains(a)||h?.contains(a),r=!function(e,a){let{x:s,y:t}=e,r=!1;for(let e=0,i=a.length-1;e<a.length;i=e++){let l=a[e],n=a[i],c=l.x,d=l.y,o=n.x,x=n.y;d>t!=x>t&&s<(o-c)*(t-d)/(x-d)+c&&(r=!r)}return r}(s,c);t?p():r&&(p(),x())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[o,h,c,x,p]),(0,t.jsx)(K,{...e,ref:n})}),[J,V]=E(D,{isInside:!1}),H=(0,_.Dc)("TooltipContent"),K=r.forwardRef((e,a)=>{let{__scopeTooltip:s,children:i,"aria-label":l,onEscapeKeyDown:n,onPointerDownOutside:c,...d}=e,o=L(G,s),x=$(s),{onClose:h}=o;return r.useEffect(()=>(document.addEventListener(M,h),()=>document.removeEventListener(M,h)),[h]),r.useEffect(()=>{if(o.trigger){let e=e=>{let a=e.target;a?.contains(o.trigger)&&h()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[o.trigger,h]),(0,t.jsx)(y.qW,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:n,onPointerDownOutside:c,onFocusOutside:e=>e.preventDefault(),onDismiss:h,children:(0,t.jsxs)(w.UC,{"data-state":o.stateAttribute,...x,...d,ref:a,style:{...d.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,t.jsx)(H,{children:i}),(0,t.jsx)(J,{scope:s,isInside:!0,children:(0,t.jsx)(T.bL,{id:o.contentId,role:"tooltip",children:l||i})})]})})});W.displayName=G;var Y="TooltipArrow";r.forwardRef((e,a)=>{let{__scopeTooltip:s,...r}=e,i=$(s);return V(Y,s).isInside?null:(0,t.jsx)(w.i3,{...i,...r,ref:a})}).displayName=Y;var Q=s(4780);let ee=r.forwardRef(({className:e,sideOffset:a=4,...s},r)=>(0,t.jsx)(W,{ref:r,sideOffset:a,className:(0,Q.cn)("z-50 overflow-hidden rounded-md bg-primary px-3 py-1.5 text-xs text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...s}));ee.displayName=W.displayName;var ea=s(54987),es=s(19080),et=s(98971),er=s(62688);let ei=(0,er.A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]);var el=s(64398),en=s(78200),ec=s(93613),ed=s(96474),eo=s(11860),ex=s(13861),eh=s(8819),em=s(23562),ep=s(91821);let eu=(0,er.A)("wifi-off",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}],["path",{d:"M5 12.859a10 10 0 0 1 5.17-2.69",key:"1dl1wf"}],["path",{d:"M19 12.859a10 10 0 0 0-2.007-1.523",key:"4k23kn"}],["path",{d:"M2 8.82a15 15 0 0 1 4.177-2.643",key:"1grhjp"}],["path",{d:"M22 8.82a15 15 0 0 0-11.288-3.764",key:"z3jwby"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]);var ej=s(16023),eg=s(62140),eb=s(88233);let ef=(0,er.A)("rotate-cw",[["path",{d:"M21 12a9 9 0 1 1-9-9c2.52 0 4.93 1 6.74 2.74L21 8",key:"1p45f6"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}]]);var ev=s(13964);function ey({images:e,onImagesChange:a,maxImages:s=10,maxSize:i=5242880,acceptedTypes:l=["image/jpeg","image/png","image/webp"],className:o=""}){let[x,h]=(0,r.useState)(!1),[m,p]=(0,r.useState)({}),[u,j]=(0,r.useState)(!0),[g,b]=(0,r.useState)([]),[f,v]=(0,r.useState)({}),[y,N]=(0,r.useState)({}),w=(0,r.useMemo)(()=>Array.isArray(e)?e:[],[e]),k=(0,r.useCallback)(async()=>{try{let e=(await fetch("/api/health",{method:"HEAD"})).ok;return j(e),e}catch{return j(!1),!1}},[]),C=(0,r.useCallback)(e=>l.includes(e.type)?e.size>i?`حجم الملف كبير جداً. الحد الأقصى ${(i/1024/1024).toFixed(1)} ميجابايت`:null:"نوع الملف غير مدعوم. يرجى اختيار صورة بصيغة JPG, PNG, أو WebP",[l,i]),_=(0,r.useCallback)((e,a)=>new Promise((s,t)=>{let r=new FileReader;r.onload=()=>{try{let e=r.result;localStorage.setItem(`fallback_image_${a}`,e),s(e)}catch(e){t(e)}},r.onerror=t,r.readAsDataURL(e)}),[]),A=(0,r.useCallback)(async e=>new Promise(a=>{let s=document.createElement("canvas"),t=s.getContext("2d"),r=new Image;r.onload=()=>{let{width:i,height:l}=r;i>l?i>1200&&(l=1200*l/i,i=1200):l>1200&&(i=1200*i/l,l=1200),s.width=i,s.height=l,t?.drawImage(r,0,0,i,l),s.toBlob(s=>{s?a(new File([s],e.name,{type:"image/jpeg",lastModified:Date.now()})):a(e)},"image/jpeg",.8)},r.src=URL.createObjectURL(e)}),[]),T=(0,r.useCallback)(async e=>{N(a=>({...a,[e]:!0}));try{await new Promise(e=>setTimeout(e,2e3+3e3*Math.random())),console.log(`تم تحسين الصورة ${e} باستخدام الذكاء الاصطناعي`)}catch(e){console.error("خطأ في تحسين الصورة:",e)}finally{N(a=>({...a,[e]:!1}))}},[]),E=(0,r.useCallback)(async(e,a)=>{v(e=>({...e,[a]:!0}));try{let a=await A(e);return await new Promise(e=>setTimeout(e,1e3)),a}finally{v(e=>({...e,[a]:!1}))}},[A]),z=(0,r.useCallback)(async(e,a,s=2)=>{let t=await A(e);for(let r=1;r<=s;r++)try{p(e=>({...e,[a]:(r-1)*40}));let e=new FormData;e.append("files",t),e.append("folder","products");let s=await fetch("/api/upload",{method:"POST",body:e});if(!s.ok)throw Error(`HTTP ${s.status}`);let i=await s.json();if(p(e=>({...e,[a]:100})),i.uploadedFiles&&i.uploadedFiles.length>0)return i.uploadedFiles[0].url;throw Error("لم يتم إرجاع رابط الصورة")}catch(t){if(console.error(`Upload attempt ${r} failed:`,t),r===s){let s=await _(e,a);return p(e=>({...e,[a]:100})),s}await new Promise(e=>setTimeout(e,1e3*r))}throw Error("فشل في رفع الصورة")},[A,_]),$=(0,r.useCallback)(async t=>{let r=[],i=(Array.isArray(e)?e:[]).length,l=[];await k();for(let e=0;e<t.length&&i+r.length<s;e++){let a=t[e],s=C(a),i=`${Date.now()}-${e}`;if(s){l.push(`${a.name}: ${s}`);continue}try{let e=await new Promise((e,s)=>{let t=new FileReader;t.onload=a=>e(a.target?.result),t.onerror=s,t.readAsDataURL(a)}),s={file:a,preview:e,id:i,uploading:!0,uploaded:!1};r.push(s)}catch(e){console.error("Preview creation failed:",e),l.push(`${a.name}: فشل في إنشاء المعاينة`)}}if(r.length>0){a([...e,...r]);let s=r.map(async e=>{try{let s=await E(e.file,e.id),t=await z(s,e.id);a(a=>a.map(a=>a.id===e.id?{...a,uploading:!1,uploaded:!0,fallbackUrl:t}:a))}catch(s){console.error("Upload failed:",s),l.push(`${e.file.name}: فشل في الرفع`),a(a=>a.map(a=>a.id===e.id?{...a,uploading:!1,uploaded:!1,error:"فشل في الرفع"}:a))}});await Promise.allSettled(s)}l.length>0&&(b(l),setTimeout(()=>b([]),5e3))},[e,s,a,k,C,z,E]),S=(0,r.useCallback)(e=>{e.preventDefault(),e.stopPropagation(),"dragenter"===e.type||"dragover"===e.type?h(!0):"dragleave"===e.type&&h(!1)},[]),M=(0,r.useCallback)(e=>{e.preventDefault(),e.stopPropagation(),h(!1),e.dataTransfer.files&&e.dataTransfer.files[0]&&$(e.dataTransfer.files)},[$]),q=e=>{a(w.filter(a=>a.id!==e))},P=(e,s)=>{let t=[...w],[r]=t.splice(e,1);t.splice(s,0,r),a(t)};return(0,t.jsxs)("div",{className:`space-y-4 ${o}`,children:[!u&&(0,t.jsxs)(ep.Fc,{className:"border-orange-200 bg-orange-50",children:[(0,t.jsx)(eu,{className:"h-4 w-4"}),(0,t.jsx)(ep.TN,{className:"arabic-text",children:"لا يوجد اتصال بالإنترنت. سيتم حفظ الصور محلياً كنسخة احتياطية."})]}),g.length>0&&(0,t.jsxs)(ep.Fc,{className:"border-red-200 bg-red-50",children:[(0,t.jsx)(ec.A,{className:"h-4 w-4"}),(0,t.jsx)(ep.TN,{children:(0,t.jsx)("div",{className:"space-y-1",children:g.map((e,a)=>(0,t.jsx)("div",{className:"text-sm arabic-text",children:e},a))})})]}),(0,t.jsxs)("div",{className:`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${x?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-300 dark:border-gray-600 hover:border-gray-400"} ${e.length>=s?"opacity-50 pointer-events-none":""}`,onDragEnter:S,onDragLeave:S,onDragOver:S,onDrop:M,children:[(0,t.jsx)(ej.A,{className:`h-12 w-12 mx-auto mb-4 ${x?"text-blue-500":"text-gray-400"}`}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("p",{className:"text-lg font-medium arabic-text",children:x?"أفلت الصور هنا":"اسحب الصور هنا أو"}),w.length<s&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("input",{ref:e=>{e&&(window.fileInput=e)},type:"file",multiple:!0,accept:l.join(","),className:"hidden",onChange:e=>{e.target.files&&e.target.files.length>0&&($(e.target.files),e.target.value="")},disabled:w.length>=s}),(0,t.jsxs)(c.$,{type:"button",variant:"outline",size:"sm",disabled:w.length>=s,onClick:()=>{let e=window.fileInput;e&&e.click()},children:[(0,t.jsx)(eg.A,{className:"h-4 w-4 mr-2"}),"اختر الصور"]})]})]}),(0,t.jsxs)("div",{className:"mt-4 space-y-1",children:[(0,t.jsxs)("p",{className:"text-sm text-gray-500 arabic-text",children:["يمكنك رفع حتى ",s," صور بحجم أقصى ",(i/1024/1024).toFixed(1)," ميجابايت لكل صورة"]}),(0,t.jsxs)("p",{className:"text-xs text-gray-400",children:["الصيغ المدعومة: ",l.map(e=>e.split("/")[1].toUpperCase()).join(", ")]})]}),w.length>=s&&(0,t.jsxs)("div",{className:"mt-4 flex items-center justify-center gap-2 text-orange-600",children:[(0,t.jsx)(ec.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"text-sm arabic-text",children:"تم الوصول للحد الأقصى من الصور"})]})]}),w.length>0&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("h3",{className:"text-lg font-medium arabic-text",children:["الصور المرفوعة (",w.length,"/",s,")"]}),(0,t.jsxs)(c.$,{type:"button",variant:"outline",size:"sm",onClick:()=>a([]),className:"text-red-600 hover:text-red-700",children:[(0,t.jsx)(eb.A,{className:"h-4 w-4 mr-2"}),"حذف الكل"]})]}),(0,t.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:w.map((a,s)=>(0,t.jsx)(n.Zp,{className:"relative group overflow-hidden",children:(0,t.jsx)(n.Wu,{className:"p-0",children:a.error?(0,t.jsx)("div",{className:"aspect-square flex items-center justify-center bg-red-50 dark:bg-red-900/20",children:(0,t.jsxs)("div",{className:"text-center p-4",children:[(0,t.jsx)(ec.A,{className:"h-8 w-8 text-red-500 mx-auto mb-2"}),(0,t.jsx)("p",{className:"text-xs text-red-600 arabic-text",children:a.error})]})}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("img",{src:a.preview,alt:`صورة ${s+1}`,className:"w-full aspect-square object-cover"}),a.uploading&&(0,t.jsx)("div",{className:"absolute inset-0 bg-black/60 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-4 min-w-[140px] shadow-lg",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-3",children:[(0,t.jsx)(ef,{className:"h-5 w-5 animate-spin text-blue-500"}),(0,t.jsx)("span",{className:"text-sm font-medium arabic-text",children:"جاري الرفع..."})]}),(0,t.jsx)(em.k,{value:m[a.id]||0,className:"h-3 mb-2"}),(0,t.jsxs)("div",{className:"text-xs text-center text-gray-600 dark:text-gray-400",children:[m[a.id]||0,"%"]})]})}),a.uploaded&&(0,t.jsx)("div",{className:"absolute top-2 left-2",children:(0,t.jsxs)(d.E,{className:a.fallbackUrl?.startsWith("data:")?"bg-orange-600":"bg-green-600",children:[(0,t.jsx)(ev.A,{className:"h-3 w-3 mr-1"}),a.fallbackUrl?.startsWith("data:")?"محفوظ محلياً":"تم الرفع"]})}),!u&&a.uploaded&&a.fallbackUrl?.startsWith("data:")&&(0,t.jsx)("div",{className:"absolute bottom-2 left-2",children:(0,t.jsxs)(d.E,{variant:"outline",className:"bg-white/90",children:[(0,t.jsx)(eu,{className:"h-3 w-3 mr-1"}),"غير متصل"]})}),(0,t.jsx)("div",{className:"absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity",children:(0,t.jsxs)("div",{className:"flex gap-1",children:[a.uploaded&&!y[a.id]&&(0,t.jsx)(c.$,{type:"button",size:"sm",variant:"outline",onClick:()=>T(a.id),className:"h-8 w-8 p-0 bg-blue-600 text-white hover:bg-blue-700",title:"تحسين بالذكاء الاصطناعي",children:(0,t.jsx)(en.A,{className:"h-3 w-3"})}),y[a.id]&&(0,t.jsx)(c.$,{type:"button",size:"sm",variant:"outline",disabled:!0,className:"h-8 w-8 p-0 bg-blue-600 text-white",children:(0,t.jsx)(ef,{className:"h-3 w-3 animate-spin"})}),f[a.id]&&(0,t.jsx)(c.$,{type:"button",size:"sm",variant:"outline",disabled:!0,className:"h-8 w-8 p-0 bg-orange-600 text-white",children:(0,t.jsx)(ef,{className:"h-3 w-3 animate-spin"})}),(0,t.jsx)(c.$,{type:"button",size:"sm",variant:"destructive",onClick:()=>q(a.id),className:"h-8 w-8 p-0",disabled:a.uploading||f[a.id]||y[a.id],children:(0,t.jsx)(eo.A,{className:"h-3 w-3"})})]})}),0===s&&(0,t.jsx)(d.E,{className:"absolute bottom-2 left-2 bg-blue-600",children:"الصورة الرئيسية"}),(0,t.jsx)("div",{className:"absolute bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity",children:(0,t.jsxs)("div",{className:"flex gap-1",children:[s>0&&(0,t.jsx)(c.$,{type:"button",size:"sm",variant:"secondary",onClick:()=>P(s,s-1),className:"h-6 w-6 p-0",children:"←"}),s<e.length-1&&(0,t.jsx)(c.$,{type:"button",size:"sm",variant:"secondary",onClick:()=>P(s,s+1),className:"h-6 w-6 p-0",children:"→"})]})})]})})},a.id))})]})]})}var eN=s(41862),ew=s(5336),ek=s(10022),eC=s(37360);let e_=(0,er.A)("list",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]]);var eA=s(84027),eT=s(47158),eE=s(99270),ez=s(70615);function e$({productData:e,onUpdate:a,className:s=""}){let{model:i,available:l,loading:o}=function(){let{activeModels:e,loading:a,error:s}=function(){let[e,a]=(0,r.useState)([]),[s,t]=(0,r.useState)(!0),[i,l]=(0,r.useState)(null),n=async()=>{try{t(!0),l(null);let e=await fetch("/api/ai-models?include_inactive=true");if(!e.ok)throw Error("فشل في جلب نماذج الذكاء الاصطناعي");let s=await e.json();a(s.models||[])}catch(e){l(e instanceof Error?e.message:"خطأ غير معروف"),console.error("Error fetching AI models:",e)}finally{t(!1)}},c=e.filter(e=>e.isActive&&"active"===e.status&&e.apiKey&&e.baseUrl);return{models:e,activeModels:c,loading:s,error:i,refetch:n}}(),t=e.find(e=>"text"===e.type||"multimodal"===e.type||"openai"===e.provider||"anthropic"===e.provider);return{model:t,available:!!t,loading:a,error:s}}(),[x,h]=(0,r.useState)(null),[m,p]=(0,r.useState)({}),[u,j]=(0,r.useState)(""),b=async a=>{if(!l||!i)return void alert("لا توجد نماذج ذكاء اصطناعي نشطة");h(a);try{let s=await fetch("/api/ai/product-enhancement",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:a,productData:e,modelId:u||i.id,language:"ar"})});if(!s.ok)throw Error("فشل في تحسين المنتج");let t=await s.json();if(t.success)p(e=>({...e,[a]:t.result}));else throw Error(t.error||"خطأ غير معروف")}catch(e){console.error("AI Enhancement Error:",e),alert(e instanceof Error?e.message:"خطأ في تحسين المنتج")}finally{h(null)}},f=(e,s,t)=>{a(s,t),p(a=>{let s={...a};return delete s[e],s})},v=e=>{navigator.clipboard.writeText(e)};return o?(0,t.jsx)(n.Zp,{className:s,children:(0,t.jsxs)(n.Wu,{className:"flex items-center justify-center p-6",children:[(0,t.jsx)(eN.A,{className:"h-6 w-6 animate-spin mr-2"}),(0,t.jsx)("span",{className:"arabic-text",children:"جاري تحميل نماذج الذكاء الاصطناعي..."})]})}):l?(0,t.jsxs)(n.Zp,{className:s,children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2 arabic-text",children:[(0,t.jsx)(en.A,{className:"h-5 w-5 text-blue-600"}),"تحسين المنتج بالذكاء الاصطناعي"]}),(0,t.jsx)(n.BT,{className:"arabic-text",children:"استخدم الذكاء الاصطناعي لتحسين بيانات المنتج وتوليد محتوى احترافي"}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsxs)(d.E,{variant:"outline",className:"text-green-600",children:[(0,t.jsx)(ew.A,{className:"h-3 w-3 mr-1"}),i.name]}),(0,t.jsx)(d.E,{variant:"secondary",children:i.provider})]})]}),(0,t.jsxs)(n.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-3",children:[(0,t.jsxs)(c.$,{variant:"outline",size:"sm",onClick:()=>b("generate_description"),disabled:"generate_description"===x,className:"arabic-text",children:["generate_description"===x?(0,t.jsx)(eN.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(ek.A,{className:"h-4 w-4 mr-2"}),"توليد وصف"]}),(0,t.jsxs)(c.$,{variant:"outline",size:"sm",onClick:()=>b("generate_title"),disabled:"generate_title"===x,className:"arabic-text",children:["generate_title"===x?(0,t.jsx)(eN.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(eC.A,{className:"h-4 w-4 mr-2"}),"توليد عنوان"]}),(0,t.jsxs)(c.$,{variant:"outline",size:"sm",onClick:()=>b("generate_features"),disabled:"generate_features"===x,className:"arabic-text",children:["generate_features"===x?(0,t.jsx)(eN.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(e_,{className:"h-4 w-4 mr-2"}),"توليد ميزات"]}),(0,t.jsxs)(c.$,{variant:"outline",size:"sm",onClick:()=>b("generate_specifications"),disabled:"generate_specifications"===x,className:"arabic-text",children:["generate_specifications"===x?(0,t.jsx)(eN.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(eA.A,{className:"h-4 w-4 mr-2"}),"توليد مواصفات"]}),(0,t.jsxs)(c.$,{variant:"outline",size:"sm",onClick:()=>b("suggest_category"),disabled:"suggest_category"===x,className:"arabic-text",children:["suggest_category"===x?(0,t.jsx)(eN.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(eT.A,{className:"h-4 w-4 mr-2"}),"اقتراح فئة"]}),(0,t.jsxs)(c.$,{variant:"outline",size:"sm",onClick:()=>b("optimize_seo"),disabled:"optimize_seo"===x,className:"arabic-text",children:["optimize_seo"===x?(0,t.jsx)(eN.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(eE.A,{className:"h-4 w-4 mr-2"}),"تحسين SEO"]})]}),Object.entries(m).map(([e,a])=>{var s;return(0,t.jsxs)(n.Zp,{className:"border-green-200 bg-green-50 dark:bg-green-900/20",children:[(0,t.jsx)(n.aR,{className:"pb-3",children:(0,t.jsxs)(n.ZB,{className:"text-sm text-green-700 dark:text-green-300 arabic-text",children:["نتيجة ",{generate_description:"توليد الوصف",generate_title:"توليد العنوان",generate_features:"توليد الميزات",generate_specifications:"توليد المواصفات",suggest_category:"اقتراح الفئة",optimize_seo:"تحسين SEO"}[e]]})}),(0,t.jsxs)(n.Wu,{className:"space-y-3",children:[a.description&&(0,t.jsxs)("div",{children:[(0,t.jsx)(g.T,{value:a.description,readOnly:!0,className:"min-h-[100px] arabic-text"}),(0,t.jsxs)("div",{className:"flex gap-2 mt-2",children:[(0,t.jsx)(c.$,{size:"sm",onClick:()=>f(e,"description",a.description),children:"تطبيق"}),(0,t.jsx)(c.$,{size:"sm",variant:"outline",onClick:()=>v(a.description),children:(0,t.jsx)(ez.A,{className:"h-4 w-4"})})]})]}),a.title&&(0,t.jsxs)("div",{children:[(0,t.jsx)("input",{type:"text",value:a.title,readOnly:!0,className:"w-full p-2 border rounded arabic-text"}),(0,t.jsxs)("div",{className:"flex gap-2 mt-2",children:[(0,t.jsx)(c.$,{size:"sm",onClick:()=>f(e,"name",a.title),children:"تطبيق"}),(0,t.jsx)(c.$,{size:"sm",variant:"outline",onClick:()=>v(a.title),children:(0,t.jsx)(ez.A,{className:"h-4 w-4"})})]})]}),a.features&&(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"space-y-1",children:a.features.map((e,a)=>(0,t.jsx)("div",{className:"p-2 bg-white dark:bg-gray-800 rounded border arabic-text",children:e},a))}),(0,t.jsx)("div",{className:"flex gap-2 mt-2",children:(0,t.jsx)(c.$,{size:"sm",onClick:()=>f(e,"features",a.features),children:"تطبيق الكل"})})]}),a.specifications&&(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"space-y-1",children:Object.entries(a.specifications).map(([e,a])=>(0,t.jsxs)("div",{className:"flex gap-2 p-2 bg-white dark:bg-gray-800 rounded border",children:[(0,t.jsxs)("span",{className:"font-medium arabic-text",children:[e,":"]}),(0,t.jsx)("span",{className:"arabic-text",children:a})]},e))}),(0,t.jsx)("div",{className:"flex gap-2 mt-2",children:(0,t.jsx)(c.$,{size:"sm",onClick:()=>f(e,"specifications",a.specifications),children:"تطبيق الكل"})})]}),a.suggestedCategory&&(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"p-2 bg-white dark:bg-gray-800 rounded border arabic-text",children:["الفئة المقترحة: ",(0,t.jsx)("strong",{children:{gown:"ثوب التخرج",cap:"قبعة التخرج",stole:"وشاح التخرج",tassel:"شرابة التخرج",hood:"قلنسوة التخرج"}[s=a.suggestedCategory]||s}),a.confidence&&(0,t.jsxs)(d.E,{variant:"secondary",className:"mr-2",children:[Math.round(100*a.confidence),"% ثقة"]})]}),(0,t.jsx)("div",{className:"flex gap-2 mt-2",children:(0,t.jsx)(c.$,{size:"sm",onClick:()=>f(e,"category",a.suggestedCategory),children:"تطبيق"})})]}),a.keywords&&(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:a.keywords.map((e,a)=>(0,t.jsx)(d.E,{variant:"outline",className:"arabic-text",children:e},a))}),a.metaDescription&&(0,t.jsx)(g.T,{value:a.metaDescription,readOnly:!0,className:"mt-2 arabic-text",placeholder:"وصف SEO"})]})]})]},e)})]})]}):(0,t.jsx)(n.Zp,{className:s,children:(0,t.jsx)(n.Wu,{className:"flex items-center justify-center p-6 text-center",children:(0,t.jsxs)("div",{children:[(0,t.jsx)(ec.A,{className:"h-12 w-12 text-orange-500 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2 arabic-text",children:"لا توجد نماذج ذكاء اصطناعي نشطة"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-400 arabic-text",children:"يرجى تفعيل نموذج ذكاء اصطناعي واحد على الأقل لاستخدام هذه الميزة"})]})})})}let eS=["أسود","أزرق داكن","بورجوندي","ذهبي","فضي","أبيض","أحمر","أخضر","بنفسجي","وردي","برتقالي","بني"],eM=["XS","S","M","L","XL","XXL","XXXL","واحد"];function eq({onSubmit:e,onCancel:a,initialData:s={},isEditing:i=!1}){let[l,m]=(0,r.useState)({name:s.name||"",description:s.description||"",category:s.category||"",price:s.price||0,rental_price:s.rental_price||0,colors:s.colors||[],sizes:s.sizes||[],images:s.images||[],stock_quantity:s.stock_quantity||0,is_available:s.is_available??!0,is_published:s.is_published??!0,features:s.features||[],specifications:s.specifications||{}}),[u,j]=(0,r.useState)([]),[b,f]=(0,r.useState)(!0),[v,y]=(0,r.useState)(""),[N,w]=(0,r.useState)(""),[k,C]=(0,r.useState)(""),[_,A]=(0,r.useState)(""),[T,E]=(0,r.useState)(""),[z,$]=(0,r.useState)({}),S=()=>{let e={};return l.name.trim()||(e.name="اسم المنتج مطلوب"),l.description.trim()||(e.description="وصف المنتج مطلوب"),l.category||(e.category="فئة المنتج مطلوبة"),l.price<=0&&(e.price="السعر يجب أن يكون أكبر من صفر"),0===l.colors.length&&(e.colors="يجب إضافة لون واحد على الأقل"),0===l.sizes.length&&(e.sizes="يجب إضافة مقاس واحد على الأقل"),l.stock_quantity<0&&(e.stock_quantity="كمية المخزون لا يمكن أن تكون سالبة"),$(e),0===Object.keys(e).length},M=e=>{m(a=>({...a,colors:a.colors.filter(a=>a!==e)}))},q=e=>{m(a=>({...a,sizes:a.sizes.filter(a=>a!==e)}))},P=e=>{m(a=>({...a,features:a.features.filter(a=>a!==e)}))},R=e=>{m(a=>{let s={...a.specifications};return delete s[e],{...a,specifications:s}})};return(0,t.jsxs)("form",{onSubmit:a=>{a.preventDefault(),S()&&e(l)},className:"space-y-6",children:[(0,t.jsxs)(p.tU,{defaultValue:"basic",className:"w-full",children:[(0,t.jsxs)(p.j7,{className:"grid w-full grid-cols-5",children:[(0,t.jsxs)(p.Xi,{value:"basic",className:"arabic-text",children:[(0,t.jsx)(es.A,{className:"h-4 w-4 mr-2"}),"المعلومات الأساسية"]}),(0,t.jsxs)(p.Xi,{value:"details",className:"arabic-text",children:[(0,t.jsx)(et.A,{className:"h-4 w-4 mr-2"}),"التفاصيل والألوان"]}),(0,t.jsxs)(p.Xi,{value:"images",className:"arabic-text",children:[(0,t.jsx)(ei,{className:"h-4 w-4 mr-2"}),"الصور"]}),(0,t.jsxs)(p.Xi,{value:"features",className:"arabic-text",children:[(0,t.jsx)(el.A,{className:"h-4 w-4 mr-2"}),"المميزات والمواصفات"]}),(0,t.jsxs)(p.Xi,{value:"ai",className:"arabic-text",children:[(0,t.jsx)(en.A,{className:"h-4 w-4 mr-2"}),"الذكاء الاصطناعي"]})]}),(0,t.jsxs)(p.av,{value:"basic",className:"space-y-6 mt-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(x.J,{htmlFor:"name",className:"arabic-text",children:"اسم المنتج *"}),(0,t.jsx)(o.p,{id:"name",value:l.name,onChange:e=>m(a=>({...a,name:e.target.value})),placeholder:"أدخل اسم المنتج",className:"arabic-text"}),z.name&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(ec.A,{className:"h-4 w-4"}),z.name]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(x.J,{htmlFor:"category",className:"arabic-text",children:"فئة المنتج *"}),(0,t.jsxs)(h.l6,{value:l.category,onValueChange:e=>m(a=>({...a,category:e})),disabled:b,children:[(0,t.jsx)(h.bq,{children:(0,t.jsx)(h.yv,{placeholder:b?"جاري تحميل الفئات...":"اختر فئة المنتج"})}),(0,t.jsx)(h.gC,{children:u.map(e=>(0,t.jsx)(h.eb,{value:e.slug,children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[e.icon&&(0,t.jsx)("span",{children:e.icon}),(0,t.jsx)("span",{className:"arabic-text",children:e.name_ar})]})},e.slug))})]}),z.category&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(ec.A,{className:"h-4 w-4"}),z.category]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(x.J,{htmlFor:"description",className:"arabic-text",children:"وصف المنتج *"}),(0,t.jsx)(g.T,{id:"description",value:l.description,onChange:e=>m(a=>({...a,description:e.target.value})),placeholder:"أدخل وصف مفصل للمنتج",className:"arabic-text min-h-[100px]"}),z.description&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(ec.A,{className:"h-4 w-4"}),z.description]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(x.J,{htmlFor:"price",className:"arabic-text",children:"السعر (درهم) *"}),(0,t.jsx)(o.p,{id:"price",type:"number",min:"0",step:"0.01",value:l.price,onChange:e=>m(a=>({...a,price:parseFloat(e.target.value)||0})),placeholder:"0.00"}),z.price&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(ec.A,{className:"h-4 w-4"}),z.price]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(x.J,{htmlFor:"rental_price",className:"arabic-text",children:"سعر الإيجار (درهم)"}),(0,t.jsx)(o.p,{id:"rental_price",type:"number",min:"0",step:"0.01",value:l.rental_price,onChange:e=>m(a=>({...a,rental_price:parseFloat(e.target.value)||0})),placeholder:"0.00"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(x.J,{htmlFor:"stock_quantity",className:"arabic-text",children:"كمية المخزون *"}),(0,t.jsx)(o.p,{id:"stock_quantity",type:"number",min:"0",value:l.stock_quantity,onChange:e=>m(a=>({...a,stock_quantity:parseInt(e.target.value)||0})),placeholder:"0"}),z.stock_quantity&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(ec.A,{className:"h-4 w-4"}),z.stock_quantity]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(ea.d,{id:"is_available",checked:l.is_available,onCheckedChange:e=>m(a=>({...a,is_available:e}))}),(0,t.jsx)(x.J,{htmlFor:"is_available",className:"arabic-text",children:"متاح للبيع"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(ea.d,{id:"is_published",checked:l.is_published,onCheckedChange:e=>m(a=>({...a,is_published:e}))}),(0,t.jsx)(x.J,{htmlFor:"is_published",className:"arabic-text",children:"نشر المنتج في الكتالوج"})]})]}),(0,t.jsxs)(p.av,{value:"details",className:"space-y-6 mt-6",children:[(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsx)(n.ZB,{className:"arabic-text",children:"الألوان المتاحة"}),(0,t.jsx)(n.BT,{className:"arabic-text",children:"أضف الألوان المتاحة للمنتج"})]}),(0,t.jsxs)(n.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(h.l6,{value:v,onValueChange:y,children:[(0,t.jsx)(h.bq,{className:"flex-1",children:(0,t.jsx)(h.yv,{placeholder:"اختر لون"})}),(0,t.jsx)(h.gC,{children:eS.map(e=>(0,t.jsx)(h.eb,{value:e,children:e},e))})]}),(0,t.jsx)(o.p,{value:v,onChange:e=>y(e.target.value),placeholder:"أو أدخل لون مخصص",className:"flex-1 arabic-text"}),(0,t.jsx)(c.$,{type:"button",onClick:()=>{v.trim()&&!l.colors.includes(v.trim())&&(m(e=>({...e,colors:[...e.colors,v.trim()]})),y(""))},size:"sm",children:(0,t.jsx)(ed.A,{className:"h-4 w-4"})})]}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:l.colors.map((e,a)=>(0,t.jsxs)(d.E,{variant:"secondary",className:"arabic-text",children:[e,(0,t.jsx)("button",{type:"button",onClick:()=>M(e),className:"ml-2 hover:text-red-600",children:(0,t.jsx)(eo.A,{className:"h-3 w-3"})})]},a))}),z.colors&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(ec.A,{className:"h-4 w-4"}),z.colors]})]})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsx)(n.ZB,{className:"arabic-text",children:"المقاسات المتاحة"}),(0,t.jsx)(n.BT,{className:"arabic-text",children:"أضف المقاسات المتاحة للمنتج"})]}),(0,t.jsxs)(n.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(h.l6,{value:N,onValueChange:w,children:[(0,t.jsx)(h.bq,{className:"flex-1",children:(0,t.jsx)(h.yv,{placeholder:"اختر مقاس"})}),(0,t.jsx)(h.gC,{children:eM.map(e=>(0,t.jsx)(h.eb,{value:e,children:e},e))})]}),(0,t.jsx)(o.p,{value:N,onChange:e=>w(e.target.value),placeholder:"أو أدخل مقاس مخصص",className:"flex-1 arabic-text"}),(0,t.jsx)(c.$,{type:"button",onClick:()=>{N.trim()&&!l.sizes.includes(N.trim())&&(m(e=>({...e,sizes:[...e.sizes,N.trim()]})),w(""))},size:"sm",children:(0,t.jsx)(ed.A,{className:"h-4 w-4"})})]}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:l.sizes.map((e,a)=>(0,t.jsxs)(d.E,{variant:"secondary",className:"arabic-text",children:[e,(0,t.jsx)("button",{type:"button",onClick:()=>q(e),className:"ml-2 hover:text-red-600",children:(0,t.jsx)(eo.A,{className:"h-3 w-3"})})]},a))}),z.sizes&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(ec.A,{className:"h-4 w-4"}),z.sizes]})]})]})]}),(0,t.jsx)(p.av,{value:"images",className:"space-y-6 mt-6",children:(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsx)(n.ZB,{className:"arabic-text",children:"صور المنتج"}),(0,t.jsx)(n.BT,{className:"arabic-text",children:"أضف صور عالية الجودة للمنتج (يُفضل 500x600 بكسل أو أكبر)"})]}),(0,t.jsx)(n.Wu,{children:(0,t.jsx)(ey,{images:l.images,onImagesChange:e=>m(a=>({...a,images:e})),maxImages:8,maxSize:5242880,acceptedTypes:["image/jpeg","image/png","image/webp"]})})]})}),(0,t.jsxs)(p.av,{value:"features",className:"space-y-6 mt-6",children:[(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsx)(n.ZB,{className:"arabic-text",children:"مميزات المنتج"}),(0,t.jsx)(n.BT,{className:"arabic-text",children:"أضف المميزات الرئيسية للمنتج"})]}),(0,t.jsxs)(n.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(o.p,{value:k,onChange:e=>C(e.target.value),placeholder:"أدخل ميزة جديدة",className:"flex-1 arabic-text"}),(0,t.jsx)(c.$,{type:"button",onClick:()=>{k.trim()&&!l.features.includes(k.trim())&&(m(e=>({...e,features:[...e.features,k.trim()]})),C(""))},size:"sm",children:(0,t.jsx)(ed.A,{className:"h-4 w-4"})})]}),(0,t.jsx)("div",{className:"space-y-2",children:l.features.map((e,a)=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg",children:[(0,t.jsx)("span",{className:"arabic-text",children:e}),(0,t.jsx)("button",{type:"button",onClick:()=>P(e),className:"text-red-600 hover:text-red-700",children:(0,t.jsx)(eo.A,{className:"h-4 w-4"})})]},a))}),0===l.features.length&&(0,t.jsx)("p",{className:"text-gray-500 text-center py-4 arabic-text",children:"لم يتم إضافة أي مميزات بعد"})]})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsx)(n.ZB,{className:"arabic-text",children:"مواصفات المنتج"}),(0,t.jsx)(n.BT,{className:"arabic-text",children:"أضف المواصفات التقنية للمنتج"})]}),(0,t.jsxs)(n.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,t.jsx)(o.p,{value:_,onChange:e=>A(e.target.value),placeholder:"اسم المواصفة (مثل: المادة)",className:"arabic-text"}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(o.p,{value:T,onChange:e=>E(e.target.value),placeholder:"قيمة المواصفة",className:"flex-1 arabic-text"}),(0,t.jsx)(c.$,{type:"button",onClick:()=>{_.trim()&&T.trim()&&(m(e=>({...e,specifications:{...e.specifications,[_.trim()]:T.trim()}})),A(""),E(""))},size:"sm",children:(0,t.jsx)(ed.A,{className:"h-4 w-4"})})]})]}),(0,t.jsx)("div",{className:"space-y-2",children:Object.entries(l.specifications).map(([e,a])=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg",children:[(0,t.jsxs)("div",{className:"arabic-text",children:[(0,t.jsxs)("span",{className:"font-medium",children:[e,":"]})," ",a]}),(0,t.jsx)("button",{type:"button",onClick:()=>R(e),className:"text-red-600 hover:text-red-700",children:(0,t.jsx)(eo.A,{className:"h-4 w-4"})})]},e))}),0===Object.keys(l.specifications).length&&(0,t.jsx)("p",{className:"text-gray-500 text-center py-4 arabic-text",children:"لم يتم إضافة أي مواصفات بعد"})]})]})]}),(0,t.jsx)(p.av,{value:"ai",className:"space-y-6 mt-6",children:(0,t.jsx)(e$,{productData:{name:l.name,description:l.description,category:l.category,price:l.price,features:l.features,specifications:l.specifications},onUpdate:(e,a)=>{m(s=>({...s,[e]:a}))}})})]}),(0,t.jsxs)("div",{className:"flex justify-end gap-4 pt-6 border-t",children:[(0,t.jsx)(c.$,{type:"button",variant:"outline",onClick:a,children:"إلغاء"}),(0,t.jsxs)(c.$,{type:"button",variant:"outline",children:[(0,t.jsx)(ex.A,{className:"h-4 w-4 mr-2"}),"معاينة"]}),(0,t.jsxs)(c.$,{type:"submit",children:[(0,t.jsx)(eh.A,{className:"h-4 w-4 mr-2"}),i?"تحديث المنتج":"إضافة المنتج"]})]})]})}function eP({product:e,open:a,onOpenChange:s,onSave:i}){let[l,n]=(0,r.useState)(!1),c=async a=>{try{n(!0),await i({...a,id:e?.id}),s(!1)}catch(e){console.error("Error updating product:",e)}finally{n(!1)}};return e?(0,t.jsx)(m.lG,{open:a,onOpenChange:s,children:(0,t.jsxs)(m.Cf,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,t.jsx)(m.c7,{children:(0,t.jsxs)(m.L3,{className:"arabic-text",children:["تعديل المنتج: ",e.name]})}),(0,t.jsx)(eq,{initialData:{name:e.name,description:e.description,category:e.category,price:e.price,rental_price:e.rental_price,colors:e.colors,sizes:e.sizes,images:e.images.map((e,a)=>({id:`existing-${a}`,file:new File([],"existing-image"),preview:e,uploaded:!0,fallbackUrl:e})),stock_quantity:e.stock_quantity,is_available:e.is_available,features:e.features||[],specifications:e.specifications||{}},onSubmit:c,onCancel:()=>{s(!1)},isEditing:!0})]})}):null}var eR=s(50954),eD=s(62694),eF=s(85814),eL=s.n(eF),eO=s(28559),eU=s(31158),eI=s(43649),eB=s(25541),eZ=s(63143),eG=s(93661);let eW={gown:"ثوب التخرج",cap:"قبعة التخرج",tassel:"الشرابة",stole:"الوشاح",hood:"القلنسوة"};function eX(){let{user:e,profile:a}=(0,i.A)(),s=(0,eD.dj)(),[b,f]=(0,r.useState)([]),[v,y]=(0,r.useState)([]),[N,w]=(0,r.useState)(""),[k,C]=(0,r.useState)("all"),[_,A]=(0,r.useState)("all"),[T,E]=(0,r.useState)("all"),[z,$]=(0,r.useState)("created_at"),[S,M]=(0,r.useState)("desc"),[q,P]=(0,r.useState)(!0),[D,F]=(0,r.useState)(!1),[L,U]=(0,r.useState)(null),[B,Z]=(0,r.useState)(!1),[G,W]=(0,r.useState)({open:!1,productId:"",productName:""}),[X,J]=(0,r.useState)([]),[V,H]=(0,r.useState)(!1),[K,Y]=(0,r.useState)(null),[Q,ea]=(0,r.useState)({name_ar:"",slug:"",description:"",icon:"",is_active:!0,order_index:0}),[et,er]=(0,r.useState)("products"),ei=async()=>{try{P(!0);let e=await fetch("/api/products");if(!e.ok)throw Error("فشل في جلب المنتجات");let a=await e.json();console.log("Fetched products response:",a),console.log("Products loaded:",a.products?.length||0),f(a.products||[]),y(a.products||[])}catch(e){console.error("Error fetching products:",e),s.error("فشل في جلب المنتجات")}finally{P(!1)}},ec=(e,a)=>{W({open:!0,productId:e,productName:a})},eo=async()=>{try{console.log("Attempting to delete product with ID:",G.productId),console.log("Delete confirm state:",G);let e=await fetch(`/api/products/${G.productId}`,{method:"DELETE"});if(console.log("Delete response status:",e.status),!e.ok){let a=await e.json();throw console.log("Delete error response:",a),Error(a.error||"فشل في حذف المنتج")}let a=await e.json();console.log("Delete success response:",a),W({open:!1,productId:"",productName:""}),await ei(),s.success("تم حذف المنتج بنجاح!")}catch(e){console.error("Error deleting product:",e),s.error(e instanceof Error?e.message:"فشل في حذف المنتج"),W({open:!1,productId:"",productName:""})}},eh=async e=>{try{let a=b.find(a=>a.id===e);if(!a)return;let t=await fetch(`/api/products/${e}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({is_available:!a.is_available})});if(!t.ok){let e=await t.json();throw Error(e.error||"فشل في تحديث حالة المنتج")}await ei(),s.success(`تم ${!a.is_available?"تفعيل":"إلغاء تفعيل"} المنتج بنجاح`)}catch(e){console.error("Error toggling availability:",e),s.error(e instanceof Error?e.message:"فشل في تحديث حالة المنتج")}},em=async e=>{try{let a=b.find(a=>a.id===e);if(!a)return;let t=await fetch(`/api/products/${e}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({is_published:!a.is_published})});if(!t.ok){let e=await t.json();throw Error(e.error||"فشل في تحديث حالة النشر")}await ei(),s.success(`تم ${!a.is_published?"نشر":"إلغاء نشر"} المنتج بنجاح`)}catch(e){console.error("Error toggling published status:",e),s.error(e instanceof Error?e.message:"فشل في تحديث حالة النشر")}},ep=async e=>{try{P(!0);let a=[];e.images&&e.images.length>0&&(a=e.images.map(e=>e.uploaded&&e.fallbackUrl?e.fallbackUrl:e.preview).filter(Boolean));let t=await fetch("/api/products",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...e,images:a})});if(!t.ok){let e=await t.json();throw Error(e.error||"فشل في إضافة المنتج")}await t.json(),await ei(),F(!1),s.success("تم إضافة المنتج بنجاح!")}catch(e){console.error("Error adding product:",e),s.error(e instanceof Error?e.message:"فشل في إضافة المنتج")}finally{P(!1)}},eu=e=>{U(e),Z(!0)},ej=async e=>{try{P(!0);let a=[];e.images&&e.images.length>0&&(a=e.images.map(e=>e.uploaded&&e.fallbackUrl?e.fallbackUrl:e.preview).filter(Boolean));let t=await fetch(`/api/products/${e.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({...e,images:a})});if(!t.ok){let e=await t.json();throw Error(e.error||"فشل في تحديث المنتج")}await ei(),s.success("تم تحديث المنتج بنجاح!")}catch(e){console.error("Error updating product:",e),s.error(e instanceof Error?e.message:"فشل في تحديث المنتج")}finally{P(!1)}},eg=async()=>{try{let e=await fetch("/api/categories");if(e.ok){let a=await e.json();J(a.categories||[])}}catch(e){console.error("Error fetching categories:",e)}},ef=async()=>{try{let e=K?`/api/categories/${K.id}`:"/api/categories",a=K?"PUT":"POST",t=await fetch(e,{method:a,headers:{"Content-Type":"application/json"},body:JSON.stringify(Q)});if(t.ok)await eg(),ev(),s.success(K?"تم تحديث الفئة بنجاح!":"تم إضافة الفئة بنجاح!");else{let e=await t.json();s.error(e.error||"فشل في حفظ الفئة")}}catch(e){console.error("Error saving category:",e),s.error("فشل في حفظ الفئة")}},ev=()=>{ea({name_ar:"",slug:"",description:"",icon:"",is_active:!0,order_index:0}),Y(null),H(!1)},ey=e=>{ea({name_ar:e.name_ar,slug:e.slug,description:e.description||"",icon:e.icon||"",is_active:e.is_active,order_index:e.order_index}),Y(e),H(!0)},eN=async e=>{try{let a=await fetch(`/api/categories/${e}`,{method:"DELETE"});if(a.ok)await eg(),s.success("تم حذف الفئة بنجاح!");else{let e=await a.json();s.error(e.error||"فشل في حذف الفئة")}}catch(e){console.error("Error deleting category:",e),s.error("فشل في حذف الفئة")}};return(console.log("User:",e),console.log("Profile:",a),console.log("Profile role:",a?.role),e&&a?.role==="admin")?(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",children:[(0,t.jsx)(l.V,{}),(0,t.jsxs)("main",{className:"container mx-auto px-4 py-8",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("div",{className:"flex items-center gap-4 mb-4",children:(0,t.jsx)(c.$,{variant:"outline",size:"sm",asChild:!0,children:(0,t.jsxs)(eL(),{href:"/dashboard/admin",children:[(0,t.jsx)(eO.A,{className:"h-4 w-4 mr-2"}),"العودة للوحة التحكم"]})})}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white arabic-text",children:"إدارة المنتجات والفئات \uD83D\uDCE6"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-300 mt-2 arabic-text",children:"إضافة وتعديل وإدارة منتجات وفئات المنصة"})]}),(0,t.jsxs)("div",{className:"flex gap-3",children:[(0,t.jsxs)(c.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(eU.A,{className:"h-4 w-4 mr-2"}),"تصدير"]}),"products"===et&&(0,t.jsxs)(c.$,{size:"sm",onClick:()=>F(!0),children:[(0,t.jsx)(ed.A,{className:"h-4 w-4 mr-2"}),"إضافة منتج جديد"]}),"categories"===et&&(0,t.jsxs)(c.$,{size:"sm",onClick:()=>{ev(),H(!0)},children:[(0,t.jsx)(ed.A,{className:"h-4 w-4 mr-2"}),"إضافة فئة جديدة"]})]})]})]}),(0,t.jsx)(n.Zp,{className:"mb-6 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border-blue-200 dark:border-blue-800",children:(0,t.jsxs)(n.Wu,{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"p-3 bg-blue-600 rounded-lg",children:(0,t.jsx)(en.A,{className:"h-6 w-6 text-white"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white arabic-text",children:"ميزات الذكاء الاصطناعي المتاحة"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-400 arabic-text",children:"استخدم الذكاء الاصطناعي لتحسين منتجاتك تلقائياً"})]})]}),(0,t.jsxs)("div",{className:"flex gap-4",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:"6"}),(0,t.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"ميزات متاحة"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-green-600",children:"نشط"}),(0,t.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"حالة النظام"})]})]})]}),(0,t.jsxs)("div",{className:"mt-4 flex flex-wrap gap-2",children:[(0,t.jsxs)(d.E,{variant:"outline",className:"bg-white dark:bg-gray-800",children:[(0,t.jsx)(ek.A,{className:"h-3 w-3 mr-1"}),"توليد الأوصاف"]}),(0,t.jsxs)(d.E,{variant:"outline",className:"bg-white dark:bg-gray-800",children:[(0,t.jsx)(eC.A,{className:"h-3 w-3 mr-1"}),"توليد العناوين"]}),(0,t.jsxs)(d.E,{variant:"outline",className:"bg-white dark:bg-gray-800",children:[(0,t.jsx)(e_,{className:"h-3 w-3 mr-1"}),"توليد الميزات"]}),(0,t.jsxs)(d.E,{variant:"outline",className:"bg-white dark:bg-gray-800",children:[(0,t.jsx)(eA.A,{className:"h-3 w-3 mr-1"}),"توليد المواصفات"]}),(0,t.jsxs)(d.E,{variant:"outline",className:"bg-white dark:bg-gray-800",children:[(0,t.jsx)(eT.A,{className:"h-3 w-3 mr-1"}),"اقتراح الفئات"]}),(0,t.jsxs)(d.E,{variant:"outline",className:"bg-white dark:bg-gray-800",children:[(0,t.jsx)(eE.A,{className:"h-3 w-3 mr-1"}),"تحسين SEO"]})]})]})}),(0,t.jsxs)(p.tU,{value:et,onValueChange:er,className:"space-y-6",children:[(0,t.jsxs)(p.j7,{className:"grid w-full grid-cols-2",children:[(0,t.jsx)(p.Xi,{value:"products",className:"arabic-text",children:"المنتجات"}),(0,t.jsx)(p.Xi,{value:"categories",className:"arabic-text",children:"الفئات"})]}),(0,t.jsxs)(p.av,{value:"products",className:"space-y-6",children:[(0,t.jsxs)("div",{className:"product-grid grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[(0,t.jsx)(n.Zp,{children:(0,t.jsx)(n.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text",children:"إجمالي المنتجات"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:b.length})]}),(0,t.jsx)(es.A,{className:"h-8 w-8 text-blue-600"})]})})}),(0,t.jsx)(n.Zp,{children:(0,t.jsx)(n.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text",children:"المنتجات المتاحة"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:b.filter(e=>e.is_available).length})]}),(0,t.jsx)(el.A,{className:"h-8 w-8 text-green-600"})]})})}),(0,t.jsx)(n.Zp,{children:(0,t.jsx)(n.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text",children:"المنتجات المنشورة"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:b.filter(e=>e.is_published).length})]}),(0,t.jsx)(ex.A,{className:"h-8 w-8 text-blue-600"})]})})}),(0,t.jsx)(n.Zp,{children:(0,t.jsx)(n.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text",children:"مخزون منخفض"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:b.filter(e=>e.stock_quantity<20).length})]}),(0,t.jsx)(eI.A,{className:"h-8 w-8 text-orange-600"})]})})}),(0,t.jsx)(n.Zp,{children:(0,t.jsx)(n.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text",children:"متوسط التقييم"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:(b.reduce((e,a)=>e+(a.rating||0),0)/b.length).toFixed(1)})]}),(0,t.jsx)(eB.A,{className:"h-8 w-8 text-purple-600"})]})})})]}),(0,t.jsx)(n.Zp,{className:"mb-6",children:(0,t.jsx)(n.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(eE.A,{className:"absolute left-3 top-3 h-4 w-4 text-gray-400"}),(0,t.jsx)(o.p,{placeholder:"البحث في المنتجات...",value:N,onChange:e=>w(e.target.value),className:"pl-10 arabic-text"})]}),(0,t.jsxs)(h.l6,{value:k,onValueChange:C,children:[(0,t.jsx)(h.bq,{children:(0,t.jsx)(h.yv,{placeholder:"جميع الفئات"})}),(0,t.jsxs)(h.gC,{children:[(0,t.jsx)(h.eb,{value:"all",children:"جميع الفئات"}),Object.entries(eW).map(([e,a])=>(0,t.jsx)(h.eb,{value:e,children:a},e))]})]}),(0,t.jsxs)(h.l6,{value:_,onValueChange:A,children:[(0,t.jsx)(h.bq,{children:(0,t.jsx)(h.yv,{placeholder:"جميع المنتجات"})}),(0,t.jsxs)(h.gC,{children:[(0,t.jsx)(h.eb,{value:"all",children:"جميع المنتجات"}),(0,t.jsx)(h.eb,{value:"available",children:"متاح"}),(0,t.jsx)(h.eb,{value:"unavailable",children:"غير متاح"})]})]}),(0,t.jsxs)(h.l6,{value:T,onValueChange:E,children:[(0,t.jsx)(h.bq,{children:(0,t.jsx)(h.yv,{placeholder:"حالة النشر"})}),(0,t.jsxs)(h.gC,{children:[(0,t.jsx)(h.eb,{value:"all",children:"جميع المنتجات"}),(0,t.jsx)(h.eb,{value:"published",children:"منشور"}),(0,t.jsx)(h.eb,{value:"unpublished",children:"غير منشور"})]})]}),(0,t.jsxs)(h.l6,{value:z,onValueChange:$,children:[(0,t.jsx)(h.bq,{children:(0,t.jsx)(h.yv,{placeholder:"ترتيب حسب"})}),(0,t.jsxs)(h.gC,{children:[(0,t.jsx)(h.eb,{value:"created_at",children:"تاريخ الإنشاء"}),(0,t.jsx)(h.eb,{value:"name",children:"الاسم"}),(0,t.jsx)(h.eb,{value:"price",children:"السعر"}),(0,t.jsx)(h.eb,{value:"stock_quantity",children:"المخزون"}),(0,t.jsx)(h.eb,{value:"rating",children:"التقييم"})]})]}),(0,t.jsxs)(h.l6,{value:S,onValueChange:e=>M(e),children:[(0,t.jsx)(h.bq,{children:(0,t.jsx)(h.yv,{})}),(0,t.jsxs)(h.gC,{children:[(0,t.jsx)(h.eb,{value:"desc",children:"تنازلي"}),(0,t.jsx)(h.eb,{value:"asc",children:"تصاعدي"})]})]})]})})}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"arabic-text",children:["قائمة المنتجات (",v.length,")"]})}),(0,t.jsx)(n.Wu,{children:q?(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,t.jsx)("p",{className:"text-gray-500 mt-2 arabic-text",children:"جاري التحميل..."})]}):0===v.length?(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)(es.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-500 arabic-text",children:"لا توجد منتجات"})]}):(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)("table",{className:"w-full",children:[(0,t.jsx)("thead",{children:(0,t.jsxs)("tr",{className:"border-b",children:[(0,t.jsx)("th",{className:"text-right py-3 px-4 font-medium text-gray-900 dark:text-white arabic-text",children:"المنتج"}),(0,t.jsx)("th",{className:"text-right py-3 px-4 font-medium text-gray-900 dark:text-white arabic-text",children:"الفئة"}),(0,t.jsx)("th",{className:"text-right py-3 px-4 font-medium text-gray-900 dark:text-white arabic-text",children:"السعر"}),(0,t.jsx)("th",{className:"text-right py-3 px-4 font-medium text-gray-900 dark:text-white arabic-text",children:"المخزون"}),(0,t.jsx)("th",{className:"text-right py-3 px-4 font-medium text-gray-900 dark:text-white arabic-text",children:"التقييم"}),(0,t.jsx)("th",{className:"text-right py-3 px-4 font-medium text-gray-900 dark:text-white arabic-text",children:"الحالة"}),(0,t.jsx)("th",{className:"text-right py-3 px-4 font-medium text-gray-900 dark:text-white arabic-text",children:"النشر"}),(0,t.jsx)("th",{className:"text-right py-3 px-4 font-medium text-gray-900 dark:text-white arabic-text",children:"الإجراءات"})]})}),(0,t.jsx)("tbody",{children:v.map(e=>(0,t.jsxs)("tr",{className:"border-b hover:bg-gray-50 dark:hover:bg-gray-800",children:[(0,t.jsx)("td",{className:"py-4 px-4",children:(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("img",{src:e.images[0]||"/api/placeholder/80/80",alt:e.name,className:"w-16 h-16 rounded-lg object-cover border border-gray-200 dark:border-gray-700"}),e.images.length>1&&(0,t.jsx)("div",{className:"absolute -top-1 -right-1 bg-blue-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center",children:e.images.length})]}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsx)("p",{className:"font-medium text-gray-900 dark:text-white arabic-text line-clamp-1",children:e.name}),(0,t.jsx)("p",{className:"text-sm text-gray-500 arabic-text line-clamp-2 mt-1",children:e.description})]})]})}),(0,t.jsx)("td",{className:"py-4 px-4",children:(0,t.jsx)(d.E,{variant:"outline",className:"arabic-text",children:eW[e.category]})}),(0,t.jsx)("td",{className:"py-4 px-4",children:(0,t.jsxs)("div",{className:"text-sm",children:[(0,t.jsxs)("p",{className:"price font-medium text-gray-900 dark:text-white",children:[e.price," Dhs"]}),e.rental_price&&(0,t.jsxs)("p",{className:"price text-gray-500",children:["إيجار: ",e.rental_price," Dhs"]})]})}),(0,t.jsx)("td",{className:"py-4 px-4",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{className:`font-medium ${e.stock_quantity<20?"text-red-600":e.stock_quantity<50?"text-orange-600":"text-green-600"}`,children:e.stock_quantity}),e.stock_quantity<20&&(0,t.jsx)(eI.A,{className:"h-4 w-4 text-red-600"})]})}),(0,t.jsx)("td",{className:"py-4 px-4",children:(0,t.jsxs)("div",{className:"rating flex items-center gap-1",children:[(0,t.jsx)(el.A,{className:"h-4 w-4 text-yellow-400 fill-current"}),(0,t.jsx)("span",{className:"number text-sm font-medium",children:e.rating?.toFixed(1)||"N/A"}),(0,t.jsxs)("span",{className:"number text-xs text-gray-500",children:["(",e.reviews_count||0,")"]})]})}),(0,t.jsx)("td",{className:"py-4 px-4",children:(0,t.jsx)(d.E,{variant:e.is_available?"default":"secondary",className:"arabic-text",children:e.is_available?"متاح":"غير متاح"})}),(0,t.jsx)("td",{className:"py-4 px-4",children:(0,t.jsx)(d.E,{variant:e.is_published?"default":"destructive",className:"arabic-text",children:e.is_published?"منشور":"غير منشور"})}),(0,t.jsx)("td",{className:"py-4 px-4",children:(0,t.jsx)(R,{children:(0,t.jsxs)("div",{className:"actions flex items-center gap-2",children:[(0,t.jsxs)(O,{children:[(0,t.jsx)(I,{asChild:!0,children:(0,t.jsx)(c.$,{size:"sm",variant:"outline",asChild:!0,children:(0,t.jsx)("a",{href:`/product/${e.id}`,target:"_blank",children:(0,t.jsx)(ex.A,{className:"h-4 w-4"})})})}),(0,t.jsx)(ee,{children:(0,t.jsx)("p",{children:"عرض المنتج"})})]}),(0,t.jsxs)(O,{children:[(0,t.jsx)(I,{asChild:!0,children:(0,t.jsx)(c.$,{size:"sm",variant:"outline",onClick:()=>eu(e),children:(0,t.jsx)(eZ.A,{className:"h-4 w-4"})})}),(0,t.jsx)(ee,{children:(0,t.jsx)("p",{children:"تعديل المنتج"})})]}),(0,t.jsxs)(O,{children:[(0,t.jsx)(I,{asChild:!0,children:(0,t.jsx)(c.$,{size:"sm",variant:"outline",onClick:()=>eh(e.id),children:e.is_available?"\uD83D\uDD12":"\uD83D\uDD13"})}),(0,t.jsx)(ee,{children:(0,t.jsx)("p",{children:e.is_available?"إلغاء التوفر":"تفعيل التوفر"})})]}),(0,t.jsxs)(O,{children:[(0,t.jsx)(I,{asChild:!0,children:(0,t.jsx)(c.$,{size:"sm",variant:"outline",onClick:()=>em(e.id),children:e.is_published?"\uD83D\uDC41️":"\uD83D\uDE48"})}),(0,t.jsx)(ee,{children:(0,t.jsx)("p",{children:e.is_published?"إلغاء النشر":"نشر المنتج"})})]}),(0,t.jsxs)(O,{children:[(0,t.jsx)(I,{asChild:!0,children:(0,t.jsx)(c.$,{size:"sm",variant:"outline",onClick:()=>ec(e.id,e.name),className:"text-red-600 hover:text-red-700",children:(0,t.jsx)(eb.A,{className:"h-4 w-4"})})}),(0,t.jsx)(ee,{children:(0,t.jsx)("p",{children:"حذف المنتج"})})]})]})})})]},e.id))})]})})})]})]}),(0,t.jsx)(p.av,{value:"categories",className:"space-y-6",children:(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsx)(n.ZB,{className:"arabic-text",children:"إدارة الفئات"}),(0,t.jsx)(n.BT,{className:"arabic-text",children:"إضافة وتعديل وإدارة فئات المنتجات"})]}),(0,t.jsx)(n.Wu,{children:(0,t.jsxs)(u.XI,{children:[(0,t.jsx)(u.A0,{children:(0,t.jsxs)(u.Hj,{children:[(0,t.jsx)(u.nd,{className:"arabic-text",children:"الاسم"}),(0,t.jsx)(u.nd,{className:"arabic-text",children:"الرابط المختصر"}),(0,t.jsx)(u.nd,{className:"arabic-text",children:"الحالة"}),(0,t.jsx)(u.nd,{className:"arabic-text",children:"الترتيب"}),(0,t.jsx)(u.nd,{className:"arabic-text",children:"تاريخ الإنشاء"}),(0,t.jsx)(u.nd,{className:"arabic-text",children:"الإجراءات"})]})}),(0,t.jsx)(u.BF,{children:X.map(e=>(0,t.jsxs)(u.Hj,{children:[(0,t.jsx)(u.nA,{children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[e.icon&&(0,t.jsx)("span",{className:"text-lg",children:e.icon}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium arabic-text",children:e.name_ar}),e.description&&(0,t.jsx)("div",{className:"text-sm text-gray-500 arabic-text",children:e.description})]})]})}),(0,t.jsx)(u.nA,{children:(0,t.jsx)("code",{className:"bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded text-sm",children:e.slug})}),(0,t.jsx)(u.nA,{children:(0,t.jsx)(d.E,{variant:e.is_active?"default":"secondary",children:e.is_active?"نشط":"غير نشط"})}),(0,t.jsx)(u.nA,{children:e.order_index}),(0,t.jsx)(u.nA,{children:new Date(e.created_at).toLocaleDateString("en-US")}),(0,t.jsx)(u.nA,{children:(0,t.jsxs)(j.rI,{children:[(0,t.jsx)(j.ty,{asChild:!0,children:(0,t.jsx)(c.$,{variant:"ghost",className:"h-8 w-8 p-0",children:(0,t.jsx)(eG.A,{className:"h-4 w-4"})})}),(0,t.jsxs)(j.SQ,{align:"end",children:[(0,t.jsxs)(j._2,{onClick:()=>ey(e),children:[(0,t.jsx)(eZ.A,{className:"h-4 w-4 mr-2"}),"تعديل"]}),(0,t.jsxs)(j._2,{onClick:()=>eN(e.id),className:"text-red-600",children:[(0,t.jsx)(eb.A,{className:"h-4 w-4 mr-2"}),"حذف"]})]})]})})]},e.id))})]})})]})})]})]}),(0,t.jsx)(m.lG,{open:V,onOpenChange:H,children:(0,t.jsxs)(m.Cf,{className:"max-w-2xl",children:[(0,t.jsxs)(m.c7,{children:[(0,t.jsx)(m.L3,{className:"arabic-text",children:K?"تعديل الفئة":"إضافة فئة جديدة"}),(0,t.jsx)(m.rr,{className:"arabic-text",children:K?"تعديل بيانات الفئة":"أدخل بيانات الفئة الجديدة"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(x.J,{htmlFor:"category-name",className:"arabic-text",children:"اسم الفئة (مطلوب)"}),(0,t.jsx)(o.p,{id:"category-name",value:Q.name_ar,onChange:e=>ea(a=>({...a,name_ar:e.target.value})),placeholder:"أدخل اسم الفئة",className:"arabic-text"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(x.J,{htmlFor:"category-slug",className:"arabic-text",children:"الرابط المختصر (مطلوب)"}),(0,t.jsx)(o.p,{id:"category-slug",value:Q.slug,onChange:e=>ea(a=>({...a,slug:e.target.value})),placeholder:"category-slug"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(x.J,{htmlFor:"category-description",className:"arabic-text",children:"الوصف"}),(0,t.jsx)(g.T,{id:"category-description",value:Q.description,onChange:e=>ea(a=>({...a,description:e.target.value})),placeholder:"وصف الفئة",className:"arabic-text"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(x.J,{htmlFor:"category-icon",className:"arabic-text",children:"الأيقونة"}),(0,t.jsx)(o.p,{id:"category-icon",value:Q.icon,onChange:e=>ea(a=>({...a,icon:e.target.value})),placeholder:"\uD83C\uDFF7️"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("input",{type:"checkbox",id:"category-active",checked:Q.is_active,onChange:e=>ea(a=>({...a,is_active:e.target.checked}))}),(0,t.jsx)(x.J,{htmlFor:"category-active",className:"arabic-text",children:"فئة نشطة"})]}),(0,t.jsxs)("div",{className:"flex justify-end gap-3",children:[(0,t.jsx)(c.$,{variant:"outline",onClick:ev,children:"إلغاء"}),(0,t.jsx)(c.$,{onClick:ef,children:K?"تحديث":"إضافة"})]})]})]})}),(0,t.jsx)(m.lG,{open:D,onOpenChange:F,children:(0,t.jsxs)(m.Cf,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,t.jsxs)(m.c7,{children:[(0,t.jsx)(m.L3,{className:"arabic-text",children:"إضافة منتج جديد"}),(0,t.jsx)(m.rr,{className:"arabic-text",children:"أدخل تفاصيل المنتج الجديد"})]}),(0,t.jsx)(eq,{onSubmit:ep,onCancel:()=>F(!1)})]})}),(0,t.jsx)(eP,{product:L,open:B,onOpenChange:Z,onSave:ej}),(0,t.jsx)(eR.T,{open:G.open,onOpenChange:e=>W(a=>({...a,open:e})),title:"تأكيد حذف المنتج",description:`هل أنت متأكد من حذف المنتج "${G.productName}"؟ هذا الإجراء لا يمكن التراجع عنه.`,confirmText:"حذف",cancelText:"إلغاء",variant:"destructive",onConfirm:eo}),(0,t.jsx)(eD.N9,{toasts:s.toasts,onRemove:s.removeToast})]}):(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-red-600 arabic-text",children:"غير مصرح لك بالوصول"}),(0,t.jsx)("p",{className:"text-gray-600 mt-2 arabic-text",children:"هذه الصفحة مخصصة للمديرين فقط"}),(0,t.jsxs)("p",{className:"text-sm text-gray-500 mt-2",children:["User: ",e?"موجود":"غير موجود"]}),(0,t.jsxs)("p",{className:"text-sm text-gray-500",children:["Role: ",a?.role||"غير محدد"]})]})})}},55146:(e,a,s)=>{"use strict";s.d(a,{B8:()=>E,UC:()=>$,bL:()=>T,l9:()=>z});var t=s(43210),r=s(70569),i=s(11273),l=s(72942),n=s(46059),c=s(14163),d=s(43),o=s(65551),x=s(96963),h=s(60687),m="Tabs",[p,u]=(0,i.A)(m,[l.RG]),j=(0,l.RG)(),[g,b]=p(m),f=t.forwardRef((e,a)=>{let{__scopeTabs:s,value:t,onValueChange:r,defaultValue:i,orientation:l="horizontal",dir:n,activationMode:p="automatic",...u}=e,j=(0,d.jH)(n),[b,f]=(0,o.i)({prop:t,onChange:r,defaultProp:i??"",caller:m});return(0,h.jsx)(g,{scope:s,baseId:(0,x.B)(),value:b,onValueChange:f,orientation:l,dir:j,activationMode:p,children:(0,h.jsx)(c.sG.div,{dir:j,"data-orientation":l,...u,ref:a})})});f.displayName=m;var v="TabsList",y=t.forwardRef((e,a)=>{let{__scopeTabs:s,loop:t=!0,...r}=e,i=b(v,s),n=j(s);return(0,h.jsx)(l.bL,{asChild:!0,...n,orientation:i.orientation,dir:i.dir,loop:t,children:(0,h.jsx)(c.sG.div,{role:"tablist","aria-orientation":i.orientation,...r,ref:a})})});y.displayName=v;var N="TabsTrigger",w=t.forwardRef((e,a)=>{let{__scopeTabs:s,value:t,disabled:i=!1,...n}=e,d=b(N,s),o=j(s),x=_(d.baseId,t),m=A(d.baseId,t),p=t===d.value;return(0,h.jsx)(l.q7,{asChild:!0,...o,focusable:!i,active:p,children:(0,h.jsx)(c.sG.button,{type:"button",role:"tab","aria-selected":p,"aria-controls":m,"data-state":p?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:x,...n,ref:a,onMouseDown:(0,r.m)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():d.onValueChange(t)}),onKeyDown:(0,r.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&d.onValueChange(t)}),onFocus:(0,r.m)(e.onFocus,()=>{let e="manual"!==d.activationMode;p||i||!e||d.onValueChange(t)})})})});w.displayName=N;var k="TabsContent",C=t.forwardRef((e,a)=>{let{__scopeTabs:s,value:r,forceMount:i,children:l,...d}=e,o=b(k,s),x=_(o.baseId,r),m=A(o.baseId,r),p=r===o.value,u=t.useRef(p);return t.useEffect(()=>{let e=requestAnimationFrame(()=>u.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,h.jsx)(n.C,{present:i||p,children:({present:s})=>(0,h.jsx)(c.sG.div,{"data-state":p?"active":"inactive","data-orientation":o.orientation,role:"tabpanel","aria-labelledby":x,hidden:!s,id:m,tabIndex:0,...d,ref:a,style:{...e.style,animationDuration:u.current?"0s":void 0},children:s&&l})})});function _(e,a){return`${e}-trigger-${a}`}function A(e,a){return`${e}-content-${a}`}C.displayName=k;var T=f,E=y,z=w,$=C},57802:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Graduation Toqs\\\\frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\products\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\admin\\products\\page.tsx","default")},62140:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("file-image",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["circle",{cx:"10",cy:"12",r:"2",key:"737tya"}],["path",{d:"m20 17-1.296-1.296a2.41 2.41 0 0 0-3.408 0L9 22",key:"wt3hpn"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64398:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},70615:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},78200:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]])},79550:(e,a,s)=>{Promise.resolve().then(s.bind(s,57802))},79551:e=>{"use strict";e.exports=require("url")},85763:(e,a,s)=>{"use strict";s.d(a,{Xi:()=>c,av:()=>d,j7:()=>n,tU:()=>l});var t=s(60687);s(43210);var r=s(55146),i=s(4780);function l({className:e,...a}){return(0,t.jsx)(r.bL,{"data-slot":"tabs",className:(0,i.cn)("flex flex-col gap-2",e),...a})}function n({className:e,...a}){return(0,t.jsx)(r.B8,{"data-slot":"tabs-list",className:(0,i.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",e),...a})}function c({className:e,...a}){return(0,t.jsx)(r.l9,{"data-slot":"tabs-trigger",className:(0,i.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...a})}function d({className:e,...a}){return(0,t.jsx)(r.UC,{"data-slot":"tabs-content",className:(0,i.cn)("flex-1 outline-none",e),...a})}},91821:(e,a,s)=>{"use strict";s.d(a,{Fc:()=>n,TN:()=>c});var t=s(60687);s(43210);var r=s(24224),i=s(4780);let l=(0,r.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function n({className:e,variant:a,...s}){return(0,t.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,i.cn)(l({variant:a}),e),...s})}function c({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"alert-description",className:(0,i.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",e),...a})}}};var a=require("../../../../webpack-runtime.js");a.C(e);var s=e=>a(a.s=e),t=a.X(0,[4447,8773,4097,6126,5068,3932,7801,1428],()=>s(15116));module.exports=t})();