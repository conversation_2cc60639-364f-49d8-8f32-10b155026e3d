exports.id=3932,exports.ids=[3932],exports.modules={4780:(e,t,r)=>{"use strict";r.d(t,{cn:()=>o});var s=r(49384),a=r(82348);function o(...e){return(0,a.QP)((0,s.$)(e))}},8520:(e,t,r)=>{"use strict";r.d(t,{B:()=>n});var s=r(43210),a=r(89589);let o=JSON.parse('{"common":{"loading":"جاري التحميل...","error":"حدث خطأ","success":"تم بنجاح","cancel":"إلغاء","confirm":"تأكيد","save":"حفظ","edit":"تعديل","delete":"حذف","search":"بحث","filter":"تصفية","sort":"ترتيب","next":"التالي","previous":"السابق","close":"إغلاق"},"navigation":{"home":"الرئيسية","catalog":"الكتالوج","customize":"التخصيص","orders":"الطلبات","profile":"الملف الشخصي","dashboard":"لوحة التحكم","login":"تسجيل الدخول","register":"إنشاء حساب","logout":"تسجيل الخروج","trackOrder":"تتبع الطلب","about":"من نحن","contact":"تواصل معنا","cart":"السلة","wishlist":"المفضلة"},"home":{"title":"أول منصة مغربية لأزياء التخرج","subtitle":"منصة ذكية متعددة اللغات لتأجير وبيع أزياء التخرج مع إمكانيات التخصيص والذكاء الاصطناعي","startCustomizing":"ابدأ التخصيص الآن","browseCatalog":"تصفح الكتالوج","hero":{"title":"\uD83C\uDF93 أول منصة مغربية ذكية لأزياء التخرج","subtitle":"اكتشف مجموعة واسعة من أزياء التخرج الأنيقة مع إمكانيات التخصيص المتقدمة والذكاء الاصطناعي","description":"منصة متكاملة تجمع بين التقنيات الحديثة والتصاميم العصرية لتوفير تجربة فريدة في عالم أزياء التخرج","cta":{"primary":"ابدأ رحلتك الآن","secondary":"اكتشف المجموعة","watchDemo":"شاهد العرض التوضيحي"},"badges":{"trusted":"موثوق من قبل +1000 طالب","schools":"+50 مدرسة وجامعة","satisfaction":"رضا العملاء 98%"}},"stats":{"title":"أرقام تتحدث عن نفسها","subtitle":"إنجازاتنا في أرقام","customers":{"number":"1,200+","label":"عميل راضٍ"},"schools":{"number":"50+","label":"مدرسة وجامعة"},"orders":{"number":"2,500+","label":"طلب مكتمل"},"satisfaction":{"number":"98%","label":"معدل الرضا"}},"features":{"title":"ميزات تجعلنا الأفضل","subtitle":"اكتشف ما يميزنا عن الآخرين","customization":{"title":"تخصيص متقدم","description":"اختر الألوان والأنماط والإكسسوارات حسب ذوقك مع معاينة فورية ثلاثية الأبعاد"},"ai":{"title":"ذكاء اصطناعي","description":"مساعد ذكي يقدم اقتراحات مخصصة ويساعدك في اختيار التصميم المثالي"},"roles":{"title":"أدوار متعددة","description":"لوحات تحكم متخصصة للطلاب والمدارس وشركاء التوصيل والإدارة"},"tracking":{"title":"تتبع متقدم","description":"تتبع طلبك بالتفصيل من لحظة الطلب حتى التسليم مع إشعارات فورية"},"quality":{"title":"جودة عالية","description":"أقمشة فاخرة وتصنيع احترافي يضمن مظهراً أنيقاً في يومك المميز"},"support":{"title":"دعم 24/7","description":"فريق دعم متخصص متاح على مدار الساعة لمساعدتك في أي استفسار"}},"products":{"title":"مجموعة مختارة من أفضل التصاميم","subtitle":"اكتشف أحدث صيحات أزياء التخرج","viewAll":"عرض جميع المنتجات","featured":"منتجات مميزة","newArrivals":"وصل حديثاً","popular":"الأكثر طلباً"},"testimonials":{"title":"ماذا يقول عملاؤنا","subtitle":"تجارب حقيقية من طلاب تخرجوا معنا","viewAll":"عرض جميع التقييمات"},"ai":{"title":"قوة الذكاء الاصطناعي في خدمتك","subtitle":"تقنيات متقدمة لتجربة تخصيص فريدة","features":{"assistant":{"title":"مساعد ذكي","description":"احصل على اقتراحات مخصصة بناءً على تفضيلاتك ومناسبتك"},"visualization":{"title":"معاينة ثلاثية الأبعاد","description":"شاهد تصميمك بتقنية ثلاثية الأبعاد قبل الطلب"},"recommendations":{"title":"توصيات ذكية","description":"اقتراحات مبنية على الذكاء الاصطناعي لأفضل التصاميم"},"chat":{"title":"دردشة ذكية","description":"تحدث مع مساعدنا الذكي للحصول على المساعدة الفورية"}}},"cta":{"title":"جاهز لبدء رحلتك؟","subtitle":"انضم إلى آلاف الطلاب الذين اختاروا التميز","description":"احصل على زي التخرج المثالي مع خدماتنا المتميزة","button":"ابدأ الآن","contact":"تحدث معنا"},"languageSupport":"دعم متعدد اللغات","footer":"\xa9 2024 Graduation Toqs - أول منصة مغربية لأزياء التخرج"},"products":{"gown":"ثوب التخرج","cap":"قبعة التخرج","tassel":"شرابة","stole":"وشاح","hood":"غطاء الرأس","colors":"الألوان","sizes":"المقاسات","price":"السعر","rent":"إيجار","buy":"شراء","addToCart":"إضافة للسلة"},"auth":{"login":"تسجيل الدخول","register":"إنشاء حساب جديد","email":"البريد الإلكتروني","password":"كلمة المرور","confirmPassword":"تأكيد كلمة المرور","firstName":"الاسم الأول","lastName":"اسم العائلة","phone":"رقم الهاتف","forgotPassword":"نسيت كلمة المرور؟","resetPassword":"إعادة تعيين كلمة المرور","newPassword":"كلمة المرور الجديدة","rememberMe":"تذكرني","alreadyHaveAccount":"لديك حساب بالفعل؟","dontHaveAccount":"ليس لديك حساب؟","sendResetLink":"إرسال رابط إعادة التعيين","resetLinkSent":"تم إرسال رابط إعادة التعيين","backToLogin":"العودة لتسجيل الدخول","logout":"تسجيل الخروج"}}'),i={ar:o,fr:JSON.parse('{"common":{"loading":"Chargement...","error":"Une erreur s\'est produite","success":"Succ\xe8s","cancel":"Annuler","confirm":"Confirmer","save":"Enregistrer","edit":"Modifier","delete":"Supprimer","search":"Rechercher","filter":"Filtrer","sort":"Trier","next":"Suivant","previous":"Pr\xe9c\xe9dent","close":"Fermer"},"navigation":{"home":"Accueil","catalog":"Catalogue","customize":"Personnaliser","orders":"Commandes","profile":"Profil","dashboard":"Tableau de bord","login":"Connexion","register":"S\'inscrire","logout":"D\xe9connexion","trackOrder":"Suivre la commande","about":"\xc0 propos","contact":"Contactez-nous","cart":"Panier","wishlist":"Liste de souhaits"},"home":{"title":"Premi\xe8re plateforme marocaine de tenues de remise de dipl\xf4mes","subtitle":"Plateforme intelligente multilingue pour la location et la vente de tenues de remise de dipl\xf4mes avec personnalisation et IA","startCustomizing":"Commencer la personnalisation","browseCatalog":"Parcourir le catalogue","features":{"customization":{"title":"Personnalisation des tenues","description":"Choisissez les couleurs, styles et accessoires selon vos go\xfbts"},"ai":{"title":"Intelligence artificielle","description":"Assistant intelligent et suggestions personnalis\xe9es"},"roles":{"title":"R\xf4les multiples","description":"Tableaux de bord pour \xe9tudiants, \xe9coles et administration"},"tracking":{"title":"Suivi des commandes","description":"Suivez votre commande de la commande \xe0 la livraison"}},"languageSupport":"Support multilingue","footer":"\xa9 2024 Graduation Toqs - Premi\xe8re plateforme marocaine de tenues de remise de dipl\xf4mes"},"products":{"gown":"Toge de remise de dipl\xf4mes","cap":"Toque","tassel":"Gland","stole":"\xc9tole","hood":"Capuche","colors":"Couleurs","sizes":"Tailles","price":"Prix","rent":"Louer","buy":"Acheter","addToCart":"Ajouter au panier"},"auth":{"login":"Connexion","register":"Cr\xe9er un compte","email":"Email","password":"Mot de passe","confirmPassword":"Confirmer le mot de passe","firstName":"Pr\xe9nom","lastName":"Nom de famille","phone":"Num\xe9ro de t\xe9l\xe9phone","forgotPassword":"Mot de passe oubli\xe9 ?","resetPassword":"R\xe9initialiser le mot de passe","newPassword":"Nouveau mot de passe","rememberMe":"Se souvenir de moi","alreadyHaveAccount":"Vous avez d\xe9j\xe0 un compte ?","dontHaveAccount":"Vous n\'avez pas de compte ?","sendResetLink":"Envoyer le lien de r\xe9initialisation","resetLinkSent":"Lien de r\xe9initialisation envoy\xe9","backToLogin":"Retour \xe0 la connexion","logout":"Se d\xe9connecter"}}'),en:JSON.parse('{"common":{"loading":"Loading...","error":"An error occurred","success":"Success","cancel":"Cancel","confirm":"Confirm","save":"Save","edit":"Edit","delete":"Delete","search":"Search","filter":"Filter","sort":"Sort","next":"Next","previous":"Previous","close":"Close"},"navigation":{"home":"Home","catalog":"Catalog","customize":"Customize","orders":"Orders","profile":"Profile","dashboard":"Dashboard","login":"Login","register":"Register","logout":"Logout","trackOrder":"Track Order","about":"About Us","contact":"Contact Us","cart":"Cart","wishlist":"Wishlist"},"home":{"title":"First Moroccan Graduation Attire Platform","subtitle":"Smart multilingual platform for graduation attire rental and sales with customization and AI features","startCustomizing":"Start Customizing Now","browseCatalog":"Browse Catalog","features":{"customization":{"title":"Attire Customization","description":"Choose colors, styles, and accessories according to your taste"},"ai":{"title":"Artificial Intelligence","description":"Smart assistant and personalized suggestions"},"roles":{"title":"Multiple Roles","description":"Dashboards for students, schools, and administration"},"tracking":{"title":"Order Tracking","description":"Track your order from placement to delivery"}},"languageSupport":"Multilingual Support","footer":"\xa9 2024 Graduation Toqs - First Moroccan Graduation Attire Platform"},"products":{"gown":"Graduation Gown","cap":"Graduation Cap","tassel":"Tassel","stole":"Stole","hood":"Hood","colors":"Colors","sizes":"Sizes","price":"Price","rent":"Rent","buy":"Buy","addToCart":"Add to Cart"},"auth":{"login":"Login","register":"Create Account","email":"Email","password":"Password","confirmPassword":"Confirm Password","firstName":"First Name","lastName":"Last Name","phone":"Phone Number","forgotPassword":"Forgot Password?","resetPassword":"Reset Password","newPassword":"New Password","rememberMe":"Remember Me","alreadyHaveAccount":"Already have an account?","dontHaveAccount":"Don\'t have an account?","sendResetLink":"Send Reset Link","resetLinkSent":"Reset Link Sent","backToLogin":"Back to Login","logout":"Logout"}}')};function n(){let[e,t]=(0,s.useState)(a.q);return{locale:e,changeLocale:e=>{t(e),localStorage.setItem("locale",e),document.documentElement.lang=e,document.documentElement.dir="ar"===e?"rtl":"ltr"},t:t=>{let r=t.split("."),s=i[e];for(let e of r)s=s?.[e];return s||t}}}},8827:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},14329:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h});var s=r(60687);r(43210);var a=r(29523),o=r(44493),i=r(43984),n=r(30917),l=r(27351),d=r(43649),c=r(78122),u=r(32192),m=r(28559);function h({error:e,reset:t}){return(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-red-50 via-white to-orange-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",children:[(0,s.jsx)("header",{className:"bg-white dark:bg-gray-800 shadow-sm border-b",children:(0,s.jsxs)("div",{className:"container mx-auto px-4 py-4 flex justify-between items-center",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(l.A,{className:"h-8 w-8 text-blue-600 dark:text-blue-400"}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Toqs"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(n.J,{}),(0,s.jsx)(i.U,{})]})]})}),(0,s.jsx)("main",{className:"container mx-auto px-4 py-16 flex items-center justify-center min-h-[calc(100vh-80px)]",children:(0,s.jsx)(o.Zp,{className:"w-full max-w-2xl",children:(0,s.jsxs)(o.Wu,{className:"p-8 text-center",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("div",{className:"mx-auto w-24 h-24 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mb-4",children:(0,s.jsx)(d.A,{className:"h-12 w-12 text-red-600 dark:text-red-400"})}),(0,s.jsx)("h1",{className:"text-4xl font-bold text-gray-900 dark:text-white mb-2",children:"حدث خطأ!"}),(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-700 dark:text-gray-300 arabic-text",children:"عذراً، حدث خطأ غير متوقع"})]}),(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400 text-lg mb-4 arabic-text",children:"نعتذر عن هذا الإزعاج. حدث خطأ تقني أثناء معالجة طلبك."}),(0,s.jsx)("p",{className:"text-gray-500 dark:text-gray-500 arabic-text",children:"يرجى المحاولة مرة أخرى أو العودة إلى الصفحة الرئيسية."})]}),!1,(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,s.jsxs)(a.$,{onClick:t,className:"flex items-center gap-2",children:[(0,s.jsx)(c.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"arabic-text",children:"إعادة المحاولة"})]}),(0,s.jsxs)(a.$,{variant:"outline",onClick:()=>{window.location.href="/"},className:"flex items-center gap-2",children:[(0,s.jsx)(u.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"arabic-text",children:"الصفحة الرئيسية"})]}),(0,s.jsxs)(a.$,{variant:"outline",onClick:()=>{window.history.back()},className:"flex items-center gap-2",children:[(0,s.jsx)(m.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"arabic-text",children:"العودة للخلف"})]})]}),(0,s.jsxs)("div",{className:"mt-8 pt-8 border-t border-gray-200 dark:border-gray-700",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 arabic-text",children:"إذا استمر الخطأ"}),(0,s.jsxs)("div",{className:"text-sm text-gray-600 dark:text-gray-400 space-y-2 arabic-text",children:[(0,s.jsx)("p",{children:"• تأكد من اتصالك بالإنترنت"}),(0,s.jsx)("p",{children:"• امسح ذاكرة التخزين المؤقت للمتصفح"}),(0,s.jsx)("p",{children:"• جرب إعادة تحميل الصفحة"}),(0,s.jsx)("p",{children:"• تواصل مع فريق الدعم إذا استمرت المشكلة"})]})]})]})})})]})}},21342:(e,t,r)=>{"use strict";r.d(t,{SQ:()=>l,_2:()=>d,lp:()=>c,mB:()=>u,rI:()=>i,ty:()=>n});var s=r(60687);r(43210);var a=r(26312),o=r(4780);function i({...e}){return(0,s.jsx)(a.bL,{"data-slot":"dropdown-menu",...e})}function n({...e}){return(0,s.jsx)(a.l9,{"data-slot":"dropdown-menu-trigger",...e})}function l({className:e,sideOffset:t=4,...r}){return(0,s.jsx)(a.ZL,{children:(0,s.jsx)(a.UC,{"data-slot":"dropdown-menu-content",sideOffset:t,className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",e),...r})})}function d({className:e,inset:t,variant:r="default",...i}){return(0,s.jsx)(a.q7,{"data-slot":"dropdown-menu-item","data-inset":t,"data-variant":r,className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...i})}function c({className:e,inset:t,...r}){return(0,s.jsx)(a.JU,{"data-slot":"dropdown-menu-label","data-inset":t,className:(0,o.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",e),...r})}function u({className:e,...t}){return(0,s.jsx)(a.wv,{"data-slot":"dropdown-menu-separator",className:(0,o.cn)("bg-border -mx-1 my-1 h-px",e),...t})}},26056:(e,t,r)=>{Promise.resolve().then(r.bind(r,54413))},28168:(e,t,r)=>{Promise.resolve().then(r.bind(r,52581)),Promise.resolve().then(r.bind(r,40715)),Promise.resolve().then(r.bind(r,96871)),Promise.resolve().then(r.bind(r,63213)),Promise.resolve().then(r.bind(r,28253)),Promise.resolve().then(r.bind(r,86748)),Promise.resolve().then(r.bind(r,46952))},28253:(e,t,r)=>{"use strict";r.d(t,{CartProvider:()=>i,_:()=>n});var s=r(60687),a=r(43210);let o=(0,a.createContext)(void 0);function i({children:e}){let[t,r]=(0,a.useState)([]),[i,n]=(0,a.useState)([]),l=e=>{r(t=>t.filter(t=>t.id!==e))},d={cartItems:t,cartCount:t.reduce((e,t)=>e+t.quantity,0),addToCart:(e,t,s="purchase")=>{r(r=>r.find(t=>t.id===e&&t.type===s)?r.map(t=>t.id===e&&t.type===s?{...t,quantity:t.quantity+1}:t):[...r,{id:e,name:t.name,price:"rental"===s&&t.rental_price||t.price,image:t.images?.[0]||t.image||"/images/products/placeholder.jpg",quantity:1,type:s,rental_price:t.rental_price}])},removeFromCart:l,updateQuantity:(e,t)=>{if(t<=0)return void l(e);r(r=>r.map(r=>r.id===e?{...r,quantity:t}:r))},clearCart:()=>{r([])},getCartTotal:()=>t.reduce((e,t)=>e+t.price*t.quantity,0),wishlistItems:i,wishlistCount:i.length,addToWishlist:(e,t)=>{n(r=>r.find(t=>t.id===e)?r:[...r,{id:e,name:t.name,price:t.price,image:t.images?.[0]||t.image||"/images/products/placeholder.jpg",rental_price:t.rental_price}])},removeFromWishlist:e=>{n(t=>t.filter(t=>t.id!==e))},isInWishlist:e=>i.some(t=>t.id===e),clearWishlist:()=>{n([])}};return(0,s.jsx)(o.Provider,{value:d,children:e})}function n(){let e=(0,a.useContext)(o);if(void 0===e)throw Error("useCart must be used within a CartProvider");return e}},29131:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>a});var s=r(12907);let a=(0,s.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\contexts\\AuthContext.tsx","AuthProvider");(0,s.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\contexts\\AuthContext.tsx","useAuth")},29523:(e,t,r)=>{"use strict";r.d(t,{$:()=>l,r:()=>n});var s=r(60687);r(43210);var a=r(8730),o=r(24224),i=r(4780);let n=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:t,size:r,asChild:o=!1,...l}){let d=o?a.DX:"button";return(0,s.jsx)(d,{"data-slot":"button",className:(0,i.cn)(n({variant:t,size:r,className:e})),...l})}},30917:(e,t,r)=>{"use strict";r.d(t,{J:()=>c});var s=r(60687);r(43210);var a=r(11437),o=r(13964),i=r(8520),n=r(89589),l=r(29523),d=r(21342);function c(){let{locale:e,changeLocale:t}=(0,i.B)(),r={flag:n.ax[e],name:n.L$[e],code:e.toUpperCase()};return(0,s.jsxs)(d.rI,{children:[(0,s.jsx)(d.ty,{asChild:!0,children:(0,s.jsxs)(l.$,{variant:"ghost",size:"sm",className:"h-9 px-3 gap-2 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-300 group",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:"text-lg group-hover:scale-110 transition-transform duration-300",children:r.flag}),(0,s.jsx)("span",{className:"hidden sm:inline-block text-sm font-medium",children:r.code})]}),(0,s.jsx)(a.A,{className:"h-4 w-4 opacity-60 group-hover:opacity-100 transition-opacity duration-300"}),(0,s.jsx)("span",{className:"sr-only",children:"تغيير اللغة / Change language"})]})}),(0,s.jsxs)(d.SQ,{align:"end",className:"w-48 p-2",sideOffset:8,children:[(0,s.jsx)(d.lp,{className:"text-xs font-medium text-gray-500 dark:text-gray-400 px-2 py-1",children:"ar"===e?"اختر اللغة":"fr"===e?"Choisir la langue":"Choose Language"}),(0,s.jsx)(d.mB,{}),Object.entries(n.L$).map(([r,a])=>(0,s.jsxs)(d._2,{onClick:()=>t(r),className:`flex items-center gap-3 px-3 py-2.5 rounded-lg cursor-pointer transition-all duration-200 ${e===r?"bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300":"hover:bg-gray-100 dark:hover:bg-gray-700"}`,children:[(0,s.jsx)("span",{className:"text-lg",children:n.ax[r]}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("div",{className:"font-medium text-sm",children:a}),(0,s.jsx)("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"ar"===r?"العربية":"fr"===r?"Fran\xe7ais":"English"})]}),e===r&&(0,s.jsx)(o.A,{className:"h-4 w-4 text-blue-600 dark:text-blue-400"})]},r))]})]})}},37043:(e,t,r)=>{"use strict";r.d(t,{CartProvider:()=>a});var s=r(12907);let a=(0,s.registerClientReference)(function(){throw Error("Attempted to call CartProvider() from the server but CartProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\contexts\\CartContext.tsx","CartProvider");(0,s.registerClientReference)(function(){throw Error("Attempted to call useCart() from the server but useCart is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\contexts\\CartContext.tsx","useCart")},40715:(e,t,r)=>{"use strict";r.d(t,{LiveChat:()=>b});var s=r(60687),a=r(43210),o=r(63213),i=r(44493),n=r(29523),l=r(89667),d=r(42692),c=r(48730),u=r(5336),m=r(58887),h=r(79351),x=r(49153),p=r(11860),f=r(3876),g=r(60020),v=r(27900);function b(){let{user:e,profile:t}=(0,o.A)(),[r,b]=(0,a.useState)(!1),[j,N]=(0,a.useState)(!1),[y,w]=(0,a.useState)([]),[C,k]=(0,a.useState)(""),[P,A]=(0,a.useState)(!1),[S,T]=(0,a.useState)(null),[E,D]=(0,a.useState)(!1),z=(0,a.useRef)(null),I=e=>{let t={...e,id:Date.now().toString(),timestamp:new Date().toISOString(),status:e.isFromUser?"sending":"delivered"};w(e=>[...e,t]),e.isFromUser&&setTimeout(()=>{w(e=>e.map(e=>e.id===t.id?{...e,status:"delivered"}:e))},1e3)},L=()=>{C.trim()&&(I({content:C,isFromUser:!0}),k(""),D(!0),setTimeout(()=>{D(!1);let e=["شكراً لك على تواصلك معنا. سأقوم بالتحقق من هذا الأمر.","فهمت طلبك. دعني أساعدك في حل هذه المشكلة.","هذا سؤال ممتاز. سأحتاج لبعض التفاصيل الإضافية.","سأقوم بتحويل طلبك للقسم المختص وسنتواصل معك قريباً.","هل يمكنك تزويدي برقم الطلب لأتمكن من مساعدتك بشكل أفضل؟"];I({content:e[Math.floor(Math.random()*e.length)],isFromUser:!1})},2e3))},R=e=>{switch(e){case"sending":return(0,s.jsx)(c.A,{className:"h-3 w-3 text-gray-400"});case"sent":return(0,s.jsx)(u.A,{className:"h-3 w-3 text-gray-400"});case"delivered":return(0,s.jsx)(u.A,{className:"h-3 w-3 text-blue-500"});case"read":return(0,s.jsx)(u.A,{className:"h-3 w-3 text-green-500"});default:return null}};return r?(0,s.jsxs)(i.Zp,{className:`fixed bottom-6 right-6 z-50 shadow-xl transition-all duration-300 ${j?"w-80 h-16":"w-80 h-96"}`,children:[(0,s.jsx)(i.aR,{className:"p-4 bg-blue-600 text-white rounded-t-lg",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-white rounded-full flex items-center justify-center",children:(0,s.jsx)(m.A,{className:"h-4 w-4 text-blue-600"})}),P&&(0,s.jsx)("div",{className:"absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-medium text-sm arabic-text",children:P?"الدردشة المباشرة":"جاري الاتصال..."}),S&&(0,s.jsxs)("p",{className:"text-xs opacity-90 arabic-text",children:[S.name," - ","online"===S.status?"متاح":"غير متاح"]})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>N(!j),className:"h-8 w-8 p-0 text-white hover:bg-white/20",children:j?(0,s.jsx)(h.A,{className:"h-4 w-4"}):(0,s.jsx)(x.A,{className:"h-4 w-4"})}),(0,s.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>b(!1),className:"h-8 w-8 p-0 text-white hover:bg-white/20",children:(0,s.jsx)(p.A,{className:"h-4 w-4"})})]})]})}),!j&&(0,s.jsxs)(i.Wu,{className:"p-0 flex flex-col h-80",children:[(0,s.jsx)(d.F,{className:"flex-1 p-4",children:P?(0,s.jsxs)("div",{className:"space-y-4",children:[y.map(e=>(0,s.jsx)("div",{className:`flex ${e.isFromUser?"justify-end":"justify-start"}`,children:(0,s.jsxs)("div",{className:`max-w-[80%] ${e.isFromUser?"bg-blue-600 text-white rounded-l-lg rounded-tr-lg":"bg-gray-100 dark:bg-gray-800 rounded-r-lg rounded-tl-lg"} p-3`,children:[(0,s.jsx)("p",{className:"text-sm arabic-text",children:e.content}),(0,s.jsxs)("div",{className:"flex items-center justify-between mt-1",children:[(0,s.jsx)("span",{className:"text-xs opacity-70",children:new Date(e.timestamp).toLocaleTimeString("ar-SA",{hour:"2-digit",minute:"2-digit"})}),e.isFromUser&&R(e.status)]})]})},e.id)),E&&(0,s.jsx)("div",{className:"flex justify-start",children:(0,s.jsx)("div",{className:"bg-gray-100 dark:bg-gray-800 rounded-r-lg rounded-tl-lg p-3",children:(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsxs)("div",{className:"flex gap-1",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce"}),(0,s.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),(0,s.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]}),(0,s.jsx)("span",{className:"text-xs text-gray-500 mr-2 arabic-text",children:"يكتب..."})]})})}),(0,s.jsx)("div",{ref:z})]}):(0,s.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"}),(0,s.jsx)("p",{className:"text-sm text-gray-500 arabic-text",children:"جاري الاتصال بفريق الدعم..."})]})})}),P&&(0,s.jsxs)("div",{className:"p-4 border-t",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(n.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",children:(0,s.jsx)(f.A,{className:"h-4 w-4"})}),(0,s.jsx)(l.p,{value:C,onChange:e=>k(e.target.value),placeholder:"اكتب رسالتك...",className:"flex-1 arabic-text",onKeyPress:e=>{"Enter"===e.key&&L()}}),(0,s.jsx)(n.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",children:(0,s.jsx)(g.A,{className:"h-4 w-4"})}),(0,s.jsx)(n.$,{onClick:L,disabled:!C.trim(),size:"sm",children:(0,s.jsx)(v.A,{className:"h-4 w-4"})})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between mt-2 text-xs text-gray-500",children:[(0,s.jsx)("span",{className:"arabic-text",children:"اضغط Enter للإرسال"}),S&&(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,s.jsxs)("span",{className:"arabic-text",children:[S.name," متاح"]})]})]})]})]})]}):(0,s.jsx)(n.$,{onClick:()=>b(!0),className:"fixed bottom-6 right-6 z-50 rounded-full w-14 h-14 shadow-lg",size:"lg",children:(0,s.jsx)(m.A,{className:"h-6 w-6"})})}},41320:(e,t,r)=>{Promise.resolve().then(r.bind(r,6931)),Promise.resolve().then(r.bind(r,42629)),Promise.resolve().then(r.bind(r,83701)),Promise.resolve().then(r.bind(r,29131)),Promise.resolve().then(r.bind(r,37043)),Promise.resolve().then(r.bind(r,67270)),Promise.resolve().then(r.bind(r,81326))},42629:(e,t,r)=>{"use strict";r.d(t,{LiveChat:()=>a});var s=r(12907);let a=(0,s.registerClientReference)(function(){throw Error("Attempted to call LiveChat() from the server but LiveChat is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\chat\\LiveChat.tsx","LiveChat");(0,s.registerClientReference)(function(){throw Error("Attempted to call ChatButton() from the server but ChatButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\chat\\LiveChat.tsx","ChatButton")},42692:(e,t,r)=>{"use strict";r.d(t,{F:()=>i});var s=r(60687);r(43210);var a=r(68123),o=r(4780);function i({className:e,children:t,...r}){return(0,s.jsxs)(a.bL,{"data-slot":"scroll-area",className:(0,o.cn)("relative",e),...r,children:[(0,s.jsx)(a.LM,{"data-slot":"scroll-area-viewport",className:"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1",children:t}),(0,s.jsx)(n,{}),(0,s.jsx)(a.OK,{})]})}function n({className:e,orientation:t="vertical",...r}){return(0,s.jsx)(a.VM,{"data-slot":"scroll-area-scrollbar",orientation:t,className:(0,o.cn)("flex touch-none p-px transition-colors select-none","vertical"===t&&"h-full w-2.5 border-l border-l-transparent","horizontal"===t&&"h-2.5 flex-col border-t border-t-transparent",e),...r,children:(0,s.jsx)(a.lr,{"data-slot":"scroll-area-thumb",className:"bg-border relative flex-1 rounded-full"})})}},43984:(e,t,r)=>{"use strict";r.d(t,{U:()=>d});var s=r(60687);r(43210);var a=r(21134),o=r(363),i=r(10218),n=r(29523),l=r(21342);function d(){let{setTheme:e}=(0,i.D)();return(0,s.jsxs)(l.rI,{children:[(0,s.jsx)(l.ty,{asChild:!0,children:(0,s.jsxs)(n.$,{variant:"outline",size:"icon",children:[(0,s.jsx)(a.A,{className:"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),(0,s.jsx)(o.A,{className:"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),(0,s.jsx)("span",{className:"sr-only",children:"Toggle theme"})]})}),(0,s.jsxs)(l.SQ,{align:"end",children:[(0,s.jsx)(l._2,{onClick:()=>e("light"),children:"Light"}),(0,s.jsx)(l._2,{onClick:()=>e("dark"),children:"Dark"}),(0,s.jsx)(l._2,{onClick:()=>e("system"),children:"System"})]})]})}},44493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>l,Wu:()=>d,ZB:()=>n,Zp:()=>o,aR:()=>i});var s=r(60687);r(43210);var a=r(4780);function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...t})}function l({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...t})}function d({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t})}},45318:(e,t,r)=>{Promise.resolve().then(r.bind(r,14329))},46952:(e,t,r)=>{"use strict";r.d(t,{By:()=>d,E$:()=>u,NotificationProvider:()=>n,bQ:()=>c,lO:()=>l});var s=r(60687),a=r(43210),o=r(63213);let i=(0,a.createContext)(void 0);function n({children:e}){let{user:t}=(0,o.A)(),[r,n]=(0,a.useState)([]),l=()=>r.filter(e=>!e.isRead),d=l().length;return(0,s.jsx)(i.Provider,{value:{notifications:r,unreadCount:d,addNotification:e=>{let r={...e,id:Date.now().toString(),createdAt:new Date().toISOString(),userId:t?.id||"",isRead:!1};n(e=>[r,...e]),"granted"===Notification.permission&&new Notification(r.title,{body:r.message,icon:"/favicon.ico",tag:r.id})},markAsRead:e=>{n(t=>t.map(t=>t.id===e?{...t,isRead:!0}:t))},markAllAsRead:()=>{n(e=>e.map(e=>({...e,isRead:!0})))},removeNotification:e=>{n(t=>t.filter(t=>t.id!==e))},clearAll:()=>{n([])},getNotificationsByType:e=>r.filter(t=>t.type===e),getUnreadNotifications:l},children:e})}function l(){let e=(0,a.useContext)(i);if(void 0===e)throw Error("useNotifications must be used within a NotificationProvider");return e}function d(e){switch(e){case"order_confirmed":case"order_shipped":case"order_delivered":return"\uD83D\uDCE6";case"payment_received":return"\uD83D\uDCB3";case"payment_failed":return"❌";case"promotion":return"\uD83C\uDF89";case"reminder":return"⏰";case"system":return"⚙️";case"message":return"\uD83D\uDCAC";case"review_request":return"⭐";default:return"\uD83D\uDD14"}}function c(e){switch(e){case"urgent":return"text-red-600 bg-red-50 border-red-200";case"high":return"text-orange-600 bg-orange-50 border-orange-200";case"medium":return"text-blue-600 bg-blue-50 border-blue-200";default:return"text-gray-600 bg-gray-50 border-gray-200"}}function u(e){let t=new Date,r=new Date(e),s=Math.floor((t.getTime()-r.getTime())/6e4);if(s<1)return"الآن";if(s<60)return`منذ ${s} دقيقة`;if(s<1440){let e=Math.floor(s/60);return`منذ ${e} ساعة`}{let e=Math.floor(s/1440);return`منذ ${e} يوم`}}},53881:(e,t,r)=>{"use strict";r.d(t,{gG:()=>s,ly:()=>a});var s=function(e){return e.STUDENT="student",e.SCHOOL="school",e.ADMIN="admin",e.DELIVERY="delivery",e}({});function a(e,t){let r={admin:4,school:3,delivery:2,student:1};return r[e]>=r[t]}},54413:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Graduation Toqs\\\\frontend\\\\src\\\\app\\\\not-found.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\not-found.tsx","default")},54431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Graduation Toqs\\\\frontend\\\\src\\\\app\\\\error.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\error.tsx","default")},57347:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>b});var s=r(60687),a=r(43210),o=r(85814),i=r.n(o),n=r(16189),l=r(29523),d=r(44493),c=r(43984),u=r(30917),m=r(8520),h=r(27351),x=r(43649),p=r(78122),f=r(32192),g=r(28559),v=r(99270);function b(){let e=(0,n.useRouter)(),{t}=(0,m.B)(),[r,o]=(0,a.useState)(10),[b,j]=(0,a.useState)(!1);return(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",children:[(0,s.jsx)("header",{className:"bg-white dark:bg-gray-800 shadow-sm border-b",children:(0,s.jsxs)("div",{className:"container mx-auto px-4 py-4 flex justify-between items-center",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(h.A,{className:"h-8 w-8 text-blue-600 dark:text-blue-400"}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Toqs"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(u.J,{}),(0,s.jsx)(c.U,{})]})]})}),(0,s.jsx)("main",{className:"container mx-auto px-4 py-16 flex items-center justify-center min-h-[calc(100vh-80px)]",children:(0,s.jsx)(d.Zp,{className:"w-full max-w-2xl",children:(0,s.jsxs)(d.Wu,{className:"p-8 text-center",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("div",{className:"mx-auto w-24 h-24 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mb-4",children:(0,s.jsx)(x.A,{className:"h-12 w-12 text-red-600 dark:text-red-400"})}),(0,s.jsx)("h1",{className:"text-6xl font-bold text-gray-900 dark:text-white mb-2",children:"404"}),(0,s.jsx)("h2",{className:"text-2xl font-semibold text-gray-700 dark:text-gray-300 arabic-text",children:"الصفحة غير موجودة"})]}),(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400 text-lg mb-4 arabic-text",children:"عذراً، لا يمكننا العثور على الصفحة التي تبحث عنها."}),(0,s.jsx)("p",{className:"text-gray-500 dark:text-gray-500 arabic-text",children:"قد تكون الصفحة قد تم نقلها أو حذفها أو أن الرابط غير صحيح."})]}),!b&&(0,s.jsx)("div",{className:"mb-8 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800",children:(0,s.jsxs)("p",{className:"text-blue-700 dark:text-blue-300 arabic-text",children:["سيتم توجيهك تلقائياً إلى الصفحة الرئيسية خلال ",r," ثانية"]})}),b&&(0,s.jsx)("div",{className:"mb-8 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800",children:(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,s.jsx)(p.A,{className:"h-4 w-4 animate-spin text-green-600 dark:text-green-400"}),(0,s.jsx)("p",{className:"text-green-700 dark:text-green-300 arabic-text",children:"جاري التوجيه..."})]})}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,s.jsxs)(l.$,{onClick:()=>{j(!0),e.push("/")},className:"flex items-center gap-2",disabled:b,children:[(0,s.jsx)(f.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"arabic-text",children:"الصفحة الرئيسية"})]}),(0,s.jsxs)(l.$,{variant:"outline",onClick:()=>{e.back()},className:"flex items-center gap-2",disabled:b,children:[(0,s.jsx)(g.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"arabic-text",children:"العودة للخلف"})]}),(0,s.jsxs)(l.$,{variant:"outline",onClick:()=>{window.location.reload()},className:"flex items-center gap-2",disabled:b,children:[(0,s.jsx)(p.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"arabic-text",children:"إعادة تحميل"})]})]}),(0,s.jsxs)("div",{className:"mt-8 pt-8 border-t border-gray-200 dark:border-gray-700",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 arabic-text",children:"ماذا يمكنك أن تفعل؟"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm",children:[(0,s.jsxs)("div",{className:"p-4 bg-gray-50 dark:bg-gray-800 rounded-lg",children:[(0,s.jsx)(v.A,{className:"h-6 w-6 text-blue-600 dark:text-blue-400 mx-auto mb-2"}),(0,s.jsx)("p",{className:"text-gray-700 dark:text-gray-300 arabic-text",children:"ابحث عن المنتجات في الكتالوج"}),(0,s.jsx)(i(),{href:"/catalog",className:"text-blue-600 dark:text-blue-400 hover:underline",children:"تصفح الكتالوج"})]}),(0,s.jsxs)("div",{className:"p-4 bg-gray-50 dark:bg-gray-800 rounded-lg",children:[(0,s.jsx)(h.A,{className:"h-6 w-6 text-green-600 dark:text-green-400 mx-auto mb-2"}),(0,s.jsx)("p",{className:"text-gray-700 dark:text-gray-300 arabic-text",children:"تعرف على خدماتنا"}),(0,s.jsx)(i(),{href:"/about",className:"text-blue-600 dark:text-blue-400 hover:underline",children:"من نحن"})]}),(0,s.jsxs)("div",{className:"p-4 bg-gray-50 dark:bg-gray-800 rounded-lg",children:[(0,s.jsx)(f.A,{className:"h-6 w-6 text-purple-600 dark:text-purple-400 mx-auto mb-2"}),(0,s.jsx)("p",{className:"text-gray-700 dark:text-gray-300 arabic-text",children:"ابدأ من الصفحة الرئيسية"}),(0,s.jsx)(i(),{href:"/",className:"text-blue-600 dark:text-blue-400 hover:underline",children:"الصفحة الرئيسية"})]})]})]})]})})})]})}},61135:()=>{},61971:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},63213:(e,t,r)=>{"use strict";r.d(t,{A:()=>l,AuthProvider:()=>n});var s=r(60687),a=r(43210),o=r(53881);let i=(0,a.createContext)(void 0);function n({children:e}){let[t,r]=(0,a.useState)(null),[n,l]=(0,a.useState)(null),[d,c]=(0,a.useState)(!0),[u,m]=(0,a.useState)(!1),h=async(e,t,r)=>(console.log("Sign up:",e,r),{data:{user:{id:"1",email:e}},error:null}),x=async(e,t)=>{console.log("Sign in:",e);let s={id:"1",email:e},a=o.gG.STUDENT;e.includes("admin")?a=o.gG.ADMIN:e.includes("school")?a=o.gG.SCHOOL:e.includes("delivery")&&(a=o.gG.DELIVERY);let i={id:"1",email:e,full_name:e.split("@")[0]||"مستخدم",role:a,created_at:new Date().toISOString(),updated_at:new Date().toISOString()};r(s),l(i);try{localStorage.setItem("mockUser",JSON.stringify(s)),localStorage.setItem("mockProfile",JSON.stringify(i)),localStorage.setItem("sessionTimestamp",Date.now().toString()),console.log("User data saved to localStorage:",{mockUser:s,mockProfile:i})}catch(e){console.error("Error saving user data to localStorage:",e)}return setTimeout(()=>{a===o.gG.ADMIN?window.location.href="/dashboard/admin":a===o.gG.SCHOOL?window.location.href="/dashboard/school":a===o.gG.DELIVERY?window.location.href="/dashboard/delivery":window.location.href="/dashboard/student"},100),{data:{user:s},error:null}},p=async()=>{try{return r(null),l(null),localStorage.removeItem("mockUser"),localStorage.removeItem("mockProfile"),localStorage.removeItem("sessionTimestamp"),console.log("User data cleared from localStorage"),{error:null}}catch(e){return console.error("Error during sign out:",e),{error:"فشل في تسجيل الخروج"}}},f=async e=>{if(!t)return{data:null,error:"No user logged in"};let r={...n,...e};return l(r),{data:r,error:null}};return(0,s.jsx)(i.Provider,{value:{user:t,profile:n,loading:d,signUp:h,signIn:x,signOut:p,updateProfile:f,hasRole:e=>!!n&&(0,o.ly)(n.role,e)},children:e})}function l(){let e=(0,a.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},67270:(e,t,r)=>{"use strict";r.d(t,{MenuProvider:()=>a});var s=r(12907);let a=(0,s.registerClientReference)(function(){throw Error("Attempted to call MenuProvider() from the server but MenuProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\contexts\\MenuContext.tsx","MenuProvider");(0,s.registerClientReference)(function(){throw Error("Attempted to call useMenu() from the server but useMenu is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\contexts\\MenuContext.tsx","useMenu")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},76862:(e,t,r)=>{Promise.resolve().then(r.bind(r,54431))},81326:(e,t,r)=>{"use strict";r.d(t,{NotificationProvider:()=>a});var s=r(12907);let a=(0,s.registerClientReference)(function(){throw Error("Attempted to call NotificationProvider() from the server but NotificationProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\contexts\\NotificationContext.tsx","NotificationProvider");(0,s.registerClientReference)(function(){throw Error("Attempted to call useNotifications() from the server but useNotifications is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\contexts\\NotificationContext.tsx","useNotifications"),(0,s.registerClientReference)(function(){throw Error("Attempted to call getNotificationIcon() from the server but getNotificationIcon is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\contexts\\NotificationContext.tsx","getNotificationIcon"),(0,s.registerClientReference)(function(){throw Error("Attempted to call getNotificationColor() from the server but getNotificationColor is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\contexts\\NotificationContext.tsx","getNotificationColor"),(0,s.registerClientReference)(function(){throw Error("Attempted to call formatNotificationTime() from the server but formatNotificationTime is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\contexts\\NotificationContext.tsx","formatNotificationTime")},83701:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\theme-provider.tsx","ThemeProvider")},86748:(e,t,r)=>{"use strict";r.d(t,{MenuProvider:()=>n,b:()=>l});var s=r(60687),a=r(43210),o=r(52581);let i=(0,a.createContext)(void 0);function n({children:e}){let[t,r]=(0,a.useState)([]),[n,l]=(0,a.useState)(!0),[d,c]=(0,a.useState)(null),u=(0,a.useCallback)(async(e=!1)=>{try{l(!0),c(null);let t=await fetch(e?"/api/menu-items?include_inactive=true":"/api/menu-items"),s=await t.json();t.ok?r(s.menuItems||[]):(c(s.error||"فشل في جلب عناصر القائمة"),console.error("Failed to fetch menu items:",s.error))}catch(e){c("خطأ في الاتصال بالخادم"),console.error("Error fetching menu items:",e)}finally{l(!1)}},[]),m=(0,a.useCallback)(async e=>{try{let t=await fetch("/api/menu-items",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),s=await t.json();if(t.ok)return r(e=>[...e,s.menuItem]),o.o.success(s.message),!0;return o.o.error(s.error||"فشل في إضافة عنصر القائمة"),!1}catch(e){return console.error("Error adding menu item:",e),o.o.error("خطأ في الاتصال بالخادم"),!1}},[]),h=(0,a.useCallback)(async(e,s)=>{try{let a=t.find(t=>t.id===e);if(!a)return o.o.error("عنصر القائمة غير موجود"),!1;let i={...a,...s},n=await fetch(`/api/menu-items/${e}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(i)}),l=await n.json();if(n.ok)return r(t=>t.map(t=>t.id===e?{...t,...s}:t)),o.o.success(l.message),!0;return o.o.error(l.error||"فشل في تحديث عنصر القائمة"),!1}catch(e){return console.error("Error updating menu item:",e),o.o.error("خطأ في الاتصال بالخادم"),!1}},[t]),x=(0,a.useCallback)(async e=>{try{let t=await fetch(`/api/menu-items/${e}`,{method:"DELETE"}),s=await t.json();if(t.ok)return r(t=>t.filter(t=>t.id!==e)),o.o.success(s.message),!0;return o.o.error(s.error||"فشل في حذف عنصر القائمة"),!1}catch(e){return console.error("Error deleting menu item:",e),o.o.error("خطأ في الاتصال بالخادم"),!1}},[]),p=(0,a.useCallback)(async e=>{let r=t.find(t=>t.id===e);return r?await h(e,{is_active:!r.is_active}):(o.o.error("عنصر القائمة غير موجود"),!1)},[t,h]),f=(0,a.useCallback)(async e=>{try{r(e);let t=e.filter(e=>!e.parent_id).map((e,t)=>({id:e.id,order_index:t+1})),s=await fetch("/api/menu-items/reorder",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({items:t})}),a=await s.json();if(s.ok)return o.o.success(a.message),!0;return await u(),o.o.error(a.error||"فشل في إعادة ترتيب عناصر القائمة"),!1}catch(e){return await u(),console.error("Error reordering menu items:",e),o.o.error("خطأ في الاتصال بالخادم"),!1}},[u]),g=(0,a.useCallback)(()=>{u()},[u]),v=(0,a.useMemo)(()=>({menuItems:t,loading:n,error:d,fetchMenuItems:u,addMenuItem:m,updateMenuItem:h,deleteMenuItem:x,toggleItemStatus:p,reorderMenuItems:f,refreshMenu:g}),[t,n,d,u,m,h,x,p,f,g]);return(0,s.jsx)(i.Provider,{value:v,children:e})}function l(){let e=(0,a.useContext)(i);if(void 0===e)throw Error("useMenu must be used within a MenuProvider");return e}},89589:(e,t,r)=>{"use strict";r.d(t,{L$:()=>a,ax:()=>o,q:()=>s});let s="ar",a={ar:"العربية",fr:"Fran\xe7ais",en:"English"},o={ar:"\uD83C\uDDF2\uD83C\uDDE6",fr:"\uD83C\uDDEB\uD83C\uDDF7",en:"\uD83C\uDDEC\uD83C\uDDE7"}},89608:(e,t,r)=>{Promise.resolve().then(r.bind(r,57347))},89667:(e,t,r)=>{"use strict";r.d(t,{p:()=>o});var s=r(60687);r(43210);var a=r(4780);function o({className:e,type:t,...r}){return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>f,metadata:()=>p});var s=r(37413),a=r(92676),o=r.n(a),i=r(68726),n=r.n(i);r(61135);var l=r(83701),d=r(29131),c=r(81326),u=r(37043),m=r(67270),h=r(42629),x=r(6931);let p={title:"Graduation Toqs - منصة أزياء التخرج المغربية",description:"أول منصة مغربية ذكية لتأجير وبيع أزياء التخرج مع ميزات التخصيص والذكاء الاصطناعي"};function f({children:e}){return(0,s.jsx)("html",{lang:"ar",dir:"rtl",suppressHydrationWarning:!0,children:(0,s.jsx)("body",{className:`${o().variable} ${n().variable} antialiased font-cairo`,children:(0,s.jsx)(l.ThemeProvider,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,children:(0,s.jsx)(d.AuthProvider,{children:(0,s.jsx)(c.NotificationProvider,{children:(0,s.jsxs)(m.MenuProvider,{children:[(0,s.jsx)(u.CartProvider,{children:e}),(0,s.jsx)(h.LiveChat,{}),(0,s.jsx)(x.Toaster,{position:"top-right",dir:"rtl",richColors:!0,closeButton:!0})]})})})})})})}},96871:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>o});var s=r(60687);r(43210);var a=r(10218);function o({children:e,...t}){return(0,s.jsx)(a.N,{...t,children:e})}}};