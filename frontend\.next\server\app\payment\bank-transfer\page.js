(()=>{var e={};e.id=1022,e.ids=[1022],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16023:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},16103:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("qr-code",[["rect",{width:"5",height:"5",x:"3",y:"3",rx:"1",key:"1tu5fj"}],["rect",{width:"5",height:"5",x:"16",y:"3",rx:"1",key:"1v8r4q"}],["rect",{width:"5",height:"5",x:"3",y:"16",rx:"1",key:"1x03jg"}],["path",{d:"M21 16h-3a2 2 0 0 0-2 2v3",key:"177gqh"}],["path",{d:"M21 21v.01",key:"ents32"}],["path",{d:"M12 7v3a2 2 0 0 1-2 2H7",key:"8crl2c"}],["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M12 3h.01",key:"n36tog"}],["path",{d:"M12 16v.01",key:"133mhm"}],["path",{d:"M16 12h1",key:"1slzba"}],["path",{d:"M21 12v.01",key:"1lwtk9"}],["path",{d:"M12 21v-1",key:"1880an"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23928:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34729:(e,s,a)=>{"use strict";a.d(s,{T:()=>l});var t=a(60687);a(43210);var r=a(4780);function l({className:e,...s}){return(0,t.jsx)("textarea",{"data-slot":"textarea",className:(0,r.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...s})}},41550:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},43552:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t});let t=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Graduation Toqs\\\\frontend\\\\src\\\\app\\\\payment\\\\bank-transfer\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\payment\\bank-transfer\\page.tsx","default")},55466:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>i.a,__next_app__:()=>o,pages:()=>x,routeModule:()=>h,tree:()=>d});var t=a(65239),r=a(48088),l=a(88170),i=a.n(l),n=a(30893),c={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);a.d(s,c);let d={children:["",{children:["payment",{children:["bank-transfer",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,43552)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\payment\\bank-transfer\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(a.bind(a,54431)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,54413)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,x=["C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\payment\\bank-transfer\\page.tsx"],o={require:a,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/payment/bank-transfer/page",pathname:"/payment/bank-transfer",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},62140:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("file-image",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["circle",{cx:"10",cy:"12",r:"2",key:"737tya"}],["path",{d:"m20 17-1.296-1.296a2.41 2.41 0 0 0-3.408 0L9 22",key:"wt3hpn"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66801:(e,s,a)=>{Promise.resolve().then(a.bind(a,43552))},69768:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>C});var t=a(60687),r=a(43210),l=a(16189),i=a(89940),n=a(44493),c=a(29523),d=a(89667),x=a(80013),o=a(34729),h=a(96834),m=a(35950),p=a(43649),u=a(28559),b=a(85778),j=a(5336),y=a(79410),f=a(70615),g=a(58869);let N=(0,a(62688).A)("hash",[["line",{x1:"4",x2:"20",y1:"9",y2:"9",key:"4lhtct"}],["line",{x1:"4",x2:"20",y1:"15",y2:"15",key:"vyu0kd"}],["line",{x1:"10",x2:"8",y1:"3",y2:"21",key:"1ggp8o"}],["line",{x1:"16",x2:"14",y1:"3",y2:"21",key:"weycgp"}]]);var v=a(16103),k=a(23928),w=a(62140),A=a(16023),_=a(48730),M=a(52581);let q=[{bank_name:"البنك الشعبي المغربي",account_holder:"منصة أزياء التخرج المغربية",account_number:"****************",rib:"236 000 ********** 12",swift:"BMCEMAMC",branch:"بني ملال"},{bank_name:"بنك المغرب",account_holder:"منصة أزياء التخرج المغربية",account_number:"011000**********",rib:"011 000 ********** 34",swift:"BMCEMAMC",branch:"خنيفرة"}];function C(){(0,l.useSearchParams)();let[e,s]=(0,r.useState)(0),[a,C]=(0,r.useState)(null),[z,P]=(0,r.useState)(""),[G,D]=(0,r.useState)(""),[T,$]=(0,r.useState)(""),[J,B]=(0,r.useState)(!1),[F,R]=(0,r.useState)(null),E=(e,s)=>{navigator.clipboard.writeText(e),M.o.success(`تم نسخ ${s} بنجاح`)},S=async()=>{if(!a||!z||!G)return void M.o.error("يرجى ملء جميع الحقول المطلوبة ورفع إثبات الدفع");B(!0);try{let s=new FormData;s.append("proof_file",a),s.append("order_id",F?.order_id||""),s.append("transfer_reference",z),s.append("transfer_date",G),s.append("bank_account",JSON.stringify(q[e])),s.append("notes",T),await new Promise(e=>setTimeout(e,2e3)),M.o.success("تم إرسال إثبات الدفع بنجاح"),window.location.href=`/payment/confirmation?order_id=${F?.order_id}&method=bank_transfer`}catch(e){M.o.error("حدث خطأ أثناء إرسال إثبات الدفع")}finally{B(!1)}};return F?(0,t.jsxs)(i.Mx,{containerClassName:"container mx-auto px-4 py-8",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)(c.$,{variant:"outline",size:"sm",asChild:!0,className:"mb-4",children:(0,t.jsxs)("a",{href:"/checkout",children:[(0,t.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"العودة لإتمام الطلب"]})}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsxs)("div",{className:"flex items-center justify-center gap-3 mb-4",children:[(0,t.jsx)(b.A,{className:"h-8 w-8 text-blue-600"}),(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white arabic-text",children:"الدفع عبر التحويل البنكي"})]}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-300 arabic-text",children:"اتبع الخطوات التالية لإتمام عملية الدفع"})]})]}),(0,t.jsxs)("div",{className:"max-w-6xl mx-auto grid lg:grid-cols-3 gap-8",children:[(0,t.jsx)("div",{className:"lg:col-span-1",children:(0,t.jsxs)(n.Zp,{className:"sticky top-4",children:[(0,t.jsx)(n.aR,{children:(0,t.jsx)(n.ZB,{className:"arabic-text",children:"ملخص الطلب"})}),(0,t.jsxs)(n.Wu,{className:"space-y-4",children:[(0,t.jsx)("div",{className:"space-y-2",children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"رقم الطلب:"}),(0,t.jsx)(h.E,{variant:"outline",children:F.order_id})]})}),(0,t.jsx)(m.w,{}),(0,t.jsx)("div",{className:"space-y-3",children:F.items.map((e,s)=>(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("p",{className:"font-medium arabic-text",children:e.name}),(0,t.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:["الكمية: ",e.quantity]})]}),(0,t.jsxs)("span",{className:"font-medium",children:[e.price," Dhs"]})]},s))}),(0,t.jsx)(m.w,{}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"arabic-text",children:"المجموع الفرعي:"}),(0,t.jsxs)("span",{children:[F.items.reduce((e,s)=>e+s.price,0)," Dhs"]})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"arabic-text",children:"الشحن:"}),(0,t.jsxs)("span",{children:[F.shipping_cost," Dhs"]})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"arabic-text",children:"الضريبة:"}),(0,t.jsxs)("span",{children:[F.tax_amount," Dhs"]})]})]}),(0,t.jsx)(m.w,{}),(0,t.jsxs)("div",{className:"flex justify-between items-center text-lg font-bold",children:[(0,t.jsx)("span",{className:"arabic-text",children:"المجموع الكلي:"}),(0,t.jsxs)("span",{className:"text-blue-600",children:[F.total_amount," Dhs"]})]})]})]})}),(0,t.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2 arabic-text",children:[(0,t.jsx)("span",{className:"bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm",children:"1"}),"اختر الحساب البنكي"]})}),(0,t.jsx)(n.Wu,{children:(0,t.jsx)("div",{className:"grid gap-4",children:q.map((a,r)=>(0,t.jsx)("div",{className:`border rounded-lg p-4 cursor-pointer transition-all ${e===r?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-200 dark:border-gray-700 hover:border-gray-300"}`,onClick:()=>s(r),children:(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:`w-4 h-4 rounded-full border-2 ${e===r?"border-blue-500 bg-blue-500":"border-gray-300"}`,children:e===r&&(0,t.jsx)(j.A,{className:"w-4 h-4 text-white"})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,t.jsx)(y.A,{className:"h-4 w-4 text-blue-600"}),(0,t.jsx)("span",{className:"font-medium arabic-text",children:a.bank_name})]}),(0,t.jsxs)("div",{className:"text-sm text-gray-600 dark:text-gray-400 space-y-1",children:[(0,t.jsxs)("p",{children:["الفرع: ",a.branch]}),(0,t.jsxs)("p",{children:["صاحب الحساب: ",a.account_holder]})]})]})]})},r))})})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2 arabic-text",children:[(0,t.jsx)("span",{className:"bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm",children:"2"}),"تفاصيل الحساب البنكي"]})}),(0,t.jsx)(n.Wu,{children:(0,t.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-800 rounded-lg p-6 space-y-4",children:[(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(x.J,{className:"flex items-center gap-2 arabic-text",children:[(0,t.jsx)(y.A,{className:"h-4 w-4"}),"اسم البنك"]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(d.p,{value:q[e].bank_name,readOnly:!0,className:"bg-white dark:bg-gray-700"}),(0,t.jsx)(c.$,{size:"sm",variant:"outline",onClick:()=>E(q[e].bank_name,"اسم البنك"),children:(0,t.jsx)(f.A,{className:"h-4 w-4"})})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(x.J,{className:"flex items-center gap-2 arabic-text",children:[(0,t.jsx)(g.A,{className:"h-4 w-4"}),"صاحب الحساب"]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(d.p,{value:q[e].account_holder,readOnly:!0,className:"bg-white dark:bg-gray-700"}),(0,t.jsx)(c.$,{size:"sm",variant:"outline",onClick:()=>E(q[e].account_holder,"صاحب الحساب"),children:(0,t.jsx)(f.A,{className:"h-4 w-4"})})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(x.J,{className:"flex items-center gap-2 arabic-text",children:[(0,t.jsx)(N,{className:"h-4 w-4"}),"رقم الحساب"]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(d.p,{value:q[e].account_number,readOnly:!0,className:"bg-white dark:bg-gray-700 font-mono"}),(0,t.jsx)(c.$,{size:"sm",variant:"outline",onClick:()=>E(q[e].account_number,"رقم الحساب"),children:(0,t.jsx)(f.A,{className:"h-4 w-4"})})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(x.J,{className:"flex items-center gap-2 arabic-text",children:[(0,t.jsx)(v.A,{className:"h-4 w-4"}),"RIB"]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(d.p,{value:q[e].rib,readOnly:!0,className:"bg-white dark:bg-gray-700 font-mono"}),(0,t.jsx)(c.$,{size:"sm",variant:"outline",onClick:()=>E(q[e].rib,"RIB"),children:(0,t.jsx)(f.A,{className:"h-4 w-4"})})]})]})]}),(0,t.jsx)("div",{className:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-start gap-3",children:[(0,t.jsx)(k.A,{className:"h-5 w-5 text-blue-600 mt-0.5"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium text-blue-900 dark:text-blue-100 arabic-text",children:"المبلغ المطلوب تحويله"}),(0,t.jsxs)("p",{className:"text-2xl font-bold text-blue-600 mt-1",children:[F.total_amount," درهم مغربي"]}),(0,t.jsxs)("p",{className:"text-sm text-blue-700 dark:text-blue-300 mt-1 arabic-text",children:["يرجى تحويل المبلغ بالضبط مع ذكر رقم الطلب: ",F.order_id]})]})]})})]})})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2 arabic-text",children:[(0,t.jsx)("span",{className:"bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm",children:"3"}),"رفع إثبات الدفع"]})}),(0,t.jsxs)(n.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(x.J,{htmlFor:"transfer_reference",className:"arabic-text",children:"رقم العملية / المرجع *"}),(0,t.jsx)(d.p,{id:"transfer_reference",value:z,onChange:e=>P(e.target.value),placeholder:"أدخل رقم العملية",className:"arabic-text"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(x.J,{htmlFor:"transfer_date",className:"arabic-text",children:"تاريخ التحويل *"}),(0,t.jsx)(d.p,{id:"transfer_date",type:"date",value:G,onChange:e=>D(e.target.value)})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(x.J,{htmlFor:"proof_file",className:"arabic-text",children:"صورة إثبات الدفع *"}),(0,t.jsx)("div",{className:"border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center",children:a?(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(w.A,{className:"h-8 w-8 text-green-600 mx-auto"}),(0,t.jsx)("p",{className:"text-sm font-medium text-green-600",children:a.name}),(0,t.jsxs)("p",{className:"text-xs text-gray-500",children:[(a.size/1024/1024).toFixed(2)," MB"]}),(0,t.jsx)(c.$,{size:"sm",variant:"outline",onClick:()=>C(null),children:"إزالة الملف"})]}):(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(A.A,{className:"h-8 w-8 text-gray-400 mx-auto"}),(0,t.jsxs)("div",{children:[(0,t.jsxs)(x.J,{htmlFor:"proof_file",className:"cursor-pointer",children:[(0,t.jsx)("span",{className:"text-blue-600 hover:text-blue-500",children:"اختر ملف"}),(0,t.jsx)("span",{className:"text-gray-500",children:" أو اسحب الملف هنا"})]}),(0,t.jsx)(d.p,{id:"proof_file",type:"file",accept:"image/*",onChange:e=>{let s=e.target.files?.[0];if(s){if(s.size>5242880)return void M.o.error("حجم الملف يجب أن يكون أقل من 5 ميجابايت");if(!s.type.startsWith("image/"))return void M.o.error("يرجى اختيار ملف صورة صالح");C(s),M.o.success("تم اختيار الملف بنجاح")}},className:"hidden"})]}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"PNG, JPG, JPEG حتى 5MB"})]})})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(x.J,{htmlFor:"notes",className:"arabic-text",children:"ملاحظات إضافية"}),(0,t.jsx)(o.T,{id:"notes",value:T,onChange:e=>$(e.target.value),placeholder:"أي ملاحظات إضافية حول التحويل...",className:"arabic-text",rows:3})]}),(0,t.jsx)("div",{className:"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-start gap-3",children:[(0,t.jsx)(_.A,{className:"h-5 w-5 text-yellow-600 mt-0.5"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium text-yellow-900 dark:text-yellow-100 arabic-text",children:"مهم: أوقات المعالجة"}),(0,t.jsx)("p",{className:"text-sm text-yellow-700 dark:text-yellow-300 mt-1 arabic-text",children:"سيتم مراجعة إثبات الدفع خلال 2-4 ساعات عمل. ستتلقون تأكيداً عبر البريد الإلكتروني عند الموافقة."})]})]})}),(0,t.jsx)(c.$,{onClick:S,disabled:!a||!z||!G||J,className:"w-full",size:"lg",children:J?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(_.A,{className:"h-4 w-4 mr-2 animate-spin"}),"جاري الإرسال..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"إرسال إثبات الدفع"]})})]})]})]})]})]}):(0,t.jsx)(i.Mx,{containerClassName:"container mx-auto px-4 py-8",children:(0,t.jsxs)("div",{className:"text-center py-16",children:[(0,t.jsx)(p.A,{className:"h-12 w-12 text-yellow-500 mx-auto mb-4"}),(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-2 arabic-text",children:"معلومات الطلب غير متوفرة"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-6 arabic-text",children:"يرجى العودة إلى صفحة إتمام الطلب"}),(0,t.jsx)(c.$,{asChild:!0,children:(0,t.jsx)("a",{href:"/checkout",className:"arabic-text",children:"العودة لإتمام الطلب"})})]})})}},70615:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},78148:(e,s,a)=>{"use strict";a.d(s,{b:()=>n});var t=a(43210),r=a(14163),l=a(60687),i=t.forwardRef((e,s)=>(0,l.jsx)(r.sG.label,{...e,ref:s,onMouseDown:s=>{s.target.closest("button, input, select, textarea")||(e.onMouseDown?.(s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));i.displayName="Label";var n=i},78272:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},79410:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},79551:e=>{"use strict";e.exports=require("url")},80013:(e,s,a)=>{"use strict";a.d(s,{J:()=>i});var t=a(60687);a(43210);var r=a(78148),l=a(4780);function i({className:e,...s}){return(0,t.jsx)(r.b,{"data-slot":"label",className:(0,l.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...s})}},82481:(e,s,a)=>{Promise.resolve().then(a.bind(a,69768))},85778:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},89940:(e,s,a)=>{"use strict";a.d(s,{NI:()=>f,Mx:()=>y});var t=a(60687),r=a(87801),l=a(85814),i=a.n(l),n=a(8520),c=a(19526),d=a(72575),x=a(66232);let o=(0,a(62688).A)("linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]]);var h=a(27351),m=a(41550),p=a(48340),u=a(97992),b=a(67760);function j(){let{t:e}=(0,n.B)(),s=new Date().getFullYear(),a=[{href:"#",icon:c.A,label:"Facebook"},{href:"#",icon:d.A,label:"Twitter"},{href:"#",icon:x.A,label:"Instagram"},{href:"#",icon:o,label:"LinkedIn"}];return(0,t.jsx)("footer",{className:"bg-gray-900 text-white mt-16",children:(0,t.jsxs)("div",{className:"container mx-auto px-4 py-12",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(h.A,{className:"h-8 w-8 text-blue-400"}),(0,t.jsx)("span",{className:"text-xl font-bold",children:"Graduation Toqs"})]}),(0,t.jsx)("p",{className:"text-gray-300 arabic-text leading-relaxed",children:"أول منصة مغربية متخصصة في تأجير وبيع أزياء التخرج مع ميزات التخصيص والذكاء الاصطناعي"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[(0,t.jsx)(m.A,{className:"h-4 w-4 text-blue-400"}),(0,t.jsx)("span",{children:"<EMAIL>"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[(0,t.jsx)(p.A,{className:"h-4 w-4 text-blue-400"}),(0,t.jsx)("span",{children:"+212 6 12 34 56 78"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[(0,t.jsx)(u.A,{className:"h-4 w-4 text-blue-400"}),(0,t.jsx)("span",{className:"arabic-text",children:"بني ملال، المغرب"})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-4 arabic-text",children:"الشركة"}),(0,t.jsx)("ul",{className:"space-y-2",children:[{href:"/about",label:"من نحن"},{href:"/contact",label:"تواصل معنا"},{href:"/support",label:"الدعم الفني"},{href:"/privacy",label:"سياسة الخصوصية"}].map(e=>(0,t.jsx)("li",{children:(0,t.jsx)(i(),{href:e.href,className:"text-gray-300 hover:text-blue-400 transition-colors arabic-text",children:e.label})},e.href))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-4 arabic-text",children:"الخدمات"}),(0,t.jsx)("ul",{className:"space-y-2",children:[{href:"/catalog",label:"الكتالوج"},{href:"/customize",label:"التخصيص"},{href:"/track-order",label:"تتبع الطلب"},{href:"/size-guide",label:"دليل المقاسات"}].map(e=>(0,t.jsx)("li",{children:(0,t.jsx)(i(),{href:e.href,className:"text-gray-300 hover:text-blue-400 transition-colors arabic-text",children:e.label})},e.href))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-4 arabic-text",children:"الدعم"}),(0,t.jsx)("ul",{className:"space-y-2",children:[{href:"/faq",label:"الأسئلة الشائعة"},{href:"/terms-conditions",label:"الشروط والأحكام"},{href:"/privacy-policy",label:"سياسة الخصوصية"},{href:"/support",label:"الدعم الفني"}].map(e=>(0,t.jsx)("li",{children:(0,t.jsx)(i(),{href:e.href,className:"text-gray-300 hover:text-blue-400 transition-colors arabic-text",children:e.label})},e.href))})]})]}),(0,t.jsx)("div",{className:"border-t border-gray-800 mt-8 pt-8",children:(0,t.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center gap-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)("span",{className:"text-gray-400 arabic-text",children:"تابعنا على:"}),a.map(e=>{let s=e.icon;return(0,t.jsx)("a",{href:e.href,className:"text-gray-400 hover:text-blue-400 transition-colors","aria-label":e.label,children:(0,t.jsx)(s,{className:"h-5 w-5"})},e.label)})]}),(0,t.jsxs)("div",{className:"text-center md:text-right",children:[(0,t.jsxs)("p",{className:"text-gray-400 text-sm arabic-text",children:["\xa9 ",s," Graduation Toqs. جميع الحقوق محفوظة"]}),(0,t.jsxs)("p",{className:"text-gray-500 text-xs mt-1 flex items-center justify-center md:justify-end gap-1",children:[(0,t.jsx)("span",{className:"arabic-text",children:"صُنع بـ"}),(0,t.jsx)(b.A,{className:"h-3 w-3 text-red-500"}),(0,t.jsx)("span",{className:"arabic-text",children:"في المغرب"})]})]})]})})]})})}function y({children:e,className:s="",showFooter:a=!0,containerClassName:l="container mx-auto px-4 py-8"}){return(0,t.jsxs)("div",{className:`min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 ${s}`,children:[(0,t.jsx)(r.V,{}),(0,t.jsx)("main",{className:l,children:e}),a&&(0,t.jsx)(j,{})]})}function f({children:e,className:s="",title:a,description:l}){return(0,t.jsxs)("div",{className:`min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 ${s}`,children:[(0,t.jsx)(r.V,{}),(0,t.jsxs)("main",{className:"container mx-auto px-4 py-8",children:[(a||l)&&(0,t.jsxs)("div",{className:"mb-8",children:[a&&(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white arabic-text mb-2",children:a}),l&&(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-300 arabic-text",children:l})]}),e]})]})}},97992:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])}};var s=require("../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[4447,8773,2762,3932,7801],()=>a(55466));module.exports=t})();