"use strict";(()=>{var e={};e.id=2041,e.ids=[2041],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3745:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>P,routeModule:()=>c,serverHooks:()=>x,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>j});var s={};r.r(s),r.d(s,{DELETE:()=>g,GET:()=>d,POST:()=>p,PUT:()=>l});var n=r(96559),a=r(48088),o=r(37719),i=r(32190),u=r(38561);async function d(e){try{let{searchParams:t}=new URL(e.url),r="true"===t.get("include_unpublished"),s=t.get("created_by"),n=parseInt(t.get("page")||"1"),a=parseInt(t.get("limit")||"10"),o=t.get("sort_by")||"updated_at",d=t.get("sort_order")||"desc",p=u.CN.getPageProjects();r||(p=p.filter(e=>e.isPublished)),s&&(p=p.filter(e=>e.createdBy===s)),p.sort((e,t)=>{let r=e[o],s=t[o];return"desc"===d?new Date(s).getTime()-new Date(r).getTime():new Date(r).getTime()-new Date(s).getTime()});let l=(n-1)*a,g=p.slice(l,l+a),c={total:p.length,published:p.filter(e=>e.isPublished).length,unpublished:p.filter(e=>!e.isPublished).length,byGenerationMode:p.reduce((e,t)=>(e[t.generationMode]=(e[t.generationMode]||0)+1,e),{}),byLanguage:p.reduce((e,t)=>(e[t.settings.language]=(e[t.settings.language]||0)+1,e),{})};return i.NextResponse.json({projects:g,total:p.length,page:n,limit:a,totalPages:Math.ceil(p.length/a),stats:c})}catch(e){return console.error("Error fetching page projects:",e),i.NextResponse.json({error:"خطأ في جلب مشاريع الصفحات"},{status:500})}}async function p(e){try{let{name:t,description:r,templateId:s,generationMode:n,settings:a}=await e.json();if(!t||!n)return i.NextResponse.json({error:"اسم المشروع وطريقة الإنشاء مطلوبان"},{status:400});let o=u.CN.getPageProjects();if(o.find(e=>e.name.toLowerCase()===t.toLowerCase()))return i.NextResponse.json({error:"مشروع بنفس الاسم موجود بالفعل"},{status:400});let d=[];if(s){let e=u.CN.getPageTemplates().find(e=>e.id===s);e&&(d=e.components)}let p={id:u.CN.generateId(),name:t,description:r||"",components:d,templateId:s,generationMode:n,settings:{title:t,description:r||"",keywords:[],language:"ar",direction:"rtl",...a},isPublished:!1,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),createdBy:"admin-1",version:1};return o.push(p),u.CN.savePageProjects(o),i.NextResponse.json({message:"تم إنشاء المشروع بنجاح",project:p},{status:201})}catch(e){return console.error("Error creating page project:",e),i.NextResponse.json({error:"خطأ في إنشاء المشروع"},{status:500})}}async function l(e){try{let{action:t,projectIds:r,data:s}=await e.json();if(!t||!r||!Array.isArray(r))return i.NextResponse.json({error:"الإجراء ومعرفات المشاريع مطلوبة"},{status:400});let n=u.CN.getPageProjects(),a=0;for(let e of r){let r=n.findIndex(t=>t.id===e);if(-1===r)continue;let o=n[r];switch(t){case"publish":o.isPublished=!0,o.publishedUrl=`https://example.com/pages/${o.id}`;break;case"unpublish":o.isPublished=!1,o.publishedUrl=void 0;break;case"duplicate":let i={...o,id:u.CN.generateId(),name:`${o.name} - نسخة`,isPublished:!1,publishedUrl:void 0,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),version:1};n.push(i);break;case"update_settings":s&&(o.settings={...o.settings,...s});break;default:continue}o.updatedAt=new Date().toISOString(),"duplicate"!==t&&(n[r]=o),a++}return u.CN.savePageProjects(n),i.NextResponse.json({message:`تم تحديث ${a} مشروع بنجاح`,updatedCount:a})}catch(e){return console.error("Error updating page projects:",e),i.NextResponse.json({error:"خطأ في تحديث المشاريع"},{status:500})}}async function g(e){try{let{searchParams:t}=new URL(e.url),r=t.get("ids")?.split(",")||[];if(0===r.length)return i.NextResponse.json({error:"معرفات المشاريع مطلوبة"},{status:400});let s=u.CN.getPageProjects(),n=0,a=s.filter(e=>r.includes(e.id)&&e.isPublished);if(a.length>0)return i.NextResponse.json({error:"لا يمكن حذف المشاريع المنشورة. يرجى إلغاء نشرها أولاً.",publishedProjects:a.map(e=>({id:e.id,name:e.name}))},{status:400});let o=s.filter(e=>!r.includes(e.id)||(n++,!1));return u.CN.savePageProjects(o),i.NextResponse.json({message:`تم حذف ${n} مشروع بنجاح`,deletedCount:n})}catch(e){return console.error("Error deleting page projects:",e),i.NextResponse.json({error:"خطأ في حذف المشاريع"},{status:500})}}let c=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/page-builder/route",pathname:"/api/page-builder",filename:"route",bundlePath:"app/api/page-builder/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\page-builder\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:h,workUnitAsyncStorage:j,serverHooks:x}=c;function P(){return(0,o.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:j})}},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,8554],()=>r(3745));module.exports=s})();