"use strict";(()=>{var e={};e.id=7378,e.ids=[7378],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},82216:(e,t,s)=>{s.r(t),s.d(t,{patchFetch:()=>f,routeModule:()=>p,serverHooks:()=>m,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>g});var r={};s.r(r),s.d(r,{GET:()=>d,POST:()=>l});var o=s(96559),a=s(48088),n=s(37719),i=s(32190),u=s(38561);async function l(e){try{let{modelId:t,subModelId:s,prompt:r,settings:o}=await e.json();if(!t||!r)return i.NextResponse.json({error:"معرف النموذج والنص المطلوب مطلوبان"},{status:400});let a=u.CN.getAIModels(),n=a.find(e=>e.id===t);if(!n)return i.NextResponse.json({error:"النموذج غير موجود"},{status:404});if(!n.isActive)return i.NextResponse.json({error:"النموذج غير نشط"},{status:400});let l=null;if(s){if(!(l=n.subModels.find(e=>e.id===s)))return i.NextResponse.json({error:"النموذج الفرعي غير موجود"},{status:404});if(!l.isActive)return i.NextResponse.json({error:"النموذج الفرعي غير نشط"},{status:400})}Date.now();let d=("openai"===n.provider?1e3:"anthropic"===n.provider?1500:"google"===n.provider?800:1200)+Math.floor(1e3*Math.random()),p=Math.random()>.05,c=Math.ceil(r.length/4),g=p?Math.floor(500*Math.random())+50:0,m=c+g,f=l?.pricing||{inputTokens:.001,outputTokens:.002,currency:"USD"},h=p?c/1e3*f.inputTokens+g/1e3*f.outputTokens:0,v={success:p,responseTime:d,tokensUsed:m,cost:h,metadata:{model:n.name,subModel:l?.name,provider:n.provider,promptTokens:c,completionTokens:g,settings:{...n.settings,...o}}};if(p){let e=["مرحباً! أنا مساعد ذكي جاهز لمساعدتك في أي استفسار.","شكراً لك على اختبار النموذج. يبدو أن كل شيء يعمل بشكل صحيح.","هذا اختبار ناجح للنموذج. يمكنني مساعدتك في مختلف المهام.","النموذج يعمل بكفاءة عالية ومستعد للاستخدام.","تم اختبار النموذج بنجاح. جودة الاستجابة ممتازة."];v.response=e[Math.floor(Math.random()*e.length)]}else v.error="فشل في الاتصال بالنموذج. يرجى التحقق من إعدادات API.";let x=a.findIndex(e=>e.id===t);if(-1!==x){let e=a[x];if(e.lastTestedAt=new Date().toISOString(),e.testResult={success:p,responseTime:d,error:p?void 0:v.error},e.status=p?"active":"error",p){e.usage.totalRequests+=1,e.usage.totalTokens+=m,e.usage.totalCost+=h,e.usage.lastUsed=new Date().toISOString();let t=e.usage.averageResponseTime*(e.usage.totalRequests-1)+d;e.usage.averageResponseTime=Math.round(t/e.usage.totalRequests);let s=Math.round(e.usage.successRate*(e.usage.totalRequests-1)/100)+1;e.usage.successRate=Math.round(s/e.usage.totalRequests*100)}e.updatedAt=new Date().toISOString(),a[x]=e,u.CN.saveAIModels(a)}let R=u.CN.getModelActivities();return R.push({id:u.CN.generateId(),modelId:t,subModelId:s,type:"test",description:`اختبار النموذج: ${p?"نجح":"فشل"}`,details:{prompt:r.substring(0,100)+(r.length>100?"...":""),settings:{...n.settings,...o}},timestamp:new Date().toISOString(),duration:d,tokensUsed:m,cost:h,success:p,errorMessage:p?void 0:v.error}),u.CN.saveModelActivities(R),i.NextResponse.json(v)}catch(e){return console.error("Error testing AI model:",e),i.NextResponse.json({error:"خطأ في اختبار النموذج"},{status:500})}}async function d(e){try{let{searchParams:t}=new URL(e.url),s=t.get("model_id"),r=parseInt(t.get("limit")||"20"),o=u.CN.getModelActivities();o=o.filter(e=>"test"===e.type),s&&(o=o.filter(e=>e.modelId===s)),o.sort((e,t)=>new Date(t.timestamp).getTime()-new Date(e.timestamp).getTime()),o=o.slice(0,r);let a=u.CN.getModelActivities().filter(e=>"test"===e.type),n={total:a.length,successful:a.filter(e=>e.success).length,failed:a.filter(e=>!e.success).length,averageResponseTime:a.length>0?Math.round(a.reduce((e,t)=>e+(t.duration||0),0)/a.length):0,totalCost:a.reduce((e,t)=>e+(t.cost||0),0),byModel:a.reduce((e,t)=>{let s=t.modelId;return e[s]||(e[s]={total:0,successful:0,failed:0}),e[s].total+=1,t.success?e[s].successful+=1:e[s].failed+=1,e},{})};return i.NextResponse.json({tests:o,stats:n,total:o.length})}catch(e){return console.error("Error fetching test results:",e),i.NextResponse.json({error:"خطأ في جلب نتائج الاختبارات"},{status:500})}}let p=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/ai-models/test/route",pathname:"/api/ai-models/test",filename:"route",bundlePath:"app/api/ai-models/test/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\ai-models\\test\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:c,workUnitAsyncStorage:g,serverHooks:m}=p;function f(){return(0,n.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:g})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,580,8554],()=>s(82216));module.exports=r})();