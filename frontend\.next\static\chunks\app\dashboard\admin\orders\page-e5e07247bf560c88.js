(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1147],{6982:(e,s,t)=>{"use strict";t.d(s,{N9:()=>m,dj:()=>h});var a=t(95155),r=t(12115),l=t(40646),i=t(85339),n=t(1243),c=t(81284),d=t(54416),x=t(30285);function o(e){let{toast:s,onRemove:t}=e;return(0,r.useEffect)(()=>{let e=setTimeout(()=>{t(s.id)},s.duration||5e3);return()=>clearTimeout(e)},[s.id,s.duration,t]),(0,a.jsx)("div",{className:"rounded-lg border p-4 shadow-lg ".concat((()=>{switch(s.type){case"success":return"bg-green-50 border-green-200";case"error":return"bg-red-50 border-red-200";case"warning":return"bg-yellow-50 border-yellow-200";case"info":return"bg-blue-50 border-blue-200"}})()," animate-in slide-in-from-right-full"),children:(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(()=>{switch(s.type){case"success":return(0,a.jsx)(l.A,{className:"h-5 w-5 text-green-600"});case"error":return(0,a.jsx)(i.A,{className:"h-5 w-5 text-red-600"});case"warning":return(0,a.jsx)(n.A,{className:"h-5 w-5 text-yellow-600"});case"info":return(0,a.jsx)(c.A,{className:"h-5 w-5 text-blue-600"})}})(),(0,a.jsxs)("div",{className:"flex-1",children:[s.title&&(0,a.jsx)("h4",{className:"font-medium text-gray-900 arabic-text",children:s.title}),(0,a.jsx)("p",{className:"text-sm text-gray-700 arabic-text",children:s.description})]}),(0,a.jsx)(x.$,{variant:"ghost",size:"sm",className:"h-6 w-6 p-0",onClick:()=>t(s.id),children:(0,a.jsx)(d.A,{className:"h-4 w-4"})})]})})}function m(e){let{toasts:s,onRemove:t}=e;return(0,a.jsx)("div",{className:"fixed top-4 right-4 z-50 space-y-2 max-w-sm",children:s.map(e=>(0,a.jsx)(o,{toast:e,onRemove:t},e.id))})}function h(){let[e,s]=(0,r.useState)([]),t=e=>{let t=Date.now().toString();s(s=>[...s,{...e,id:t}])};return{toasts:e,addToast:t,removeToast:e=>{s(s=>s.filter(s=>s.id!==e))},success:(e,s)=>{t({type:"success",description:e,title:s})},error:(e,s)=>{t({type:"error",description:e,title:s})},warning:(e,s)=>{t({type:"warning",description:e,title:s})},info:(e,s)=>{t({type:"info",description:e,title:s})}}}},27375:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>P});var a=t(95155),r=t(12115),l=t(40283),i=t(86566),n=t(66695),c=t(30285),d=t(62523),x=t(59409),o=t(44838),m=t(6982),h=t(26126),u=t(14186),j=t(40646),g=t(37108),p=t(29799),b=t(54861),f=t(85339);function N(e){let{status:s,showIcon:t=!0}=e,r=(e=>{switch(e){case"pending":return{text:"في الانتظار",className:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",icon:u.A};case"confirmed":return{text:"مؤكد",className:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",icon:j.A};case"in_production":return{text:"قيد الإنتاج",className:"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300",icon:g.A};case"shipped":return{text:"تم الشحن",className:"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300",icon:p.A};case"delivered":return{text:"تم التسليم",className:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",icon:j.A};case"cancelled":return{text:"ملغي",className:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",icon:b.A};default:return{text:e,className:"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300",icon:f.A}}})(s),l=r.icon;return(0,a.jsxs)(h.E,{className:r.className,children:[t&&(0,a.jsx)(l,{className:"h-3 w-3 mr-1"}),(0,a.jsx)("span",{className:"arabic-text",children:r.text})]})}function v(e){let{status:s,showIcon:t=!0}=e,r=(e=>{switch(e){case"pending":return{text:"في الانتظار",className:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",icon:u.A};case"paid":return{text:"مدفوع",className:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",icon:j.A};case"failed":return{text:"فشل",className:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",icon:b.A};case"refunded":return{text:"مسترد",className:"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300",icon:f.A};default:return{text:e,className:"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300",icon:f.A}}})(s),l=r.icon;return(0,a.jsxs)(h.E,{className:r.className,children:[t&&(0,a.jsx)(l,{className:"h-3 w-3 mr-1"}),(0,a.jsx)("span",{className:"arabic-text",children:r.text})]})}var y=t(54165),w=t(88539),k=t(71007),_=t(28883),A=t(19420),C=t(4516),z=t(81586),S=t(4229),Z=t(54416);function D(e){let{order:s,isOpen:t,onClose:l,onUpdate:i}=e,d=(0,m.dj)(),[o,h]=(0,r.useState)(!1),[u,j]=(0,r.useState)(""),[p,b]=(0,r.useState)(""),[f,D]=(0,r.useState)("");if(!s)return null;let E=async()=>{try{h(!0);let e={};u&&u!==s.status&&(e.status=u),p&&p!==s.payment_status&&(e.payment_status=p),f!==s.notes&&(e.notes=f),Object.keys(e).length>0?(await i(s.id,e),d.success("تم تحديث الطلب بنجاح"),l()):d.info("لا توجد تغييرات للحفظ")}catch(e){d.error("فشل في تحديث الطلب")}finally{h(!1)}};return(0,a.jsx)(y.lG,{open:t,onOpenChange:l,children:(0,a.jsxs)(y.Cf,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsx)(y.c7,{children:(0,a.jsxs)(y.L3,{className:"arabic-text",children:["تفاصيل الطلب ",s.order_number]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsxs)(n.ZB,{className:"arabic-text flex items-center gap-2",children:[(0,a.jsx)(k.A,{className:"h-5 w-5"}),"معلومات العميل"]})}),(0,a.jsxs)(n.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(k.A,{className:"h-4 w-4 text-gray-500"}),(0,a.jsx)("span",{className:"font-medium arabic-text",children:s.customer_name})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(_.A,{className:"h-4 w-4 text-gray-500"}),(0,a.jsx)("span",{className:"text-sm",children:s.customer_email})]}),s.customer_phone&&(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(A.A,{className:"h-4 w-4 text-gray-500"}),(0,a.jsx)("span",{className:"text-sm",children:s.customer_phone})]}),s.school_name&&(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(g.A,{className:"h-4 w-4 text-gray-500"}),(0,a.jsx)("span",{className:"text-sm arabic-text",children:s.school_name})]})]})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsxs)(n.ZB,{className:"arabic-text flex items-center gap-2",children:[(0,a.jsx)(g.A,{className:"h-5 w-5"}),"حالة الطلب"]})}),(0,a.jsxs)(n.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium arabic-text",children:"حالة الطلب:"}),(0,a.jsx)(N,{status:s.status})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium arabic-text",children:"حالة الدفع:"}),(0,a.jsx)(v,{status:s.payment_status})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium arabic-text",children:"طريقة الدفع:"}),(0,a.jsx)("span",{className:"text-sm",children:s.payment_method||"غير محدد"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium arabic-text",children:"تاريخ الطلب:"}),(0,a.jsx)("span",{className:"text-sm",children:new Date(s.created_at).toLocaleDateString("ar-SA")})]}),s.tracking_number&&(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium arabic-text",children:"رقم التتبع:"}),(0,a.jsx)("span",{className:"text-sm font-mono",children:s.tracking_number})]})]})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsxs)(n.ZB,{className:"arabic-text flex items-center gap-2",children:[(0,a.jsx)(C.A,{className:"h-5 w-5"}),"عنوان التوصيل"]})}),(0,a.jsx)(n.Wu,{children:(0,a.jsxs)("div",{className:"text-sm space-y-1",children:[(0,a.jsx)("div",{children:s.shipping_address.street}),(0,a.jsxs)("div",{children:[s.shipping_address.city,", ",s.shipping_address.state]}),(0,a.jsx)("div",{children:s.shipping_address.postal_code}),(0,a.jsx)("div",{children:s.shipping_address.country})]})})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsxs)(n.ZB,{className:"arabic-text flex items-center gap-2",children:[(0,a.jsx)(z.A,{className:"h-5 w-5"}),"ملخص الطلب"]})}),(0,a.jsxs)(n.Wu,{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"arabic-text",children:"المجموع الفرعي:"}),(0,a.jsxs)("span",{children:[s.subtotal.toFixed(2)," Dhs"]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"arabic-text",children:"الضريبة:"}),(0,a.jsxs)("span",{children:[s.tax.toFixed(2)," Dhs"]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"arabic-text",children:"الشحن:"}),(0,a.jsxs)("span",{children:[s.shipping_cost.toFixed(2)," Dhs"]})]}),(0,a.jsx)("div",{className:"border-t pt-3",children:(0,a.jsxs)("div",{className:"flex justify-between font-bold",children:[(0,a.jsx)("span",{className:"arabic-text",children:"الإجمالي:"}),(0,a.jsxs)("span",{children:[s.total.toFixed(2)," Dhs"]})]})})]})]})]}),(0,a.jsxs)(n.Zp,{className:"mt-6",children:[(0,a.jsx)(n.aR,{children:(0,a.jsx)(n.ZB,{className:"arabic-text",children:"عناصر الطلب"})}),(0,a.jsx)(n.Wu,{children:(0,a.jsx)("div",{className:"space-y-4",children:s.items.map(e=>(0,a.jsxs)("div",{className:"flex items-center gap-4 p-4 border rounded-lg",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-lg flex items-center justify-center",children:(0,a.jsx)(g.A,{className:"h-8 w-8 text-gray-500"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h4",{className:"font-medium arabic-text",children:e.product_name}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["الكمية: ",e.quantity]}),e.customizations&&(0,a.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:[e.customizations.color&&(0,a.jsxs)("span",{children:["اللون: ",e.customizations.color," "]}),e.customizations.size&&(0,a.jsxs)("span",{children:["المقاس: ",e.customizations.size," "]}),e.customizations.embroidery&&(0,a.jsxs)("span",{children:["التطريز: ",e.customizations.embroidery]})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:"font-medium",children:[e.total_price.toFixed(2)," Dhs"]}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:[e.unit_price.toFixed(2)," Dhs / قطعة"]})]})]},e.id))})})]}),(0,a.jsxs)(n.Zp,{className:"mt-6",children:[(0,a.jsx)(n.aR,{children:(0,a.jsx)(n.ZB,{className:"arabic-text",children:"تحديث الطلب"})}),(0,a.jsxs)(n.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium arabic-text",children:"حالة الطلب"}),(0,a.jsxs)(x.l6,{value:u||s.status,onValueChange:j,children:[(0,a.jsx)(x.bq,{children:(0,a.jsx)(x.yv,{})}),(0,a.jsxs)(x.gC,{children:[(0,a.jsx)(x.eb,{value:"pending",children:"في الانتظار"}),(0,a.jsx)(x.eb,{value:"confirmed",children:"مؤكد"}),(0,a.jsx)(x.eb,{value:"in_production",children:"قيد الإنتاج"}),(0,a.jsx)(x.eb,{value:"shipped",children:"تم الشحن"}),(0,a.jsx)(x.eb,{value:"delivered",children:"تم التسليم"}),(0,a.jsx)(x.eb,{value:"cancelled",children:"ملغي"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium arabic-text",children:"حالة الدفع"}),(0,a.jsxs)(x.l6,{value:p||s.payment_status,onValueChange:b,children:[(0,a.jsx)(x.bq,{children:(0,a.jsx)(x.yv,{})}),(0,a.jsxs)(x.gC,{children:[(0,a.jsx)(x.eb,{value:"pending",children:"في الانتظار"}),(0,a.jsx)(x.eb,{value:"paid",children:"مدفوع"}),(0,a.jsx)(x.eb,{value:"failed",children:"فشل"}),(0,a.jsx)(x.eb,{value:"refunded",children:"مسترد"})]})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium arabic-text",children:"ملاحظات"}),(0,a.jsx)(w.T,{value:f||s.notes||"",onChange:e=>D(e.target.value),placeholder:"إضافة ملاحظات...",className:"arabic-text"})]}),(0,a.jsxs)("div",{className:"flex gap-3 pt-4",children:[(0,a.jsxs)(c.$,{onClick:E,disabled:o,children:[(0,a.jsx)(S.A,{className:"h-4 w-4 mr-2"}),o?"جاري الحفظ...":"حفظ التغييرات"]}),(0,a.jsxs)(c.$,{variant:"outline",onClick:l,children:[(0,a.jsx)(Z.A,{className:"h-4 w-4 mr-2"}),"إلغاء"]})]})]})]})]})})}var E=t(6874),L=t.n(E),W=t(35169),T=t(53904),B=t(91788),F=t(55868),R=t(47924),V=t(5623),q=t(92657),$=t(13717),O=t(62525);function P(){let{user:e,profile:s}=(0,l.A)(),t=(0,m.dj)(),[h,b]=(0,r.useState)([]),[f,y]=(0,r.useState)([]),[w,k]=(0,r.useState)(null),[_,A]=(0,r.useState)(""),[C,z]=(0,r.useState)("all"),[S,Z]=(0,r.useState)("all"),[E,P]=(0,r.useState)("created_at"),[U,G]=(0,r.useState)("desc"),[I,J]=(0,r.useState)(!0),[M,Q]=(0,r.useState)(null),[Y,H]=(0,r.useState)(!1);(0,r.useEffect)(()=>{if(!e||!s||"admin"!==s.role){window.location.href="/dashboard";return}},[e,s]);let K=async()=>{try{J(!0);let e=await fetch("/api/orders"),s=await e.json();e.ok?(b(s.orders||[]),k(s.stats||null)):t.error(s.error||"فشل في جلب الطلبات")}catch(e){console.error("Error fetching orders:",e),t.error("خطأ في الاتصال بالخادم")}finally{J(!1)}};(0,r.useEffect)(()=>{K()},[]);let X=async(e,s)=>{try{let a=await fetch("/api/orders/".concat(e),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)}),r=await a.json();a.ok?(b(t=>t.map(t=>t.id===e?{...t,...s}:t)),t.success("تم تحديث الطلب بنجاح")):t.error(r.error||"فشل في تحديث الطلب")}catch(e){console.error("Error updating order:",e),t.error("خطأ في الاتصال بالخادم")}},ee=async e=>{if(confirm("هل أنت متأكد من حذف هذا الطلب؟"))try{let s=await fetch("/api/orders/".concat(e),{method:"DELETE"}),a=await s.json();s.ok?(b(s=>s.filter(s=>s.id!==e)),t.success("تم حذف الطلب بنجاح")):t.error(a.error||"فشل في حذف الطلب")}catch(e){console.error("Error deleting order:",e),t.error("خطأ في الاتصال بالخادم")}},es=e=>{Q(e),H(!0)};return((0,r.useEffect)(()=>{let e=[...h];_&&(e=e.filter(e=>e.order_number.toLowerCase().includes(_.toLowerCase())||e.customer_name.toLowerCase().includes(_.toLowerCase())||e.customer_email.toLowerCase().includes(_.toLowerCase()))),"all"!==C&&(e=e.filter(e=>e.status===C)),"all"!==S&&(e=e.filter(e=>e.payment_status===S)),e.sort((e,s)=>{let t=e[E],a=s[E];return("created_at"===E&&(t=new Date(t).getTime(),a=new Date(a).getTime()),"desc"===U)?a>t?1:-1:t>a?1:-1}),y(e)},[h,_,C,S,E,U]),I)?(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",children:[(0,a.jsx)(i.V,{}),(0,a.jsx)("main",{className:"container mx-auto px-4 py-8",children:(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})})})]}):(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",children:[(0,a.jsx)(i.V,{}),(0,a.jsxs)("main",{className:"container mx-auto px-4 py-8",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("div",{className:"flex items-center gap-4 mb-4",children:(0,a.jsx)(c.$,{variant:"outline",size:"sm",asChild:!0,children:(0,a.jsxs)(L(),{href:"/dashboard/admin",children:[(0,a.jsx)(W.A,{className:"h-4 w-4 mr-2"}),"العودة للوحة التحكم"]})})}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white arabic-text",children:"إدارة الطلبات \uD83D\uDCE6"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-300 mt-2 arabic-text",children:"إدارة ومتابعة جميع طلبات العملاء"})]}),(0,a.jsxs)("div",{className:"flex gap-3",children:[(0,a.jsxs)(c.$,{variant:"outline",onClick:K,children:[(0,a.jsx)(T.A,{className:"h-4 w-4 mr-2"}),"تحديث"]}),(0,a.jsxs)(c.$,{variant:"outline",children:[(0,a.jsx)(B.A,{className:"h-4 w-4 mr-2"}),"تصدير"]})]})]})]}),w&&(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,a.jsx)(n.Zp,{children:(0,a.jsx)(n.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text",children:"إجمالي الطلبات"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:w.total})]}),(0,a.jsx)(g.A,{className:"h-8 w-8 text-blue-600"})]})})}),(0,a.jsx)(n.Zp,{children:(0,a.jsx)(n.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text",children:"الطلبات المعلقة"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-yellow-600",children:w.pending})]}),(0,a.jsx)(u.A,{className:"h-8 w-8 text-yellow-600"})]})})}),(0,a.jsx)(n.Zp,{children:(0,a.jsx)(n.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text",children:"الطلبات المكتملة"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-green-600",children:w.delivered})]}),(0,a.jsx)(j.A,{className:"h-8 w-8 text-green-600"})]})})}),(0,a.jsx)(n.Zp,{children:(0,a.jsx)(n.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text",children:"إجمالي المبيعات"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-blue-600",children:[w.total_revenue.toFixed(2)," Dhs"]})]}),(0,a.jsx)(F.A,{className:"h-8 w-8 text-blue-600"})]})})})]}),(0,a.jsxs)(n.Zp,{className:"mb-6",children:[(0,a.jsx)(n.aR,{children:(0,a.jsx)(n.ZB,{className:"arabic-text",children:"البحث والفلترة"})}),(0,a.jsx)(n.Wu,{children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,a.jsx)("div",{children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(R.A,{className:"absolute left-3 top-3 h-4 w-4 text-gray-400"}),(0,a.jsx)(d.p,{placeholder:"البحث في الطلبات...",value:_,onChange:e=>A(e.target.value),className:"pl-10 arabic-text"})]})}),(0,a.jsxs)(x.l6,{value:C,onValueChange:z,children:[(0,a.jsx)(x.bq,{className:"arabic-text",children:(0,a.jsx)(x.yv,{placeholder:"حالة الطلب"})}),(0,a.jsxs)(x.gC,{children:[(0,a.jsx)(x.eb,{value:"all",children:"جميع الحالات"}),(0,a.jsx)(x.eb,{value:"pending",children:"في الانتظار"}),(0,a.jsx)(x.eb,{value:"confirmed",children:"مؤكد"}),(0,a.jsx)(x.eb,{value:"in_production",children:"قيد الإنتاج"}),(0,a.jsx)(x.eb,{value:"shipped",children:"تم الشحن"}),(0,a.jsx)(x.eb,{value:"delivered",children:"تم التسليم"}),(0,a.jsx)(x.eb,{value:"cancelled",children:"ملغي"})]})]}),(0,a.jsxs)(x.l6,{value:S,onValueChange:Z,children:[(0,a.jsx)(x.bq,{className:"arabic-text",children:(0,a.jsx)(x.yv,{placeholder:"حالة الدفع"})}),(0,a.jsxs)(x.gC,{children:[(0,a.jsx)(x.eb,{value:"all",children:"جميع حالات الدفع"}),(0,a.jsx)(x.eb,{value:"pending",children:"في الانتظار"}),(0,a.jsx)(x.eb,{value:"paid",children:"مدفوع"}),(0,a.jsx)(x.eb,{value:"failed",children:"فشل"}),(0,a.jsx)(x.eb,{value:"refunded",children:"مسترد"})]})]}),(0,a.jsxs)(x.l6,{value:"".concat(E,"-").concat(U),onValueChange:e=>{let[s,t]=e.split("-");P(s),G(t)},children:[(0,a.jsx)(x.bq,{className:"arabic-text",children:(0,a.jsx)(x.yv,{placeholder:"ترتيب حسب"})}),(0,a.jsxs)(x.gC,{children:[(0,a.jsx)(x.eb,{value:"created_at-desc",children:"الأحدث أولاً"}),(0,a.jsx)(x.eb,{value:"created_at-asc",children:"الأقدم أولاً"}),(0,a.jsx)(x.eb,{value:"total-desc",children:"المبلغ (الأعلى أولاً)"}),(0,a.jsx)(x.eb,{value:"total-asc",children:"المبلغ (الأقل أولاً)"}),(0,a.jsx)(x.eb,{value:"customer_name-asc",children:"اسم العميل (أ-ي)"}),(0,a.jsx)(x.eb,{value:"customer_name-desc",children:"اسم العميل (ي-أ)"})]})]})]})})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(n.ZB,{className:"arabic-text",children:"قائمة الطلبات"}),(0,a.jsxs)("div",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:[f.length," من ",h.length," طلب"]})]})}),(0,a.jsx)(n.Wu,{children:(0,a.jsxs)("div",{className:"overflow-x-auto",children:[(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{className:"border-b",children:[(0,a.jsx)("th",{className:"text-right p-4 font-medium text-gray-900 dark:text-white arabic-text",children:"رقم الطلب"}),(0,a.jsx)("th",{className:"text-right p-4 font-medium text-gray-900 dark:text-white arabic-text",children:"العميل"}),(0,a.jsx)("th",{className:"text-right p-4 font-medium text-gray-900 dark:text-white arabic-text",children:"المدرسة"}),(0,a.jsx)("th",{className:"text-right p-4 font-medium text-gray-900 dark:text-white arabic-text",children:"حالة الطلب"}),(0,a.jsx)("th",{className:"text-right p-4 font-medium text-gray-900 dark:text-white arabic-text",children:"حالة الدفع"}),(0,a.jsx)("th",{className:"text-right p-4 font-medium text-gray-900 dark:text-white arabic-text",children:"المبلغ"}),(0,a.jsx)("th",{className:"text-right p-4 font-medium text-gray-900 dark:text-white arabic-text",children:"التاريخ"}),(0,a.jsx)("th",{className:"text-right p-4 font-medium text-gray-900 dark:text-white arabic-text",children:"الإجراءات"})]})}),(0,a.jsx)("tbody",{children:f.map(e=>(0,a.jsxs)("tr",{className:"border-b hover:bg-gray-50 dark:hover:bg-gray-800",children:[(0,a.jsxs)("td",{className:"p-4",children:[(0,a.jsx)("div",{className:"font-medium text-blue-600 arabic-text",children:e.order_number}),(0,a.jsxs)("div",{className:"text-sm text-gray-500 arabic-text",children:[e.items.length," عنصر"]})]}),(0,a.jsxs)("td",{className:"p-4",children:[(0,a.jsx)("div",{className:"font-medium text-gray-900 dark:text-white arabic-text",children:e.customer_name}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.customer_email})]}),(0,a.jsx)("td",{className:"p-4",children:(0,a.jsx)("div",{className:"text-sm text-gray-900 dark:text-white arabic-text",children:e.school_name||"غير محدد"})}),(0,a.jsx)("td",{className:"p-4",children:(0,a.jsx)(N,{status:e.status})}),(0,a.jsx)("td",{className:"p-4",children:(0,a.jsx)(v,{status:e.payment_status})}),(0,a.jsx)("td",{className:"p-4",children:(0,a.jsxs)("div",{className:"font-medium text-gray-900 dark:text-white",children:[e.total.toFixed(2)," Dhs"]})}),(0,a.jsx)("td",{className:"p-4",children:(0,a.jsx)("div",{className:"text-sm text-gray-900 dark:text-white",children:new Date(e.created_at).toLocaleDateString("ar-SA")})}),(0,a.jsx)("td",{className:"p-4",children:(0,a.jsxs)(o.rI,{children:[(0,a.jsx)(o.ty,{asChild:!0,children:(0,a.jsx)(c.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(V.A,{className:"h-4 w-4"})})}),(0,a.jsxs)(o.SQ,{align:"end",children:[(0,a.jsxs)(o._2,{onClick:()=>es(e),children:[(0,a.jsx)(q.A,{className:"h-4 w-4 mr-2"}),"عرض التفاصيل"]}),(0,a.jsxs)(o._2,{onClick:()=>es(e),children:[(0,a.jsx)($.A,{className:"h-4 w-4 mr-2"}),"تحديث الحالة"]}),(0,a.jsxs)(o._2,{children:[(0,a.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"تتبع الشحنة"]}),(0,a.jsx)(o.mB,{}),(0,a.jsxs)(o._2,{className:"text-red-600",onClick:()=>ee(e.id),children:[(0,a.jsx)(O.A,{className:"h-4 w-4 mr-2"}),"حذف"]})]})]})})]},e.id))})]}),0===f.length&&(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(g.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-500 arabic-text",children:"لا توجد طلبات"})]})]})})]}),(0,a.jsx)(D,{order:M,isOpen:Y,onClose:()=>{H(!1),Q(null)},onUpdate:X})]})]})}},54165:(e,s,t)=>{"use strict";t.d(s,{Cf:()=>o,Es:()=>h,L3:()=>u,c7:()=>m,lG:()=>n,rr:()=>j,zM:()=>c});var a=t(95155);t(12115);var r=t(15452),l=t(54416),i=t(59434);function n(e){let{...s}=e;return(0,a.jsx)(r.bL,{"data-slot":"dialog",...s})}function c(e){let{...s}=e;return(0,a.jsx)(r.l9,{"data-slot":"dialog-trigger",...s})}function d(e){let{...s}=e;return(0,a.jsx)(r.ZL,{"data-slot":"dialog-portal",...s})}function x(e){let{className:s,...t}=e;return(0,a.jsx)(r.hJ,{"data-slot":"dialog-overlay",className:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",s),...t})}function o(e){let{className:s,children:t,showCloseButton:n=!0,...c}=e;return(0,a.jsxs)(d,{"data-slot":"dialog-portal",children:[(0,a.jsx)(x,{}),(0,a.jsxs)(r.UC,{"data-slot":"dialog-content",className:(0,i.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",s),...c,children:[t,n&&(0,a.jsxs)(r.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,a.jsx)(l.A,{}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function m(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"dialog-header",className:(0,i.cn)("flex flex-col gap-2 text-center sm:text-left",s),...t})}function h(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"dialog-footer",className:(0,i.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",s),...t})}function u(e){let{className:s,...t}=e;return(0,a.jsx)(r.hE,{"data-slot":"dialog-title",className:(0,i.cn)("text-lg leading-none font-semibold",s),...t})}function j(e){let{className:s,...t}=e;return(0,a.jsx)(r.VY,{"data-slot":"dialog-description",className:(0,i.cn)("text-muted-foreground text-sm",s),...t})}},59409:(e,s,t)=>{"use strict";t.d(s,{bq:()=>o,eb:()=>h,gC:()=>m,l6:()=>d,yv:()=>x});var a=t(95155);t(12115);var r=t(31992),l=t(66474),i=t(5196),n=t(47863),c=t(59434);function d(e){let{...s}=e;return(0,a.jsx)(r.bL,{"data-slot":"select",...s})}function x(e){let{...s}=e;return(0,a.jsx)(r.WT,{"data-slot":"select-value",...s})}function o(e){let{className:s,size:t="default",children:i,...n}=e;return(0,a.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":t,className:(0,c.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",s),...n,children:[i,(0,a.jsx)(r.In,{asChild:!0,children:(0,a.jsx)(l.A,{className:"size-4 opacity-50"})})]})}function m(e){let{className:s,children:t,position:l="popper",...i}=e;return(0,a.jsx)(r.ZL,{children:(0,a.jsxs)(r.UC,{"data-slot":"select-content",className:(0,c.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===l&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",s),position:l,...i,children:[(0,a.jsx)(u,{}),(0,a.jsx)(r.LM,{className:(0,c.cn)("p-1","popper"===l&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,a.jsx)(j,{})]})})}function h(e){let{className:s,children:t,...l}=e;return(0,a.jsxs)(r.q7,{"data-slot":"select-item",className:(0,c.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",s),...l,children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(r.VF,{children:(0,a.jsx)(i.A,{className:"size-4"})})}),(0,a.jsx)(r.p4,{children:t})]})}function u(e){let{className:s,...t}=e;return(0,a.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,c.cn)("flex cursor-default items-center justify-center py-1",s),...t,children:(0,a.jsx)(n.A,{className:"size-4"})})}function j(e){let{className:s,...t}=e;return(0,a.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,c.cn)("flex cursor-default items-center justify-center py-1",s),...t,children:(0,a.jsx)(l.A,{className:"size-4"})})}},62523:(e,s,t)=>{"use strict";t.d(s,{p:()=>l});var a=t(95155);t(12115);var r=t(59434);function l(e){let{className:s,type:t,...l}=e;return(0,a.jsx)("input",{type:t,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",s),...l})}},80703:(e,s,t)=>{Promise.resolve().then(t.bind(t,27375))},88539:(e,s,t)=>{"use strict";t.d(s,{T:()=>l});var a=t(95155);t(12115);var r=t(59434);function l(e){let{className:s,...t}=e;return(0,a.jsx)("textarea",{"data-slot":"textarea",className:(0,r.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",s),...t})}}},e=>{var s=s=>e(e.s=s);e.O(0,[7598,5486,380,2433,6874,8698,6671,7889,9221,4358,6893,2632,3898,6566,8441,1684,7358],()=>s(80703)),_N_E=e.O()}]);