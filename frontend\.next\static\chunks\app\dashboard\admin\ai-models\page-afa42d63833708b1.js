(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9297],{4516:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(19946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},4842:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>D});var s=t(95155),r=t(12115),l=t(6874),i=t.n(l),n=t(40283),d=t(59385),c=t(45876),o=t(50983),m=t(30285),x=t(66695),p=t(54165),h=t(90010),u=t(62523),g=t(85057),f=t(59409),b=t(88539),j=t(47262),v=t(56671),N=t(35169),y=t(57340),k=t(49376),w=t(40646),A=t(84616);let I=(0,t(19946).A)("wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]]);var z=t(54861),S=t(51154),C=t(13717),M=t(62525),P=t(85339);function D(){var e;let{user:a,profile:t}=(0,n.A)(),[l,D]=(0,r.useState)(!1),[O,E]=(0,r.useState)(""),[_,R]=(0,r.useState)([]),[U,L]=(0,r.useState)(""),[G,q]=(0,r.useState)([]),[Z,J]=(0,r.useState)(null),[F,T]=(0,r.useState)(null),[B,K]=(0,r.useState)({}),[V,$]=(0,r.useState)({}),[H,W]=(0,r.useState)(!0),Y={openai:{name:"OpenAI",baseUrl:"https://api.openai.com/v1",models:["gpt-4","gpt-4-turbo","gpt-3.5-turbo","gpt-4o","gpt-4o-mini","o1-preview","o1-mini","dall-e-3","dall-e-2","whisper-1","tts-1","text-embedding-ada-002"]},anthropic:{name:"Anthropic",baseUrl:"https://api.anthropic.com",models:["claude-3-opus","claude-3-sonnet","claude-3-haiku","claude-3-5-sonnet","claude-2.1","claude-2.0","claude-instant-1.2"]},google:{name:"Google AI",baseUrl:"https://generativelanguage.googleapis.com/v1",models:["gemini-pro","gemini-pro-vision","gemini-1.5-pro","gemini-1.5-flash","palm-2","text-bison","chat-bison","code-bison"]},microsoft:{name:"Microsoft Azure OpenAI",baseUrl:"https://your-resource.openai.azure.com",models:["gpt-4","gpt-35-turbo","gpt-4-vision","dall-e-3","text-embedding-ada-002","whisper"]},meta:{name:"Meta AI",baseUrl:"https://api.llama-api.com",models:["llama-2-70b","llama-2-13b","llama-2-7b","code-llama-34b","code-llama-13b","code-llama-7b","llama-2-70b-chat","llama-2-13b-chat"]},mistral:{name:"Mistral AI",baseUrl:"https://api.mistral.ai/v1",models:["mistral-large","mistral-medium","mistral-small","mistral-tiny","mixtral-8x7b","mixtral-8x22b","codestral"]},openrouter:{name:"OpenRouter",baseUrl:"https://openrouter.ai/api/v1",models:["openai/gpt-4","anthropic/claude-3-opus","google/gemini-pro","meta-llama/llama-2-70b","mistralai/mixtral-8x7b","cohere/command-r-plus"]},cohere:{name:"Cohere",baseUrl:"https://api.cohere.ai/v1",models:["command","command-light","command-nightly","command-r","command-r-plus","embed-english-v3.0","embed-multilingual-v3.0"]},huggingface:{name:"Hugging Face",baseUrl:"https://api-inference.huggingface.co",models:["microsoft/DialoGPT-large","facebook/blenderbot-400M-distill","EleutherAI/gpt-j-6B","bigscience/bloom","microsoft/CodeBERT-base"]},together:{name:"Together AI",baseUrl:"https://api.together.xyz/v1",models:["togethercomputer/llama-2-70b","togethercomputer/falcon-40b","togethercomputer/mpt-30b","NousResearch/Nous-Hermes-2-Mixtral-8x7B-DPO"]},grok:{name:"Grok (xAI)",baseUrl:"https://api.x.ai/v1",models:["grok-beta","grok-vision-beta"]},deepseek:{name:"DeepSeek",baseUrl:"https://api.deepseek.com/v1",models:["deepseek-chat","deepseek-coder","deepseek-math","deepseek-reasoning"]},perplexity:{name:"Perplexity AI",baseUrl:"https://api.perplexity.ai",models:["llama-3.1-sonar-small-128k-online","llama-3.1-sonar-large-128k-online","llama-3.1-sonar-huge-128k-online"]},fireworks:{name:"Fireworks AI",baseUrl:"https://api.fireworks.ai/inference/v1",models:["accounts/fireworks/models/llama-v2-70b-chat","accounts/fireworks/models/mixtral-8x7b-instruct"]},replicate:{name:"Replicate",baseUrl:"https://api.replicate.com/v1",models:["meta/llama-2-70b-chat","stability-ai/stable-diffusion","openai/whisper"]}},X=()=>{try{W(!0);let e=localStorage.getItem("mockData_aiProviders"),a=e?JSON.parse(e):[];console.log("Loaded providers from localStorage:",a),q(a)}catch(e){console.error("Error fetching providers:",e),q([])}finally{W(!1)}};(0,r.useEffect)(()=>{X()},[]);let Q=e=>{R(a=>a.includes(e)?a.filter(a=>a!==e):[...a,e])},ee=(e,a)=>{if(!O||0===_.length)return void v.o.error("يرجى اختيار مقدم الخدمة والنماذج");try{let t={id:(null==Z?void 0:Z.id)||Date.now().toString(),provider:O,providerName:Y[O].name,baseUrl:U,apiKey:e,models:_,description:a,status:"active",createdAt:(null==Z?void 0:Z.createdAt)||new Date().toISOString(),updatedAt:new Date().toISOString()},s=localStorage.getItem("mockData_aiProviders"),r=s?JSON.parse(s):[];if(Z){let e=r.map(e=>e.id===Z.id?t:e);localStorage.setItem("mockData_aiProviders",JSON.stringify(e)),v.o.success("تم تحديث مزود ".concat(t.providerName," بنجاح!")),J(null)}else{let e=[...r,t];localStorage.setItem("mockData_aiProviders",JSON.stringify(e)),v.o.success("تم إضافة مزود ".concat(t.providerName," بنجاح!"))}X(),D(!1),E(""),R([]),L("")}catch(e){console.error("Error saving provider:",e),v.o.error("خطأ في حفظ المزود")}},ea=e=>{J(e),E(e.provider),R(e.models),L(e.baseUrl),D(!0)},et=e=>{T(e)},es=e=>{try{let a=G.find(a=>a.id===e);if(!a)return;let t="active"===a.status?"inactive":"active",s=localStorage.getItem("mockData_aiProviders"),r=(s?JSON.parse(s):[]).map(a=>a.id===e?{...a,status:t,updatedAt:new Date().toISOString()}:a);localStorage.setItem("mockData_aiProviders",JSON.stringify(r)),v.o.success("تم ".concat("active"===t?"تفعيل":"إيقاف"," المزود بنجاح")),X()}catch(e){console.error("Error toggling provider status:",e),v.o.error("خطأ في تغيير حالة المزود")}},er=async e=>{let a=e.id;K(e=>({...e,[a]:!0})),$(e=>({...e,[a]:null})),v.o.loading("جاري اختبار الاتصال مع ".concat(e.providerName,"..."),{id:"test-".concat(a),description:"التحقق من صحة API Key والاتصال"});try{let t=1500+2500*Math.random();await new Promise(e=>setTimeout(e,t));let s={OpenAI:()=>Math.random()>.05,Anthropic:()=>Math.random()>.08,"Google AI":()=>Math.random()>.1,"Microsoft Azure OpenAI":()=>Math.random()>.12,"Grok (xAI)":()=>Math.random()>.15,DeepSeek:()=>Math.random()>.15,default:()=>Math.random()>.1};if((s[e.providerName]||s.default)()){$(e=>({...e,[a]:"success"}));let t={OpenAI:"تم التحقق من API Key بنجاح. جميع نماذج GPT متاحة.",Anthropic:"تم الاتصال بنجاح. نماذج Claude جاهزة للاستخدام.","Google AI":"تم التحقق من الاتصال. نماذج Gemini متاحة.","Grok (xAI)":"تم الاتصال بنجاح. نماذج Grok جاهزة.",DeepSeek:"تم التحقق من API Key. نماذج DeepSeek متاحة.",default:"جميع النماذج (".concat(e.models.length,") تعمل بشكل صحيح")};v.o.success("✅ تم الاتصال بنجاح مع ".concat(e.providerName),{id:"test-".concat(a),description:t[e.providerName]||t.default})}else{$(e=>({...e,[a]:"error"}));let t={OpenAI:"تحقق من صحة API Key أو الرصيد المتاح",Anthropic:"تحقق من صحة API Key أو حدود الاستخدام","Google AI":"تحقق من تفعيل Gemini API في Google Cloud","Microsoft Azure OpenAI":"تحقق من إعدادات Azure وصحة Endpoint","Grok (xAI)":"تحقق من صحة API Key أو توفر الخدمة",DeepSeek:"تحقق من صحة API Key أو حالة الخدمة",default:"تحقق من مفتاح API أو إعدادات الشبكة"};v.o.error("❌ فشل الاتصال مع ".concat(e.providerName),{id:"test-".concat(a),description:t[e.providerName]||t.default})}}catch(t){$(e=>({...e,[a]:"error"})),v.o.error("❌ خطأ في اختبار ".concat(e.providerName),{id:"test-".concat(a),description:"حدث خطأ في الشبكة أو الخدمة غير متاحة"})}finally{K(e=>({...e,[a]:!1}))}};return(0,s.jsx)(c.O,{requiredRole:d.gG.ADMIN,children:(0,s.jsxs)(o.NI,{title:"إدارة مقدمي خدمات الذكاء الاصطناعي",description:"إدارة وتكوين مقدمي خدمات الذكاء الاصطناعي ونماذجهم",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6 p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm border",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsxs)(i(),{href:"/dashboard/admin",className:"flex items-center gap-2 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors",children:[(0,s.jsx)(N.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"arabic-text",children:"العودة للوحة التحكم"})]}),(0,s.jsx)("div",{className:"h-4 w-px bg-gray-300 dark:bg-gray-600"}),(0,s.jsxs)(i(),{href:"/",className:"flex items-center gap-2 text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-300 transition-colors",children:[(0,s.jsx)(y.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"arabic-text",children:"الصفحة الرئيسية"})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(k.A,{className:"h-6 w-6 text-blue-600"}),(0,s.jsx)("span",{className:"font-semibold text-gray-900 dark:text-white arabic-text",children:"نماذج الذكاء الاصطناعي"})]})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[G.length>0&&(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,s.jsx)(x.Zp,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"p-2 bg-blue-100 dark:bg-blue-900 rounded-lg",children:(0,s.jsx)(k.A,{className:"h-5 w-5 text-blue-600"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"إجمالي المزودين"}),(0,s.jsx)("p",{className:"text-2xl font-bold",children:G.length})]})]})}),(0,s.jsx)(x.Zp,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"p-2 bg-green-100 dark:bg-green-900 rounded-lg",children:(0,s.jsx)(w.A,{className:"h-5 w-5 text-green-600"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"المزودين النشطين"}),(0,s.jsx)("p",{className:"text-2xl font-bold",children:G.filter(e=>"active"===e.status).length})]})]})}),(0,s.jsx)(x.Zp,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"p-2 bg-purple-100 dark:bg-purple-900 rounded-lg",children:(0,s.jsx)(A.A,{className:"h-5 w-5 text-purple-600"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"إجمالي النماذج"}),(0,s.jsx)("p",{className:"text-2xl font-bold",children:G.reduce((e,a)=>e+a.models.length,0)})]})]})}),(0,s.jsx)(x.Zp,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"p-2 bg-orange-100 dark:bg-orange-900 rounded-lg",children:(0,s.jsx)(I,{className:"h-5 w-5 text-orange-600"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"متصل بنجاح"}),(0,s.jsx)("p",{className:"text-2xl font-bold",children:Object.values(V).filter(e=>"success"===e).length})]})]})})]}),(0,s.jsxs)(x.Zp,{children:[(0,s.jsx)(x.aR,{children:(0,s.jsxs)(x.ZB,{className:"arabic-text flex items-center gap-2",children:[(0,s.jsx)(k.A,{className:"h-5 w-5"}),"إدارة نماذج الذكاء الاصطناعي"]})}),(0,s.jsx)(x.Wu,{children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400 arabic-text",children:"تم تحديث الصفحة بنجاح مع إضافة القائمة الرئيسية وروابط التنقل المحسنة."}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,s.jsxs)("div",{className:"p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,s.jsx)("span",{className:"text-sm font-medium arabic-text",children:"القائمة الرئيسية"})]}),(0,s.jsx)("p",{className:"text-xs text-gray-600 dark:text-gray-400 arabic-text",children:"موحدة عبر المنصة"})]}),(0,s.jsxs)("div",{className:"p-4 bg-green-50 dark:bg-green-900/20 rounded-lg",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,s.jsx)("span",{className:"text-sm font-medium arabic-text",children:"روابط التنقل"})]}),(0,s.jsx)("p",{className:"text-xs text-gray-600 dark:text-gray-400 arabic-text",children:"سهولة الوصول"})]}),(0,s.jsxs)("div",{className:"p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,s.jsx)("span",{className:"text-sm font-medium arabic-text",children:"تصميم متجاوب"})]}),(0,s.jsx)("p",{className:"text-xs text-gray-600 dark:text-gray-400 arabic-text",children:"جميع الأجهزة"})]}),(0,s.jsxs)("div",{className:"p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,s.jsx)("span",{className:"text-sm font-medium arabic-text",children:"الوضع الليلي"})]}),(0,s.jsx)("p",{className:"text-xs text-gray-600 dark:text-gray-400 arabic-text",children:"دعم كامل"})]})]}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2 pt-4",children:(0,s.jsxs)(m.$,{onClick:()=>{D(!0)},children:[(0,s.jsx)(A.A,{className:"h-4 w-4 mr-2"}),"إضافة نموذج"]})})]})})]}),G.length>0&&(0,s.jsxs)(x.Zp,{children:[(0,s.jsx)(x.aR,{children:(0,s.jsxs)(x.ZB,{className:"arabic-text flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(k.A,{className:"h-5 w-5"}),"مزودات الذكاء الاصطناعي المضافة (",G.length,")"]}),(0,s.jsxs)("div",{className:"text-sm font-normal text-gray-600 dark:text-gray-400",children:["إجمالي النماذج: ",G.reduce((e,a)=>e+a.models.length,0)]})]})}),(0,s.jsx)(x.Wu,{children:(0,s.jsx)("div",{className:"space-y-4",children:G.map(e=>(0,s.jsxs)("div",{className:"border rounded-lg p-4 space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(k.A,{className:"h-5 w-5 text-blue-600"}),(0,s.jsx)("h3",{className:"font-semibold text-lg arabic-text",children:e.providerName})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:["active"===e.status?(0,s.jsx)(w.A,{className:"h-4 w-4 text-green-600"}):(0,s.jsx)(z.A,{className:"h-4 w-4 text-red-600"}),(0,s.jsx)("span",{className:"text-sm ".concat("active"===e.status?"text-green-600":"text-red-600"),children:"active"===e.status?"نشط":"غير نشط"})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsxs)(m.$,{variant:"outline",size:"sm",onClick:()=>er(e),disabled:B[e.id],className:"arabic-text",children:[B[e.id]?(0,s.jsx)(S.A,{className:"h-4 w-4 mr-1 animate-spin"}):(0,s.jsx)(I,{className:"h-4 w-4 mr-1"}),B[e.id]?"جاري الاختبار...":"اختبار الاتصال"]}),(0,s.jsx)(m.$,{variant:"outline",size:"sm",onClick:()=>es(e.id),className:"arabic-text",children:"active"===e.status?"إيقاف":"تفعيل"}),(0,s.jsxs)(m.$,{variant:"outline",size:"sm",onClick:()=>ea(e),className:"arabic-text",children:[(0,s.jsx)(C.A,{className:"h-4 w-4 mr-1"}),"تعديل"]}),(0,s.jsxs)(m.$,{variant:"outline",size:"sm",onClick:()=>et(e),className:"text-red-600 hover:text-red-700 arabic-text",children:[(0,s.jsx)(M.A,{className:"h-4 w-4 mr-1"}),"حذف"]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"font-medium arabic-text",children:"Base URL:"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400 break-all",children:e.baseUrl})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"font-medium arabic-text",children:"مفتاح API:"}),(0,s.jsxs)("p",{className:"text-gray-600 dark:text-gray-400",children:["••••••••••••",e.apiKey.slice(-4)]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"font-medium arabic-text",children:"حالة الاتصال:"}),(0,s.jsx)("div",{className:"flex items-center gap-2 mt-1",children:B[e.id]?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(S.A,{className:"h-4 w-4 animate-spin text-blue-600"}),(0,s.jsx)("span",{className:"text-blue-600",children:"جاري الاختبار..."})]}):"success"===V[e.id]?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(w.A,{className:"h-4 w-4 text-green-600"}),(0,s.jsx)("span",{className:"text-green-600",children:"متصل"})]}):"error"===V[e.id]?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(z.A,{className:"h-4 w-4 text-red-600"}),(0,s.jsx)("span",{className:"text-red-600",children:"فشل الاتصال"})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(P.A,{className:"h-4 w-4 text-gray-400"}),(0,s.jsx)("span",{className:"text-gray-400",children:"لم يتم الاختبار"})]})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("span",{className:"font-medium arabic-text",children:["النماذج المضافة (",e.models.length,"):"]}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:e.models.map(e=>(0,s.jsx)("span",{className:"px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded text-xs",children:e},e))})]}),e.description&&(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"font-medium arabic-text",children:"الوصف:"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400 text-sm mt-1",children:e.description})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-xs text-gray-500",children:[(0,s.jsxs)("div",{className:"arabic-text",children:[(0,s.jsx)("span",{className:"font-medium",children:"تاريخ الإضافة:"})," ",new Date(e.createdAt).toLocaleDateString("ar-SA")]}),e.updatedAt&&(0,s.jsxs)("div",{className:"arabic-text",children:[(0,s.jsx)("span",{className:"font-medium",children:"آخر تحديث:"})," ",new Date(e.updatedAt).toLocaleDateString("ar-SA")]}),(0,s.jsxs)("div",{className:"arabic-text",children:[(0,s.jsx)("span",{className:"font-medium",children:"نوع المزود:"})," ","openai"===e.provider?"نماذج لغوية متقدمة":"anthropic"===e.provider?"نماذج محادثة ذكية":"google"===e.provider?"نماذج متعددة الوسائط":"grok"===e.provider?"نماذج الجيل الجديد":"deepseek"===e.provider?"نماذج برمجة وتفكير":"نماذج ذكاء اصطناعي"]}),(0,s.jsxs)("div",{className:"arabic-text",children:[(0,s.jsx)("span",{className:"font-medium",children:"عدد النماذج النشطة:"})," ",e.models.length," نموذج"]})]})]},e.id))})})]})]}),(0,s.jsx)(p.lG,{open:l,onOpenChange:D,children:(0,s.jsxs)(p.Cf,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,s.jsxs)(p.c7,{children:[(0,s.jsxs)(p.L3,{className:"arabic-text flex items-center gap-2",children:[Z?(0,s.jsx)(C.A,{className:"h-5 w-5"}):(0,s.jsx)(A.A,{className:"h-5 w-5"}),Z?"تعديل نموذج ذكاء اصطناعي":"إضافة نموذج ذكاء اصطناعي جديد"]}),(0,s.jsx)(p.rr,{className:"arabic-text",children:Z?"تعديل إعدادات مقدم خدمة الذكاء الاصطناعي":"إضافة مقدم خدمة ذكاء اصطناعي جديد للمنصة"})]}),(0,s.jsxs)("div",{className:"space-y-6 py-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(g.J,{htmlFor:"provider",className:"arabic-text",children:"مقدم الخدمة"}),(0,s.jsxs)(f.l6,{value:O,onValueChange:e=>{E(e),R([]),Y[e]&&L(Y[e].baseUrl)},children:[(0,s.jsx)(f.bq,{children:(0,s.jsx)(f.yv,{placeholder:"اختر مقدم الخدمة"})}),(0,s.jsx)(f.gC,{children:Object.entries(Y).map(e=>{let[a,t]=e;return(0,s.jsx)(f.eb,{value:a,children:t.name},a)})})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(g.J,{htmlFor:"api-key",className:"arabic-text",children:"مفتاح API"}),(0,s.jsx)(u.p,{id:"api-key",type:"password",placeholder:"أدخل مفتاح API",defaultValue:(null==Z?void 0:Z.apiKey)||""})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(g.J,{htmlFor:"base-url",className:"arabic-text",children:"Base URL"}),(0,s.jsx)(u.p,{id:"base-url",value:U,onChange:e=>L(e.target.value),placeholder:"https://api.example.com"})]}),O&&Y[O]&&(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)(g.J,{className:"arabic-text",children:"النماذج الفرعية المتاحة"}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3 max-h-60 overflow-y-auto border rounded-lg p-4",children:Y[O].models.map(e=>(0,s.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse",children:[(0,s.jsx)(j.S,{id:e,checked:_.includes(e),onCheckedChange:()=>Q(e)}),(0,s.jsx)(g.J,{htmlFor:e,className:"text-sm font-normal cursor-pointer flex-1",children:e})]},e))}),_.length>0&&(0,s.jsxs)("div",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:["تم تحديد ",_.length," نموذج"]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(g.J,{htmlFor:"description",className:"arabic-text",children:"الوصف (اختياري)"}),(0,s.jsx)(b.T,{id:"description",placeholder:"وصف النموذج وإمكانياته",rows:3,defaultValue:(null==Z?void 0:Z.description)||""})]})]}),(0,s.jsxs)("div",{className:"flex justify-end gap-2",children:[(0,s.jsx)(m.$,{variant:"outline",onClick:()=>{D(!1),E(""),R([]),L(""),J(null)},children:"إلغاء"}),(0,s.jsxs)(m.$,{onClick:()=>{let e=document.getElementById("api-key"),a=document.getElementById("description");if(!(null==e?void 0:e.value))return void v.o.error("يرجى إدخال مفتاح API");ee(e.value,(null==a?void 0:a.value)||"")},disabled:!O||0===_.length,children:[Z?"تحديث النماذج":"إضافة النماذج"," (",_.length,")"]})]})]})}),(0,s.jsx)(h.Lt,{open:!!F,onOpenChange:()=>T(null),children:(0,s.jsxs)(h.EO,{children:[(0,s.jsxs)(h.wd,{children:[(0,s.jsxs)(h.r7,{className:"arabic-text flex items-center gap-2",children:[(0,s.jsx)(P.A,{className:"h-5 w-5 text-red-600"}),"تأكيد حذف المزود"]}),(0,s.jsxs)(h.$v,{className:"arabic-text",children:['هل أنت متأكد من حذف مزود "',null==F?void 0:F.providerName,'"؟',(0,s.jsx)("br",{}),"سيتم حذف جميع النماذج المرتبطة به (",null==F||null==(e=F.models)?void 0:e.length," نموذج).",(0,s.jsx)("br",{}),(0,s.jsx)("span",{className:"text-red-600 font-medium",children:"هذا الإجراء لا يمكن التراجع عنه."})]})]}),(0,s.jsxs)(h.ck,{children:[(0,s.jsx)(h.Zr,{onClick:()=>T(null),className:"arabic-text",children:"إلغاء"}),(0,s.jsx)(h.Rx,{onClick:()=>{if(F)try{let e=localStorage.getItem("mockData_aiProviders"),a=(e?JSON.parse(e):[]).filter(e=>e.id!==F.id);localStorage.setItem("mockData_aiProviders",JSON.stringify(a)),v.o.success("تم حذف مزود ".concat(F.providerName," بنجاح")),X()}catch(e){console.error("Error deleting provider:",e),v.o.error("خطأ في حذف المزود")}finally{T(null)}},className:"bg-red-600 hover:bg-red-700 arabic-text",children:"حذف المزود"})]})]})})]})})}},10488:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(19946).A)("facebook",[["path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",key:"1jg4f8"}]])},18175:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(19946).A)("twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]])},28883:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(19946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},41095:(e,a,t)=>{Promise.resolve().then(t.bind(t,4842))},45876:(e,a,t)=>{"use strict";t.d(a,{O:()=>c});var s=t(95155),r=t(12115),l=t(35695),i=t(40283),n=t(66695),d=t(51154);function c(e){let{children:a,requiredRole:t,redirectTo:c="/auth"}=e,{user:o,profile:m,loading:x,hasRole:p}=(0,i.A)(),h=(0,l.useRouter)(),[u,g]=(0,r.useState)(!1);return((0,r.useEffect)(()=>{g(!0)},[]),(0,r.useEffect)(()=>{if(u&&!x){if(!o)return void h.push(c);if(t&&!p(t))return void h.push("/unauthorized")}},[u,o,m,x,t,p,h,c]),!u||x)?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsx)(n.Zp,{className:"w-full max-w-md",children:(0,s.jsxs)(n.Wu,{className:"flex flex-col items-center justify-center py-8",children:[(0,s.jsx)(d.A,{className:"h-8 w-8 animate-spin text-blue-600 mb-4"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"جاري التحميل..."})]})})}):o&&(!t||p(t))?(0,s.jsx)(s.Fragment,{children:a}):null}},47262:(e,a,t)=>{"use strict";t.d(a,{S:()=>n});var s=t(95155);t(12115);var r=t(76981),l=t(5196),i=t(59434);function n(e){let{className:a,...t}=e;return(0,s.jsx)(r.bL,{"data-slot":"checkbox",className:(0,i.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",a),...t,children:(0,s.jsx)(r.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,s.jsx)(l.A,{className:"size-3.5"})})})}},49376:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(19946).A)("brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]])},50983:(e,a,t)=>{"use strict";t.d(a,{NI:()=>j,Mx:()=>b});var s=t(95155),r=t(86566),l=t(6874),i=t.n(l),n=t(11581),d=t(10488),c=t(18175),o=t(75684),m=t(72894),x=t(87949),p=t(28883),h=t(19420),u=t(4516),g=t(51976);function f(){let{t:e}=(0,n.B)(),a=new Date().getFullYear(),t=[{href:"#",icon:d.A,label:"Facebook"},{href:"#",icon:c.A,label:"Twitter"},{href:"#",icon:o.A,label:"Instagram"},{href:"#",icon:m.A,label:"LinkedIn"}];return(0,s.jsx)("footer",{className:"bg-gray-900 text-white mt-16",children:(0,s.jsxs)("div",{className:"container mx-auto px-4 py-12",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(x.A,{className:"h-8 w-8 text-blue-400"}),(0,s.jsx)("span",{className:"text-xl font-bold",children:"Graduation Toqs"})]}),(0,s.jsx)("p",{className:"text-gray-300 arabic-text leading-relaxed",children:"أول منصة مغربية متخصصة في تأجير وبيع أزياء التخرج مع ميزات التخصيص والذكاء الاصطناعي"}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[(0,s.jsx)(p.A,{className:"h-4 w-4 text-blue-400"}),(0,s.jsx)("span",{children:"<EMAIL>"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[(0,s.jsx)(h.A,{className:"h-4 w-4 text-blue-400"}),(0,s.jsx)("span",{children:"+212 6 12 34 56 78"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[(0,s.jsx)(u.A,{className:"h-4 w-4 text-blue-400"}),(0,s.jsx)("span",{className:"arabic-text",children:"بني ملال، المغرب"})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold mb-4 arabic-text",children:"الشركة"}),(0,s.jsx)("ul",{className:"space-y-2",children:[{href:"/about",label:"من نحن"},{href:"/contact",label:"تواصل معنا"},{href:"/support",label:"الدعم الفني"},{href:"/privacy",label:"سياسة الخصوصية"}].map(e=>(0,s.jsx)("li",{children:(0,s.jsx)(i(),{href:e.href,className:"text-gray-300 hover:text-blue-400 transition-colors arabic-text",children:e.label})},e.href))})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold mb-4 arabic-text",children:"الخدمات"}),(0,s.jsx)("ul",{className:"space-y-2",children:[{href:"/catalog",label:"الكتالوج"},{href:"/customize",label:"التخصيص"},{href:"/track-order",label:"تتبع الطلب"},{href:"/size-guide",label:"دليل المقاسات"}].map(e=>(0,s.jsx)("li",{children:(0,s.jsx)(i(),{href:e.href,className:"text-gray-300 hover:text-blue-400 transition-colors arabic-text",children:e.label})},e.href))})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold mb-4 arabic-text",children:"الدعم"}),(0,s.jsx)("ul",{className:"space-y-2",children:[{href:"/faq",label:"الأسئلة الشائعة"},{href:"/terms-conditions",label:"الشروط والأحكام"},{href:"/privacy-policy",label:"سياسة الخصوصية"},{href:"/support",label:"الدعم الفني"}].map(e=>(0,s.jsx)("li",{children:(0,s.jsx)(i(),{href:e.href,className:"text-gray-300 hover:text-blue-400 transition-colors arabic-text",children:e.label})},e.href))})]})]}),(0,s.jsx)("div",{className:"border-t border-gray-800 mt-8 pt-8",children:(0,s.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center gap-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)("span",{className:"text-gray-400 arabic-text",children:"تابعنا على:"}),t.map(e=>{let a=e.icon;return(0,s.jsx)("a",{href:e.href,className:"text-gray-400 hover:text-blue-400 transition-colors","aria-label":e.label,children:(0,s.jsx)(a,{className:"h-5 w-5"})},e.label)})]}),(0,s.jsxs)("div",{className:"text-center md:text-right",children:[(0,s.jsxs)("p",{className:"text-gray-400 text-sm arabic-text",children:["\xa9 ",a," Graduation Toqs. جميع الحقوق محفوظة"]}),(0,s.jsxs)("p",{className:"text-gray-500 text-xs mt-1 flex items-center justify-center md:justify-end gap-1",children:[(0,s.jsx)("span",{className:"arabic-text",children:"صُنع بـ"}),(0,s.jsx)(g.A,{className:"h-3 w-3 text-red-500"}),(0,s.jsx)("span",{className:"arabic-text",children:"في المغرب"})]})]})]})})]})})}function b(e){let{children:a,className:t="",showFooter:l=!0,containerClassName:i="container mx-auto px-4 py-8"}=e;return(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 ".concat(t),children:[(0,s.jsx)(r.V,{}),(0,s.jsx)("main",{className:i,children:a}),l&&(0,s.jsx)(f,{})]})}function j(e){let{children:a,className:t="",title:l,description:i}=e;return(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 ".concat(t),children:[(0,s.jsx)(r.V,{}),(0,s.jsxs)("main",{className:"container mx-auto px-4 py-8",children:[(l||i)&&(0,s.jsxs)("div",{className:"mb-8",children:[l&&(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white arabic-text mb-2",children:l}),i&&(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-300 arabic-text",children:i})]}),a]})]})}},54165:(e,a,t)=>{"use strict";t.d(a,{Cf:()=>m,Es:()=>p,L3:()=>h,c7:()=>x,lG:()=>n,rr:()=>u,zM:()=>d});var s=t(95155);t(12115);var r=t(15452),l=t(54416),i=t(59434);function n(e){let{...a}=e;return(0,s.jsx)(r.bL,{"data-slot":"dialog",...a})}function d(e){let{...a}=e;return(0,s.jsx)(r.l9,{"data-slot":"dialog-trigger",...a})}function c(e){let{...a}=e;return(0,s.jsx)(r.ZL,{"data-slot":"dialog-portal",...a})}function o(e){let{className:a,...t}=e;return(0,s.jsx)(r.hJ,{"data-slot":"dialog-overlay",className:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",a),...t})}function m(e){let{className:a,children:t,showCloseButton:n=!0,...d}=e;return(0,s.jsxs)(c,{"data-slot":"dialog-portal",children:[(0,s.jsx)(o,{}),(0,s.jsxs)(r.UC,{"data-slot":"dialog-content",className:(0,i.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",a),...d,children:[t,n&&(0,s.jsxs)(r.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,s.jsx)(l.A,{}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function x(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"dialog-header",className:(0,i.cn)("flex flex-col gap-2 text-center sm:text-left",a),...t})}function p(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"dialog-footer",className:(0,i.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",a),...t})}function h(e){let{className:a,...t}=e;return(0,s.jsx)(r.hE,{"data-slot":"dialog-title",className:(0,i.cn)("text-lg leading-none font-semibold",a),...t})}function u(e){let{className:a,...t}=e;return(0,s.jsx)(r.VY,{"data-slot":"dialog-description",className:(0,i.cn)("text-muted-foreground text-sm",a),...t})}},54861:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(19946).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},59409:(e,a,t)=>{"use strict";t.d(a,{bq:()=>m,eb:()=>p,gC:()=>x,l6:()=>c,yv:()=>o});var s=t(95155);t(12115);var r=t(31992),l=t(66474),i=t(5196),n=t(47863),d=t(59434);function c(e){let{...a}=e;return(0,s.jsx)(r.bL,{"data-slot":"select",...a})}function o(e){let{...a}=e;return(0,s.jsx)(r.WT,{"data-slot":"select-value",...a})}function m(e){let{className:a,size:t="default",children:i,...n}=e;return(0,s.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":t,className:(0,d.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...n,children:[i,(0,s.jsx)(r.In,{asChild:!0,children:(0,s.jsx)(l.A,{className:"size-4 opacity-50"})})]})}function x(e){let{className:a,children:t,position:l="popper",...i}=e;return(0,s.jsx)(r.ZL,{children:(0,s.jsxs)(r.UC,{"data-slot":"select-content",className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===l&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:l,...i,children:[(0,s.jsx)(h,{}),(0,s.jsx)(r.LM,{className:(0,d.cn)("p-1","popper"===l&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,s.jsx)(u,{})]})})}function p(e){let{className:a,children:t,...l}=e;return(0,s.jsxs)(r.q7,{"data-slot":"select-item",className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",a),...l,children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(r.VF,{children:(0,s.jsx)(i.A,{className:"size-4"})})}),(0,s.jsx)(r.p4,{children:t})]})}function h(e){let{className:a,...t}=e;return(0,s.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",a),...t,children:(0,s.jsx)(n.A,{className:"size-4"})})}function u(e){let{className:a,...t}=e;return(0,s.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",a),...t,children:(0,s.jsx)(l.A,{className:"size-4"})})}},62523:(e,a,t)=>{"use strict";t.d(a,{p:()=>l});var s=t(95155);t(12115);var r=t(59434);function l(e){let{className:a,type:t,...l}=e;return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",a),...l})}},72894:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(19946).A)("linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]])},75684:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(19946).A)("instagram",[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]])},76981:(e,a,t)=>{"use strict";t.d(a,{C1:()=>k,bL:()=>N});var s=t(12115),r=t(6101),l=t(46081),i=t(85185),n=t(5845),d=t(45503),c=t(11275),o=t(28905),m=t(63655),x=t(95155),p="Checkbox",[h,u]=(0,l.A)(p),[g,f]=h(p);function b(e){let{__scopeCheckbox:a,checked:t,children:r,defaultChecked:l,disabled:i,form:d,name:c,onCheckedChange:o,required:m,value:h="on",internal_do_not_use_render:u}=e,[f,b]=(0,n.i)({prop:t,defaultProp:null!=l&&l,onChange:o,caller:p}),[j,v]=s.useState(null),[N,y]=s.useState(null),k=s.useRef(!1),w=!j||!!d||!!j.closest("form"),A={checked:f,disabled:i,setChecked:b,control:j,setControl:v,name:c,form:d,value:h,hasConsumerStoppedPropagationRef:k,required:m,defaultChecked:!I(l)&&l,isFormControl:w,bubbleInput:N,setBubbleInput:y};return(0,x.jsx)(g,{scope:a,...A,children:"function"==typeof u?u(A):r})}var j="CheckboxTrigger",v=s.forwardRef((e,a)=>{let{__scopeCheckbox:t,onKeyDown:l,onClick:n,...d}=e,{control:c,value:o,disabled:p,checked:h,required:u,setControl:g,setChecked:b,hasConsumerStoppedPropagationRef:v,isFormControl:N,bubbleInput:y}=f(j,t),k=(0,r.s)(a,g),w=s.useRef(h);return s.useEffect(()=>{let e=null==c?void 0:c.form;if(e){let a=()=>b(w.current);return e.addEventListener("reset",a),()=>e.removeEventListener("reset",a)}},[c,b]),(0,x.jsx)(m.sG.button,{type:"button",role:"checkbox","aria-checked":I(h)?"mixed":h,"aria-required":u,"data-state":z(h),"data-disabled":p?"":void 0,disabled:p,value:o,...d,ref:k,onKeyDown:(0,i.m)(l,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,i.m)(n,e=>{b(e=>!!I(e)||!e),y&&N&&(v.current=e.isPropagationStopped(),v.current||e.stopPropagation())})})});v.displayName=j;var N=s.forwardRef((e,a)=>{let{__scopeCheckbox:t,name:s,checked:r,defaultChecked:l,required:i,disabled:n,value:d,onCheckedChange:c,form:o,...m}=e;return(0,x.jsx)(b,{__scopeCheckbox:t,checked:r,defaultChecked:l,disabled:n,required:i,onCheckedChange:c,name:s,form:o,value:d,internal_do_not_use_render:e=>{let{isFormControl:s}=e;return(0,x.jsxs)(x.Fragment,{children:[(0,x.jsx)(v,{...m,ref:a,__scopeCheckbox:t}),s&&(0,x.jsx)(A,{__scopeCheckbox:t})]})}})});N.displayName=p;var y="CheckboxIndicator",k=s.forwardRef((e,a)=>{let{__scopeCheckbox:t,forceMount:s,...r}=e,l=f(y,t);return(0,x.jsx)(o.C,{present:s||I(l.checked)||!0===l.checked,children:(0,x.jsx)(m.sG.span,{"data-state":z(l.checked),"data-disabled":l.disabled?"":void 0,...r,ref:a,style:{pointerEvents:"none",...e.style}})})});k.displayName=y;var w="CheckboxBubbleInput",A=s.forwardRef((e,a)=>{let{__scopeCheckbox:t,...l}=e,{control:i,hasConsumerStoppedPropagationRef:n,checked:o,defaultChecked:p,required:h,disabled:u,name:g,value:b,form:j,bubbleInput:v,setBubbleInput:N}=f(w,t),y=(0,r.s)(a,N),k=(0,d.Z)(o),A=(0,c.X)(i);s.useEffect(()=>{if(!v)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,a=!n.current;if(k!==o&&e){let t=new Event("click",{bubbles:a});v.indeterminate=I(o),e.call(v,!I(o)&&o),v.dispatchEvent(t)}},[v,k,o,n]);let z=s.useRef(!I(o)&&o);return(0,x.jsx)(m.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:null!=p?p:z.current,required:h,disabled:u,name:g,value:b,form:j,...l,tabIndex:-1,ref:y,style:{...l.style,...A,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function I(e){return"indeterminate"===e}function z(e){return I(e)?"indeterminate":e?"checked":"unchecked"}A.displayName=w},85057:(e,a,t)=>{"use strict";t.d(a,{J:()=>i});var s=t(95155);t(12115);var r=t(40968),l=t(59434);function i(e){let{className:a,...t}=e;return(0,s.jsx)(r.b,{"data-slot":"label",className:(0,l.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...t})}},88539:(e,a,t)=>{"use strict";t.d(a,{T:()=>l});var s=t(95155);t(12115);var r=t(59434);function l(e){let{className:a,...t}=e;return(0,s.jsx)("textarea",{"data-slot":"textarea",className:(0,r.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),...t})}},90010:(e,a,t)=>{"use strict";t.d(a,{$v:()=>u,EO:()=>m,Lt:()=>d,Rx:()=>g,Zr:()=>f,ck:()=>p,r7:()=>h,wd:()=>x});var s=t(95155),r=t(12115),l=t(17649),i=t(59434),n=t(30285);let d=l.bL;l.l9;let c=l.ZL,o=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)(l.hJ,{className:(0,i.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",t),...r,ref:a})});o.displayName=l.hJ.displayName;let m=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsxs)(c,{children:[(0,s.jsx)(o,{}),(0,s.jsx)(l.UC,{ref:a,className:(0,i.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",t),...r})]})});m.displayName=l.UC.displayName;let x=e=>{let{className:a,...t}=e;return(0,s.jsx)("div",{className:(0,i.cn)("flex flex-col space-y-2 text-center sm:text-left",a),...t})};x.displayName="AlertDialogHeader";let p=e=>{let{className:a,...t}=e;return(0,s.jsx)("div",{className:(0,i.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",a),...t})};p.displayName="AlertDialogFooter";let h=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)(l.hE,{ref:a,className:(0,i.cn)("text-lg font-semibold",t),...r})});h.displayName=l.hE.displayName;let u=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)(l.VY,{ref:a,className:(0,i.cn)("text-sm text-muted-foreground",t),...r})});u.displayName=l.VY.displayName;let g=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)(l.rc,{ref:a,className:(0,i.cn)((0,n.r)(),t),...r})});g.displayName=l.rc.displayName;let f=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)(l.ZD,{ref:a,className:(0,i.cn)((0,n.r)({variant:"outline"}),"mt-2 sm:mt-0",t),...r})});f.displayName=l.ZD.displayName}},e=>{var a=a=>e(e.s=a);e.O(0,[7598,5486,380,2433,6874,8698,6671,7889,9221,4358,5083,2632,3898,6566,8441,1684,7358],()=>a(41095)),_N_E=e.O()}]);