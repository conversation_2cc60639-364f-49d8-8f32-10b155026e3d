{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/theme-provider.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { ThemeProvider as NextThemesProvider } from \"next-themes\"\n\ninterface ThemeProviderProps {\n  children: React.ReactNode\n  attribute?: string\n  defaultTheme?: string\n  enableSystem?: boolean\n  disableTransitionOnChange?: boolean\n}\n\nexport function ThemeProvider({ children, ...props }: ThemeProviderProps) {\n  return <NextThemesProvider {...props}>{children}</NextThemesProvider>\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAaO,SAAS,cAAc,EAAE,QAAQ,EAAE,GAAG,OAA2B;IACtE,qBAAO,8OAAC,gJAAA,CAAA,gBAAkB;QAAE,GAAG,KAAK;kBAAG;;;;;;AACzC", "debugId": null}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/types/auth.ts"], "sourcesContent": ["// أنواع البيانات للمصادقة والتفويض\n\nexport enum UserRole {\n  STUDENT = 'student',\n  SCHOOL = 'school',\n  ADMIN = 'admin',\n  DELIVERY = 'delivery'\n}\n\nexport interface UserProfile {\n  id: string\n  email: string\n  full_name: string\n  role: UserRole\n  phone?: string\n  school_name?: string\n  created_at: string\n  updated_at: string\n}\n\n// نوع مبسط للمستخدم للتطوير\nexport interface User {\n  id: string\n  email?: string\n}\n\nexport interface AuthContextType {\n  user: User | null\n  profile: UserProfile | null\n  loading: boolean\n  signUp: (email: string, password: string, userData: {\n    full_name: string\n    role: UserRole\n    phone?: string\n    school_name?: string\n  }) => Promise<{ data: unknown, error: string | null }>\n  signIn: (email: string, password: string) => Promise<{ data: unknown, error: string | null }>\n  signOut: () => Promise<{ error: string | null }>\n  updateProfile: (updates: Partial<UserProfile>) => Promise<{ data: unknown, error: string | null }>\n  hasRole: (requiredRole: UserRole) => boolean\n}\n\n// أنواع إضافية للمصادقة\nexport interface SignUpData {\n  email: string\n  password: string\n  full_name: string\n  role: UserRole\n  phone?: string\n  school_name?: string\n}\n\nexport interface SignInData {\n  email: string\n  password: string\n}\n\nexport interface AuthResponse {\n  data: unknown\n  error: string | null\n}\n\nexport interface SessionData {\n  user: User\n  profile: UserProfile\n  timestamp: number\n}\n\n// أذونات الأدوار\nexport const RolePermissions = {\n  [UserRole.ADMIN]: [\n    'manage_users',\n    'manage_products',\n    'manage_orders',\n    'manage_schools',\n    'manage_categories',\n    'manage_pages',\n    'manage_menu',\n    'manage_ai_models',\n    'manage_page_builder',\n    'view_analytics',\n    'manage_settings'\n  ],\n  [UserRole.SCHOOL]: [\n    'view_students',\n    'manage_school_orders',\n    'view_school_analytics',\n    'manage_school_profile'\n  ],\n  [UserRole.DELIVERY]: [\n    'view_assigned_orders',\n    'update_delivery_status',\n    'view_delivery_routes'\n  ],\n  [UserRole.STUDENT]: [\n    'place_orders',\n    'view_own_orders',\n    'update_profile'\n  ]\n} as const\n\nexport type Permission = typeof RolePermissions[UserRole][number]\n\n// دالة مساعدة للتحقق من الأذونات\nexport function hasPermission(userRole: UserRole, permission: Permission): boolean {\n  return RolePermissions[userRole].includes(permission as any)\n}\n\n// دالة مساعدة للتحقق من الدور الهرمي\nexport function hasRoleOrHigher(userRole: UserRole, requiredRole: UserRole): boolean {\n  const roleHierarchy = {\n    [UserRole.ADMIN]: 4,\n    [UserRole.SCHOOL]: 3,\n    [UserRole.DELIVERY]: 2,\n    [UserRole.STUDENT]: 1\n  }\n\n  return roleHierarchy[userRole] >= roleHierarchy[requiredRole]\n}\n"], "names": [], "mappings": "AAAA,mCAAmC;;;;;;;AAE5B,IAAA,AAAK,kCAAA;;;;;WAAA;;AAmEL,MAAM,kBAAkB;IAC7B,SAAgB,EAAE;QAChB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,UAAiB,EAAE;QACjB;QACA;QACA;QACA;KACD;IACD,YAAmB,EAAE;QACnB;QACA;QACA;KACD;IACD,WAAkB,EAAE;QAClB;QACA;QACA;KACD;AACH;AAKO,SAAS,cAAc,QAAkB,EAAE,UAAsB;IACtE,OAAO,eAAe,CAAC,SAAS,CAAC,QAAQ,CAAC;AAC5C;AAGO,SAAS,gBAAgB,QAAkB,EAAE,YAAsB;IACxE,MAAM,gBAAgB;QACpB,SAAgB,EAAE;QAClB,UAAiB,EAAE;QACnB,YAAmB,EAAE;QACrB,WAAkB,EAAE;IACtB;IAEA,OAAO,aAAa,CAAC,SAAS,IAAI,aAAa,CAAC,aAAa;AAC/D", "debugId": null}}, {"offset": {"line": 102, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/contexts/AuthContext.tsx"], "sourcesContent": ["\"use client\"\n\nimport React, { createContext, useContext, useEffect, useState } from 'react'\nimport { User, UserProfile, UserRole, AuthContextType, hasRoleOrHigher } from '@/types/auth'\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null)\n  const [profile, setProfile] = useState<UserProfile | null>(null)\n  const [loading, setLoading] = useState(true) // بدء التحميل بـ true\n  const [mounted, setMounted] = useState(false)\n\n  useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  useEffect(() => {\n    if (!mounted) return\n\n    // للتطوير - استرجاع المستخدم من localStorage\n    const loadUserData = async () => {\n      try {\n        const savedUser = localStorage.getItem('mockUser')\n        const savedProfile = localStorage.getItem('mockProfile')\n\n        if (savedUser && savedProfile) {\n          const userData = JSON.parse(savedUser)\n          const profileData = JSON.parse(savedProfile)\n\n          // التحقق من صحة البيانات وانتهاء الصلاحية\n          if (userData && profileData && userData.id && profileData.id) {\n            // التحقق من انتهاء صلاحية الجلسة (24 ساعة)\n            const sessionTimestamp = localStorage.getItem('sessionTimestamp')\n            const now = Date.now()\n            const sessionAge = sessionTimestamp ? now - parseInt(sessionTimestamp) : 0\n            const maxSessionAge = 24 * 60 * 60 * 1000 // 24 ساعة\n\n            if (sessionTimestamp && sessionAge < maxSessionAge) {\n              setUser(userData)\n              setProfile(profileData)\n              console.log('User data loaded from localStorage:', { userData, profileData })\n            } else {\n              // انتهت صلاحية الجلسة\n              console.log('Session expired, clearing user data')\n              localStorage.removeItem('mockUser')\n              localStorage.removeItem('mockProfile')\n              localStorage.removeItem('sessionTimestamp')\n            }\n          } else {\n            // إذا كانت البيانات غير صحيحة، امسحها\n            localStorage.removeItem('mockUser')\n            localStorage.removeItem('mockProfile')\n            localStorage.removeItem('sessionTimestamp')\n          }\n        }\n      } catch (error) {\n        console.error('Error loading user from localStorage:', error)\n        // في حالة خطأ، امسح البيانات المعطوبة\n        localStorage.removeItem('mockUser')\n        localStorage.removeItem('mockProfile')\n      } finally {\n        setLoading(false)\n      }\n    }\n\n    loadUserData()\n  }, [mounted])\n\n  // تجديد الجلسة عند النشاط\n  useEffect(() => {\n    if (!user || !profile) return\n\n    const refreshSession = () => {\n      try {\n        localStorage.setItem('sessionTimestamp', Date.now().toString())\n      } catch (error) {\n        console.error('Error refreshing session:', error)\n      }\n    }\n\n    // تجديد الجلسة عند النقر أو الحركة\n    const events = ['click', 'keypress', 'scroll', 'mousemove']\n    events.forEach(event => {\n      document.addEventListener(event, refreshSession, { passive: true })\n    })\n\n    return () => {\n      events.forEach(event => {\n        document.removeEventListener(event, refreshSession)\n      })\n    }\n  }, [user, profile])\n\n  const signUp = async (email: string, password: string, userData: {\n    full_name: string\n    role: UserRole\n    phone?: string\n    school_name?: string\n  }) => {\n    // للتطوير - محاكاة تسجيل حساب جديد\n    console.log('Sign up:', email, userData)\n    return { data: { user: { id: '1', email } }, error: null }\n  }\n\n  const signIn = async (email: string, password: string) => {\n    // للتطوير - محاكاة تسجيل الدخول\n    console.log('Sign in:', email)\n    const mockUser = { id: '1', email }\n\n    // تحديد الدور بناءً على الإيميل للتطوير\n    let role = UserRole.STUDENT\n    if (email.includes('admin')) {\n      role = UserRole.ADMIN\n    } else if (email.includes('school')) {\n      role = UserRole.SCHOOL\n    } else if (email.includes('delivery')) {\n      role = UserRole.DELIVERY\n    }\n\n    const mockProfile: UserProfile = {\n      id: '1',\n      email,\n      full_name: email.split('@')[0] || 'مستخدم',\n      role,\n      created_at: new Date().toISOString(),\n      updated_at: new Date().toISOString()\n    }\n    setUser(mockUser)\n    setProfile(mockProfile)\n\n    // حفظ في localStorage للتطوير مع التحقق من النجاح\n    try {\n      localStorage.setItem('mockUser', JSON.stringify(mockUser))\n      localStorage.setItem('mockProfile', JSON.stringify(mockProfile))\n      localStorage.setItem('sessionTimestamp', Date.now().toString())\n      console.log('User data saved to localStorage:', { mockUser, mockProfile })\n    } catch (error) {\n      console.error('Error saving user data to localStorage:', error)\n    }\n\n    // إعادة التوجيه بناءً على الدور\n    setTimeout(() => {\n      if (role === UserRole.ADMIN) {\n        window.location.href = '/dashboard/admin'\n      } else if (role === UserRole.SCHOOL) {\n        window.location.href = '/dashboard/school'\n      } else if (role === UserRole.DELIVERY) {\n        window.location.href = '/dashboard/delivery'\n      } else {\n        window.location.href = '/dashboard/student'\n      }\n    }, 100)\n\n    return { data: { user: mockUser }, error: null }\n  }\n\n  const signOut = async () => {\n    try {\n      setUser(null)\n      setProfile(null)\n\n      // حذف من localStorage\n      localStorage.removeItem('mockUser')\n      localStorage.removeItem('mockProfile')\n      localStorage.removeItem('sessionTimestamp')\n      console.log('User data cleared from localStorage')\n\n      return { error: null }\n    } catch (error) {\n      console.error('Error during sign out:', error)\n      return { error: 'فشل في تسجيل الخروج' }\n    }\n  }\n\n  const updateProfile = async (updates: Partial<UserProfile>) => {\n    if (!user) return { data: null, error: 'No user logged in' }\n\n    const updatedProfile = { ...profile, ...updates } as UserProfile\n    setProfile(updatedProfile)\n    return { data: updatedProfile, error: null }\n  }\n\n  const hasRole = (requiredRole: UserRole): boolean => {\n    if (!profile) return false\n    return hasRoleOrHigher(profile.role, requiredRole)\n  }\n\n  const value = {\n    user,\n    profile,\n    loading,\n    signUp,\n    signIn,\n    signOut,\n    updateProfile,\n    hasRole\n  }\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  )\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,sBAAsB;;IACnE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,SAAS;QAEd,6CAA6C;QAC7C,MAAM,eAAe;YACnB,IAAI;gBACF,MAAM,YAAY,aAAa,OAAO,CAAC;gBACvC,MAAM,eAAe,aAAa,OAAO,CAAC;gBAE1C,IAAI,aAAa,cAAc;oBAC7B,MAAM,WAAW,KAAK,KAAK,CAAC;oBAC5B,MAAM,cAAc,KAAK,KAAK,CAAC;oBAE/B,0CAA0C;oBAC1C,IAAI,YAAY,eAAe,SAAS,EAAE,IAAI,YAAY,EAAE,EAAE;wBAC5D,2CAA2C;wBAC3C,MAAM,mBAAmB,aAAa,OAAO,CAAC;wBAC9C,MAAM,MAAM,KAAK,GAAG;wBACpB,MAAM,aAAa,mBAAmB,MAAM,SAAS,oBAAoB;wBACzE,MAAM,gBAAgB,KAAK,KAAK,KAAK,KAAK,UAAU;;wBAEpD,IAAI,oBAAoB,aAAa,eAAe;4BAClD,QAAQ;4BACR,WAAW;4BACX,QAAQ,GAAG,CAAC,uCAAuC;gCAAE;gCAAU;4BAAY;wBAC7E,OAAO;4BACL,sBAAsB;4BACtB,QAAQ,GAAG,CAAC;4BACZ,aAAa,UAAU,CAAC;4BACxB,aAAa,UAAU,CAAC;4BACxB,aAAa,UAAU,CAAC;wBAC1B;oBACF,OAAO;wBACL,sCAAsC;wBACtC,aAAa,UAAU,CAAC;wBACxB,aAAa,UAAU,CAAC;wBACxB,aAAa,UAAU,CAAC;oBAC1B;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yCAAyC;gBACvD,sCAAsC;gBACtC,aAAa,UAAU,CAAC;gBACxB,aAAa,UAAU,CAAC;YAC1B,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG;QAAC;KAAQ;IAEZ,0BAA0B;IAC1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,QAAQ,CAAC,SAAS;QAEvB,MAAM,iBAAiB;YACrB,IAAI;gBACF,aAAa,OAAO,CAAC,oBAAoB,KAAK,GAAG,GAAG,QAAQ;YAC9D,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6BAA6B;YAC7C;QACF;QAEA,mCAAmC;QACnC,MAAM,SAAS;YAAC;YAAS;YAAY;YAAU;SAAY;QAC3D,OAAO,OAAO,CAAC,CAAA;YACb,SAAS,gBAAgB,CAAC,OAAO,gBAAgB;gBAAE,SAAS;YAAK;QACnE;QAEA,OAAO;YACL,OAAO,OAAO,CAAC,CAAA;gBACb,SAAS,mBAAmB,CAAC,OAAO;YACtC;QACF;IACF,GAAG;QAAC;QAAM;KAAQ;IAElB,MAAM,SAAS,OAAO,OAAe,UAAkB;QAMrD,mCAAmC;QACnC,QAAQ,GAAG,CAAC,YAAY,OAAO;QAC/B,OAAO;YAAE,MAAM;gBAAE,MAAM;oBAAE,IAAI;oBAAK;gBAAM;YAAE;YAAG,OAAO;QAAK;IAC3D;IAEA,MAAM,SAAS,OAAO,OAAe;QACnC,gCAAgC;QAChC,QAAQ,GAAG,CAAC,YAAY;QACxB,MAAM,WAAW;YAAE,IAAI;YAAK;QAAM;QAElC,wCAAwC;QACxC,IAAI,OAAO,oHAAA,CAAA,WAAQ,CAAC,OAAO;QAC3B,IAAI,MAAM,QAAQ,CAAC,UAAU;YAC3B,OAAO,oHAAA,CAAA,WAAQ,CAAC,KAAK;QACvB,OAAO,IAAI,MAAM,QAAQ,CAAC,WAAW;YACnC,OAAO,oHAAA,CAAA,WAAQ,CAAC,MAAM;QACxB,OAAO,IAAI,MAAM,QAAQ,CAAC,aAAa;YACrC,OAAO,oHAAA,CAAA,WAAQ,CAAC,QAAQ;QAC1B;QAEA,MAAM,cAA2B;YAC/B,IAAI;YACJ;YACA,WAAW,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI;YAClC;YACA,YAAY,IAAI,OAAO,WAAW;YAClC,YAAY,IAAI,OAAO,WAAW;QACpC;QACA,QAAQ;QACR,WAAW;QAEX,kDAAkD;QAClD,IAAI;YACF,aAAa,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC;YAChD,aAAa,OAAO,CAAC,eAAe,KAAK,SAAS,CAAC;YACnD,aAAa,OAAO,CAAC,oBAAoB,KAAK,GAAG,GAAG,QAAQ;YAC5D,QAAQ,GAAG,CAAC,oCAAoC;gBAAE;gBAAU;YAAY;QAC1E,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2CAA2C;QAC3D;QAEA,gCAAgC;QAChC,WAAW;YACT,IAAI,SAAS,oHAAA,CAAA,WAAQ,CAAC,KAAK,EAAE;gBAC3B,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB,OAAO,IAAI,SAAS,oHAAA,CAAA,WAAQ,CAAC,MAAM,EAAE;gBACnC,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB,OAAO,IAAI,SAAS,oHAAA,CAAA,WAAQ,CAAC,QAAQ,EAAE;gBACrC,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB,OAAO;gBACL,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB;QACF,GAAG;QAEH,OAAO;YAAE,MAAM;gBAAE,MAAM;YAAS;YAAG,OAAO;QAAK;IACjD;IAEA,MAAM,UAAU;QACd,IAAI;YACF,QAAQ;YACR,WAAW;YAEX,sBAAsB;YACtB,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;YACxB,QAAQ,GAAG,CAAC;YAEZ,OAAO;gBAAE,OAAO;YAAK;QACvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,OAAO;gBAAE,OAAO;YAAsB;QACxC;IACF;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI,CAAC,MAAM,OAAO;YAAE,MAAM;YAAM,OAAO;QAAoB;QAE3D,MAAM,iBAAiB;YAAE,GAAG,OAAO;YAAE,GAAG,OAAO;QAAC;QAChD,WAAW;QACX,OAAO;YAAE,MAAM;YAAgB,OAAO;QAAK;IAC7C;IAEA,MAAM,UAAU,CAAC;QACf,IAAI,CAAC,SAAS,OAAO;QACrB,OAAO,CAAA,GAAA,oHAAA,CAAA,kBAAe,AAAD,EAAE,QAAQ,IAAI,EAAE;IACvC;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 346, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/contexts/NotificationContext.tsx"], "sourcesContent": ["\"use client\"\n\nimport React, { createContext, useContext, useState, useEffect } from 'react'\nimport { useAuth } from '@/contexts/AuthContext'\n\n// أنواع الإشعارات\nexport type NotificationType = \n  | 'order_confirmed'      // تأكيد الطلب\n  | 'order_shipped'        // شحن الطلب\n  | 'order_delivered'      // تسليم الطلب\n  | 'payment_received'     // استلام الدفع\n  | 'payment_failed'       // فشل الدفع\n  | 'promotion'            // عرض ترويجي\n  | 'reminder'             // تذكير\n  | 'system'               // إشعار نظام\n  | 'message'              // رسالة\n  | 'review_request'       // طلب تقييم\n\nexport type NotificationPriority = 'low' | 'medium' | 'high' | 'urgent'\n\nexport interface Notification {\n  id: string\n  type: NotificationType\n  title: string\n  message: string\n  priority: NotificationPriority\n  isRead: boolean\n  createdAt: string\n  expiresAt?: string\n  actionUrl?: string\n  actionText?: string\n  metadata?: Record<string, any>\n  userId: string\n}\n\ninterface NotificationContextType {\n  notifications: Notification[]\n  unreadCount: number\n  addNotification: (notification: Omit<Notification, 'id' | 'createdAt' | 'userId' | 'isRead'>) => void\n  markAsRead: (notificationId: string) => void\n  markAllAsRead: () => void\n  removeNotification: (notificationId: string) => void\n  clearAll: () => void\n  getNotificationsByType: (type: NotificationType) => Notification[]\n  getUnreadNotifications: () => Notification[]\n}\n\nconst NotificationContext = createContext<NotificationContextType | undefined>(undefined)\n\nexport function NotificationProvider({ children }: { children: React.ReactNode }) {\n  const { user } = useAuth()\n  const [notifications, setNotifications] = useState<Notification[]>([])\n\n  // تحميل الإشعارات عند تسجيل الدخول\n  useEffect(() => {\n    if (user) {\n      loadNotifications()\n      // محاكاة استقبال إشعارات جديدة\n      const interval = setInterval(() => {\n        // محاكاة إشعار عشوائي كل 30 ثانية للتطوير\n        if (Math.random() > 0.8) {\n          addMockNotification()\n        }\n      }, 30000)\n\n      return () => clearInterval(interval)\n    }\n  }, [user])\n\n  const loadNotifications = () => {\n    // محاكاة تحميل الإشعارات من الخادم\n    const mockNotifications: Notification[] = [\n      {\n        id: '1',\n        type: 'order_confirmed',\n        title: 'تم تأكيد طلبك',\n        message: 'تم تأكيد طلبك #GT-240120-001 بنجاح وسيتم تحضيره قريباً',\n        priority: 'high',\n        isRead: false,\n        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),\n        actionUrl: '/track-order',\n        actionText: 'تتبع الطلب',\n        userId: user?.id || '',\n        metadata: { orderId: 'GT-240120-001' }\n      },\n      {\n        id: '2',\n        type: 'promotion',\n        title: 'عرض خاص - خصم 20%',\n        message: 'احصل على خصم 20% على جميع أزياء التخرج لفترة محدودة',\n        priority: 'medium',\n        isRead: false,\n        createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),\n        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),\n        actionUrl: '/catalog',\n        actionText: 'تسوق الآن',\n        userId: user?.id || '',\n        metadata: { promoCode: 'GRAD20' }\n      },\n      {\n        id: '3',\n        type: 'order_shipped',\n        title: 'تم شحن طلبك',\n        message: 'طلبك #GT-240115-002 في طريقه إليك. رقم التتبع: TRK-123456',\n        priority: 'high',\n        isRead: true,\n        createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),\n        actionUrl: '/track-order',\n        actionText: 'تتبع الشحنة',\n        userId: user?.id || '',\n        metadata: { orderId: 'GT-240115-002', trackingNumber: 'TRK-123456' }\n      },\n      {\n        id: '4',\n        type: 'review_request',\n        title: 'قيم تجربتك معنا',\n        message: 'نود معرفة رأيك في المنتجات التي استلمتها مؤخراً',\n        priority: 'low',\n        isRead: false,\n        createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),\n        actionUrl: '/reviews',\n        actionText: 'اكتب تقييم',\n        userId: user?.id || ''\n      }\n    ]\n\n    setNotifications(mockNotifications)\n  }\n\n  const addMockNotification = () => {\n    const mockTypes: NotificationType[] = ['system', 'promotion', 'reminder']\n    const randomType = mockTypes[Math.floor(Math.random() * mockTypes.length)]\n    \n    const mockMessages = {\n      system: {\n        title: 'تحديث النظام',\n        message: 'تم تحديث النظام بميزات جديدة'\n      },\n      promotion: {\n        title: 'عرض محدود',\n        message: 'خصم خاص على المنتجات المختارة'\n      },\n      reminder: {\n        title: 'تذكير',\n        message: 'لا تنس إكمال طلبك في سلة التسوق'\n      }\n    }\n\n    addNotification({\n      type: randomType,\n      title: mockMessages[randomType].title,\n      message: mockMessages[randomType].message,\n      priority: 'medium'\n    })\n  }\n\n  const addNotification = (notificationData: Omit<Notification, 'id' | 'createdAt' | 'userId' | 'isRead'>) => {\n    const newNotification: Notification = {\n      ...notificationData,\n      id: Date.now().toString(),\n      createdAt: new Date().toISOString(),\n      userId: user?.id || '',\n      isRead: false\n    }\n\n    setNotifications(prev => [newNotification, ...prev])\n\n    // إظهار إشعار المتصفح إذا كان مسموحاً\n    if (Notification.permission === 'granted') {\n      new Notification(newNotification.title, {\n        body: newNotification.message,\n        icon: '/favicon.ico',\n        tag: newNotification.id\n      })\n    }\n  }\n\n  const markAsRead = (notificationId: string) => {\n    setNotifications(prev =>\n      prev.map(notification =>\n        notification.id === notificationId\n          ? { ...notification, isRead: true }\n          : notification\n      )\n    )\n  }\n\n  const markAllAsRead = () => {\n    setNotifications(prev =>\n      prev.map(notification => ({ ...notification, isRead: true }))\n    )\n  }\n\n  const removeNotification = (notificationId: string) => {\n    setNotifications(prev =>\n      prev.filter(notification => notification.id !== notificationId)\n    )\n  }\n\n  const clearAll = () => {\n    setNotifications([])\n  }\n\n  const getNotificationsByType = (type: NotificationType) => {\n    return notifications.filter(notification => notification.type === type)\n  }\n\n  const getUnreadNotifications = () => {\n    return notifications.filter(notification => !notification.isRead)\n  }\n\n  const unreadCount = getUnreadNotifications().length\n\n  // طلب إذن الإشعارات عند التحميل\n  useEffect(() => {\n    if ('Notification' in window && Notification.permission === 'default') {\n      Notification.requestPermission()\n    }\n  }, [])\n\n  const value: NotificationContextType = {\n    notifications,\n    unreadCount,\n    addNotification,\n    markAsRead,\n    markAllAsRead,\n    removeNotification,\n    clearAll,\n    getNotificationsByType,\n    getUnreadNotifications\n  }\n\n  return (\n    <NotificationContext.Provider value={value}>\n      {children}\n    </NotificationContext.Provider>\n  )\n}\n\nexport function useNotifications() {\n  const context = useContext(NotificationContext)\n  if (context === undefined) {\n    throw new Error('useNotifications must be used within a NotificationProvider')\n  }\n  return context\n}\n\n// دوال مساعدة\nexport function getNotificationIcon(type: NotificationType): string {\n  switch (type) {\n    case 'order_confirmed':\n    case 'order_shipped':\n    case 'order_delivered':\n      return '📦'\n    case 'payment_received':\n      return '💳'\n    case 'payment_failed':\n      return '❌'\n    case 'promotion':\n      return '🎉'\n    case 'reminder':\n      return '⏰'\n    case 'system':\n      return '⚙️'\n    case 'message':\n      return '💬'\n    case 'review_request':\n      return '⭐'\n    default:\n      return '🔔'\n  }\n}\n\nexport function getNotificationColor(priority: NotificationPriority): string {\n  switch (priority) {\n    case 'urgent':\n      return 'text-red-600 bg-red-50 border-red-200'\n    case 'high':\n      return 'text-orange-600 bg-orange-50 border-orange-200'\n    case 'medium':\n      return 'text-blue-600 bg-blue-50 border-blue-200'\n    case 'low':\n      return 'text-gray-600 bg-gray-50 border-gray-200'\n    default:\n      return 'text-gray-600 bg-gray-50 border-gray-200'\n  }\n}\n\nexport function formatNotificationTime(timestamp: string): string {\n  const now = new Date()\n  const notificationTime = new Date(timestamp)\n  const diffInMinutes = Math.floor((now.getTime() - notificationTime.getTime()) / (1000 * 60))\n\n  if (diffInMinutes < 1) {\n    return 'الآن'\n  } else if (diffInMinutes < 60) {\n    return `منذ ${diffInMinutes} دقيقة`\n  } else if (diffInMinutes < 1440) {\n    const hours = Math.floor(diffInMinutes / 60)\n    return `منذ ${hours} ساعة`\n  } else {\n    const days = Math.floor(diffInMinutes / 1440)\n    return `منذ ${days} يوم`\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;AAEA;AACA;AAHA;;;;AA+CA,MAAM,oCAAsB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAuC;AAExE,SAAS,qBAAqB,EAAE,QAAQ,EAAiC;IAC9E,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IAErE,mCAAmC;IACnC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR;YACA,+BAA+B;YAC/B,MAAM,WAAW,YAAY;gBAC3B,0CAA0C;gBAC1C,IAAI,KAAK,MAAM,KAAK,KAAK;oBACvB;gBACF;YACF,GAAG;YAEH,OAAO,IAAM,cAAc;QAC7B;IACF,GAAG;QAAC;KAAK;IAET,MAAM,oBAAoB;QACxB,mCAAmC;QACnC,MAAM,oBAAoC;YACxC;gBACE,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,SAAS;gBACT,UAAU;gBACV,QAAQ;gBACR,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;gBAChE,WAAW;gBACX,YAAY;gBACZ,QAAQ,MAAM,MAAM;gBACpB,UAAU;oBAAE,SAAS;gBAAgB;YACvC;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,SAAS;gBACT,UAAU;gBACV,QAAQ;gBACR,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;gBAChE,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW;gBACrE,WAAW;gBACX,YAAY;gBACZ,QAAQ,MAAM,MAAM;gBACpB,UAAU;oBAAE,WAAW;gBAAS;YAClC;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,SAAS;gBACT,UAAU;gBACV,QAAQ;gBACR,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,MAAM,WAAW;gBACjE,WAAW;gBACX,YAAY;gBACZ,QAAQ,MAAM,MAAM;gBACpB,UAAU;oBAAE,SAAS;oBAAiB,gBAAgB;gBAAa;YACrE;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,SAAS;gBACT,UAAU;gBACV,QAAQ;gBACR,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW;gBACrE,WAAW;gBACX,YAAY;gBACZ,QAAQ,MAAM,MAAM;YACtB;SACD;QAED,iBAAiB;IACnB;IAEA,MAAM,sBAAsB;QAC1B,MAAM,YAAgC;YAAC;YAAU;YAAa;SAAW;QACzE,MAAM,aAAa,SAAS,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,UAAU,MAAM,EAAE;QAE1E,MAAM,eAAe;YACnB,QAAQ;gBACN,OAAO;gBACP,SAAS;YACX;YACA,WAAW;gBACT,OAAO;gBACP,SAAS;YACX;YACA,UAAU;gBACR,OAAO;gBACP,SAAS;YACX;QACF;QAEA,gBAAgB;YACd,MAAM;YACN,OAAO,YAAY,CAAC,WAAW,CAAC,KAAK;YACrC,SAAS,YAAY,CAAC,WAAW,CAAC,OAAO;YACzC,UAAU;QACZ;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,kBAAgC;YACpC,GAAG,gBAAgB;YACnB,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,WAAW,IAAI,OAAO,WAAW;YACjC,QAAQ,MAAM,MAAM;YACpB,QAAQ;QACV;QAEA,iBAAiB,CAAA,OAAQ;gBAAC;mBAAoB;aAAK;QAEnD,sCAAsC;QACtC,IAAI,aAAa,UAAU,KAAK,WAAW;YACzC,IAAI,aAAa,gBAAgB,KAAK,EAAE;gBACtC,MAAM,gBAAgB,OAAO;gBAC7B,MAAM;gBACN,KAAK,gBAAgB,EAAE;YACzB;QACF;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,eACP,aAAa,EAAE,KAAK,iBAChB;oBAAE,GAAG,YAAY;oBAAE,QAAQ;gBAAK,IAChC;IAGV;IAEA,MAAM,gBAAgB;QACpB,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,eAAgB,CAAC;oBAAE,GAAG,YAAY;oBAAE,QAAQ;gBAAK,CAAC;IAE/D;IAEA,MAAM,qBAAqB,CAAC;QAC1B,iBAAiB,CAAA,OACf,KAAK,MAAM,CAAC,CAAA,eAAgB,aAAa,EAAE,KAAK;IAEpD;IAEA,MAAM,WAAW;QACf,iBAAiB,EAAE;IACrB;IAEA,MAAM,yBAAyB,CAAC;QAC9B,OAAO,cAAc,MAAM,CAAC,CAAA,eAAgB,aAAa,IAAI,KAAK;IACpE;IAEA,MAAM,yBAAyB;QAC7B,OAAO,cAAc,MAAM,CAAC,CAAA,eAAgB,CAAC,aAAa,MAAM;IAClE;IAEA,MAAM,cAAc,yBAAyB,MAAM;IAEnD,gCAAgC;IAChC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,kBAAkB,UAAU,aAAa,UAAU,KAAK,WAAW;YACrE,aAAa,iBAAiB;QAChC;IACF,GAAG,EAAE;IAEL,MAAM,QAAiC;QACrC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,oBAAoB,QAAQ;QAAC,OAAO;kBAClC;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAGO,SAAS,oBAAoB,IAAsB;IACxD,OAAQ;QACN,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,SAAS,qBAAqB,QAA8B;IACjE,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,SAAS,uBAAuB,SAAiB;IACtD,MAAM,MAAM,IAAI;IAChB,MAAM,mBAAmB,IAAI,KAAK;IAClC,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,iBAAiB,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE;IAE1F,IAAI,gBAAgB,GAAG;QACrB,OAAO;IACT,OAAO,IAAI,gBAAgB,IAAI;QAC7B,OAAO,CAAC,IAAI,EAAE,cAAc,MAAM,CAAC;IACrC,OAAO,IAAI,gBAAgB,MAAM;QAC/B,MAAM,QAAQ,KAAK,KAAK,CAAC,gBAAgB;QACzC,OAAO,CAAC,IAAI,EAAE,MAAM,KAAK,CAAC;IAC5B,OAAO;QACL,MAAM,OAAO,KAAK,KAAK,CAAC,gBAAgB;QACxC,OAAO,CAAC,IAAI,EAAE,KAAK,IAAI,CAAC;IAC1B;AACF", "debugId": null}}, {"offset": {"line": 612, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/contexts/CartContext.tsx"], "sourcesContent": ["\"use client\"\n\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'\n\n// أنواع البيانات\nexport interface CartItem {\n  id: string\n  name: string\n  price: number\n  image: string\n  quantity: number\n  type: 'purchase' | 'rental'\n  rental_price?: number\n}\n\nexport interface WishlistItem {\n  id: string\n  name: string\n  price: number\n  image: string\n  rental_price?: number\n}\n\ninterface CartContextType {\n  // السلة\n  cartItems: CartItem[]\n  cartCount: number\n  addToCart: (productId: string, productData: any, type?: 'purchase' | 'rental') => void\n  removeFromCart: (productId: string) => void\n  updateQuantity: (productId: string, quantity: number) => void\n  clearCart: () => void\n  getCartTotal: () => number\n  \n  // المفضلة\n  wishlistItems: WishlistItem[]\n  wishlistCount: number\n  addToWishlist: (productId: string, productData: any) => void\n  removeFromWishlist: (productId: string) => void\n  isInWishlist: (productId: string) => boolean\n  clearWishlist: () => void\n}\n\nconst CartContext = createContext<CartContextType | undefined>(undefined)\n\nexport function CartProvider({ children }: { children: ReactNode }) {\n  const [cartItems, setCartItems] = useState<CartItem[]>([])\n  const [wishlistItems, setWishlistItems] = useState<WishlistItem[]>([])\n\n  // تحميل البيانات من localStorage عند بدء التشغيل\n  useEffect(() => {\n    try {\n      const savedCart = localStorage.getItem('cart')\n      if (savedCart) {\n        setCartItems(JSON.parse(savedCart))\n      }\n\n      const savedWishlist = localStorage.getItem('wishlist')\n      if (savedWishlist) {\n        setWishlistItems(JSON.parse(savedWishlist))\n      }\n    } catch (error) {\n      console.error('Error loading cart/wishlist from localStorage:', error)\n    }\n  }, [])\n\n  // حفظ السلة في localStorage عند التغيير\n  useEffect(() => {\n    try {\n      localStorage.setItem('cart', JSON.stringify(cartItems))\n      // تحديث localStorage القديم للتوافق مع Navigation\n      localStorage.setItem('cartItems', JSON.stringify(cartItems))\n    } catch (error) {\n      console.error('Error saving cart to localStorage:', error)\n    }\n  }, [cartItems])\n\n  // حفظ المفضلة في localStorage عند التغيير\n  useEffect(() => {\n    try {\n      localStorage.setItem('wishlist', JSON.stringify(wishlistItems))\n      // تحديث localStorage القديم للتوافق مع Navigation\n      localStorage.setItem('wishlistItems', JSON.stringify(wishlistItems))\n    } catch (error) {\n      console.error('Error saving wishlist to localStorage:', error)\n    }\n  }, [wishlistItems])\n\n  // دوال السلة\n  const addToCart = (productId: string, productData: any, type: 'purchase' | 'rental' = 'purchase') => {\n    setCartItems(prev => {\n      const existingItem = prev.find(item => item.id === productId && item.type === type)\n      \n      if (existingItem) {\n        // زيادة الكمية إذا كان المنتج موجود\n        return prev.map(item =>\n          item.id === productId && item.type === type\n            ? { ...item, quantity: item.quantity + 1 }\n            : item\n        )\n      } else {\n        // إضافة منتج جديد\n        const newItem: CartItem = {\n          id: productId,\n          name: productData.name,\n          price: type === 'rental' ? (productData.rental_price || productData.price) : productData.price,\n          image: productData.images?.[0] || productData.image || '/images/products/placeholder.jpg',\n          quantity: 1,\n          type,\n          rental_price: productData.rental_price\n        }\n        return [...prev, newItem]\n      }\n    })\n  }\n\n  const removeFromCart = (productId: string) => {\n    setCartItems(prev => prev.filter(item => item.id !== productId))\n  }\n\n  const updateQuantity = (productId: string, quantity: number) => {\n    if (quantity <= 0) {\n      removeFromCart(productId)\n      return\n    }\n\n    setCartItems(prev =>\n      prev.map(item =>\n        item.id === productId\n          ? { ...item, quantity }\n          : item\n      )\n    )\n  }\n\n  const clearCart = () => {\n    setCartItems([])\n  }\n\n  const getCartTotal = () => {\n    return cartItems.reduce((total, item) => total + (item.price * item.quantity), 0)\n  }\n\n  // دوال المفضلة\n  const addToWishlist = (productId: string, productData: any) => {\n    setWishlistItems(prev => {\n      const exists = prev.find(item => item.id === productId)\n      if (exists) {\n        return prev // المنتج موجود بالفعل\n      }\n\n      const newItem: WishlistItem = {\n        id: productId,\n        name: productData.name,\n        price: productData.price,\n        image: productData.images?.[0] || productData.image || '/images/products/placeholder.jpg',\n        rental_price: productData.rental_price\n      }\n      return [...prev, newItem]\n    })\n  }\n\n  const removeFromWishlist = (productId: string) => {\n    setWishlistItems(prev => prev.filter(item => item.id !== productId))\n  }\n\n  const isInWishlist = (productId: string) => {\n    return wishlistItems.some(item => item.id === productId)\n  }\n\n  const clearWishlist = () => {\n    setWishlistItems([])\n  }\n\n  const value: CartContextType = {\n    // السلة\n    cartItems,\n    cartCount: cartItems.reduce((total, item) => total + item.quantity, 0),\n    addToCart,\n    removeFromCart,\n    updateQuantity,\n    clearCart,\n    getCartTotal,\n    \n    // المفضلة\n    wishlistItems,\n    wishlistCount: wishlistItems.length,\n    addToWishlist,\n    removeFromWishlist,\n    isInWishlist,\n    clearWishlist\n  }\n\n  return (\n    <CartContext.Provider value={value}>\n      {children}\n    </CartContext.Provider>\n  )\n}\n\nexport function useCart() {\n  const context = useContext(CartContext)\n  if (context === undefined) {\n    throw new Error('useCart must be used within a CartProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AA0CA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAA2B;IAChE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IAErE,iDAAiD;IACjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI;YACF,MAAM,YAAY,aAAa,OAAO,CAAC;YACvC,IAAI,WAAW;gBACb,aAAa,KAAK,KAAK,CAAC;YAC1B;YAEA,MAAM,gBAAgB,aAAa,OAAO,CAAC;YAC3C,IAAI,eAAe;gBACjB,iBAAiB,KAAK,KAAK,CAAC;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kDAAkD;QAClE;IACF,GAAG,EAAE;IAEL,wCAAwC;IACxC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI;YACF,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;YAC5C,kDAAkD;YAClD,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;QACnD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;QACtD;IACF,GAAG;QAAC;KAAU;IAEd,0CAA0C;IAC1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI;YACF,aAAa,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC;YAChD,kDAAkD;YAClD,aAAa,OAAO,CAAC,iBAAiB,KAAK,SAAS,CAAC;QACvD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;QAC1D;IACF,GAAG;QAAC;KAAc;IAElB,aAAa;IACb,MAAM,YAAY,CAAC,WAAmB,aAAkB,OAA8B,UAAU;QAC9F,aAAa,CAAA;YACX,MAAM,eAAe,KAAK,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,aAAa,KAAK,IAAI,KAAK;YAE9E,IAAI,cAAc;gBAChB,oCAAoC;gBACpC,OAAO,KAAK,GAAG,CAAC,CAAA,OACd,KAAK,EAAE,KAAK,aAAa,KAAK,IAAI,KAAK,OACnC;wBAAE,GAAG,IAAI;wBAAE,UAAU,KAAK,QAAQ,GAAG;oBAAE,IACvC;YAER,OAAO;gBACL,kBAAkB;gBAClB,MAAM,UAAoB;oBACxB,IAAI;oBACJ,MAAM,YAAY,IAAI;oBACtB,OAAO,SAAS,WAAY,YAAY,YAAY,IAAI,YAAY,KAAK,GAAI,YAAY,KAAK;oBAC9F,OAAO,YAAY,MAAM,EAAE,CAAC,EAAE,IAAI,YAAY,KAAK,IAAI;oBACvD,UAAU;oBACV;oBACA,cAAc,YAAY,YAAY;gBACxC;gBACA,OAAO;uBAAI;oBAAM;iBAAQ;YAC3B;QACF;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,aAAa,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IACvD;IAEA,MAAM,iBAAiB,CAAC,WAAmB;QACzC,IAAI,YAAY,GAAG;YACjB,eAAe;YACf;QACF;QAEA,aAAa,CAAA,OACX,KAAK,GAAG,CAAC,CAAA,OACP,KAAK,EAAE,KAAK,YACR;oBAAE,GAAG,IAAI;oBAAE;gBAAS,IACpB;IAGV;IAEA,MAAM,YAAY;QAChB,aAAa,EAAE;IACjB;IAEA,MAAM,eAAe;QACnB,OAAO,UAAU,MAAM,CAAC,CAAC,OAAO,OAAS,QAAS,KAAK,KAAK,GAAG,KAAK,QAAQ,EAAG;IACjF;IAEA,eAAe;IACf,MAAM,gBAAgB,CAAC,WAAmB;QACxC,iBAAiB,CAAA;YACf,MAAM,SAAS,KAAK,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YAC7C,IAAI,QAAQ;gBACV,OAAO,KAAK,sBAAsB;;YACpC;YAEA,MAAM,UAAwB;gBAC5B,IAAI;gBACJ,MAAM,YAAY,IAAI;gBACtB,OAAO,YAAY,KAAK;gBACxB,OAAO,YAAY,MAAM,EAAE,CAAC,EAAE,IAAI,YAAY,KAAK,IAAI;gBACvD,cAAc,YAAY,YAAY;YACxC;YACA,OAAO;mBAAI;gBAAM;aAAQ;QAC3B;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,iBAAiB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IAC3D;IAEA,MAAM,eAAe,CAAC;QACpB,OAAO,cAAc,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IAChD;IAEA,MAAM,gBAAgB;QACpB,iBAAiB,EAAE;IACrB;IAEA,MAAM,QAAyB;QAC7B,QAAQ;QACR;QACA,WAAW,UAAU,MAAM,CAAC,CAAC,OAAO,OAAS,QAAQ,KAAK,QAAQ,EAAE;QACpE;QACA;QACA;QACA;QACA;QAEA,UAAU;QACV;QACA,eAAe,cAAc,MAAM;QACnC;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 780, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/contexts/MenuContext.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useState, useEffect, ReactNode, useCallback, useMemo } from 'react'\nimport { toast } from 'sonner'\n\n// تعريف نوع عنصر القائمة\nexport interface MenuItem {\n  id: string\n  title_ar: string\n  title_en?: string\n  title_fr?: string\n  slug: string\n  icon?: string\n  parent_id?: string\n  order_index: number\n  is_active: boolean\n  target_type: 'internal' | 'external' | 'page'\n  target_value: string\n  created_at: string\n  updated_at: string\n}\n\n// تعريف نوع السياق\ninterface MenuContextType {\n  menuItems: MenuItem[]\n  loading: boolean\n  error: string | null\n  fetchMenuItems: (includeInactive?: boolean) => Promise<void>\n  addMenuItem: (item: Omit<MenuItem, 'id' | 'created_at' | 'updated_at'>) => Promise<boolean>\n  updateMenuItem: (id: string, updates: Partial<MenuItem>) => Promise<boolean>\n  deleteMenuItem: (id: string) => Promise<boolean>\n  toggleItemStatus: (id: string) => Promise<boolean>\n  reorderMenuItems: (items: MenuItem[]) => Promise<boolean>\n  refreshMenu: () => void\n}\n\n// إنشاء السياق\nconst MenuContext = createContext<MenuContextType | undefined>(undefined)\n\n// مزود السياق\nexport function MenuProvider({ children }: { children: ReactNode }) {\n  const [menuItems, setMenuItems] = useState<MenuItem[]>([])\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState<string | null>(null)\n\n  // جلب عناصر القائمة - استخدام useCallback لتجنب re-renders\n  const fetchMenuItems = useCallback(async (includeInactive: boolean = false) => {\n    try {\n      setLoading(true)\n      setError(null)\n\n      const url = includeInactive ? '/api/menu-items?include_inactive=true' : '/api/menu-items'\n      const response = await fetch(url)\n      const data = await response.json()\n\n      if (response.ok) {\n        setMenuItems(data.menuItems || [])\n      } else {\n        setError(data.error || 'فشل في جلب عناصر القائمة')\n        console.error('Failed to fetch menu items:', data.error)\n      }\n    } catch (error) {\n      const errorMessage = 'خطأ في الاتصال بالخادم'\n      setError(errorMessage)\n      console.error('Error fetching menu items:', error)\n    } finally {\n      setLoading(false)\n    }\n  }, [])\n\n  // إضافة عنصر قائمة جديد - استخدام useCallback\n  const addMenuItem = useCallback(async (item: Omit<MenuItem, 'id' | 'created_at' | 'updated_at'>): Promise<boolean> => {\n    try {\n      const response = await fetch('/api/menu-items', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(item),\n      })\n\n      const data = await response.json()\n\n      if (response.ok) {\n        // تحديث القائمة محلياً\n        setMenuItems(prev => [...prev, data.menuItem])\n        toast.success(data.message)\n        return true\n      } else {\n        toast.error(data.error || 'فشل في إضافة عنصر القائمة')\n        return false\n      }\n    } catch (error) {\n      console.error('Error adding menu item:', error)\n      toast.error('خطأ في الاتصال بالخادم')\n      return false\n    }\n  }, [])\n\n  // تحديث عنصر قائمة - استخدام useCallback\n  const updateMenuItem = useCallback(async (id: string, updates: Partial<MenuItem>): Promise<boolean> => {\n    try {\n      const currentItem = menuItems.find(item => item.id === id)\n      if (!currentItem) {\n        toast.error('عنصر القائمة غير موجود')\n        return false\n      }\n\n      const updatedItem = { ...currentItem, ...updates }\n\n      const response = await fetch(`/api/menu-items/${id}`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(updatedItem),\n      })\n\n      const data = await response.json()\n\n      if (response.ok) {\n        // تحديث القائمة محلياً\n        setMenuItems(prev => prev.map(item =>\n          item.id === id ? { ...item, ...updates } : item\n        ))\n        toast.success(data.message)\n        return true\n      } else {\n        toast.error(data.error || 'فشل في تحديث عنصر القائمة')\n        return false\n      }\n    } catch (error) {\n      console.error('Error updating menu item:', error)\n      toast.error('خطأ في الاتصال بالخادم')\n      return false\n    }\n  }, [menuItems])\n\n  // حذف عنصر قائمة - استخدام useCallback\n  const deleteMenuItem = useCallback(async (id: string): Promise<boolean> => {\n    try {\n      const response = await fetch(`/api/menu-items/${id}`, {\n        method: 'DELETE',\n      })\n\n      const data = await response.json()\n\n      if (response.ok) {\n        // حذف العنصر محلياً\n        setMenuItems(prev => prev.filter(item => item.id !== id))\n        toast.success(data.message)\n        return true\n      } else {\n        toast.error(data.error || 'فشل في حذف عنصر القائمة')\n        return false\n      }\n    } catch (error) {\n      console.error('Error deleting menu item:', error)\n      toast.error('خطأ في الاتصال بالخادم')\n      return false\n    }\n  }, [])\n\n  // تبديل حالة التفعيل - استخدام useCallback\n  const toggleItemStatus = useCallback(async (id: string): Promise<boolean> => {\n    const item = menuItems.find(item => item.id === id)\n    if (!item) {\n      toast.error('عنصر القائمة غير موجود')\n      return false\n    }\n\n    return await updateMenuItem(id, { is_active: !item.is_active })\n  }, [menuItems, updateMenuItem])\n\n  // إعادة ترتيب عناصر القائمة - استخدام useCallback\n  const reorderMenuItems = useCallback(async (items: MenuItem[]): Promise<boolean> => {\n    try {\n      // تحديث الحالة المحلية أولاً للاستجابة السريعة\n      setMenuItems(items)\n\n      const itemsToUpdate = items\n        .filter(item => !item.parent_id) // العناصر الرئيسية فقط\n        .map((item, index) => ({\n          id: item.id,\n          order_index: index + 1\n        }))\n\n      const response = await fetch('/api/menu-items/reorder', {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ items: itemsToUpdate }),\n      })\n\n      const data = await response.json()\n\n      if (response.ok) {\n        toast.success(data.message)\n        return true\n      } else {\n        // في حالة الفشل، إعادة جلب البيانات من الخادم\n        await fetchMenuItems()\n        toast.error(data.error || 'فشل في إعادة ترتيب عناصر القائمة')\n        return false\n      }\n    } catch (error) {\n      // في حالة الخطأ، إعادة جلب البيانات من الخادم\n      await fetchMenuItems()\n      console.error('Error reordering menu items:', error)\n      toast.error('خطأ في الاتصال بالخادم')\n      return false\n    }\n  }, [fetchMenuItems])\n\n  // تحديث القائمة - استخدام useCallback\n  const refreshMenu = useCallback(() => {\n    fetchMenuItems()\n  }, [fetchMenuItems])\n\n  // جلب البيانات عند التحميل الأول\n  useEffect(() => {\n    fetchMenuItems()\n  }, [fetchMenuItems])\n\n  // استخدام useMemo لتجنب إعادة إنشاء القيمة في كل render\n  const value: MenuContextType = useMemo(() => ({\n    menuItems,\n    loading,\n    error,\n    fetchMenuItems,\n    addMenuItem,\n    updateMenuItem,\n    deleteMenuItem,\n    toggleItemStatus,\n    reorderMenuItems,\n    refreshMenu\n  }), [\n    menuItems,\n    loading,\n    error,\n    fetchMenuItems,\n    addMenuItem,\n    updateMenuItem,\n    deleteMenuItem,\n    toggleItemStatus,\n    reorderMenuItems,\n    refreshMenu\n  ])\n\n  return (\n    <MenuContext.Provider value={value}>\n      {children}\n    </MenuContext.Provider>\n  )\n}\n\n// Hook لاستخدام السياق\nexport function useMenu() {\n  const context = useContext(MenuContext)\n  if (context === undefined) {\n    throw new Error('useMenu must be used within a MenuProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAoCA,eAAe;AACf,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAGxD,SAAS,aAAa,EAAE,QAAQ,EAA2B;IAChE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,2DAA2D;IAC3D,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,kBAA2B,KAAK;QACxE,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,MAAM,kBAAkB,0CAA0C;YACxE,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,aAAa,KAAK,SAAS,IAAI,EAAE;YACnC,OAAO;gBACL,SAAS,KAAK,KAAK,IAAI;gBACvB,QAAQ,KAAK,CAAC,+BAA+B,KAAK,KAAK;YACzD;QACF,EAAE,OAAO,OAAO;YACd,MAAM,eAAe;YACrB,SAAS;YACT,QAAQ,KAAK,CAAC,8BAA8B;QAC9C,SAAU;YACR,WAAW;QACb;IACF,GAAG,EAAE;IAEL,8CAA8C;IAC9C,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACrC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,uBAAuB;gBACvB,aAAa,CAAA,OAAQ;2BAAI;wBAAM,KAAK,QAAQ;qBAAC;gBAC7C,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,KAAK,OAAO;gBAC1B,OAAO;YACT,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,KAAK,KAAK,IAAI;gBAC1B,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,OAAO;QACT;IACF,GAAG,EAAE;IAEL,yCAAyC;IACzC,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,IAAY;QACpD,IAAI;YACF,MAAM,cAAc,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YACvD,IAAI,CAAC,aAAa;gBAChB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,OAAO;YACT;YAEA,MAAM,cAAc;gBAAE,GAAG,WAAW;gBAAE,GAAG,OAAO;YAAC;YAEjD,MAAM,WAAW,MAAM,MAAM,CAAC,gBAAgB,EAAE,IAAI,EAAE;gBACpD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,uBAAuB;gBACvB,aAAa,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,OAC5B,KAAK,EAAE,KAAK,KAAK;4BAAE,GAAG,IAAI;4BAAE,GAAG,OAAO;wBAAC,IAAI;gBAE7C,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,KAAK,OAAO;gBAC1B,OAAO;YACT,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,KAAK,KAAK,IAAI;gBAC1B,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,OAAO;QACT;IACF,GAAG;QAAC;KAAU;IAEd,uCAAuC;IACvC,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACxC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,gBAAgB,EAAE,IAAI,EAAE;gBACpD,QAAQ;YACV;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,oBAAoB;gBACpB,aAAa,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;gBACrD,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,KAAK,OAAO;gBAC1B,OAAO;YACT,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,KAAK,KAAK,IAAI;gBAC1B,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,OAAO;QACT;IACF,GAAG,EAAE;IAEL,2CAA2C;IAC3C,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAC1C,MAAM,OAAO,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QAChD,IAAI,CAAC,MAAM;YACT,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,OAAO;QACT;QAEA,OAAO,MAAM,eAAe,IAAI;YAAE,WAAW,CAAC,KAAK,SAAS;QAAC;IAC/D,GAAG;QAAC;QAAW;KAAe;IAE9B,kDAAkD;IAClD,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAC1C,IAAI;YACF,+CAA+C;YAC/C,aAAa;YAEb,MAAM,gBAAgB,MACnB,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,SAAS,EAAE,uBAAuB;aACvD,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;oBACrB,IAAI,KAAK,EAAE;oBACX,aAAa,QAAQ;gBACvB,CAAC;YAEH,MAAM,WAAW,MAAM,MAAM,2BAA2B;gBACtD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE,OAAO;gBAAc;YAC9C;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,KAAK,OAAO;gBAC1B,OAAO;YACT,OAAO;gBACL,8CAA8C;gBAC9C,MAAM;gBACN,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,KAAK,KAAK,IAAI;gBAC1B,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,8CAA8C;YAC9C,MAAM;YACN,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,OAAO;QACT;IACF,GAAG;QAAC;KAAe;IAEnB,sCAAsC;IACtC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B;IACF,GAAG;QAAC;KAAe;IAEnB,iCAAiC;IACjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAe;IAEnB,wDAAwD;IACxD,MAAM,QAAyB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,CAAC;YAC5C;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACF,CAAC,GAAG;QACF;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;AAGO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1021, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 1037, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1134, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1191, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1217, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/scroll-area.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction ScrollArea({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof ScrollAreaPrimitive.Root>) {\n  return (\n    <ScrollAreaPrimitive.Root\n      data-slot=\"scroll-area\"\n      className={cn(\"relative\", className)}\n      {...props}\n    >\n      <ScrollAreaPrimitive.Viewport\n        data-slot=\"scroll-area-viewport\"\n        className=\"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1\"\n      >\n        {children}\n      </ScrollAreaPrimitive.Viewport>\n      <ScrollBar />\n      <ScrollAreaPrimitive.Corner />\n    </ScrollAreaPrimitive.Root>\n  )\n}\n\nfunction ScrollBar({\n  className,\n  orientation = \"vertical\",\n  ...props\n}: React.ComponentProps<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>) {\n  return (\n    <ScrollAreaPrimitive.ScrollAreaScrollbar\n      data-slot=\"scroll-area-scrollbar\"\n      orientation={orientation}\n      className={cn(\n        \"flex touch-none p-px transition-colors select-none\",\n        orientation === \"vertical\" &&\n          \"h-full w-2.5 border-l border-l-transparent\",\n        orientation === \"horizontal\" &&\n          \"h-2.5 flex-col border-t border-t-transparent\",\n        className\n      )}\n      {...props}\n    >\n      <ScrollAreaPrimitive.ScrollAreaThumb\n        data-slot=\"scroll-area-thumb\"\n        className=\"bg-border relative flex-1 rounded-full\"\n      />\n    </ScrollAreaPrimitive.ScrollAreaScrollbar>\n  )\n}\n\nexport { ScrollArea, ScrollBar }\n"], "names": [], "mappings": ";;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OACmD;IACtD,qBACE,8OAAC,0KAAA,CAAA,OAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;;0BAET,8OAAC,0KAAA,CAAA,WAA4B;gBAC3B,aAAU;gBACV,WAAU;0BAET;;;;;;0BAEH,8OAAC;;;;;0BACD,8OAAC,0KAAA,CAAA,SAA0B;;;;;;;;;;;AAGjC;AAEA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,UAAU,EACxB,GAAG,OACkE;IACrE,qBACE,8OAAC,0KAAA,CAAA,sBAAuC;QACtC,aAAU;QACV,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA,gBAAgB,cACd,8CACF,gBAAgB,gBACd,gDACF;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,0KAAA,CAAA,kBAAmC;YAClC,aAAU;YACV,WAAU;;;;;;;;;;;AAIlB", "debugId": null}}, {"offset": {"line": 1287, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/chat/LiveChat.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect, useRef } from 'react'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Badge } from '@/components/ui/badge'\nimport { ScrollArea } from '@/components/ui/scroll-area'\nimport { \n  MessageSquare,\n  Send,\n  Minimize2,\n  Maximize2,\n  X,\n  User,\n  Bot,\n  Clock,\n  CheckCircle,\n  Paperclip,\n  Smile\n} from 'lucide-react'\n\n// أنواع البيانات\ninterface ChatMessage {\n  id: string\n  content: string\n  isFromUser: boolean\n  timestamp: string\n  status?: 'sending' | 'sent' | 'delivered' | 'read'\n  attachments?: string[]\n}\n\ninterface ChatAgent {\n  id: string\n  name: string\n  avatar: string\n  status: 'online' | 'away' | 'offline'\n  isTyping: boolean\n}\n\nexport function LiveChat() {\n  const { user, profile } = useAuth()\n  const [isOpen, setIsOpen] = useState(false)\n  const [isMinimized, setIsMinimized] = useState(false)\n  const [messages, setMessages] = useState<ChatMessage[]>([])\n  const [newMessage, setNewMessage] = useState('')\n  const [isConnected, setIsConnected] = useState(false)\n  const [currentAgent, setCurrentAgent] = useState<ChatAgent | null>(null)\n  const [isTyping, setIsTyping] = useState(false)\n  const messagesEndRef = useRef<HTMLDivElement>(null)\n\n  // محاكاة الاتصال بالدردشة\n  useEffect(() => {\n    if (isOpen && !isConnected) {\n      // محاكاة الاتصال\n      setTimeout(() => {\n        setIsConnected(true)\n        setCurrentAgent({\n          id: 'agent-1',\n          name: 'سارة أحمد',\n          avatar: '/api/placeholder/40/40',\n          status: 'online',\n          isTyping: false\n        })\n        \n        // رسالة ترحيب\n        addMessage({\n          content: `مرحباً ${profile?.full_name || 'بك'}! أنا سارة من فريق الدعم الفني. كيف يمكنني مساعدتك اليوم؟`,\n          isFromUser: false\n        })\n      }, 1500)\n    }\n  }, [isOpen, isConnected, profile])\n\n  // التمرير التلقائي للأسفل\n  useEffect(() => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })\n  }, [messages])\n\n  const addMessage = (messageData: Omit<ChatMessage, 'id' | 'timestamp' | 'status'>) => {\n    const newMsg: ChatMessage = {\n      ...messageData,\n      id: Date.now().toString(),\n      timestamp: new Date().toISOString(),\n      status: messageData.isFromUser ? 'sending' : 'delivered'\n    }\n    \n    setMessages(prev => [...prev, newMsg])\n    \n    // محاكاة تحديث حالة الرسالة\n    if (messageData.isFromUser) {\n      setTimeout(() => {\n        setMessages(prev => \n          prev.map(msg => \n            msg.id === newMsg.id ? { ...msg, status: 'delivered' } : msg\n          )\n        )\n      }, 1000)\n    }\n  }\n\n  const handleSendMessage = () => {\n    if (!newMessage.trim()) return\n\n    addMessage({\n      content: newMessage,\n      isFromUser: true\n    })\n\n    setNewMessage('')\n    \n    // محاكاة رد الوكيل\n    setIsTyping(true)\n    setTimeout(() => {\n      setIsTyping(false)\n      \n      // ردود تلقائية بسيطة\n      const autoReplies = [\n        'شكراً لك على تواصلك معنا. سأقوم بالتحقق من هذا الأمر.',\n        'فهمت طلبك. دعني أساعدك في حل هذه المشكلة.',\n        'هذا سؤال ممتاز. سأحتاج لبعض التفاصيل الإضافية.',\n        'سأقوم بتحويل طلبك للقسم المختص وسنتواصل معك قريباً.',\n        'هل يمكنك تزويدي برقم الطلب لأتمكن من مساعدتك بشكل أفضل؟'\n      ]\n      \n      const randomReply = autoReplies[Math.floor(Math.random() * autoReplies.length)]\n      addMessage({\n        content: randomReply,\n        isFromUser: false\n      })\n    }, 2000)\n  }\n\n  const getStatusIcon = (status?: string) => {\n    switch (status) {\n      case 'sending': return <Clock className=\"h-3 w-3 text-gray-400\" />\n      case 'sent': return <CheckCircle className=\"h-3 w-3 text-gray-400\" />\n      case 'delivered': return <CheckCircle className=\"h-3 w-3 text-blue-500\" />\n      case 'read': return <CheckCircle className=\"h-3 w-3 text-green-500\" />\n      default: return null\n    }\n  }\n\n  if (!isOpen) {\n    return (\n      <Button\n        onClick={() => setIsOpen(true)}\n        className=\"fixed bottom-6 right-6 z-50 rounded-full w-14 h-14 shadow-lg\"\n        size=\"lg\"\n      >\n        <MessageSquare className=\"h-6 w-6\" />\n      </Button>\n    )\n  }\n\n  return (\n    <Card className={`fixed bottom-6 right-6 z-50 shadow-xl transition-all duration-300 ${\n      isMinimized ? 'w-80 h-16' : 'w-80 h-96'\n    }`}>\n      {/* Header */}\n      <CardHeader className=\"p-4 bg-blue-600 text-white rounded-t-lg\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center gap-3\">\n            <div className=\"relative\">\n              <div className=\"w-8 h-8 bg-white rounded-full flex items-center justify-center\">\n                <MessageSquare className=\"h-4 w-4 text-blue-600\" />\n              </div>\n              {isConnected && (\n                <div className=\"absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white\"></div>\n              )}\n            </div>\n            <div>\n              <h3 className=\"font-medium text-sm arabic-text\">\n                {isConnected ? 'الدردشة المباشرة' : 'جاري الاتصال...'}\n              </h3>\n              {currentAgent && (\n                <p className=\"text-xs opacity-90 arabic-text\">\n                  {currentAgent.name} - {currentAgent.status === 'online' ? 'متاح' : 'غير متاح'}\n                </p>\n              )}\n            </div>\n          </div>\n          \n          <div className=\"flex items-center gap-1\">\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={() => setIsMinimized(!isMinimized)}\n              className=\"h-8 w-8 p-0 text-white hover:bg-white/20\"\n            >\n              {isMinimized ? <Maximize2 className=\"h-4 w-4\" /> : <Minimize2 className=\"h-4 w-4\" />}\n            </Button>\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={() => setIsOpen(false)}\n              className=\"h-8 w-8 p-0 text-white hover:bg-white/20\"\n            >\n              <X className=\"h-4 w-4\" />\n            </Button>\n          </div>\n        </div>\n      </CardHeader>\n\n      {/* Chat Content */}\n      {!isMinimized && (\n        <CardContent className=\"p-0 flex flex-col h-80\">\n          {/* Messages */}\n          <ScrollArea className=\"flex-1 p-4\">\n            {!isConnected ? (\n              <div className=\"flex items-center justify-center h-full\">\n                <div className=\"text-center\">\n                  <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2\"></div>\n                  <p className=\"text-sm text-gray-500 arabic-text\">جاري الاتصال بفريق الدعم...</p>\n                </div>\n              </div>\n            ) : (\n              <div className=\"space-y-4\">\n                {messages.map((message) => (\n                  <div\n                    key={message.id}\n                    className={`flex ${message.isFromUser ? 'justify-end' : 'justify-start'}`}\n                  >\n                    <div className={`max-w-[80%] ${\n                      message.isFromUser \n                        ? 'bg-blue-600 text-white rounded-l-lg rounded-tr-lg' \n                        : 'bg-gray-100 dark:bg-gray-800 rounded-r-lg rounded-tl-lg'\n                    } p-3`}>\n                      <p className=\"text-sm arabic-text\">{message.content}</p>\n                      <div className=\"flex items-center justify-between mt-1\">\n                        <span className=\"text-xs opacity-70\">\n                          {new Date(message.timestamp).toLocaleTimeString('ar-SA', {\n                            hour: '2-digit',\n                            minute: '2-digit'\n                          })}\n                        </span>\n                        {message.isFromUser && getStatusIcon(message.status)}\n                      </div>\n                    </div>\n                  </div>\n                ))}\n                \n                {/* Typing Indicator */}\n                {isTyping && (\n                  <div className=\"flex justify-start\">\n                    <div className=\"bg-gray-100 dark:bg-gray-800 rounded-r-lg rounded-tl-lg p-3\">\n                      <div className=\"flex items-center gap-1\">\n                        <div className=\"flex gap-1\">\n                          <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"></div>\n                          <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\n                          <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\n                        </div>\n                        <span className=\"text-xs text-gray-500 mr-2 arabic-text\">يكتب...</span>\n                      </div>\n                    </div>\n                  </div>\n                )}\n                \n                <div ref={messagesEndRef} />\n              </div>\n            )}\n          </ScrollArea>\n\n          {/* Input */}\n          {isConnected && (\n            <div className=\"p-4 border-t\">\n              <div className=\"flex items-center gap-2\">\n                <Button variant=\"ghost\" size=\"sm\" className=\"h-8 w-8 p-0\">\n                  <Paperclip className=\"h-4 w-4\" />\n                </Button>\n                <Input\n                  value={newMessage}\n                  onChange={(e) => setNewMessage(e.target.value)}\n                  placeholder=\"اكتب رسالتك...\"\n                  className=\"flex-1 arabic-text\"\n                  onKeyPress={(e) => {\n                    if (e.key === 'Enter') {\n                      handleSendMessage()\n                    }\n                  }}\n                />\n                <Button variant=\"ghost\" size=\"sm\" className=\"h-8 w-8 p-0\">\n                  <Smile className=\"h-4 w-4\" />\n                </Button>\n                <Button \n                  onClick={handleSendMessage}\n                  disabled={!newMessage.trim()}\n                  size=\"sm\"\n                >\n                  <Send className=\"h-4 w-4\" />\n                </Button>\n              </div>\n              \n              <div className=\"flex items-center justify-between mt-2 text-xs text-gray-500\">\n                <span className=\"arabic-text\">اضغط Enter للإرسال</span>\n                {currentAgent && (\n                  <div className=\"flex items-center gap-1\">\n                    <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n                    <span className=\"arabic-text\">{currentAgent.name} متاح</span>\n                  </div>\n                )}\n              </div>\n            </div>\n          )}\n        </CardContent>\n      )}\n    </Card>\n  )\n}\n\n// مكون مبسط لأيقونة الدردشة\nexport function ChatButton() {\n  const [isOpen, setIsOpen] = useState(false)\n\n  return (\n    <>\n      <Button\n        onClick={() => setIsOpen(true)}\n        className=\"fixed bottom-6 left-6 z-40 rounded-full w-12 h-12 shadow-lg bg-green-600 hover:bg-green-700\"\n        size=\"sm\"\n      >\n        <MessageSquare className=\"h-5 w-5\" />\n      </Button>\n      \n      {isOpen && <LiveChat />}\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AATA;;;;;;;;;AAyCO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAC1D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IACnE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE9C,0BAA0B;IAC1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,CAAC,aAAa;YAC1B,iBAAiB;YACjB,WAAW;gBACT,eAAe;gBACf,gBAAgB;oBACd,IAAI;oBACJ,MAAM;oBACN,QAAQ;oBACR,QAAQ;oBACR,UAAU;gBACZ;gBAEA,cAAc;gBACd,WAAW;oBACT,SAAS,CAAC,OAAO,EAAE,SAAS,aAAa,KAAK,yDAAyD,CAAC;oBACxG,YAAY;gBACd;YACF,GAAG;QACL;IACF,GAAG;QAAC;QAAQ;QAAa;KAAQ;IAEjC,0BAA0B;IAC1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,eAAe,OAAO,EAAE,eAAe;YAAE,UAAU;QAAS;IAC9D,GAAG;QAAC;KAAS;IAEb,MAAM,aAAa,CAAC;QAClB,MAAM,SAAsB;YAC1B,GAAG,WAAW;YACd,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,WAAW,IAAI,OAAO,WAAW;YACjC,QAAQ,YAAY,UAAU,GAAG,YAAY;QAC/C;QAEA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAO;QAErC,4BAA4B;QAC5B,IAAI,YAAY,UAAU,EAAE;YAC1B,WAAW;gBACT,YAAY,CAAA,OACV,KAAK,GAAG,CAAC,CAAA,MACP,IAAI,EAAE,KAAK,OAAO,EAAE,GAAG;4BAAE,GAAG,GAAG;4BAAE,QAAQ;wBAAY,IAAI;YAG/D,GAAG;QACL;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,WAAW,IAAI,IAAI;QAExB,WAAW;YACT,SAAS;YACT,YAAY;QACd;QAEA,cAAc;QAEd,mBAAmB;QACnB,YAAY;QACZ,WAAW;YACT,YAAY;YAEZ,qBAAqB;YACrB,MAAM,cAAc;gBAClB;gBACA;gBACA;gBACA;gBACA;aACD;YAED,MAAM,cAAc,WAAW,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,YAAY,MAAM,EAAE;YAC/E,WAAW;gBACT,SAAS;gBACT,YAAY;YACd;QACF,GAAG;IACL;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAW,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YACxC,KAAK;gBAAQ,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAC3C,KAAK;gBAAa,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChD,KAAK;gBAAQ,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAC3C;gBAAS,OAAO;QAClB;IACF;IAEA,IAAI,CAAC,QAAQ;QACX,qBACE,8OAAC,kIAAA,CAAA,SAAM;YACL,SAAS,IAAM,UAAU;YACzB,WAAU;YACV,MAAK;sBAEL,cAAA,8OAAC,wNAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;;;;;;IAG/B;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAW,CAAC,kEAAkE,EAClF,cAAc,cAAc,aAC5B;;0BAEA,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;;;;;;wCAE1B,6BACC,8OAAC;4CAAI,WAAU;;;;;;;;;;;;8CAGnB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDACX,cAAc,qBAAqB;;;;;;wCAErC,8BACC,8OAAC;4CAAE,WAAU;;gDACV,aAAa,IAAI;gDAAC;gDAAI,aAAa,MAAM,KAAK,WAAW,SAAS;;;;;;;;;;;;;;;;;;;sCAM3E,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,eAAe,CAAC;oCAC/B,WAAU;8CAET,4BAAc,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;6DAAe,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;8CAE1E,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,UAAU;oCACzB,WAAU;8CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAOpB,CAAC,6BACA,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,8OAAC,0IAAA,CAAA,aAAU;wBAAC,WAAU;kCACnB,CAAC,4BACA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAE,WAAU;kDAAoC;;;;;;;;;;;;;;;;iDAIrD,8OAAC;4BAAI,WAAU;;gCACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;wCAEC,WAAW,CAAC,KAAK,EAAE,QAAQ,UAAU,GAAG,gBAAgB,iBAAiB;kDAEzE,cAAA,8OAAC;4CAAI,WAAW,CAAC,YAAY,EAC3B,QAAQ,UAAU,GACd,sDACA,0DACL,IAAI,CAAC;;8DACJ,8OAAC;oDAAE,WAAU;8DAAuB,QAAQ,OAAO;;;;;;8DACnD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEACb,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB,CAAC,SAAS;gEACvD,MAAM;gEACN,QAAQ;4DACV;;;;;;wDAED,QAAQ,UAAU,IAAI,cAAc,QAAQ,MAAM;;;;;;;;;;;;;uCAhBlD,QAAQ,EAAE;;;;;gCAuBlB,0BACC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;4DAAkD,OAAO;gEAAE,gBAAgB;4DAAO;;;;;;sEACjG,8OAAC;4DAAI,WAAU;4DAAkD,OAAO;gEAAE,gBAAgB;4DAAO;;;;;;;;;;;;8DAEnG,8OAAC;oDAAK,WAAU;8DAAyC;;;;;;;;;;;;;;;;;;;;;;8CAMjE,8OAAC;oCAAI,KAAK;;;;;;;;;;;;;;;;;oBAMf,6BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,MAAK;wCAAK,WAAU;kDAC1C,cAAA,8OAAC,4MAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;kDAEvB,8OAAC,iIAAA,CAAA,QAAK;wCACJ,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,aAAY;wCACZ,WAAU;wCACV,YAAY,CAAC;4CACX,IAAI,EAAE,GAAG,KAAK,SAAS;gDACrB;4CACF;wCACF;;;;;;kDAEF,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,MAAK;wCAAK,WAAU;kDAC1C,cAAA,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAEnB,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS;wCACT,UAAU,CAAC,WAAW,IAAI;wCAC1B,MAAK;kDAEL,cAAA,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAIpB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAc;;;;;;oCAC7B,8BACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAK,WAAU;;oDAAe,aAAa,IAAI;oDAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrE;AAGO,SAAS;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,qBACE;;0BACE,8OAAC,kIAAA,CAAA,SAAM;gBACL,SAAS,IAAM,UAAU;gBACzB,WAAU;gBACV,MAAK;0BAEL,cAAA,8OAAC,wNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;;;;;;YAG1B,wBAAU,8OAAC;;;;;;;AAGlB", "debugId": null}}]}