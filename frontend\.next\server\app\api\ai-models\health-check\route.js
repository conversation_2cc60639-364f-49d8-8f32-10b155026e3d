"use strict";(()=>{var e={};e.id=1239,e.ids=[1239],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},54261:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>v,routeModule:()=>g,serverHooks:()=>w,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>x});var a={};r.r(a),r.d(a,{GET:()=>h,POST:()=>u,PUT:()=>m});var s=r(96559),n=r(48088),o=r(37719),i=r(32190),l=r(38561);async function u(e){try{let{provider:t,baseUrl:r,apiKey:a,selectedModels:s}=await e.json();if(!t||!r)return i.NextResponse.json({error:"مقدم الخدمة و Base URL مطلوبان"},{status:400});let n=await d(t,r,a,s);return i.NextResponse.json(n)}catch(e){return console.error("Error performing health check:",e),i.NextResponse.json({error:"خطأ في فحص حالة API"},{status:500})}}async function h(e){try{let e=l.CN.getAIModels(),t=[];for(let r of e)if(r.isActive&&r.baseUrl){let a=await d(r.provider,r.baseUrl,r.apiKey,r.selectedModels||[]);t.push({modelId:r.id,provider:r.provider,name:r.name,...a});let s=e.findIndex(e=>e.id===r.id);-1!==s&&(e[s].status=a.status,e[s].lastCheckedAt=new Date().toISOString(),e[s].healthCheck=a)}return l.CN.saveAIModels(e),i.NextResponse.json({results:t,summary:{total:t.length,healthy:t.filter(e=>"active"===e.status).length,unhealthy:t.filter(e=>"error"===e.status).length,warning:t.filter(e=>"warning"===e.status).length}})}catch(e){return console.error("Error performing bulk health check:",e),i.NextResponse.json({error:"خطأ في فحص حالة النماذج"},{status:500})}}async function d(e,t,r,a=[]){let s=Date.now();try{let s=await p(e,t,r),n=await c(e,a,r);return{status:function(e,t){let r=e.filter(e=>"healthy"===e.status).length,a=e.length;if(0===a)return t>5e3?"warning":"active";let s=r/a*100;return s>=90?t>5e3?"warning":"active":s>=50?"warning":"error"}(n,s),responseTime:s,timestamp:new Date().toISOString(),baseUrl:t,hasApiKey:!!r,models:n,details:{endpoint:t,provider:e,modelsChecked:a.length,modelsHealthy:n.filter(e=>"healthy"===e.status).length,averageResponseTime:s}}}catch(a){return{status:"error",responseTime:Date.now()-s,timestamp:new Date().toISOString(),baseUrl:t,hasApiKey:!!r,error:a instanceof Error?a.message:"خطأ غير معروف",models:[],details:{endpoint:t,provider:e,modelsChecked:0,modelsHealthy:0,averageResponseTime:0}}}}async function p(e,t,r){let a=Date.now(),s={openai:{min:500,max:2e3},anthropic:{min:800,max:3e3},google:{min:400,max:1500},meta:{min:1e3,max:4e3},stability:{min:2e3,max:8e3},cohere:{min:600,max:2500},huggingface:{min:1500,max:5e3},deepseek:{min:700,max:2800}}[e]||{min:1e3,max:3e3},n=Math.floor(Math.random()*(s.max-s.min))+s.min;if(await new Promise(e=>setTimeout(e,Math.min(n,1e3))),.05>Math.random())throw Error(`فشل في الاتصال بـ ${e}`);if(!t.startsWith("http"))throw Error("Base URL غير صالح");if("openai"===e&&!r)throw Error("مفتاح API مطلوب لـ OpenAI");return Date.now()-a}async function c(e,t,r){let a=[];for(let e of t){let t=Math.random()>.1,r=Math.floor(1e3*Math.random())+200;a.push({name:e,status:t?"healthy":"error",responseTime:r,available:t,error:t?void 0:`النموذج ${e} غير متاح حالياً`})}return a}async function m(e){try{let{modelId:t,status:r,notes:a}=await e.json();if(!t||!r)return i.NextResponse.json({error:"معرف النموذج والحالة مطلوبان"},{status:400});let s=l.CN.getAIModels(),n=s.findIndex(e=>e.id===t);if(-1===n)return i.NextResponse.json({error:"النموذج غير موجود"},{status:404});s[n].status=r,s[n].lastCheckedAt=new Date().toISOString(),s[n].healthCheck={status:r,timestamp:new Date().toISOString(),manual:!0,notes:a},l.CN.saveAIModels(s);let o=l.CN.getModelActivities();return o.push({id:l.CN.generateId(),modelId:t,type:"health_check",description:`تم تحديث حالة النموذج يدوياً إلى: ${r}`,timestamp:new Date().toISOString(),success:!0,details:{notes:a,manual:!0}}),l.CN.saveModelActivities(o),i.NextResponse.json({message:"تم تحديث حالة النموذج بنجاح",model:s[n]})}catch(e){return console.error("Error updating model status:",e),i.NextResponse.json({error:"خطأ في تحديث حالة النموذج"},{status:500})}}let g=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/ai-models/health-check/route",pathname:"/api/ai-models/health-check",filename:"route",bundlePath:"app/api/ai-models/health-check/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\ai-models\\health-check\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:f,workUnitAsyncStorage:x,serverHooks:w}=g;function v(){return(0,o.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:x})}},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,580,8554],()=>r(54261));module.exports=a})();