/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/images/products/[...params]/route";
exports.ids = ["app/images/products/[...params]/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fimages%2Fproducts%2F%5B...params%5D%2Froute&page=%2Fimages%2Fproducts%2F%5B...params%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fimages%2Fproducts%2F%5B...params%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CGraduation%20Toqs%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CGraduation%20Toqs%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fimages%2Fproducts%2F%5B...params%5D%2Froute&page=%2Fimages%2Fproducts%2F%5B...params%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fimages%2Fproducts%2F%5B...params%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CGraduation%20Toqs%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CGraduation%20Toqs%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_faiss_Desktop_Graduation_Toqs_frontend_src_app_images_products_params_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/images/products/[...params]/route.ts */ \"(rsc)/./src/app/images/products/[...params]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/images/products/[...params]/route\",\n        pathname: \"/images/products/[...params]\",\n        filename: \"route\",\n        bundlePath: \"app/images/products/[...params]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Graduation Toqs\\\\frontend\\\\src\\\\app\\\\images\\\\products\\\\[...params]\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_faiss_Desktop_Graduation_Toqs_frontend_src_app_images_products_params_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fimages%2Fproducts%2F%5B...params%5D%2Froute&page=%2Fimages%2Fproducts%2F%5B...params%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fimages%2Fproducts%2F%5B...params%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CGraduation%20Toqs%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CGraduation%20Toqs%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/images/products/[...params]/route.ts":
/*!******************************************************!*\
  !*** ./src/app/images/products/[...params]/route.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n// قاموس الصور المتاحة مع أوصافها\nconst productImages = {\n    'gown-classic-1.jpg': {\n        name: 'ثوب التخرج الكلاسيكي',\n        category: 'gown',\n        color: 'أسود'\n    },\n    'gown-classic-2.jpg': {\n        name: 'ثوب التخرج الكلاسيكي',\n        category: 'gown',\n        color: 'أزرق داكن'\n    },\n    'cap-traditional-1.jpg': {\n        name: 'قبعة التخرج التقليدية',\n        category: 'cap',\n        color: 'أسود'\n    },\n    'cap-premium-1.jpg': {\n        name: 'قبعة التخرج المميزة',\n        category: 'cap',\n        color: 'ذهبي'\n    },\n    'stole-embroidered-1.jpg': {\n        name: 'وشاح التخرج المطرز',\n        category: 'stole',\n        color: 'أزرق'\n    },\n    'stole-silk-1.jpg': {\n        name: 'وشاح التخرج الحريري',\n        category: 'stole',\n        color: 'أحمر'\n    },\n    'tassel-gold-1.jpg': {\n        name: 'شرابة التخرج الذهبية',\n        category: 'tassel',\n        color: 'ذهبي'\n    },\n    'tassel-silver-1.jpg': {\n        name: 'شرابة التخرج الفضية',\n        category: 'tassel',\n        color: 'فضي'\n    },\n    'hood-doctoral-1.jpg': {\n        name: 'قلنسوة الدكتوراه',\n        category: 'hood',\n        color: 'أزرق'\n    },\n    'accessories-set-1.jpg': {\n        name: 'طقم إكسسوارات التخرج',\n        category: 'accessories',\n        color: 'متعدد'\n    }\n};\n// ألوان الفئات\nconst categoryColors = {\n    'gown': '#1a1a1a',\n    'cap': '#2c3e50',\n    'stole': '#8e44ad',\n    'tassel': '#f39c12',\n    'hood': '#27ae60',\n    'accessories': '#e74c3c' // أحمر\n};\n// GET - إنشاء صور منتجات ديناميكية\nasync function GET(request, { params }) {\n    try {\n        const filename = params.params.join('/');\n        const imageInfo = productImages[filename];\n        if (!imageInfo) {\n            // إنشاء صورة افتراضية للمنتجات غير المعروفة\n            return generateDefaultProductImage(filename);\n        }\n        const categoryColor = categoryColors[imageInfo.category] || '#666666';\n        // إنشاء SVG للمنتج\n        const svg = `\n      <svg width=\"400\" height=\"300\" xmlns=\"http://www.w3.org/2000/svg\">\n        <defs>\n          <linearGradient id=\"bgGrad\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n            <stop offset=\"0%\" style=\"stop-color:#f8f9fa;stop-opacity:1\" />\n            <stop offset=\"100%\" style=\"stop-color:#e9ecef;stop-opacity:1\" />\n          </linearGradient>\n          <linearGradient id=\"productGrad\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n            <stop offset=\"0%\" style=\"stop-color:${categoryColor};stop-opacity:0.8\" />\n            <stop offset=\"100%\" style=\"stop-color:${categoryColor};stop-opacity:0.6\" />\n          </linearGradient>\n        </defs>\n        \n        <!-- الخلفية -->\n        <rect width=\"100%\" height=\"100%\" fill=\"url(#bgGrad)\"/>\n        \n        <!-- إطار المنتج -->\n        <rect x=\"50\" y=\"40\" width=\"300\" height=\"220\" \n              fill=\"url(#productGrad)\" \n              stroke=\"${categoryColor}\" \n              stroke-width=\"2\" \n              rx=\"10\"/>\n        \n        <!-- أيقونة المنتج حسب الفئة -->\n        ${generateCategoryIcon(imageInfo.category, categoryColor)}\n        \n        <!-- اسم المنتج -->\n        <text x=\"200\" y=\"280\" \n              text-anchor=\"middle\" \n              font-family=\"Arial, sans-serif\" \n              font-size=\"14\" \n              font-weight=\"bold\"\n              fill=\"#333\">\n          ${imageInfo.name}\n        </text>\n        \n        <!-- اللون -->\n        <text x=\"200\" y=\"295\" \n              text-anchor=\"middle\" \n              font-family=\"Arial, sans-serif\" \n              font-size=\"12\" \n              fill=\"#666\">\n          ${imageInfo.color}\n        </text>\n        \n        <!-- شعار الجودة -->\n        <circle cx=\"350\" cy=\"70\" r=\"20\" fill=\"#28a745\" opacity=\"0.9\"/>\n        <text x=\"350\" y=\"75\" \n              text-anchor=\"middle\" \n              font-family=\"Arial, sans-serif\" \n              font-size=\"10\" \n              font-weight=\"bold\"\n              fill=\"white\">\n          HD\n        </text>\n      </svg>\n    `;\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(svg, {\n            headers: {\n                'Content-Type': 'image/svg+xml',\n                'Cache-Control': 'public, max-age=86400'\n            }\n        });\n    } catch (error) {\n        console.error('Error generating product image:', error);\n        return generateDefaultProductImage('unknown');\n    }\n}\nfunction generateCategoryIcon(category, color) {\n    const iconColor = '#ffffff';\n    switch(category){\n        case 'gown':\n            return `\n        <g transform=\"translate(180, 120)\">\n          <!-- ثوب التخرج -->\n          <path d=\"M10 20 L10 60 L40 60 L40 20 L35 15 L15 15 Z\" \n                fill=\"${iconColor}\" stroke=\"${color}\" stroke-width=\"1\"/>\n          <rect x=\"15\" y=\"15\" width=\"20\" height=\"8\" fill=\"${iconColor}\"/>\n          <line x1=\"20\" y1=\"25\" x2=\"20\" y2=\"55\" stroke=\"${color}\" stroke-width=\"1\"/>\n          <line x1=\"30\" y1=\"25\" x2=\"30\" y2=\"55\" stroke=\"${color}\" stroke-width=\"1\"/>\n        </g>\n      `;\n        case 'cap':\n            return `\n        <g transform=\"translate(180, 130)\">\n          <!-- قبعة التخرج -->\n          <ellipse cx=\"20\" cy=\"25\" rx=\"18\" ry=\"8\" fill=\"${iconColor}\" stroke=\"${color}\" stroke-width=\"1\"/>\n          <rect x=\"15\" y=\"15\" width=\"10\" height=\"15\" fill=\"${iconColor}\" stroke=\"${color}\" stroke-width=\"1\"/>\n          <rect x=\"10\" y=\"20\" width=\"20\" height=\"3\" fill=\"${iconColor}\"/>\n          <!-- الشرابة -->\n          <line x1=\"35\" y1=\"20\" x2=\"40\" y2=\"30\" stroke=\"${color}\" stroke-width=\"2\"/>\n          <circle cx=\"40\" cy=\"32\" r=\"2\" fill=\"${color}\"/>\n        </g>\n      `;\n        case 'stole':\n            return `\n        <g transform=\"translate(180, 120)\">\n          <!-- وشاح التخرج -->\n          <path d=\"M5 10 Q20 5 35 10 L35 50 Q20 55 5 50 Z\" \n                fill=\"${iconColor}\" stroke=\"${color}\" stroke-width=\"1\"/>\n          <path d=\"M8 15 Q20 12 32 15\" stroke=\"${color}\" stroke-width=\"1\" fill=\"none\"/>\n          <path d=\"M8 25 Q20 22 32 25\" stroke=\"${color}\" stroke-width=\"1\" fill=\"none\"/>\n          <path d=\"M8 35 Q20 32 32 35\" stroke=\"${color}\" stroke-width=\"1\" fill=\"none\"/>\n        </g>\n      `;\n        case 'tassel':\n            return `\n        <g transform=\"translate(190, 120)\">\n          <!-- شرابة التخرج -->\n          <rect x=\"8\" y=\"10\" width=\"4\" height=\"15\" fill=\"${iconColor}\" stroke=\"${color}\" stroke-width=\"1\"/>\n          <circle cx=\"10\" cy=\"8\" r=\"3\" fill=\"${iconColor}\" stroke=\"${color}\" stroke-width=\"1\"/>\n          <!-- الخيوط -->\n          <line x1=\"6\" y1=\"25\" x2=\"6\" y2=\"40\" stroke=\"${color}\" stroke-width=\"1\"/>\n          <line x1=\"8\" y1=\"25\" x2=\"8\" y2=\"42\" stroke=\"${color}\" stroke-width=\"1\"/>\n          <line x1=\"10\" y1=\"25\" x2=\"10\" y2=\"41\" stroke=\"${color}\" stroke-width=\"1\"/>\n          <line x1=\"12\" y1=\"25\" x2=\"12\" y2=\"43\" stroke=\"${color}\" stroke-width=\"1\"/>\n          <line x1=\"14\" y1=\"25\" x2=\"14\" y2=\"40\" stroke=\"${color}\" stroke-width=\"1\"/>\n        </g>\n      `;\n        case 'hood':\n            return `\n        <g transform=\"translate(180, 120)\">\n          <!-- قلنسوة -->\n          <path d=\"M10 20 Q25 10 40 20 L40 40 Q25 50 10 40 Z\" \n                fill=\"${iconColor}\" stroke=\"${color}\" stroke-width=\"1\"/>\n          <path d=\"M15 25 Q25 20 35 25\" stroke=\"${color}\" stroke-width=\"1\" fill=\"none\"/>\n          <circle cx=\"20\" cy=\"30\" r=\"2\" fill=\"${color}\"/>\n          <circle cx=\"30\" cy=\"30\" r=\"2\" fill=\"${color}\"/>\n        </g>\n      `;\n        default:\n            return `\n        <g transform=\"translate(180, 120)\">\n          <!-- أيقونة افتراضية -->\n          <rect x=\"10\" y=\"15\" width=\"20\" height=\"25\" \n                fill=\"${iconColor}\" stroke=\"${color}\" stroke-width=\"1\" rx=\"3\"/>\n          <circle cx=\"20\" cy=\"27\" r=\"5\" fill=\"${color}\"/>\n          <text x=\"20\" y=\"31\" text-anchor=\"middle\" font-size=\"8\" fill=\"white\">?</text>\n        </g>\n      `;\n    }\n}\nfunction generateDefaultProductImage(filename) {\n    const svg = `\n    <svg width=\"400\" height=\"300\" xmlns=\"http://www.w3.org/2000/svg\">\n      <rect width=\"100%\" height=\"100%\" fill=\"#f8f9fa\"/>\n      <rect x=\"20\" y=\"20\" width=\"360\" height=\"260\" \n            fill=\"none\" stroke=\"#dee2e6\" stroke-width=\"2\" stroke-dasharray=\"10,5\" rx=\"10\"/>\n      \n      <!-- أيقونة صورة مفقودة -->\n      <g transform=\"translate(170, 120)\">\n        <rect x=\"10\" y=\"15\" width=\"40\" height=\"30\" \n              fill=\"none\" stroke=\"#6c757d\" stroke-width=\"2\" rx=\"3\"/>\n        <circle cx=\"20\" cy=\"25\" r=\"3\" fill=\"#6c757d\"/>\n        <polygon points=\"15,35 25,28 35,32 45,22 45,40 15,40\" fill=\"#6c757d\"/>\n        <line x1=\"10\" y1=\"10\" x2=\"50\" y2=\"50\" stroke=\"#dc3545\" stroke-width=\"3\"/>\n      </g>\n      \n      <text x=\"200\" y=\"200\" \n            text-anchor=\"middle\" \n            font-family=\"Arial, sans-serif\" \n            font-size=\"14\" \n            fill=\"#6c757d\">\n        صورة غير متوفرة\n      </text>\n      \n      <text x=\"200\" y=\"220\" \n            text-anchor=\"middle\" \n            font-family=\"Arial, sans-serif\" \n            font-size=\"12\" \n            fill=\"#adb5bd\">\n        ${filename}\n      </text>\n    </svg>\n  `;\n    return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(svg, {\n        headers: {\n            'Content-Type': 'image/svg+xml',\n            'Cache-Control': 'public, max-age=3600'\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/images/products/[...params]/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fimages%2Fproducts%2F%5B...params%5D%2Froute&page=%2Fimages%2Fproducts%2F%5B...params%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fimages%2Fproducts%2F%5B...params%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CGraduation%20Toqs%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CGraduation%20Toqs%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();