(()=>{var e={};e.id=7818,e.ids=[7818],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6710:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{AD:()=>o,P:()=>T,Rn:()=>n,wU:()=>A});var E=r(64939),s=e([E]);let d=new(E=(s.then?(await s)():s)[0]).Pool({user:process.env.POSTGRES_USER||"postgres",host:process.env.POSTGRES_HOST||"localhost",database:process.env.POSTGRES_DB||"graduation_platform",password:process.env.POSTGRES_PASSWORD||"password",port:parseInt(process.env.POSTGRES_PORT||"5432"),max:20,idleTimeoutMillis:3e4,connectionTimeoutMillis:2e3,ssl:{rejectUnauthorized:!1}});async function i(){try{return await d.connect()}catch(e){throw console.error("خطأ في الاتصال بقاعدة البيانات:",e),Error("فشل في الاتصال بقاعدة البيانات")}}async function T(e,t){let r=await i();try{Date.now();let a=await r.query(e,t);return Date.now(),a}catch(e){throw console.error("خطأ في تنفيذ الاستعلام:",e),e}finally{r.release()}}async function n(e){let t=await i();try{await t.query("BEGIN");let r=await e(t);return await t.query("COMMIT"),r}catch(e){throw await t.query("ROLLBACK"),e}finally{t.release()}}async function o(){try{return(await T("SELECT NOW() as current_time")).rows.length>0}catch(e){return console.error("فشل في فحص حالة قاعدة البيانات:",e),!1}}async function A(){try{console.log("بدء تهيئة قاعدة البيانات..."),await T(`
      CREATE TABLE IF NOT EXISTS users (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        first_name VARCHAR(100) NOT NULL,
        last_name VARCHAR(100) NOT NULL,
        phone VARCHAR(20),
        role VARCHAR(20) DEFAULT 'customer' CHECK (role IN ('admin', 'customer', 'school', 'delivery')),
        is_active BOOLEAN DEFAULT true,
        email_verified BOOLEAN DEFAULT false,
        profile_image TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `),await T(`
      CREATE TABLE IF NOT EXISTS categories (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name_ar VARCHAR(100) NOT NULL,
        name_en VARCHAR(100),
        name_fr VARCHAR(100),
        slug VARCHAR(100) UNIQUE NOT NULL,
        description TEXT,
        icon VARCHAR(50),
        parent_id UUID REFERENCES categories(id) ON DELETE CASCADE,
        order_index INTEGER DEFAULT 0,
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `),await T(`
      CREATE TABLE IF NOT EXISTS products (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name VARCHAR(255) NOT NULL,
        description TEXT,
        category_id UUID REFERENCES categories(id) ON DELETE SET NULL,
        price DECIMAL(10,2) NOT NULL,
        rental_price DECIMAL(10,2),
        colors TEXT[] DEFAULT '{}',
        sizes TEXT[] DEFAULT '{}',
        images TEXT[] DEFAULT '{}',
        stock_quantity INTEGER DEFAULT 0,
        is_available BOOLEAN DEFAULT true,
        is_published BOOLEAN DEFAULT true,
        features TEXT[] DEFAULT '{}',
        specifications JSONB DEFAULT '{}',
        rating DECIMAL(3,2) DEFAULT 0,
        reviews_count INTEGER DEFAULT 0,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `),await T(`
      CREATE TABLE IF NOT EXISTS schools (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        admin_id UUID REFERENCES users(id) ON DELETE CASCADE,
        name VARCHAR(255) NOT NULL,
        name_en VARCHAR(255),
        name_fr VARCHAR(255),
        address TEXT,
        city VARCHAR(100),
        phone VARCHAR(20),
        email VARCHAR(255),
        website VARCHAR(255),
        logo_url TEXT,
        graduation_date DATE,
        student_count INTEGER DEFAULT 0,
        is_active BOOLEAN DEFAULT true,
        settings JSONB DEFAULT '{}',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `),await T(`
      CREATE TABLE IF NOT EXISTS orders (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID REFERENCES users(id) ON DELETE CASCADE,
        school_id UUID REFERENCES schools(id) ON DELETE SET NULL,
        order_number VARCHAR(50) UNIQUE NOT NULL,
        status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled')),
        total_amount DECIMAL(10,2) NOT NULL,
        shipping_amount DECIMAL(10,2) DEFAULT 0,
        tax_amount DECIMAL(10,2) DEFAULT 0,
        discount_amount DECIMAL(10,2) DEFAULT 0,
        payment_method VARCHAR(50),
        payment_status VARCHAR(20) DEFAULT 'pending' CHECK (payment_status IN ('pending', 'paid', 'failed', 'refunded')),
        shipping_address JSONB,
        billing_address JSONB,
        notes TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `),await T(`
      CREATE TABLE IF NOT EXISTS order_items (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
        product_id UUID REFERENCES products(id) ON DELETE CASCADE,
        quantity INTEGER NOT NULL,
        unit_price DECIMAL(10,2) NOT NULL,
        total_price DECIMAL(10,2) NOT NULL,
        type VARCHAR(20) DEFAULT 'purchase' CHECK (type IN ('purchase', 'rental')),
        size VARCHAR(50),
        color VARCHAR(50),
        customizations JSONB DEFAULT '{}',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `),await T(`
      CREATE TABLE IF NOT EXISTS reviews (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID REFERENCES users(id) ON DELETE CASCADE,
        product_id UUID REFERENCES products(id) ON DELETE CASCADE,
        order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
        rating INTEGER CHECK (rating >= 1 AND rating <= 5),
        comment TEXT,
        images TEXT[] DEFAULT '{}',
        is_verified BOOLEAN DEFAULT false,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(user_id, product_id, order_id)
      )
    `),await T(`
      CREATE TABLE IF NOT EXISTS menu_items (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        title_ar VARCHAR(100) NOT NULL,
        title_en VARCHAR(100),
        title_fr VARCHAR(100),
        slug VARCHAR(100) NOT NULL,
        icon VARCHAR(50),
        parent_id UUID REFERENCES menu_items(id) ON DELETE CASCADE,
        order_index INTEGER DEFAULT 0,
        is_active BOOLEAN DEFAULT true,
        target_type VARCHAR(20) DEFAULT 'internal' CHECK (target_type IN ('internal', 'external', 'page')),
        target_value VARCHAR(255),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `),await T("CREATE INDEX IF NOT EXISTS idx_products_category ON products(category_id)"),await T("CREATE INDEX IF NOT EXISTS idx_products_published ON products(is_published)"),await T("CREATE INDEX IF NOT EXISTS idx_products_available ON products(is_available)"),await T("CREATE INDEX IF NOT EXISTS idx_orders_user ON orders(user_id)"),await T("CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status)"),await T("CREATE INDEX IF NOT EXISTS idx_order_items_order ON order_items(order_id)"),await T("CREATE INDEX IF NOT EXISTS idx_reviews_product ON reviews(product_id)"),await T("CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)"),await T("CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)"),console.log("تم تهيئة قاعدة البيانات بنجاح!")}catch(e){throw console.error("خطأ في تهيئة قاعدة البيانات:",e),e}}a()}catch(e){a(e)}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},39230:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{patchFetch:()=>o,routeModule:()=>A,serverHooks:()=>N,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>u});var E=r(96559),s=r(48088),i=r(37719),T=r(50094),n=e([T]);T=(n.then?(await n)():n)[0];let A=new E.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/database/init/route",pathname:"/api/database/init",filename:"route",bundlePath:"app/api/database/init/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\database\\init\\route.ts",nextConfigOutput:"",userland:T}),{workAsyncStorage:d,workUnitAsyncStorage:u,serverHooks:N}=A;function o(){return(0,i.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:u})}a()}catch(e){a(e)}})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},50094:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{GET:()=>n,POST:()=>T});var E=r(32190),s=r(6710),i=e([s]);async function T(e){try{if(console.log("بدء تهيئة قاعدة البيانات..."),await (0,s.wU)(),await (0,s.AD)())return E.NextResponse.json({message:"تم تهيئة قاعدة البيانات بنجاح",status:"success",timestamp:new Date().toISOString()});return E.NextResponse.json({error:"فشل في التحقق من حالة قاعدة البيانات بعد التهيئة",status:"error"},{status:500})}catch(e){return console.error("Error initializing database:",e),E.NextResponse.json({error:"فشل في تهيئة قاعدة البيانات",details:e instanceof Error?e.message:"خطأ غير معروف",status:"error"},{status:500})}}async function n(e){try{if(await (0,s.AD)())return E.NextResponse.json({status:"healthy",message:"قاعدة البيانات تعمل بشكل طبيعي",timestamp:new Date().toISOString()});return E.NextResponse.json({status:"unhealthy",message:"قاعدة البيانات غير متاحة",timestamp:new Date().toISOString()},{status:503})}catch(e){return console.error("Error checking database health:",e),E.NextResponse.json({status:"error",message:"فشل في فحص حالة قاعدة البيانات",details:e instanceof Error?e.message:"خطأ غير معروف",timestamp:new Date().toISOString()},{status:500})}}s=(i.then?(await i)():i)[0],a()}catch(e){a(e)}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:e=>{"use strict";e.exports=import("pg")},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,580],()=>r(39230));module.exports=a})();