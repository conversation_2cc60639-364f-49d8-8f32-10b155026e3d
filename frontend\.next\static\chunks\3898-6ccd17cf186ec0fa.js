"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3898],{5323:(e,t,r)=>{r.d(t,{CartProvider:()=>n,_:()=>s});var o=r(95155),a=r(12115);let i=(0,a.createContext)(void 0);function n(e){let{children:t}=e,[r,n]=(0,a.useState)([]),[s,l]=(0,a.useState)([]);(0,a.useEffect)(()=>{try{let e=localStorage.getItem("cart");e&&n(JSON.parse(e));let t=localStorage.getItem("wishlist");t&&l(JSON.parse(t))}catch(e){console.error("Error loading cart/wishlist from localStorage:",e)}},[]),(0,a.useEffect)(()=>{try{localStorage.setItem("cart",JSON.stringify(r)),localStorage.setItem("cartItems",JSON.stringify(r))}catch(e){console.error("Error saving cart to localStorage:",e)}},[r]),(0,a.useEffect)(()=>{try{localStorage.setItem("wishlist",JSON.stringify(s)),localStorage.setItem("wishlistItems",JSON.stringify(s))}catch(e){console.error("Error saving wishlist to localStorage:",e)}},[s]);let c=e=>{n(t=>t.filter(t=>t.id!==e))},d={cartItems:r,cartCount:r.reduce((e,t)=>e+t.quantity,0),addToCart:function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"purchase";n(o=>{var a;return o.find(t=>t.id===e&&t.type===r)?o.map(t=>t.id===e&&t.type===r?{...t,quantity:t.quantity+1}:t):[...o,{id:e,name:t.name,price:"rental"===r&&t.rental_price||t.price,image:(null==(a=t.images)?void 0:a[0])||t.image||"/images/products/placeholder.jpg",quantity:1,type:r,rental_price:t.rental_price}]})},removeFromCart:c,updateQuantity:(e,t)=>{if(t<=0)return void c(e);n(r=>r.map(r=>r.id===e?{...r,quantity:t}:r))},clearCart:()=>{n([])},getCartTotal:()=>r.reduce((e,t)=>e+t.price*t.quantity,0),wishlistItems:s,wishlistCount:s.length,addToWishlist:(e,t)=>{l(r=>{var o;return r.find(t=>t.id===e)?r:[...r,{id:e,name:t.name,price:t.price,image:(null==(o=t.images)?void 0:o[0])||t.image||"/images/products/placeholder.jpg",rental_price:t.rental_price}]})},removeFromWishlist:e=>{l(t=>t.filter(t=>t.id!==e))},isInWishlist:e=>s.some(t=>t.id===e),clearWishlist:()=>{l([])}};return(0,o.jsx)(i.Provider,{value:d,children:t})}function s(){let e=(0,a.useContext)(i);if(void 0===e)throw Error("useCart must be used within a CartProvider");return e}},40283:(e,t,r)=>{r.d(t,{A:()=>l,AuthProvider:()=>s});var o=r(95155),a=r(12115),i=r(59385);let n=(0,a.createContext)(void 0);function s(e){let{children:t}=e,[r,s]=(0,a.useState)(null),[l,c]=(0,a.useState)(null),[d,u]=(0,a.useState)(!0),[m,g]=(0,a.useState)(!1);(0,a.useEffect)(()=>{g(!0)},[]),(0,a.useEffect)(()=>{m&&(async()=>{try{let e=localStorage.getItem("mockUser"),t=localStorage.getItem("mockProfile");if(e&&t){let r=JSON.parse(e),o=JSON.parse(t);if(r&&o&&r.id&&o.id){let e=localStorage.getItem("sessionTimestamp"),t=Date.now(),a=e?t-parseInt(e):0;e&&a<864e5?(s(r),c(o),console.log("User data loaded from localStorage:",{userData:r,profileData:o})):(console.log("Session expired, clearing user data"),localStorage.removeItem("mockUser"),localStorage.removeItem("mockProfile"),localStorage.removeItem("sessionTimestamp"))}else localStorage.removeItem("mockUser"),localStorage.removeItem("mockProfile"),localStorage.removeItem("sessionTimestamp")}}catch(e){console.error("Error loading user from localStorage:",e),localStorage.removeItem("mockUser"),localStorage.removeItem("mockProfile")}finally{u(!1)}})()},[m]),(0,a.useEffect)(()=>{if(!r||!l)return;let e=()=>{try{localStorage.setItem("sessionTimestamp",Date.now().toString())}catch(e){console.error("Error refreshing session:",e)}},t=["click","keypress","scroll","mousemove"];return t.forEach(t=>{document.addEventListener(t,e,{passive:!0})}),()=>{t.forEach(t=>{document.removeEventListener(t,e)})}},[r,l]);let f=async(e,t,r)=>(console.log("Sign up:",e,r),{data:{user:{id:"1",email:e}},error:null}),h=async(e,t)=>{console.log("Sign in:",e);let r={id:"1",email:e},o=i.gG.STUDENT;e.includes("admin")?o=i.gG.ADMIN:e.includes("school")?o=i.gG.SCHOOL:e.includes("delivery")&&(o=i.gG.DELIVERY);let a={id:"1",email:e,full_name:e.split("@")[0]||"مستخدم",role:o,created_at:new Date().toISOString(),updated_at:new Date().toISOString()};s(r),c(a);try{localStorage.setItem("mockUser",JSON.stringify(r)),localStorage.setItem("mockProfile",JSON.stringify(a)),localStorage.setItem("sessionTimestamp",Date.now().toString()),console.log("User data saved to localStorage:",{mockUser:r,mockProfile:a})}catch(e){console.error("Error saving user data to localStorage:",e)}return setTimeout(()=>{o===i.gG.ADMIN?window.location.href="/dashboard/admin":o===i.gG.SCHOOL?window.location.href="/dashboard/school":o===i.gG.DELIVERY?window.location.href="/dashboard/delivery":window.location.href="/dashboard/student"},100),{data:{user:r},error:null}},p=async()=>{try{return s(null),c(null),localStorage.removeItem("mockUser"),localStorage.removeItem("mockProfile"),localStorage.removeItem("sessionTimestamp"),console.log("User data cleared from localStorage"),{error:null}}catch(e){return console.error("Error during sign out:",e),{error:"فشل في تسجيل الخروج"}}},v=async e=>{if(!r)return{data:null,error:"No user logged in"};let t={...l,...e};return c(t),{data:t,error:null}};return(0,o.jsx)(n.Provider,{value:{user:r,profile:l,loading:d,signUp:f,signIn:h,signOut:p,updateProfile:v,hasRole:e=>!!l&&(0,i.ly)(l.role,e)},children:t})}function l(){let e=(0,a.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},59385:(e,t,r)=>{r.d(t,{gG:()=>o,ly:()=>a});var o=function(e){return e.STUDENT="student",e.SCHOOL="school",e.ADMIN="admin",e.DELIVERY="delivery",e}({});function a(e,t){let r={admin:4,school:3,delivery:2,student:1};return r[e]>=r[t]}},66424:(e,t,r)=>{r.d(t,{F:()=>n});var o=r(95155);r(12115);var a=r(47655),i=r(59434);function n(e){let{className:t,children:r,...n}=e;return(0,o.jsxs)(a.bL,{"data-slot":"scroll-area",className:(0,i.cn)("relative",t),...n,children:[(0,o.jsx)(a.LM,{"data-slot":"scroll-area-viewport",className:"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1",children:r}),(0,o.jsx)(s,{}),(0,o.jsx)(a.OK,{})]})}function s(e){let{className:t,orientation:r="vertical",...n}=e;return(0,o.jsx)(a.VM,{"data-slot":"scroll-area-scrollbar",orientation:r,className:(0,i.cn)("flex touch-none p-px transition-colors select-none","vertical"===r&&"h-full w-2.5 border-l border-l-transparent","horizontal"===r&&"h-2.5 flex-col border-t border-t-transparent",t),...n,children:(0,o.jsx)(a.lr,{"data-slot":"scroll-area-thumb",className:"bg-border relative flex-1 rounded-full"})})}},71978:(e,t,r)=>{r.d(t,{MenuProvider:()=>s,b:()=>l});var o=r(95155),a=r(12115),i=r(56671);let n=(0,a.createContext)(void 0);function s(e){let{children:t}=e,[r,s]=(0,a.useState)([]),[l,c]=(0,a.useState)(!0),[d,u]=(0,a.useState)(null),m=(0,a.useCallback)(async function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];try{c(!0),u(null);let t=await fetch(e?"/api/menu-items?include_inactive=true":"/api/menu-items"),r=await t.json();t.ok?s(r.menuItems||[]):(u(r.error||"فشل في جلب عناصر القائمة"),console.error("Failed to fetch menu items:",r.error))}catch(e){u("خطأ في الاتصال بالخادم"),console.error("Error fetching menu items:",e)}finally{c(!1)}},[]),g=(0,a.useCallback)(async e=>{try{let t=await fetch("/api/menu-items",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),r=await t.json();if(t.ok)return s(e=>[...e,r.menuItem]),i.o.success(r.message),!0;return i.o.error(r.error||"فشل في إضافة عنصر القائمة"),!1}catch(e){return console.error("Error adding menu item:",e),i.o.error("خطأ في الاتصال بالخادم"),!1}},[]),f=(0,a.useCallback)(async(e,t)=>{try{let o=r.find(t=>t.id===e);if(!o)return i.o.error("عنصر القائمة غير موجود"),!1;let a={...o,...t},n=await fetch("/api/menu-items/".concat(e),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)}),l=await n.json();if(n.ok)return s(r=>r.map(r=>r.id===e?{...r,...t}:r)),i.o.success(l.message),!0;return i.o.error(l.error||"فشل في تحديث عنصر القائمة"),!1}catch(e){return console.error("Error updating menu item:",e),i.o.error("خطأ في الاتصال بالخادم"),!1}},[r]),h=(0,a.useCallback)(async e=>{try{let t=await fetch("/api/menu-items/".concat(e),{method:"DELETE"}),r=await t.json();if(t.ok)return s(t=>t.filter(t=>t.id!==e)),i.o.success(r.message),!0;return i.o.error(r.error||"فشل في حذف عنصر القائمة"),!1}catch(e){return console.error("Error deleting menu item:",e),i.o.error("خطأ في الاتصال بالخادم"),!1}},[]),p=(0,a.useCallback)(async e=>{let t=r.find(t=>t.id===e);return t?await f(e,{is_active:!t.is_active}):(i.o.error("عنصر القائمة غير موجود"),!1)},[r,f]),v=(0,a.useCallback)(async e=>{try{s(e);let t=e.filter(e=>!e.parent_id).map((e,t)=>({id:e.id,order_index:t+1})),r=await fetch("/api/menu-items/reorder",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({items:t})}),o=await r.json();if(r.ok)return i.o.success(o.message),!0;return await m(),i.o.error(o.error||"فشل في إعادة ترتيب عناصر القائمة"),!1}catch(e){return await m(),console.error("Error reordering menu items:",e),i.o.error("خطأ في الاتصال بالخادم"),!1}},[m]),S=(0,a.useCallback)(()=>{m()},[m]);(0,a.useEffect)(()=>{m()},[m]);let y=(0,a.useMemo)(()=>({menuItems:r,loading:l,error:d,fetchMenuItems:m,addMenuItem:g,updateMenuItem:f,deleteMenuItem:h,toggleItemStatus:p,reorderMenuItems:v,refreshMenu:S}),[r,l,d,m,g,f,h,p,v,S]);return(0,o.jsx)(n.Provider,{value:y,children:t})}function l(){let e=(0,a.useContext)(n);if(void 0===e)throw Error("useMenu must be used within a MenuProvider");return e}},77470:(e,t,r)=>{r.d(t,{By:()=>c,E$:()=>u,NotificationProvider:()=>s,bQ:()=>d,lO:()=>l});var o=r(95155),a=r(12115),i=r(40283);let n=(0,a.createContext)(void 0);function s(e){let{children:t}=e,{user:r}=(0,i.A)(),[s,l]=(0,a.useState)([]);(0,a.useEffect)(()=>{if(r){c();let e=setInterval(()=>{Math.random()>.8&&d()},3e4);return()=>clearInterval(e)}},[r]);let c=()=>{l([{id:"1",type:"order_confirmed",title:"تم تأكيد طلبك",message:"تم تأكيد طلبك #GT-240120-001 بنجاح وسيتم تحضيره قريباً",priority:"high",isRead:!1,createdAt:new Date(Date.now()-72e5).toISOString(),actionUrl:"/track-order",actionText:"تتبع الطلب",userId:(null==r?void 0:r.id)||"",metadata:{orderId:"GT-240120-001"}},{id:"2",type:"promotion",title:"عرض خاص - خصم 20%",message:"احصل على خصم 20% على جميع أزياء التخرج لفترة محدودة",priority:"medium",isRead:!1,createdAt:new Date(Date.now()-144e5).toISOString(),expiresAt:new Date(Date.now()+6048e5).toISOString(),actionUrl:"/catalog",actionText:"تسوق الآن",userId:(null==r?void 0:r.id)||"",metadata:{promoCode:"GRAD20"}},{id:"3",type:"order_shipped",title:"تم شحن طلبك",message:"طلبك #GT-240115-002 في طريقه إليك. رقم التتبع: TRK-123456",priority:"high",isRead:!0,createdAt:new Date(Date.now()-864e5).toISOString(),actionUrl:"/track-order",actionText:"تتبع الشحنة",userId:(null==r?void 0:r.id)||"",metadata:{orderId:"GT-240115-002",trackingNumber:"TRK-123456"}},{id:"4",type:"review_request",title:"قيم تجربتك معنا",message:"نود معرفة رأيك في المنتجات التي استلمتها مؤخراً",priority:"low",isRead:!1,createdAt:new Date(Date.now()-2592e5).toISOString(),actionUrl:"/reviews",actionText:"اكتب تقييم",userId:(null==r?void 0:r.id)||""}])},d=()=>{let e=["system","promotion","reminder"],t=e[Math.floor(Math.random()*e.length)],r={system:{title:"تحديث النظام",message:"تم تحديث النظام بميزات جديدة"},promotion:{title:"عرض محدود",message:"خصم خاص على المنتجات المختارة"},reminder:{title:"تذكير",message:"لا تنس إكمال طلبك في سلة التسوق"}};u({type:t,title:r[t].title,message:r[t].message,priority:"medium"})},u=e=>{let t={...e,id:Date.now().toString(),createdAt:new Date().toISOString(),userId:(null==r?void 0:r.id)||"",isRead:!1};l(e=>[t,...e]),"granted"===Notification.permission&&new Notification(t.title,{body:t.message,icon:"/favicon.ico",tag:t.id})},m=()=>s.filter(e=>!e.isRead),g=m().length;return(0,a.useEffect)(()=>{"Notification"in window&&"default"===Notification.permission&&Notification.requestPermission()},[]),(0,o.jsx)(n.Provider,{value:{notifications:s,unreadCount:g,addNotification:u,markAsRead:e=>{l(t=>t.map(t=>t.id===e?{...t,isRead:!0}:t))},markAllAsRead:()=>{l(e=>e.map(e=>({...e,isRead:!0})))},removeNotification:e=>{l(t=>t.filter(t=>t.id!==e))},clearAll:()=>{l([])},getNotificationsByType:e=>s.filter(t=>t.type===e),getUnreadNotifications:m},children:t})}function l(){let e=(0,a.useContext)(n);if(void 0===e)throw Error("useNotifications must be used within a NotificationProvider");return e}function c(e){switch(e){case"order_confirmed":case"order_shipped":case"order_delivered":return"\uD83D\uDCE6";case"payment_received":return"\uD83D\uDCB3";case"payment_failed":return"❌";case"promotion":return"\uD83C\uDF89";case"reminder":return"⏰";case"system":return"⚙️";case"message":return"\uD83D\uDCAC";case"review_request":return"⭐";default:return"\uD83D\uDD14"}}function d(e){switch(e){case"urgent":return"text-red-600 bg-red-50 border-red-200";case"high":return"text-orange-600 bg-orange-50 border-orange-200";case"medium":return"text-blue-600 bg-blue-50 border-blue-200";default:return"text-gray-600 bg-gray-50 border-gray-200"}}function u(e){let t=new Date,r=new Date(e),o=Math.floor((t.getTime()-r.getTime())/6e4);if(o<1)return"الآن";if(o<60)return"منذ ".concat(o," دقيقة");if(o<1440){let e=Math.floor(o/60);return"منذ ".concat(e," ساعة")}{let e=Math.floor(o/1440);return"منذ ".concat(e," يوم")}}}}]);