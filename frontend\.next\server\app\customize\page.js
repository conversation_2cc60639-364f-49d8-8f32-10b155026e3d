(()=>{var e={};e.id=462,e.ids=[462],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8819:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11442:(e,a,s)=>{Promise.resolve().then(s.bind(s,93502))},13943:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("rotate-ccw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]])},15079:(e,a,s)=>{"use strict";s.d(a,{bq:()=>u,eb:()=>m,gC:()=>x,l6:()=>c,yv:()=>d});var t=s(60687);s(43210);var r=s(22670),l=s(78272),n=s(13964),i=s(3589),o=s(4780);function c({...e}){return(0,t.jsx)(r.bL,{"data-slot":"select",...e})}function d({...e}){return(0,t.jsx)(r.WT,{"data-slot":"select-value",...e})}function u({className:e,size:a="default",children:s,...n}){return(0,t.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":a,className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...n,children:[s,(0,t.jsx)(r.In,{asChild:!0,children:(0,t.jsx)(l.A,{className:"size-4 opacity-50"})})]})}function x({className:e,children:a,position:s="popper",...l}){return(0,t.jsx)(r.ZL,{children:(0,t.jsxs)(r.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...l,children:[(0,t.jsx)(h,{}),(0,t.jsx)(r.LM,{className:(0,o.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),(0,t.jsx)(p,{})]})})}function m({className:e,children:a,...s}){return(0,t.jsxs)(r.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...s,children:[(0,t.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,t.jsx)(r.VF,{children:(0,t.jsx)(n.A,{className:"size-4"})})}),(0,t.jsx)(r.p4,{children:a})]})}function h({className:e,...a}){return(0,t.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...a,children:(0,t.jsx)(i.A,{className:"size-4"})})}function p({className:e,...a}){return(0,t.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...a,children:(0,t.jsx)(l.A,{className:"size-4"})})}},16103:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("qr-code",[["rect",{width:"5",height:"5",x:"3",y:"3",rx:"1",key:"1tu5fj"}],["rect",{width:"5",height:"5",x:"16",y:"3",rx:"1",key:"1v8r4q"}],["rect",{width:"5",height:"5",x:"3",y:"16",rx:"1",key:"1x03jg"}],["path",{d:"M21 16h-3a2 2 0 0 0-2 2v3",key:"177gqh"}],["path",{d:"M21 21v.01",key:"ents32"}],["path",{d:"M12 7v3a2 2 0 0 1-2 2H7",key:"8crl2c"}],["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M12 3h.01",key:"n36tog"}],["path",{d:"M12 16v.01",key:"133mhm"}],["path",{d:"M16 12h1",key:"1slzba"}],["path",{d:"M21 12v.01",key:"1lwtk9"}],["path",{d:"M12 21v-1",key:"1880an"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26134:(e,a,s)=>{"use strict";s.d(a,{G$:()=>X,Hs:()=>y,UC:()=>es,VY:()=>er,ZL:()=>ee,bL:()=>Y,bm:()=>el,hE:()=>et,hJ:()=>ea,l9:()=>Q});var t=s(43210),r=s(70569),l=s(98599),n=s(11273),i=s(96963),o=s(65551),c=s(31355),d=s(32547),u=s(25028),x=s(46059),m=s(14163),h=s(1359),p=s(42247),g=s(63376),b=s(8730),f=s(60687),v="Dialog",[j,y]=(0,n.A)(v),[N,w]=j(v),k=e=>{let{__scopeDialog:a,children:s,open:r,defaultOpen:l,onOpenChange:n,modal:c=!0}=e,d=t.useRef(null),u=t.useRef(null),[x,m]=(0,o.i)({prop:r,defaultProp:l??!1,onChange:n,caller:v});return(0,f.jsx)(N,{scope:a,triggerRef:d,contentRef:u,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:x,onOpenChange:m,onOpenToggle:t.useCallback(()=>m(e=>!e),[m]),modal:c,children:s})};k.displayName=v;var C="DialogTrigger",A=t.forwardRef((e,a)=>{let{__scopeDialog:s,...t}=e,n=w(C,s),i=(0,l.s)(a,n.triggerRef);return(0,f.jsx)(m.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":n.open,"aria-controls":n.contentId,"data-state":Z(n.open),...t,ref:i,onClick:(0,r.m)(e.onClick,n.onOpenToggle)})});A.displayName=C;var z="DialogPortal",[M,D]=j(z,{forceMount:void 0}),R=e=>{let{__scopeDialog:a,forceMount:s,children:r,container:l}=e,n=w(z,a);return(0,f.jsx)(M,{scope:a,forceMount:s,children:t.Children.map(r,e=>(0,f.jsx)(x.C,{present:s||n.open,children:(0,f.jsx)(u.Z,{asChild:!0,container:l,children:e})}))})};R.displayName=z;var $="DialogOverlay",_=t.forwardRef((e,a)=>{let s=D($,e.__scopeDialog),{forceMount:t=s.forceMount,...r}=e,l=w($,e.__scopeDialog);return l.modal?(0,f.jsx)(x.C,{present:t||l.open,children:(0,f.jsx)(P,{...r,ref:a})}):null});_.displayName=$;var q=(0,b.TL)("DialogOverlay.RemoveScroll"),P=t.forwardRef((e,a)=>{let{__scopeDialog:s,...t}=e,r=w($,s);return(0,f.jsx)(p.A,{as:q,allowPinchZoom:!0,shards:[r.contentRef],children:(0,f.jsx)(m.sG.div,{"data-state":Z(r.open),...t,ref:a,style:{pointerEvents:"auto",...t.style}})})}),L="DialogContent",G=t.forwardRef((e,a)=>{let s=D(L,e.__scopeDialog),{forceMount:t=s.forceMount,...r}=e,l=w(L,e.__scopeDialog);return(0,f.jsx)(x.C,{present:t||l.open,children:l.modal?(0,f.jsx)(I,{...r,ref:a}):(0,f.jsx)(S,{...r,ref:a})})});G.displayName=L;var I=t.forwardRef((e,a)=>{let s=w(L,e.__scopeDialog),n=t.useRef(null),i=(0,l.s)(a,s.contentRef,n);return t.useEffect(()=>{let e=n.current;if(e)return(0,g.Eq)(e)},[]),(0,f.jsx)(E,{...e,ref:i,trapFocus:s.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,r.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),s.triggerRef.current?.focus()}),onPointerDownOutside:(0,r.m)(e.onPointerDownOutside,e=>{let a=e.detail.originalEvent,s=0===a.button&&!0===a.ctrlKey;(2===a.button||s)&&e.preventDefault()}),onFocusOutside:(0,r.m)(e.onFocusOutside,e=>e.preventDefault())})}),S=t.forwardRef((e,a)=>{let s=w(L,e.__scopeDialog),r=t.useRef(!1),l=t.useRef(!1);return(0,f.jsx)(E,{...e,ref:a,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:a=>{e.onCloseAutoFocus?.(a),a.defaultPrevented||(r.current||s.triggerRef.current?.focus(),a.preventDefault()),r.current=!1,l.current=!1},onInteractOutside:a=>{e.onInteractOutside?.(a),a.defaultPrevented||(r.current=!0,"pointerdown"===a.detail.originalEvent.type&&(l.current=!0));let t=a.target;s.triggerRef.current?.contains(t)&&a.preventDefault(),"focusin"===a.detail.originalEvent.type&&l.current&&a.preventDefault()}})}),E=t.forwardRef((e,a)=>{let{__scopeDialog:s,trapFocus:r,onOpenAutoFocus:n,onCloseAutoFocus:i,...o}=e,u=w(L,s),x=t.useRef(null),m=(0,l.s)(a,x);return(0,h.Oh)(),(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(d.n,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:n,onUnmountAutoFocus:i,children:(0,f.jsx)(c.qW,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":Z(u.open),...o,ref:m,onDismiss:()=>u.onOpenChange(!1)})}),(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(W,{titleId:u.titleId}),(0,f.jsx)(K,{contentRef:x,descriptionId:u.descriptionId})]})]})}),T="DialogTitle",F=t.forwardRef((e,a)=>{let{__scopeDialog:s,...t}=e,r=w(T,s);return(0,f.jsx)(m.sG.h2,{id:r.titleId,...t,ref:a})});F.displayName=T;var O="DialogDescription",U=t.forwardRef((e,a)=>{let{__scopeDialog:s,...t}=e,r=w(O,s);return(0,f.jsx)(m.sG.p,{id:r.descriptionId,...t,ref:a})});U.displayName=O;var V="DialogClose",J=t.forwardRef((e,a)=>{let{__scopeDialog:s,...t}=e,l=w(V,s);return(0,f.jsx)(m.sG.button,{type:"button",...t,ref:a,onClick:(0,r.m)(e.onClick,()=>l.onOpenChange(!1))})});function Z(e){return e?"open":"closed"}J.displayName=V;var B="DialogTitleWarning",[X,H]=(0,n.q)(B,{contentName:L,titleName:T,docsSlug:"dialog"}),W=({titleId:e})=>{let a=H(B),s=`\`${a.contentName}\` requires a \`${a.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${a.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${a.docsSlug}`;return t.useEffect(()=>{e&&(document.getElementById(e)||console.error(s))},[s,e]),null},K=({contentRef:e,descriptionId:a})=>{let s=H("DialogDescriptionWarning"),r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${s.contentName}}.`;return t.useEffect(()=>{let s=e.current?.getAttribute("aria-describedby");a&&s&&(document.getElementById(a)||console.warn(r))},[r,e,a]),null},Y=k,Q=A,ee=R,ea=_,es=G,et=F,er=U,el=J},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31158:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},33872:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},33873:e=>{"use strict";e.exports=require("path")},34729:(e,a,s)=>{"use strict";s.d(a,{T:()=>l});var t=s(60687);s(43210);var r=s(4780);function l({className:e,...a}){return(0,t.jsx)("textarea",{"data-slot":"textarea",className:(0,r.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...a})}},48394:(e,a,s)=>{Promise.resolve().then(s.bind(s,60588))},54987:(e,a,s)=>{"use strict";s.d(a,{d:()=>n});var t=s(60687);s(43210);var r=s(90270),l=s(4780);function n({className:e,...a}){return(0,t.jsx)(r.bL,{"data-slot":"switch",className:(0,l.cn)("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...a,children:(0,t.jsx)(r.zi,{"data-slot":"switch-thumb",className:(0,l.cn)("bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0")})})}},55146:(e,a,s)=>{"use strict";s.d(a,{B8:()=>D,UC:()=>$,bL:()=>M,l9:()=>R});var t=s(43210),r=s(70569),l=s(11273),n=s(72942),i=s(46059),o=s(14163),c=s(43),d=s(65551),u=s(96963),x=s(60687),m="Tabs",[h,p]=(0,l.A)(m,[n.RG]),g=(0,n.RG)(),[b,f]=h(m),v=t.forwardRef((e,a)=>{let{__scopeTabs:s,value:t,onValueChange:r,defaultValue:l,orientation:n="horizontal",dir:i,activationMode:h="automatic",...p}=e,g=(0,c.jH)(i),[f,v]=(0,d.i)({prop:t,onChange:r,defaultProp:l??"",caller:m});return(0,x.jsx)(b,{scope:s,baseId:(0,u.B)(),value:f,onValueChange:v,orientation:n,dir:g,activationMode:h,children:(0,x.jsx)(o.sG.div,{dir:g,"data-orientation":n,...p,ref:a})})});v.displayName=m;var j="TabsList",y=t.forwardRef((e,a)=>{let{__scopeTabs:s,loop:t=!0,...r}=e,l=f(j,s),i=g(s);return(0,x.jsx)(n.bL,{asChild:!0,...i,orientation:l.orientation,dir:l.dir,loop:t,children:(0,x.jsx)(o.sG.div,{role:"tablist","aria-orientation":l.orientation,...r,ref:a})})});y.displayName=j;var N="TabsTrigger",w=t.forwardRef((e,a)=>{let{__scopeTabs:s,value:t,disabled:l=!1,...i}=e,c=f(N,s),d=g(s),u=A(c.baseId,t),m=z(c.baseId,t),h=t===c.value;return(0,x.jsx)(n.q7,{asChild:!0,...d,focusable:!l,active:h,children:(0,x.jsx)(o.sG.button,{type:"button",role:"tab","aria-selected":h,"aria-controls":m,"data-state":h?"active":"inactive","data-disabled":l?"":void 0,disabled:l,id:u,...i,ref:a,onMouseDown:(0,r.m)(e.onMouseDown,e=>{l||0!==e.button||!1!==e.ctrlKey?e.preventDefault():c.onValueChange(t)}),onKeyDown:(0,r.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&c.onValueChange(t)}),onFocus:(0,r.m)(e.onFocus,()=>{let e="manual"!==c.activationMode;h||l||!e||c.onValueChange(t)})})})});w.displayName=N;var k="TabsContent",C=t.forwardRef((e,a)=>{let{__scopeTabs:s,value:r,forceMount:l,children:n,...c}=e,d=f(k,s),u=A(d.baseId,r),m=z(d.baseId,r),h=r===d.value,p=t.useRef(h);return t.useEffect(()=>{let e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,x.jsx)(i.C,{present:l||h,children:({present:s})=>(0,x.jsx)(o.sG.div,{"data-state":h?"active":"inactive","data-orientation":d.orientation,role:"tabpanel","aria-labelledby":u,hidden:!s,id:m,tabIndex:0,...c,ref:a,style:{...e.style,animationDuration:p.current?"0s":void 0},children:s&&n})})});function A(e,a){return`${e}-trigger-${a}`}function z(e,a){return`${e}-content-${a}`}C.displayName=k;var M=v,D=y,R=w,$=C},56085:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},60588:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Graduation Toqs\\\\frontend\\\\src\\\\app\\\\customize\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\customize\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63503:(e,a,s)=>{"use strict";s.d(a,{Cf:()=>u,Es:()=>m,L3:()=>h,c7:()=>x,lG:()=>i,rr:()=>p,zM:()=>o});var t=s(60687);s(43210);var r=s(26134),l=s(11860),n=s(4780);function i({...e}){return(0,t.jsx)(r.bL,{"data-slot":"dialog",...e})}function o({...e}){return(0,t.jsx)(r.l9,{"data-slot":"dialog-trigger",...e})}function c({...e}){return(0,t.jsx)(r.ZL,{"data-slot":"dialog-portal",...e})}function d({className:e,...a}){return(0,t.jsx)(r.hJ,{"data-slot":"dialog-overlay",className:(0,n.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...a})}function u({className:e,children:a,showCloseButton:s=!0,...i}){return(0,t.jsxs)(c,{"data-slot":"dialog-portal",children:[(0,t.jsx)(d,{}),(0,t.jsxs)(r.UC,{"data-slot":"dialog-content",className:(0,n.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...i,children:[a,s&&(0,t.jsxs)(r.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,t.jsx)(l.A,{}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function x({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"dialog-header",className:(0,n.cn)("flex flex-col gap-2 text-center sm:text-left",e),...a})}function m({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"dialog-footer",className:(0,n.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...a})}function h({className:e,...a}){return(0,t.jsx)(r.hE,{"data-slot":"dialog-title",className:(0,n.cn)("text-lg leading-none font-semibold",e),...a})}function p({className:e,...a}){return(0,t.jsx)(r.VY,{"data-slot":"dialog-description",className:(0,n.cn)("text-muted-foreground text-sm",e),...a})}},64398:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},70615:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},78148:(e,a,s)=>{"use strict";s.d(a,{b:()=>i});var t=s(43210),r=s(14163),l=s(60687),n=t.forwardRef((e,a)=>(0,l.jsx)(r.sG.label,{...e,ref:a,onMouseDown:a=>{a.target.closest("button, input, select, textarea")||(e.onMouseDown?.(a),!a.defaultPrevented&&a.detail>1&&a.preventDefault())}}));n.displayName="Label";var i=n},79551:e=>{"use strict";e.exports=require("url")},80013:(e,a,s)=>{"use strict";s.d(a,{J:()=>n});var t=s(60687);s(43210);var r=s(78148),l=s(4780);function n({className:e,...a}){return(0,t.jsx)(r.b,{"data-slot":"label",className:(0,l.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...a})}},81620:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("share-2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]])},85763:(e,a,s)=>{"use strict";s.d(a,{Xi:()=>o,av:()=>c,j7:()=>i,tU:()=>n});var t=s(60687);s(43210);var r=s(55146),l=s(4780);function n({className:e,...a}){return(0,t.jsx)(r.bL,{"data-slot":"tabs",className:(0,l.cn)("flex flex-col gap-2",e),...a})}function i({className:e,...a}){return(0,t.jsx)(r.B8,{"data-slot":"tabs-list",className:(0,l.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",e),...a})}function o({className:e,...a}){return(0,t.jsx)(r.l9,{"data-slot":"tabs-trigger",className:(0,l.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...a})}function c({className:e,...a}){return(0,t.jsx)(r.UC,{"data-slot":"tabs-content",className:(0,l.cn)("flex-1 outline-none",e),...a})}},88722:(e,a,s)=>{"use strict";s.r(a),s.d(a,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>c});var t=s(65239),r=s(48088),l=s(88170),n=s.n(l),i=s(30893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);s.d(a,o);let c={children:["",{children:["customize",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,60588)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\customize\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,54431)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\customize\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/customize/page",pathname:"/customize",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},90270:(e,a,s)=>{"use strict";s.d(a,{bL:()=>N,zi:()=>w});var t=s(43210),r=s(70569),l=s(98599),n=s(11273),i=s(65551),o=s(83721),c=s(18853),d=s(14163),u=s(60687),x="Switch",[m,h]=(0,n.A)(x),[p,g]=m(x),b=t.forwardRef((e,a)=>{let{__scopeSwitch:s,name:n,checked:o,defaultChecked:c,required:m,disabled:h,value:g="on",onCheckedChange:b,form:f,...v}=e,[N,w]=t.useState(null),k=(0,l.s)(a,e=>w(e)),C=t.useRef(!1),A=!N||f||!!N.closest("form"),[z,M]=(0,i.i)({prop:o,defaultProp:c??!1,onChange:b,caller:x});return(0,u.jsxs)(p,{scope:s,checked:z,disabled:h,children:[(0,u.jsx)(d.sG.button,{type:"button",role:"switch","aria-checked":z,"aria-required":m,"data-state":y(z),"data-disabled":h?"":void 0,disabled:h,value:g,...v,ref:k,onClick:(0,r.m)(e.onClick,e=>{M(e=>!e),A&&(C.current=e.isPropagationStopped(),C.current||e.stopPropagation())})}),A&&(0,u.jsx)(j,{control:N,bubbles:!C.current,name:n,value:g,checked:z,required:m,disabled:h,form:f,style:{transform:"translateX(-100%)"}})]})});b.displayName=x;var f="SwitchThumb",v=t.forwardRef((e,a)=>{let{__scopeSwitch:s,...t}=e,r=g(f,s);return(0,u.jsx)(d.sG.span,{"data-state":y(r.checked),"data-disabled":r.disabled?"":void 0,...t,ref:a})});v.displayName=f;var j=t.forwardRef(({__scopeSwitch:e,control:a,checked:s,bubbles:r=!0,...n},i)=>{let d=t.useRef(null),x=(0,l.s)(d,i),m=(0,o.Z)(s),h=(0,c.X)(a);return t.useEffect(()=>{let e=d.current;if(!e)return;let a=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(m!==s&&a){let t=new Event("click",{bubbles:r});a.call(e,s),e.dispatchEvent(t)}},[m,s,r]),(0,u.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:s,...n,tabIndex:-1,ref:x,style:{...n.style,...h,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function y(e){return e?"checked":"unchecked"}j.displayName="SwitchBubbleInput";var N=b,w=v},92363:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("crown",[["path",{d:"M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z",key:"1vdc57"}],["path",{d:"M5 21h14",key:"11awu3"}]])},93502:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>X});var t=s(60687),r=s(43210),l=s(8520),n=s(87801),i=s(29523),o=s(44493),c=s(96834),d=s(62688);let u=(0,d.A)("zoom-out",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]]),x=(0,d.A)("zoom-in",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"11",x2:"11",y1:"8",y2:"14",key:"1vmskp"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]]),m=(0,d.A)("move-3d",[["path",{d:"M5 3v16h16",key:"1mqmf9"}],["path",{d:"m5 19 6-6",key:"jh6hbb"}],["path",{d:"m2 6 3-3 3 3",key:"tkyvxa"}],["path",{d:"m18 16 3 3-3 3",key:"1d4glt"}]]);var h=s(13943),p=s(31158),g=s(81620);function b({configuration:e,className:a=""}){let[s,l]=(0,r.useState)(0),[n,d]=(0,r.useState)(1),[b,f]=(0,r.useState)(!1),v={black:"#000000",navy:"#1e3a8a",burgundy:"#7c2d12",forest:"#166534",purple:"#7c3aed",gray:"#4b5563",gold:"#fbbf24",silver:"#e5e7eb",white:"#ffffff",blue:"#3b82f6",red:"#ef4444"};return(0,t.jsx)(o.Zp,{className:`overflow-hidden ${a}`,children:(0,t.jsxs)(o.Wu,{className:"p-0",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center p-4 bg-gray-50 dark:bg-gray-800 border-b",children:[(0,t.jsx)("div",{className:"flex items-center gap-2",children:(0,t.jsx)(c.E,{variant:"outline",className:"arabic-text",children:"معاينة مباشرة"})}),(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(i.$,{variant:"ghost",size:"sm",onClick:()=>{d(e=>Math.max(e-.2,.5))},children:(0,t.jsx)(u,{className:"h-4 w-4"})}),(0,t.jsx)(i.$,{variant:"ghost",size:"sm",onClick:()=>{d(e=>Math.min(e+.2,2))},children:(0,t.jsx)(x,{className:"h-4 w-4"})}),(0,t.jsx)(i.$,{variant:"ghost",size:"sm",onClick:()=>{f(!0),l(e=>e+90),setTimeout(()=>f(!1),500)},children:(0,t.jsx)(m,{className:"h-4 w-4"})}),(0,t.jsx)(i.$,{variant:"ghost",size:"sm",onClick:()=>{l(0),d(1)},children:(0,t.jsx)(h.A,{className:"h-4 w-4"})})]})]}),(0,t.jsxs)("div",{className:"relative aspect-square bg-gradient-to-br from-gray-100 via-white to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-800 overflow-hidden",children:[(0,t.jsx)("div",{className:`absolute inset-0 flex items-center justify-center transition-transform duration-500 ${b?"animate-pulse":""}`,style:{transform:`rotate(${s}deg) scale(${n})`,transformOrigin:"center"},children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsxs)("div",{className:"w-32 h-40 rounded-t-full relative",style:{backgroundColor:v[e.gown.color]||"#000000",opacity:"luxury"===e.gown.fabric?.9:.8},children:[(0,t.jsx)("div",{className:"absolute inset-x-0 top-0 h-8 bg-gradient-to-b from-white/20 to-transparent rounded-t-full"}),(0,t.jsx)("div",{className:"absolute -left-6 top-4 w-12 h-16 rounded-full transform -rotate-12",style:{backgroundColor:v[e.gown.color]||"#000000"}}),(0,t.jsx)("div",{className:"absolute -right-6 top-4 w-12 h-16 rounded-full transform rotate-12",style:{backgroundColor:v[e.gown.color]||"#000000"}}),e.accessories.hood&&(0,t.jsx)("div",{className:"absolute -top-4 left-1/2 transform -translate-x-1/2 w-20 h-12 rounded-t-full border-2",style:{backgroundColor:v[e.gown.color]||"#000000",borderColor:v[e.cap.color]||"#000000"}})]}),(0,t.jsxs)("div",{className:"absolute -top-8 left-1/2 transform -translate-x-1/2",children:[(0,t.jsx)("div",{className:"w-16 h-4 rounded-full",style:{backgroundColor:v[e.cap.color]||"#000000"}}),(0,t.jsx)("div",{className:"absolute -top-2 left-1/2 transform -translate-x-1/2 w-20 h-20 border-4 border-gray-300",style:{backgroundColor:v[e.cap.color]||"#000000"}}),(0,t.jsx)("div",{className:"absolute top-0 right-0 w-1 h-8 transform rotate-12",style:{backgroundColor:v[e.cap.tassel.color]||"#fbbf24"},children:(0,t.jsx)("div",{className:"absolute bottom-0 w-3 h-3 rounded-full",style:{backgroundColor:v[e.cap.tassel.color]||"#fbbf24"}})})]}),e.stole.enabled&&(0,t.jsx)("div",{className:"absolute top-8 left-1/2 transform -translate-x-1/2",children:(0,t.jsx)("div",{className:"w-6 h-32 rounded-full opacity-90",style:{backgroundColor:v[e.stole.color]||"#fbbf24"},children:e.stole.embroidery&&(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-b from-yellow-200/50 to-transparent rounded-full"})})}),e.accessories.sash&&(0,t.jsx)("div",{className:"absolute top-12 left-0 w-full h-4 transform -rotate-12 opacity-80",style:{backgroundColor:"#ef4444"}}),e.accessories.medal&&(0,t.jsx)("div",{className:"absolute top-16 left-1/2 transform -translate-x-1/2",children:(0,t.jsx)("div",{className:"w-6 h-6 rounded-full bg-gradient-to-br from-yellow-400 to-yellow-600 border-2 border-yellow-300"})})]})}),(0,t.jsx)("div",{className:"absolute top-4 left-4 w-2 h-2 bg-blue-400 rounded-full animate-bounce"}),(0,t.jsx)("div",{className:"absolute top-8 right-6 w-1 h-1 bg-purple-400 rounded-full animate-pulse"}),(0,t.jsx)("div",{className:"absolute bottom-8 left-8 w-1.5 h-1.5 bg-yellow-400 rounded-full animate-bounce delay-300"}),(0,t.jsx)("div",{className:"absolute bottom-4 right-4 w-2 h-2 bg-green-400 rounded-full animate-pulse delay-500"})]}),(0,t.jsxs)("div",{className:"p-4 bg-white dark:bg-gray-900 border-t",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-3 text-sm",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-600 dark:text-gray-400 arabic-text",children:"الثوب:"}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full border",style:{backgroundColor:v[e.gown.color]}}),(0,t.jsx)("span",{className:"arabic-text",children:"black"===e.gown.color?"أسود":"navy"===e.gown.color?"أزرق داكن":"burgundy"===e.gown.color?"بورجوندي":e.gown.color})]})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-600 dark:text-gray-400 arabic-text",children:"القبعة:"}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full border",style:{backgroundColor:v[e.cap.color]}}),(0,t.jsx)("span",{className:"arabic-text",children:"black"===e.cap.color?"أسود":"navy"===e.cap.color?"أزرق داكن":e.cap.color})]})]}),e.stole.enabled&&(0,t.jsxs)("div",{className:"flex justify-between col-span-2",children:[(0,t.jsx)("span",{className:"text-gray-600 dark:text-gray-400 arabic-text",children:"الوشاح:"}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full border",style:{backgroundColor:v[e.stole.color]}}),(0,t.jsx)("span",{className:"arabic-text",children:"gold"===e.stole.color?"ذهبي":"silver"===e.stole.color?"فضي":e.stole.color}),e.stole.embroidery&&(0,t.jsx)(c.E,{variant:"secondary",className:"text-xs",children:"مطرز"})]})]})]}),(0,t.jsxs)("div",{className:"flex gap-2 mt-4",children:[(0,t.jsxs)(i.$,{variant:"outline",size:"sm",className:"flex-1",children:[(0,t.jsx)(p.A,{className:"h-4 w-4 mr-2"}),(0,t.jsx)("span",{className:"arabic-text",children:"تحميل"})]}),(0,t.jsxs)(i.$,{variant:"outline",size:"sm",className:"flex-1",children:[(0,t.jsx)(g.A,{className:"h-4 w-4 mr-2"}),(0,t.jsx)("span",{className:"arabic-text",children:"مشاركة"})]})]})]})]})})}var f=s(89667),v=s(80013),j=s(85763),y=s(64398),N=s(98971),w=s(99270),k=s(13964),C=s(67760);let A={classic:{name:"كلاسيكي",icon:"\uD83C\uDFA9",description:"الألوان التقليدية الأنيقة"},modern:{name:"عصري",icon:"✨",description:"ألوان معاصرة وجريئة"},premium:{name:"فاخر",icon:"\uD83D\uDC8E",description:"ألوان راقية ومميزة"}};function z({title:e,selectedColor:a,onColorChange:s,colors:l,showCategories:n=!0,showSearch:d=!1,allowCustom:u=!1,className:x=""}){let[m,h]=(0,r.useState)(""),[p,g]=(0,r.useState)("all"),[b,z]=(0,r.useState)("#000000"),[M,D]=(0,r.useState)([]),R=l.filter(e=>{let a=e.name.toLowerCase().includes(m.toLowerCase()),s="all"===p||e.category===p;return a&&s}),$=e=>{D(a=>a.includes(e)?a.filter(a=>a!==e):[...a,e])},_=(e=0)=>Array.from({length:5},(a,s)=>(0,t.jsx)(y.A,{className:`h-3 w-3 ${s<e?"text-yellow-400 fill-current":"text-gray-300"}`},s));return(0,t.jsxs)(o.Zp,{className:x,children:[(0,t.jsxs)(o.aR,{children:[(0,t.jsxs)(o.ZB,{className:"flex items-center gap-2 arabic-text",children:[(0,t.jsx)(N.A,{className:"h-5 w-5"}),e]}),d&&(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(w.A,{className:"absolute left-3 top-3 h-4 w-4 text-gray-400"}),(0,t.jsx)(f.p,{placeholder:"ابحث عن لون...",value:m,onChange:e=>h(e.target.value),className:"pl-10 arabic-text"})]})]}),(0,t.jsxs)(o.Wu,{className:"space-y-4",children:[n&&(0,t.jsx)(j.tU,{value:p,onValueChange:g,children:(0,t.jsxs)(j.j7,{className:"category-grid grid w-full grid-cols-4",children:[(0,t.jsx)(j.Xi,{value:"all",className:"arabic-text",children:"الكل"}),Object.entries(A).map(([e,a])=>(0,t.jsxs)(j.Xi,{value:e,className:"arabic-text",children:[(0,t.jsx)("span",{className:"mr-1",children:a.icon}),a.name]},e))]})}),(0,t.jsx)("div",{className:"grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 gap-3",children:R.map(e=>(0,t.jsxs)("div",{className:"relative group",children:[(0,t.jsxs)("button",{onClick:()=>s(e.value),className:`relative w-full aspect-square rounded-lg border-3 transition-all duration-200 hover:scale-105 hover:shadow-lg ${a===e.value?"border-blue-500 ring-2 ring-blue-200 dark:ring-blue-800":"border-gray-200 dark:border-gray-700 hover:border-gray-300"}`,style:{backgroundColor:e.hex},children:[a===e.value&&(0,t.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,t.jsx)("div",{className:"bg-white dark:bg-gray-900 rounded-full p-1",children:(0,t.jsx)(k.A,{className:"h-4 w-4 text-blue-600"})})}),e.isNew&&(0,t.jsx)(c.E,{className:"absolute -top-2 -right-2 bg-green-500 text-xs px-1 py-0",children:"جديد"})]}),(0,t.jsx)("div",{onClick:a=>{a.stopPropagation(),$(e.value)},className:"absolute top-1 left-1 opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer p-1 rounded-full hover:bg-black/10 dark:hover:bg-white/10",role:"button",tabIndex:0,onKeyDown:a=>{("Enter"===a.key||" "===a.key)&&(a.preventDefault(),a.stopPropagation(),$(e.value))},children:(0,t.jsx)(C.A,{className:`h-3 w-3 ${M.includes(e.value)?"text-red-500 fill-current":"text-white drop-shadow-lg"}`})}),(0,t.jsxs)("div",{className:"mt-2 text-center",children:[(0,t.jsx)("div",{className:"text-xs font-medium arabic-text truncate",children:e.name}),(0,t.jsx)("div",{className:"text-xs text-gray-500 uppercase",children:e.hex}),e.popularity&&(0,t.jsx)("div",{className:"flex justify-center gap-0.5 mt-1",children:_(e.popularity)})]})]},e.value))}),u&&(0,t.jsxs)("div",{className:"border-t pt-4",children:[(0,t.jsx)(v.J,{className:"text-sm font-medium arabic-text mb-3 block",children:"لون مخصص"}),(0,t.jsxs)("div",{className:"flex gap-3 items-center",children:[(0,t.jsx)("input",{type:"color",value:b,onChange:e=>z(e.target.value),className:"w-12 h-12 rounded-lg border-2 border-gray-200 dark:border-gray-700 cursor-pointer"}),(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsx)(f.p,{value:b,onChange:e=>z(e.target.value),placeholder:"#000000",className:"font-mono"})}),(0,t.jsx)(i.$,{variant:"outline",size:"sm",onClick:()=>s(b),className:"arabic-text",children:"تطبيق"})]})]}),(0,t.jsxs)("div",{className:"border-t pt-4",children:[(0,t.jsx)(v.J,{className:"text-sm font-medium arabic-text mb-3 block",children:"تركيبات شائعة"}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,t.jsxs)("button",{onClick:()=>s("black"),className:"flex items-center gap-2 p-2 rounded-lg border hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors",children:[(0,t.jsxs)("div",{className:"flex gap-1",children:[(0,t.jsx)("div",{className:"w-4 h-4 rounded-full bg-black border"}),(0,t.jsx)("div",{className:"w-4 h-4 rounded-full bg-yellow-400 border"})]}),(0,t.jsx)("span",{className:"text-xs arabic-text",children:"كلاسيكي"})]}),(0,t.jsxs)("button",{onClick:()=>s("navy"),className:"flex items-center gap-2 p-2 rounded-lg border hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors",children:[(0,t.jsxs)("div",{className:"flex gap-1",children:[(0,t.jsx)("div",{className:"w-4 h-4 rounded-full bg-blue-900 border"}),(0,t.jsx)("div",{className:"w-4 h-4 rounded-full bg-gray-300 border"})]}),(0,t.jsx)("span",{className:"text-xs arabic-text",children:"أنيق"})]})]})]}),a&&(0,t.jsx)("div",{className:"bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg",children:(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 rounded-full border-2 border-white shadow-sm",style:{backgroundColor:l.find(e=>e.value===a)?.hex}}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium arabic-text",children:l.find(e=>e.value===a)?.name}),(0,t.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:l.find(e=>e.value===a)?.hex})]})]})})]})]})}var M=s(34729),D=s(63503),R=s(19526),$=s(72575),_=s(66232),q=s(33872),P=s(8819),L=s(70615),G=s(16103);let I=(0,d.A)("printer",[["path",{d:"M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2",key:"143wyd"}],["path",{d:"M6 9V3a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v6",key:"1itne7"}],["rect",{x:"6",y:"14",width:"12",height:"8",rx:"1",key:"1ue0tg"}]]);function S({designData:e,designName:a="تصميمي المخصص",onSave:s,onShare:l,className:n=""}){let[c,d]=(0,r.useState)(!1),[u,x]=(0,r.useState)(!1),[m,h]=(0,r.useState)(a),[b,j]=(0,r.useState)(""),[y,N]=(0,r.useState)(!1),[w]=(0,r.useState)(`https://graduation-toqs.com/design/${Date.now()}`),k=async()=>{if(m.trim()){N(!0);try{await s?.(m,b),d(!1)}catch(e){}finally{N(!1)}}},A=e=>{l?.(e);let a=`شاهد تصميم زي التخرج المخصص الخاص بي على Graduation Toqs!`;switch(e){case"facebook":window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(w)}`,"_blank");break;case"twitter":window.open(`https://twitter.com/intent/tweet?text=${encodeURIComponent(a)}&url=${encodeURIComponent(w)}`,"_blank");break;case"whatsapp":window.open(`https://wa.me/?text=${encodeURIComponent(a+" "+w)}`,"_blank");break;case"copy":navigator.clipboard.writeText(w)}},z=e=>{console.log(`Downloading design as ${e}`)},S=[{id:"facebook",name:"Facebook",icon:(0,t.jsx)(R.A,{className:"h-5 w-5"}),color:"bg-blue-600 hover:bg-blue-700"},{id:"twitter",name:"Twitter",icon:(0,t.jsx)($.A,{className:"h-5 w-5"}),color:"bg-sky-500 hover:bg-sky-600"},{id:"instagram",name:"Instagram",icon:(0,t.jsx)(_.A,{className:"h-5 w-5"}),color:"bg-pink-600 hover:bg-pink-700"},{id:"whatsapp",name:"WhatsApp",icon:(0,t.jsx)(q.A,{className:"h-5 w-5"}),color:"bg-green-600 hover:bg-green-700"}];return(0,t.jsxs)("div",{className:`space-y-4 ${n}`,children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,t.jsxs)(D.lG,{open:c,onOpenChange:d,children:[(0,t.jsx)(D.zM,{asChild:!0,children:(0,t.jsxs)(i.$,{variant:"outline",className:"arabic-text",children:[(0,t.jsx)(P.A,{className:"h-4 w-4 mr-2"}),"حفظ التصميم"]})}),(0,t.jsxs)(D.Cf,{children:[(0,t.jsxs)(D.c7,{children:[(0,t.jsx)(D.L3,{className:"arabic-text",children:"حفظ التصميم"}),(0,t.jsx)(D.rr,{className:"arabic-text",children:"احفظ تصميمك المخصص لتتمكن من الوصول إليه لاحقاً"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(v.J,{htmlFor:"design-title",className:"arabic-text",children:"اسم التصميم"}),(0,t.jsx)(f.p,{id:"design-title",value:m,onChange:e=>h(e.target.value),placeholder:"أدخل اسم التصميم",className:"arabic-text"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(v.J,{htmlFor:"design-description",className:"arabic-text",children:"وصف التصميم (اختياري)"}),(0,t.jsx)(M.T,{id:"design-description",value:b,onChange:e=>j(e.target.value),placeholder:"أضف وصفاً لتصميمك...",className:"arabic-text",rows:3})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(i.$,{onClick:k,disabled:!m.trim()||y,className:"flex-1 arabic-text",children:y?"جاري الحفظ...":"حفظ"}),(0,t.jsx)(i.$,{variant:"outline",onClick:()=>d(!1),className:"arabic-text",children:"إلغاء"})]})]})]})]}),(0,t.jsxs)(D.lG,{open:u,onOpenChange:x,children:[(0,t.jsx)(D.zM,{asChild:!0,children:(0,t.jsxs)(i.$,{variant:"outline",className:"arabic-text",children:[(0,t.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"مشاركة"]})}),(0,t.jsxs)(D.Cf,{children:[(0,t.jsxs)(D.c7,{children:[(0,t.jsx)(D.L3,{className:"arabic-text",children:"مشاركة التصميم"}),(0,t.jsx)(D.rr,{className:"arabic-text",children:"شارك تصميمك المميز مع الأصدقاء والعائلة"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(v.J,{className:"arabic-text",children:"رابط التصميم"}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(f.p,{value:w,readOnly:!0,className:"flex-1"}),(0,t.jsx)(i.$,{variant:"outline",size:"sm",onClick:()=>A("copy"),children:(0,t.jsx)(L.A,{className:"h-4 w-4"})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(v.J,{className:"arabic-text mb-3 block",children:"مشاركة على وسائل التواصل"}),(0,t.jsx)("div",{className:"grid grid-cols-2 gap-3",children:S.map(e=>(0,t.jsxs)(i.$,{variant:"outline",onClick:()=>A(e.id),className:`${e.color} text-white border-0 arabic-text`,children:[e.icon,(0,t.jsx)("span",{className:"mr-2",children:e.name})]},e.id))})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"w-32 h-32 bg-gray-100 dark:bg-gray-800 rounded-lg mx-auto mb-2 flex items-center justify-center",children:(0,t.jsx)(G.A,{className:"h-16 w-16 text-gray-400"})}),(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"رمز QR للمشاركة السريعة"})]})]})]})]})]}),(0,t.jsxs)(o.Zp,{children:[(0,t.jsxs)(o.aR,{children:[(0,t.jsx)(o.ZB,{className:"text-lg arabic-text",children:"تحميل التصميم"}),(0,t.jsx)(o.BT,{className:"arabic-text",children:"احصل على نسخة من تصميمك بصيغ مختلفة"})]}),(0,t.jsxs)(o.Wu,{children:[(0,t.jsx)("div",{className:"grid grid-cols-1 gap-3",children:[{format:"png",name:"صورة PNG",description:"جودة عالية للطباعة"},{format:"jpg",name:"صورة JPG",description:"حجم أصغر للمشاركة"},{format:"pdf",name:"ملف PDF",description:"للطباعة الاحترافية"},{format:"svg",name:"ملف SVG",description:"قابل للتحرير"}].map(e=>(0,t.jsxs)("button",{onClick:()=>z(e.format),className:"flex items-center justify-between p-3 rounded-lg border hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors",children:[(0,t.jsxs)("div",{className:"text-left",children:[(0,t.jsx)("div",{className:"font-medium arabic-text",children:e.name}),(0,t.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:e.description})]}),(0,t.jsx)(p.A,{className:"h-5 w-5 text-gray-400"})]},e.format))}),(0,t.jsxs)(i.$,{variant:"outline",onClick:()=>{window.print()},className:"w-full mt-4 arabic-text",children:[(0,t.jsx)(I,{className:"h-4 w-4 mr-2"}),"طباعة التصميم"]})]})]}),(0,t.jsxs)(o.Zp,{children:[(0,t.jsx)(o.aR,{children:(0,t.jsx)(o.ZB,{className:"text-lg arabic-text",children:"إحصائيات التصميم"})}),(0,t.jsx)(o.Wu,{children:(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:"12"}),(0,t.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"مشاهدة"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-red-600",children:"3"}),(0,t.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"إعجاب"})]})]})})]}),(0,t.jsxs)(i.$,{variant:"outline",className:"w-full arabic-text",children:[(0,t.jsx)(C.A,{className:"h-4 w-4 mr-2"}),"إضافة للمفضلة"]})]})}var E=s(15079),T=s(54987),F=s(28561);let O=(0,d.A)("shirt",[["path",{d:"M20.38 3.46 16 2a4 4 0 0 1-8 0L3.62 3.46a2 2 0 0 0-1.34 2.23l.58 3.47a1 1 0 0 0 .99.84H6v10c0 1.1.9 2 2 2h8a2 2 0 0 0 2-2V10h2.15a1 1 0 0 0 .99-.84l.58-3.47a2 2 0 0 0-1.34-2.23z",key:"1wgbhj"}]]);var U=s(92363);let V=(0,d.A)("ribbon",[["path",{d:"M12 11.22C11 9.997 10 9 10 8a2 2 0 0 1 4 0c0 1-.998 2.002-2.01 3.22",key:"1rnhq3"}],["path",{d:"m12 18 2.57-3.5",key:"116vt7"}],["path",{d:"M6.243 9.016a7 7 0 0 1 11.507-.009",key:"10dq0b"}],["path",{d:"M9.35 14.53 12 11.22",key:"tdsyp2"}],["path",{d:"M9.35 14.53C7.728 12.246 6 10.221 6 7a6 5 0 0 1 12 0c-.005 3.22-1.778 5.235-3.43 7.5l3.557 4.527a1 1 0 0 1-.203 1.43l-1.894 1.36a1 1 0 0 1-1.384-.215L12 18l-2.679 3.593a1 1 0 0 1-1.39.213l-1.865-1.353a1 1 0 0 1-.203-1.422z",key:"nmifey"}]]);var J=s(56085);let Z={gown:{color:"black",style:"classic",size:"M",fabric:"premium"},cap:{color:"black",style:"traditional",tassel:{color:"gold",style:"classic"}},stole:{enabled:!1,color:"gold",pattern:"plain",text:"",embroidery:!1},accessories:{hood:!1,sash:!1,medal:!1}},B={colors:[{name:"أسود",value:"black",hex:"#000000",category:"classic",popularity:5},{name:"أزرق داكن",value:"navy",hex:"#1e3a8a",category:"classic",popularity:4},{name:"بورجوندي",value:"burgundy",hex:"#7c2d12",category:"premium",popularity:3},{name:"أخضر داكن",value:"forest",hex:"#166534",category:"modern",popularity:2},{name:"بنفسجي",value:"purple",hex:"#7c3aed",category:"modern",popularity:3,isNew:!0},{name:"رمادي",value:"gray",hex:"#4b5563",category:"classic",popularity:3}],tasselColors:[{name:"ذهبي",value:"gold",hex:"#fbbf24",category:"classic",popularity:5},{name:"فضي",value:"silver",hex:"#e5e7eb",category:"premium",popularity:4},{name:"أسود",value:"black",hex:"#000000",category:"classic",popularity:4},{name:"أبيض",value:"white",hex:"#ffffff",category:"classic",popularity:3},{name:"أزرق",value:"blue",hex:"#3b82f6",category:"modern",popularity:2},{name:"أحمر",value:"red",hex:"#ef4444",category:"modern",popularity:2,isNew:!0}],gownStyles:[{name:"كلاسيكي",value:"classic",description:"التصميم التقليدي الأنيق"},{name:"عصري",value:"modern",description:"تصميم معاصر مع لمسات حديثة"},{name:"فاخر",value:"luxury",description:"تصميم راقي مع تفاصيل مميزة"}],fabrics:[{name:"قياسي",value:"standard",price:0},{name:"مميز",value:"premium",price:50},{name:"فاخر",value:"luxury",price:100}],sizes:["XS","S","M","L","XL","XXL"]};function X(){let{t:e}=(0,l.B)(),[a,s]=(0,r.useState)(Z),[d,u]=(0,r.useState)("gown"),[x,m]=(0,r.useState)(299.99),p=(e,a)=>{s(s=>({...s,[e]:{...s[e],...a}}))};return(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",children:[(0,t.jsx)(n.V,{}),(0,t.jsxs)("main",{className:"container mx-auto px-4 py-8",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("h1",{className:"text-4xl font-bold text-gray-900 dark:text-white mb-4 arabic-text",children:"\uD83C\uDFA8 تخصيص زي التخرج"}),(0,t.jsx)("p",{className:"text-xl text-gray-600 dark:text-gray-300 arabic-text",children:"صمم زي التخرج المثالي الذي يعكس شخصيتك"})]}),(0,t.jsxs)("div",{className:"grid lg:grid-cols-3 gap-8",children:[(0,t.jsx)("div",{className:"lg:col-span-1 space-y-6",children:(0,t.jsxs)("div",{className:"sticky top-24",children:[(0,t.jsx)(b,{configuration:a,className:"mb-6"}),(0,t.jsxs)(o.Zp,{children:[(0,t.jsx)(o.aR,{children:(0,t.jsx)(o.ZB,{className:"arabic-text",children:"ملخص السعر"})}),(0,t.jsxs)(o.Wu,{children:[(0,t.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"arabic-text",children:"الثوب الأساسي:"}),(0,t.jsx)("span",{children:"299 درهم"})]}),a.stole.enabled&&(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"arabic-text",children:"الوشاح:"}),(0,t.jsx)("span",{children:"50 درهم"})]}),a.accessories.hood&&(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"arabic-text",children:"غطاء الرأس:"}),(0,t.jsx)("span",{children:"30 درهم"})]})]}),(0,t.jsx)("div",{className:"border-t pt-2",children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-lg font-semibold arabic-text",children:"الإجمالي:"}),(0,t.jsxs)("span",{className:"text-2xl font-bold text-blue-600",children:[x," درهم"]})]})}),(0,t.jsxs)(i.$,{className:"w-full mt-4 arabic-text",children:[(0,t.jsx)(F.A,{className:"h-4 w-4 mr-2"}),"إضافة للسلة"]})]})]}),(0,t.jsx)(S,{designData:a,designName:"تصميم زي التخرج المخصص",onSave:(e,a)=>{console.log("Saving design:",e,a)},onShare:e=>{console.log("Sharing on:",e)}})]})}),(0,t.jsx)("div",{className:"lg:col-span-2",children:(0,t.jsxs)(o.Zp,{children:[(0,t.jsx)(o.aR,{children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)(o.ZB,{className:"arabic-text",children:"خيارات التخصيص"}),(0,t.jsxs)(i.$,{variant:"outline",size:"sm",onClick:()=>{s(Z)},children:[(0,t.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"إعادة تعيين"]})]})}),(0,t.jsx)(o.Wu,{children:(0,t.jsxs)(j.tU,{value:d,onValueChange:u,children:[(0,t.jsxs)(j.j7,{className:"grid w-full grid-cols-4",children:[(0,t.jsxs)(j.Xi,{value:"gown",className:"arabic-text",children:[(0,t.jsx)(O,{className:"h-4 w-4 mr-2"}),"الثوب"]}),(0,t.jsxs)(j.Xi,{value:"cap",className:"arabic-text",children:[(0,t.jsx)(U.A,{className:"h-4 w-4 mr-2"}),"القبعة"]}),(0,t.jsxs)(j.Xi,{value:"stole",className:"arabic-text",children:[(0,t.jsx)(V,{className:"h-4 w-4 mr-2"}),"الوشاح"]}),(0,t.jsxs)(j.Xi,{value:"accessories",className:"arabic-text",children:[(0,t.jsx)(J.A,{className:"h-4 w-4 mr-2"}),"الإكسسوارات"]})]}),(0,t.jsxs)(j.av,{value:"gown",className:"space-y-6 mt-6",children:[(0,t.jsx)(z,{title:"لون الثوب",colors:B.colors,selectedColor:a.gown.color,onColorChange:e=>p("gown",{color:e}),showCategories:!0,showSearch:!0,allowCustom:!0}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(v.J,{className:"arabic-text",children:"نمط الثوب"}),(0,t.jsx)("div",{className:"grid gap-3",children:B.gownStyles.map(e=>(0,t.jsxs)("button",{onClick:()=>p("gown",{style:e.value}),className:`p-4 rounded-lg border-2 text-left transition-colors ${a.gown.style===e.value?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-200 dark:border-gray-700 hover:border-gray-300"}`,children:[(0,t.jsx)("div",{className:"font-medium arabic-text",children:e.name}),(0,t.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:e.description})]},e.value))})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(v.J,{className:"arabic-text",children:"المقاس"}),(0,t.jsxs)(E.l6,{value:a.gown.size,onValueChange:e=>p("gown",{size:e}),children:[(0,t.jsx)(E.bq,{children:(0,t.jsx)(E.yv,{})}),(0,t.jsx)(E.gC,{children:B.sizes.map(e=>(0,t.jsx)(E.eb,{value:e,children:e},e))})]})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(v.J,{className:"arabic-text",children:"نوع القماش"}),(0,t.jsx)("div",{className:"grid gap-3",children:B.fabrics.map(e=>(0,t.jsxs)("button",{onClick:()=>p("gown",{fabric:e.value}),className:`p-4 rounded-lg border-2 flex justify-between items-center transition-colors ${a.gown.fabric===e.value?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-200 dark:border-gray-700 hover:border-gray-300"}`,children:[(0,t.jsx)("span",{className:"font-medium arabic-text",children:e.name}),e.price>0&&(0,t.jsxs)(c.E,{variant:"secondary",children:["+",e.price," درهم"]})]},e.value))})]})]}),(0,t.jsxs)(j.av,{value:"cap",className:"space-y-6 mt-6",children:[(0,t.jsx)(z,{title:"لون القبعة",colors:B.colors,selectedColor:a.cap.color,onColorChange:e=>p("cap",{color:e}),showCategories:!0}),(0,t.jsx)(z,{title:"لون الشرابة",colors:B.tasselColors,selectedColor:a.cap.tassel.color,onColorChange:e=>p("cap",{tassel:{...a.cap.tassel,color:e}}),showCategories:!1})]}),(0,t.jsxs)(j.av,{value:"stole",className:"space-y-6 mt-6",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(T.d,{id:"stole-enabled",checked:a.stole.enabled,onCheckedChange:e=>p("stole",{enabled:e})}),(0,t.jsx)(v.J,{htmlFor:"stole-enabled",className:"arabic-text",children:"إضافة وشاح التخرج"})]}),a.stole.enabled&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(z,{title:"لون الوشاح",colors:B.tasselColors,selectedColor:a.stole.color,onColorChange:e=>p("stole",{color:e}),showCategories:!1}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(T.d,{id:"stole-embroidery",checked:a.stole.embroidery,onCheckedChange:e=>p("stole",{embroidery:e})}),(0,t.jsx)(v.J,{htmlFor:"stole-embroidery",className:"arabic-text",children:"تطريز مخصص (+50 درهم)"})]})]})]}),(0,t.jsx)(j.av,{value:"accessories",className:"space-y-6 mt-6",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(v.J,{className:"arabic-text",children:"غطاء الرأس الأكاديمي"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"للدرجات العليا (+30 درهم)"})]}),(0,t.jsx)(T.d,{checked:a.accessories.hood,onCheckedChange:e=>p("accessories",{hood:e})})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(v.J,{className:"arabic-text",children:"حزام الشرف"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"للمتفوقين (+25 درهم)"})]}),(0,t.jsx)(T.d,{checked:a.accessories.sash,onCheckedChange:e=>p("accessories",{sash:e})})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(v.J,{className:"arabic-text",children:"ميدالية التخرج"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"تذكار مميز (+40 درهم)"})]}),(0,t.jsx)(T.d,{checked:a.accessories.medal,onCheckedChange:e=>p("accessories",{medal:e})})]})]})})]})})]})})]})]})]})}}};var a=require("../../webpack-runtime.js");a.C(e);var s=e=>a(a.s=e),t=a.X(0,[4447,8773,4097,2762,3932,7801],()=>s(88722));module.exports=t})();