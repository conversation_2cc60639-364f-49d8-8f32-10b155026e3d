@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  --primary: 222.2 47.4% 11.2%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96%;
  --secondary-foreground: 222.2 47.4% 11.2%;
  --muted: 210 40% 96%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96%;
  --accent-foreground: 222.2 47.4% 11.2%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 222.2 84% 4.9%;
  --radius: 0.5rem;
}

.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --card: 222.2 84% 4.9%;
  --card-foreground: 210 40% 98%;
  --popover: 222.2 84% 4.9%;
  --popover-foreground: 210 40% 98%;
  --primary: 210 40% 98%;
  --primary-foreground: 222.2 47.4% 11.2%;
  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;
  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;
  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 212.7 26.8% 83.9%;
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground font-cairo;
  }

  /* تحسين عرض النص العربي */
  html[dir="rtl"] {
    font-feature-settings: "liga" 1, "kern" 1;
  }

  /* تحسين المسافات للنص العربي */
  .arabic-text {
    line-height: 1.8;
    letter-spacing: 0.02em;
  }

  /* تحسين Grid Layout للـ RTL */
  html[dir="rtl"] .grid {
    direction: rtl;
  }

  /* تحسين Flexbox للـ RTL */
  html[dir="rtl"] .flex {
    direction: rtl;
  }

  /* تحسين عرض البطاقات والمنتجات */
  html[dir="rtl"] .product-grid,
  html[dir="rtl"] .category-grid {
    direction: rtl;
  }

  /* تحسين عرض الجداول */
  html[dir="rtl"] table {
    direction: rtl;
  }

  /* تحسين عرض الأزرار والعناصر التفاعلية */
  html[dir="rtl"] .btn-group {
    direction: rtl;
  }

  /* تحسين عرض البطاقات في الشبكة */
  html[dir="rtl"] .product-grid > *,
  html[dir="rtl"] .category-grid > * {
    direction: ltr; /* المحتوى الداخلي للبطاقات يبقى LTR */
  }

  /* تحسين عرض النصوص العربية في البطاقات */
  html[dir="rtl"] .product-grid .arabic-text,
  html[dir="rtl"] .category-grid .arabic-text {
    direction: rtl;
    text-align: right;
  }

  /* تحسين عرض الصور والأيقونات */
  html[dir="rtl"] .product-grid img,
  html[dir="rtl"] .category-grid img {
    direction: ltr;
  }

  /* تحسين عرض الأسعار والأرقام */
  html[dir="rtl"] .price,
  html[dir="rtl"] .number {
    direction: ltr;
    text-align: left;
  }

  /* تحسين عرض الجداول في RTL */
  html[dir="rtl"] table th,
  html[dir="rtl"] table td {
    text-align: right;
  }

  /* تحسين عرض الأرقام والأسعار في الجداول */
  html[dir="rtl"] table .price,
  html[dir="rtl"] table .number,
  html[dir="rtl"] table .rating {
    text-align: left;
    direction: ltr;
  }

  /* تحسين عرض الأزرار في الجداول */
  html[dir="rtl"] table .actions {
    direction: ltr;
  }
}

/* Navigation animations */
@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes pulse-scale {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* Smooth transitions for RTL/LTR switching */
html {
  transition: direction 0.3s ease;
}

/* Enhanced focus styles for accessibility */
.focus-visible:focus-visible {
  @apply ring-2 ring-blue-500 ring-offset-2 outline-none;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Dark mode scrollbar */
.dark ::-webkit-scrollbar-track {
  background: #374151;
}

.dark ::-webkit-scrollbar-thumb {
  background: #6b7280;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* تنسيق العملة RTL - العملة من الجهة اليمنى */
.currency-rtl {
  direction: ltr;
  text-align: right;
  unicode-bidi: bidi-override;
}

.currency-rtl::after {
  content: " Dhs";
  margin-left: 0.25rem;
}

/* فئة للأسعار مع تنسيق RTL */
.price-rtl {
  direction: ltr;
  text-align: right;
  display: inline-block;
}

.price-rtl .currency {
  margin-left: 0.25rem;
}

/* تحسين عرض الأرقام والعملة في البيئة العربية */
html[dir="rtl"] .price,
html[dir="rtl"] .currency,
html[dir="rtl"] .amount {
  direction: ltr;
  text-align: right;
  display: inline-block;
}

/* تنسيق خاص للجداول */
html[dir="rtl"] table .price,
html[dir="rtl"] table .currency,
html[dir="rtl"] table .amount {
  text-align: left;
}

/* تنسيق للبطاقات والكروت */
html[dir="rtl"] .card .price,
html[dir="rtl"] .card .currency,
html[dir="rtl"] .card .amount {
  text-align: right;
}

/* Enhanced Animations and Effects for Homepage */
@layer utilities {
  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }

  /* Floating animation */
  .float {
    animation: float 6s ease-in-out infinite;
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-20px);
    }
  }

  /* Pulse glow effect */
  .pulse-glow {
    animation: pulse-glow 2s infinite;
  }

  @keyframes pulse-glow {
    0%, 100% {
      box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
    }
    50% {
      box-shadow: 0 0 40px rgba(59, 130, 246, 0.8);
    }
  }

  /* Slide in from bottom */
  .slide-in-bottom {
    animation: slide-in-bottom 0.8s ease-out;
  }

  @keyframes slide-in-bottom {
    from {
      opacity: 0;
      transform: translateY(50px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Fade in scale */
  .fade-in-scale {
    animation: fade-in-scale 0.6s ease-out;
  }

  @keyframes fade-in-scale {
    from {
      opacity: 0;
      transform: scale(0.9);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  /* Shimmer effect */
  .shimmer {
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
  }

  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }

  /* Hover lift effect */
  .hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .hover-lift:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
  }

  /* Navigation specific animations */
  .nav-item-enter {
    animation: nav-item-enter 0.4s ease-out forwards;
  }

  @keyframes nav-item-enter {
    from {
      opacity: 0;
      transform: translateY(-10px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  .nav-item-exit {
    animation: nav-item-exit 0.3s ease-in forwards;
  }

  @keyframes nav-item-exit {
    from {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
    to {
      opacity: 0;
      transform: translateY(-10px) scale(0.95);
    }
  }

  /* Stagger animation for navigation items */
  .nav-stagger-1 { animation-delay: 0ms; }
  .nav-stagger-2 { animation-delay: 50ms; }
  .nav-stagger-3 { animation-delay: 100ms; }
  .nav-stagger-4 { animation-delay: 150ms; }
  .nav-stagger-5 { animation-delay: 200ms; }
  .nav-stagger-6 { animation-delay: 250ms; }

  /* Enhanced shimmer effect for skeletons */
  .shimmer {
    background: linear-gradient(90deg,
      rgba(255,255,255,0) 0%,
      rgba(255,255,255,0.4) 50%,
      rgba(255,255,255,0) 100%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    position: relative;
    overflow: hidden;
  }

  .dark .shimmer {
    background: linear-gradient(90deg,
      rgba(255,255,255,0) 0%,
      rgba(255,255,255,0.1) 50%,
      rgba(255,255,255,0) 100%);
  }

  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }

  /* Smooth height transition */
  .height-transition {
    transition: height 0.3s ease-in-out, min-height 0.3s ease-in-out;
  }

  /* Navigation container stability */
  .nav-container {
    min-height: 60px;
    transition: all 0.3s ease-in-out;
  }

  /* Mobile navigation slide animation */
  @keyframes slideInFromRight {
    from {
      opacity: 0;
      transform: translateX(20px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  /* Backdrop blur enhanced */
  .backdrop-blur-enhanced {
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
  }

  /* ===== تحسينات التصميم المتجاوب للهواتف والآيباد ===== */

  /* تحسين اللمس والتفاعل */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
    touch-action: manipulation;
  }

  /* تحسين الأزرار للمس */
  .mobile-button {
    min-height: 48px;
    padding: 12px 20px;
    font-size: 16px;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }

  /* تحسين المدخلات للمس */
  .mobile-input {
    min-height: 48px;
    font-size: 16px;
    padding: 12px 16px;
    touch-action: manipulation;
  }

  /* منع التكبير التلقائي في iOS */
  @media screen and (max-width: 768px) {
    input[type="text"],
    input[type="email"],
    input[type="password"],
    input[type="number"],
    input[type="tel"],
    input[type="url"],
    input[type="search"],
    textarea,
    select {
      font-size: 16px !important;
      transform: translateZ(0);
      -webkit-appearance: none;
      border-radius: 0;
    }
  }

  /* تحسين التمرير للهواتف */
  .mobile-scroll {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  /* تحسين الجداول للهواتف */
  .mobile-table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
  }

  .mobile-table {
    min-width: 600px;
    width: 100%;
  }

  /* تحسين البطاقات للهواتف */
  .mobile-card {
    margin-bottom: 16px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
  }

  .mobile-card:active {
    transform: scale(0.98);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  }

  /* تحسين الشبكة للهواتف */
  .mobile-grid {
    display: grid;
    gap: 16px;
  }

  @media (max-width: 640px) {
    .mobile-grid {
      grid-template-columns: 1fr;
    }
  }

  @media (min-width: 641px) and (max-width: 768px) {
    .mobile-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (min-width: 769px) and (max-width: 1024px) {
    .mobile-grid {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  @media (min-width: 1025px) {
    .mobile-grid {
      grid-template-columns: repeat(4, 1fr);
    }
  }

  /* تحسين النصوص للهواتف */
  .mobile-text-sm {
    font-size: 14px;
    line-height: 1.5;
  }

  .mobile-text-base {
    font-size: 16px;
    line-height: 1.6;
  }

  .mobile-text-lg {
    font-size: 18px;
    line-height: 1.6;
  }

  .mobile-text-xl {
    font-size: 20px;
    line-height: 1.5;
  }

  .mobile-text-2xl {
    font-size: 24px;
    line-height: 1.4;
  }

  .mobile-text-3xl {
    font-size: 28px;
    line-height: 1.3;
  }

  /* تحسين المسافات للهواتف */
  .mobile-spacing {
    padding: 16px;
  }

  .mobile-spacing-sm {
    padding: 12px;
  }

  .mobile-spacing-lg {
    padding: 24px;
  }

  /* تحسين الحاويات للهواتف */
  .mobile-container {
    max-width: 100%;
    padding-left: 16px;
    padding-right: 16px;
    margin-left: auto;
    margin-right: auto;
  }

  @media (min-width: 640px) {
    .mobile-container {
      padding-left: 24px;
      padding-right: 24px;
    }
  }

  @media (min-width: 1024px) {
    .mobile-container {
      padding-left: 32px;
      padding-right: 32px;
    }
  }

  /* تحسين القوائم المنسدلة للهواتف */
  .mobile-dropdown {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    border-radius: 16px 16px 0 0;
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    transform: translateY(100%);
    transition: transform 0.3s ease;
  }

  .mobile-dropdown.open {
    transform: translateY(0);
  }

  .mobile-dropdown-backdrop {
    position: fixed;
    inset: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .mobile-dropdown-backdrop.open {
    opacity: 1;
  }

  /* تحسين النماذج للهواتف */
  .mobile-form {
    padding: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  }

  .mobile-form-group {
    margin-bottom: 20px;
  }

  .mobile-form-label {
    display: block;
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
  }

  .mobile-form-input {
    width: 100%;
    min-height: 48px;
    padding: 12px 16px;
    font-size: 16px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    background: white;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
  }

  .mobile-form-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  /* تحسين الأزرار للهواتف */
  .mobile-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-height: 48px;
    padding: 12px 24px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 8px;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
    user-select: none;
  }

  .mobile-btn:active {
    transform: scale(0.98);
  }

  .mobile-btn-primary {
    background: #3b82f6;
    color: white;
  }

  .mobile-btn-primary:hover {
    background: #2563eb;
  }

  .mobile-btn-secondary {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
  }

  .mobile-btn-secondary:hover {
    background: #e5e7eb;
  }

  /* تحسين التنبيهات للهواتف */
  .mobile-alert {
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 16px;
    font-size: 14px;
    line-height: 1.5;
  }

  .mobile-alert-success {
    background: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
  }

  .mobile-alert-error {
    background: #fef2f2;
    color: #dc2626;
    border: 1px solid #fecaca;
  }

  .mobile-alert-warning {
    background: #fffbeb;
    color: #d97706;
    border: 1px solid #fed7aa;
  }

  .mobile-alert-info {
    background: #eff6ff;
    color: #2563eb;
    border: 1px solid #bfdbfe;
  }

  /* تحسين الشارات للهواتف */
  .mobile-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    font-size: 12px;
    font-weight: 600;
    border-radius: 6px;
    white-space: nowrap;
  }

  .mobile-badge-primary {
    background: #dbeafe;
    color: #1d4ed8;
  }

  .mobile-badge-success {
    background: #dcfce7;
    color: #166534;
  }

  .mobile-badge-warning {
    background: #fef3c7;
    color: #d97706;
  }

  .mobile-badge-error {
    background: #fee2e2;
    color: #dc2626;
  }

  /* تحسين التحميل للهواتف */
  .mobile-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
  }

  .mobile-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #f3f4f6;
    border-top: 3px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  /* تحسين الفواصل للهواتف */
  .mobile-divider {
    height: 1px;
    background: #e5e7eb;
    margin: 20px 0;
  }

  .mobile-divider-vertical {
    width: 1px;
    background: #e5e7eb;
    margin: 0 16px;
  }

  /* Gradient border animation */
  .gradient-border {
    position: relative;
    background: linear-gradient(45deg, #667eea, #764ba2, #667eea);
    background-size: 200% 200%;
    animation: gradient-border 3s ease infinite;
  }

  @keyframes gradient-border {
    0%, 100% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
  }

  /* Smooth transitions */
  .transition-smooth {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Loading spinner */
  .spinner {
    animation: spin 1s linear infinite;
  }

  /* Line clamp utilities */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}
