(()=>{var e={};e.id=7504,e.ids=[7504],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6710:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{AD:()=>d,P:()=>o,Rn:()=>n,wU:()=>c});var s=r(64939),i=e([s]);let u=new(s=(i.then?(await i)():i)[0]).Pool({user:process.env.POSTGRES_USER||"postgres",host:process.env.POSTGRES_HOST||"localhost",database:process.env.POSTGRES_DB||"graduation_platform",password:process.env.POSTGRES_PASSWORD||"password",port:parseInt(process.env.POSTGRES_PORT||"5432"),max:20,idleTimeoutMillis:3e4,connectionTimeoutMillis:2e3,ssl:{rejectUnauthorized:!1}});async function E(){try{return await u.connect()}catch(e){throw console.error("خطأ في الاتصال بقاعدة البيانات:",e),Error("فشل في الاتصال بقاعدة البيانات")}}async function o(e,t){let r=await E();try{Date.now();let a=await r.query(e,t);return Date.now(),a}catch(e){throw console.error("خطأ في تنفيذ الاستعلام:",e),e}finally{r.release()}}async function n(e){let t=await E();try{await t.query("BEGIN");let r=await e(t);return await t.query("COMMIT"),r}catch(e){throw await t.query("ROLLBACK"),e}finally{t.release()}}async function d(){try{return(await o("SELECT NOW() as current_time")).rows.length>0}catch(e){return console.error("فشل في فحص حالة قاعدة البيانات:",e),!1}}async function c(){try{console.log("بدء تهيئة قاعدة البيانات..."),await o(`
      CREATE TABLE IF NOT EXISTS users (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        first_name VARCHAR(100) NOT NULL,
        last_name VARCHAR(100) NOT NULL,
        phone VARCHAR(20),
        role VARCHAR(20) DEFAULT 'customer' CHECK (role IN ('admin', 'customer', 'school', 'delivery')),
        is_active BOOLEAN DEFAULT true,
        email_verified BOOLEAN DEFAULT false,
        profile_image TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `),await o(`
      CREATE TABLE IF NOT EXISTS categories (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name_ar VARCHAR(100) NOT NULL,
        name_en VARCHAR(100),
        name_fr VARCHAR(100),
        slug VARCHAR(100) UNIQUE NOT NULL,
        description TEXT,
        icon VARCHAR(50),
        parent_id UUID REFERENCES categories(id) ON DELETE CASCADE,
        order_index INTEGER DEFAULT 0,
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `),await o(`
      CREATE TABLE IF NOT EXISTS products (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name VARCHAR(255) NOT NULL,
        description TEXT,
        category_id UUID REFERENCES categories(id) ON DELETE SET NULL,
        price DECIMAL(10,2) NOT NULL,
        rental_price DECIMAL(10,2),
        colors TEXT[] DEFAULT '{}',
        sizes TEXT[] DEFAULT '{}',
        images TEXT[] DEFAULT '{}',
        stock_quantity INTEGER DEFAULT 0,
        is_available BOOLEAN DEFAULT true,
        is_published BOOLEAN DEFAULT true,
        features TEXT[] DEFAULT '{}',
        specifications JSONB DEFAULT '{}',
        rating DECIMAL(3,2) DEFAULT 0,
        reviews_count INTEGER DEFAULT 0,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `),await o(`
      CREATE TABLE IF NOT EXISTS schools (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        admin_id UUID REFERENCES users(id) ON DELETE CASCADE,
        name VARCHAR(255) NOT NULL,
        name_en VARCHAR(255),
        name_fr VARCHAR(255),
        address TEXT,
        city VARCHAR(100),
        phone VARCHAR(20),
        email VARCHAR(255),
        website VARCHAR(255),
        logo_url TEXT,
        graduation_date DATE,
        student_count INTEGER DEFAULT 0,
        is_active BOOLEAN DEFAULT true,
        settings JSONB DEFAULT '{}',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `),await o(`
      CREATE TABLE IF NOT EXISTS orders (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID REFERENCES users(id) ON DELETE CASCADE,
        school_id UUID REFERENCES schools(id) ON DELETE SET NULL,
        order_number VARCHAR(50) UNIQUE NOT NULL,
        status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled')),
        total_amount DECIMAL(10,2) NOT NULL,
        shipping_amount DECIMAL(10,2) DEFAULT 0,
        tax_amount DECIMAL(10,2) DEFAULT 0,
        discount_amount DECIMAL(10,2) DEFAULT 0,
        payment_method VARCHAR(50),
        payment_status VARCHAR(20) DEFAULT 'pending' CHECK (payment_status IN ('pending', 'paid', 'failed', 'refunded')),
        shipping_address JSONB,
        billing_address JSONB,
        notes TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `),await o(`
      CREATE TABLE IF NOT EXISTS order_items (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
        product_id UUID REFERENCES products(id) ON DELETE CASCADE,
        quantity INTEGER NOT NULL,
        unit_price DECIMAL(10,2) NOT NULL,
        total_price DECIMAL(10,2) NOT NULL,
        type VARCHAR(20) DEFAULT 'purchase' CHECK (type IN ('purchase', 'rental')),
        size VARCHAR(50),
        color VARCHAR(50),
        customizations JSONB DEFAULT '{}',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `),await o(`
      CREATE TABLE IF NOT EXISTS reviews (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID REFERENCES users(id) ON DELETE CASCADE,
        product_id UUID REFERENCES products(id) ON DELETE CASCADE,
        order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
        rating INTEGER CHECK (rating >= 1 AND rating <= 5),
        comment TEXT,
        images TEXT[] DEFAULT '{}',
        is_verified BOOLEAN DEFAULT false,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(user_id, product_id, order_id)
      )
    `),await o(`
      CREATE TABLE IF NOT EXISTS menu_items (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        title_ar VARCHAR(100) NOT NULL,
        title_en VARCHAR(100),
        title_fr VARCHAR(100),
        slug VARCHAR(100) NOT NULL,
        icon VARCHAR(50),
        parent_id UUID REFERENCES menu_items(id) ON DELETE CASCADE,
        order_index INTEGER DEFAULT 0,
        is_active BOOLEAN DEFAULT true,
        target_type VARCHAR(20) DEFAULT 'internal' CHECK (target_type IN ('internal', 'external', 'page')),
        target_value VARCHAR(255),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `),await o("CREATE INDEX IF NOT EXISTS idx_products_category ON products(category_id)"),await o("CREATE INDEX IF NOT EXISTS idx_products_published ON products(is_published)"),await o("CREATE INDEX IF NOT EXISTS idx_products_available ON products(is_available)"),await o("CREATE INDEX IF NOT EXISTS idx_orders_user ON orders(user_id)"),await o("CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status)"),await o("CREATE INDEX IF NOT EXISTS idx_order_items_order ON order_items(order_id)"),await o("CREATE INDEX IF NOT EXISTS idx_reviews_product ON reviews(product_id)"),await o("CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)"),await o("CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)"),console.log("تم تهيئة قاعدة البيانات بنجاح!")}catch(e){throw console.error("خطأ في تهيئة قاعدة البيانات:",e),e}}a()}catch(e){a(e)}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},21253:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{L:()=>E});var s=r(6710),i=e([s]);s=(i.then?(await i)():i)[0];class E{static async getAll(e={}){let t=[],r=[],a=1;e.category&&(t.push(`category_id = $${a}`),r.push(e.category),a++),void 0!==e.available&&(t.push(`is_available = $${a}`),r.push(e.available),a++),void 0!==e.published&&(t.push(`is_published = $${a}`),r.push(e.published),a++),e.search&&(t.push(`(name ILIKE $${a} OR description ILIKE $${a})`),r.push(`%${e.search}%`),a++),void 0!==e.minPrice&&(t.push(`price >= $${a}`),r.push(e.minPrice),a++),void 0!==e.maxPrice&&(t.push(`price <= $${a}`),r.push(e.maxPrice),a++);let i=t.length>0?`WHERE ${t.join(" AND ")}`:"",E=e.sortBy||"created_at",o=e.sortOrder||"DESC",n=`ORDER BY ${E} ${o}`,d=e.limit||50,c=e.offset||0,u=`LIMIT $${a} OFFSET $${a+1}`;r.push(d,c);let T=`SELECT COUNT(*) as total FROM products ${i}`,A=await (0,s.P)(T,r.slice(0,-2)),p=parseInt(A.rows[0].total),l=`
      SELECT 
        id, name, description, category_id, price, rental_price,
        colors, sizes, images, stock_quantity, is_available, is_published,
        features, specifications, rating, reviews_count, created_at, updated_at
      FROM products 
      ${i} 
      ${n} 
      ${u}
    `;return{products:(await (0,s.P)(l,r)).rows,total:p}}static async getById(e){return(await (0,s.P)("SELECT * FROM products WHERE id = $1",[e])).rows[0]||null}static async create(e){return(await (0,s.P)(`
      INSERT INTO products (
        name, description, category_id, price, rental_price,
        colors, sizes, images, stock_quantity, is_available, is_published,
        features, specifications, rating, reviews_count
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15
      ) RETURNING *
    `,[e.name,e.description,e.category_id,e.price,e.rental_price,e.colors,e.sizes,e.images,e.stock_quantity,e.is_available,e.is_published,e.features,JSON.stringify(e.specifications),e.rating||0,e.reviews_count||0])).rows[0]}static async update(e,t){let r=[],a=[],i=1;if(Object.entries(t).forEach(([e,t])=>{"id"!==e&&"created_at"!==e&&void 0!==t&&("specifications"===e?(r.push(`${e} = $${i}`),a.push(JSON.stringify(t))):(r.push(`${e} = $${i}`),a.push(t)),i++)}),0===r.length)throw Error("لا توجد حقول للتحديث");return r.push("updated_at = NOW()"),a.push(e),(await (0,s.P)(`
      UPDATE products 
      SET ${r.join(", ")} 
      WHERE id = $${i} 
      RETURNING *
    `,a)).rows[0]||null}static async delete(e){return(await (0,s.P)("DELETE FROM products WHERE id = $1",[e])).rowCount>0}static async updateRating(e){await (0,s.P)(`
      UPDATE products 
      SET 
        rating = (
          SELECT COALESCE(AVG(rating), 0) 
          FROM reviews 
          WHERE product_id = $1
        ),
        reviews_count = (
          SELECT COUNT(*) 
          FROM reviews 
          WHERE product_id = $1
        ),
        updated_at = NOW()
      WHERE id = $1
    `,[e])}static async updateStock(e,t){return await (0,s.Rn)(async r=>{let a=await r.query("SELECT stock_quantity FROM products WHERE id = $1 FOR UPDATE",[e]);if(0===a.rows.length)throw Error("المنتج غير موجود");let s=a.rows[0].stock_quantity+t;if(s<0)throw Error("المخزون غير كافي");return(await r.query("UPDATE products SET stock_quantity = $1, updated_at = NOW() WHERE id = $2",[s,e])).rowCount>0})}static async search(e,t=20){return(await (0,s.P)(`
      SELECT * FROM products 
      WHERE 
        is_published = true 
        AND is_available = true 
        AND (
          name ILIKE $1 
          OR description ILIKE $1 
          OR $2 = ANY(features)
        )
      ORDER BY 
        CASE 
          WHEN name ILIKE $1 THEN 1
          WHEN description ILIKE $1 THEN 2
          ELSE 3
        END,
        rating DESC
      LIMIT $3
    `,[`%${e}%`,e,t])).rows}static async getBestSellers(e=10){return(await (0,s.P)(`
      SELECT p.*, COALESCE(SUM(oi.quantity), 0) as total_sold
      FROM products p
      LEFT JOIN order_items oi ON p.id = oi.product_id
      LEFT JOIN orders o ON oi.order_id = o.id
      WHERE p.is_published = true AND p.is_available = true
        AND (o.status IS NULL OR o.status IN ('confirmed', 'processing', 'shipped', 'delivered'))
      GROUP BY p.id
      ORDER BY total_sold DESC, p.rating DESC
      LIMIT $1
    `,[e])).rows}static async getNewProducts(e=10){return(await (0,s.P)(`
      SELECT * FROM products
      WHERE is_published = true AND is_available = true
      ORDER BY created_at DESC
      LIMIT $1
    `,[e])).rows}static async getTopRated(e=10){return(await (0,s.P)(`
      SELECT * FROM products
      WHERE is_published = true AND is_available = true AND rating > 0
      ORDER BY rating DESC, reviews_count DESC
      LIMIT $1
    `,[e])).rows}}a()}catch(e){a(e)}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},42622:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{patchFetch:()=>d,routeModule:()=>c,serverHooks:()=>A,workAsyncStorage:()=>u,workUnitAsyncStorage:()=>T});var s=r(96559),i=r(48088),E=r(37719),o=r(46604),n=e([o]);o=(n.then?(await n)():n)[0];let c=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/products/postgres/route",pathname:"/api/products/postgres",filename:"route",bundlePath:"app/api/products/postgres/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\products\\postgres\\route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:u,workUnitAsyncStorage:T,serverHooks:A}=c;function d(){return(0,E.patchFetch)({workAsyncStorage:u,workUnitAsyncStorage:T})}a()}catch(e){a(e)}})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},46604:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{GET:()=>n,POST:()=>d});var s=r(32190),i=r(21253),E=r(6710),o=e([i,E]);async function n(e){try{if(!await (0,E.AD)())return s.NextResponse.json({error:"قاعدة البيانات غير متاحة"},{status:503});let{searchParams:t}=new URL(e.url),r={category:t.get("category")||void 0,available:"true"===t.get("available")||"false"!==t.get("available")&&void 0,published:"true"===t.get("published")||"false"!==t.get("published")&&void 0,search:t.get("search")||void 0,minPrice:t.get("minPrice")?parseFloat(t.get("minPrice")):void 0,maxPrice:t.get("maxPrice")?parseFloat(t.get("maxPrice")):void 0,limit:t.get("limit")?parseInt(t.get("limit")):50,offset:t.get("offset")?parseInt(t.get("offset")):0,sortBy:t.get("sortBy")||"created_at",sortOrder:t.get("sortOrder")||"DESC"},a=await i.L.getAll(r);return s.NextResponse.json({products:a.products,total:a.total,page:Math.floor(r.offset/r.limit)+1,totalPages:Math.ceil(a.total/r.limit),filters:r,source:"postgresql"})}catch(e){return console.error("Error fetching products from PostgreSQL:",e),s.NextResponse.json({error:"فشل في جلب المنتجات من قاعدة البيانات"},{status:500})}}async function d(e){try{let t=await e.json();if(!t.name||!t.price)return s.NextResponse.json({error:"اسم المنتج والسعر مطلوبان"},{status:400});let r=await i.L.create({name:t.name,description:t.description||"",category_id:t.category_id,price:parseFloat(t.price),rental_price:t.rental_price?parseFloat(t.rental_price):void 0,colors:Array.isArray(t.colors)?t.colors:[],sizes:Array.isArray(t.sizes)?t.sizes:[],images:Array.isArray(t.images)?t.images:[],stock_quantity:parseInt(t.stock_quantity)||0,is_available:t.is_available??!0,is_published:t.is_published??!0,features:Array.isArray(t.features)?t.features:[],specifications:t.specifications||{},rating:0,reviews_count:0});return s.NextResponse.json({message:"تم إنشاء المنتج بنجاح",product:r},{status:201})}catch(e){return console.error("Error creating product:",e),s.NextResponse.json({error:"فشل في إنشاء المنتج"},{status:500})}}[i,E]=o.then?(await o)():o,a()}catch(e){a(e)}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:e=>{"use strict";e.exports=import("pg")},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,580],()=>r(42622));module.exports=a})();