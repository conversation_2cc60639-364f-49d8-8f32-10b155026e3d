"use client"

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { Navigation } from '@/components/Navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Input } from '@/components/ui/input'
import Link from 'next/link'
import { QuickAdminActions } from '@/components/admin/QuickAdminActions'
import { AdminStatsCards } from '@/components/admin/AdminStatsCards'
import { AdminQuickNav } from '@/components/admin/AdminQuickNav'
import { AdminDashboardHeader } from '@/components/admin/AdminDashboardHeader'
import {
  Shield,
  Users,
  ShoppingCart,
  TrendingUp,
  Calendar,
  FileText,
  Download,
  Upload,
  Plus,
  Eye,
  Edit,
  Search,
  Filter,
  BarChart3,
  PieChart,
  DollarSign,
  Package,
  Settings,
  AlertTriangle,
  CheckCircle,
  Clock,
  School,
  Truck,
  Menu,
  ArrowRight,
  Building,
  User,
  Folder,
  Brain,
  Wand2,
  Layout,
  Palette,
  Crown,
  Badge as BadgeIcon,
  Activity,
  Star,
  Heart,
  MessageSquare,
  CreditCard
} from 'lucide-react'

// أنواع البيانات
interface AdminStats {
  total_users: number
  total_schools: number
  total_orders: number
  total_revenue: number
  monthly_growth: number
  active_deliveries: number
}

interface SystemAlert {
  id: string
  type: 'error' | 'warning' | 'info' | 'success'
  title: string
  message: string
  timestamp: string
  resolved: boolean
}

interface RecentActivity {
  id: string
  type: 'order' | 'user' | 'school' | 'system'
  description: string
  timestamp: string
  user?: string
}

export default function AdminDashboard() {
  const { user, profile, loading: authLoading } = useAuth()
  const [activeTab, setActiveTab] = useState('overview')
  const [stats, setStats] = useState<AdminStats>({
    total_users: 0,
    total_schools: 0,
    total_orders: 0,
    total_revenue: 0,
    monthly_growth: 0,
    active_deliveries: 0
  })
  const [alerts, setAlerts] = useState<SystemAlert[]>([])
  const [activities, setActivities] = useState<RecentActivity[]>([])
  const [loading, setLoading] = useState(true)

  // بيانات وهمية للتطوير
  useEffect(() => {
    const mockStats: AdminStats = {
      total_users: 1247,
      total_schools: 45,
      total_orders: 3892,
      total_revenue: 1234567.89,
      monthly_growth: 12.5,
      active_deliveries: 156
    }

    const mockAlerts: SystemAlert[] = [
      {
        id: '1',
        type: 'warning',
        title: 'مخزون منخفض',
        message: 'مخزون أزياء التخرج الكلاسيكية أقل من 50 قطعة',
        timestamp: '2024-01-20T10:30:00Z',
        resolved: false
      },
      {
        id: '2',
        type: 'info',
        title: 'طلب جماعي جديد',
        message: 'مدرسة الأمل قدمت طلب جماعي لـ 120 طالب',
        timestamp: '2024-01-20T09:15:00Z',
        resolved: false
      },
      {
        id: '3',
        type: 'success',
        title: 'تحديث النظام',
        message: 'تم تحديث النظام بنجاح إلى الإصدار 2.1.0',
        timestamp: '2024-01-19T22:00:00Z',
        resolved: true
      }
    ]

    const mockActivities: RecentActivity[] = [
      {
        id: '1',
        type: 'order',
        description: 'طلب جديد من أحمد محمد - زي التخرج الكلاسيكي',
        timestamp: '2024-01-20T11:00:00Z',
        user: 'أحمد محمد'
      },
      {
        id: '2',
        type: 'school',
        description: 'تسجيل مدرسة جديدة - مدرسة النور الثانوية',
        timestamp: '2024-01-20T10:45:00Z'
      },
      {
        id: '3',
        type: 'user',
        description: 'مستخدم جديد انضم للمنصة - فاطمة أحمد',
        timestamp: '2024-01-20T10:30:00Z',
        user: 'فاطمة أحمد'
      },
      {
        id: '4',
        type: 'system',
        description: 'تم إنشاء نسخة احتياطية من قاعدة البيانات',
        timestamp: '2024-01-20T02:00:00Z'
      }
    ]

    setStats(mockStats)
    setAlerts(mockAlerts)
    setActivities(mockActivities)
    setLoading(false)
  }, [])

  const getAlertIcon = (type: SystemAlert['type']) => {
    switch (type) {
      case 'error': return <AlertTriangle className="h-5 w-5 text-red-600" />
      case 'warning': return <AlertTriangle className="h-5 w-5 text-yellow-600" />
      case 'info': return <Clock className="h-5 w-5 text-blue-600" />
      case 'success': return <CheckCircle className="h-5 w-5 text-green-600" />
      default: return <Clock className="h-5 w-5 text-gray-600" />
    }
  }

  const getAlertColor = (type: SystemAlert['type']) => {
    switch (type) {
      case 'error': return 'bg-red-50 border-red-200 dark:bg-red-900/20'
      case 'warning': return 'bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20'
      case 'info': return 'bg-blue-50 border-blue-200 dark:bg-blue-900/20'
      case 'success': return 'bg-green-50 border-green-200 dark:bg-green-900/20'
      default: return 'bg-gray-50 border-gray-200 dark:bg-gray-900/20'
    }
  }

  const getActivityIcon = (type: RecentActivity['type']) => {
    switch (type) {
      case 'order': return <ShoppingCart className="h-4 w-4 text-blue-600" />
      case 'user': return <Users className="h-4 w-4 text-green-600" />
      case 'school': return <School className="h-4 w-4 text-purple-600" />
      case 'system': return <Settings className="h-4 w-4 text-gray-600" />
      default: return <Clock className="h-4 w-4 text-gray-600" />
    }
  }

  if (authLoading || loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-red-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <Navigation />
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <Navigation />

      <main className="mobile-container py-6 sm:py-8">
        {/* Header */}
        <div className="mb-6 sm:mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h1 className="mobile-text-xl sm:text-3xl font-bold text-gray-900 dark:text-white arabic-text flex items-center gap-2">
                <Crown className="h-6 w-6 sm:h-8 sm:w-8 text-yellow-500" />
                لوحة تحكم الإدارة
              </h1>
              <p className="mobile-text-sm sm:text-base text-gray-600 dark:text-gray-300 mt-2 arabic-text">
                إدارة شاملة للمنصة والمستخدمين والطلبات
              </p>
            </div>

            {/* Navigation Links */}
            <div className="flex items-center gap-2 sm:gap-4">
              <Link href="/" className="mobile-btn mobile-btn-secondary text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 flex items-center gap-2 mobile-text-sm transition-colors">
                <span>الصفحة الرئيسية</span>
                <ArrowRight className="h-3 w-3 sm:h-4 sm:w-4" />
              </Link>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <AdminStatsCards stats={stats} />

        {/* Main Content Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-1 sm:gap-0">
            <TabsTrigger value="overview" className="arabic-text mobile-text-sm p-2 sm:p-3">
              <BarChart3 className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
              <span className="hidden sm:inline">نظرة عامة</span>
              <span className="sm:hidden">عامة</span>
            </TabsTrigger>
            <TabsTrigger value="users" className="arabic-text mobile-text-sm p-2 sm:p-3">
              <Users className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
              <span className="hidden sm:inline">المستخدمين</span>
              <span className="sm:hidden">مستخدمين</span>
            </TabsTrigger>
            <TabsTrigger value="orders" className="arabic-text mobile-text-sm p-2 sm:p-3">
              <Package className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
              <span className="hidden sm:inline">الطلبات</span>
              <span className="sm:hidden">طلبات</span>
            </TabsTrigger>
            <TabsTrigger value="analytics" className="arabic-text mobile-text-sm p-2 sm:p-3 hidden sm:flex">
              <PieChart className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
              التحليلات
            </TabsTrigger>
            <TabsTrigger value="settings" className="arabic-text mobile-text-sm p-2 sm:p-3 hidden sm:flex">
              <Settings className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
              الإعدادات
            </TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6 mt-6">
            {/* Quick Actions for Overview */}
            <Card>
              <CardHeader>
                <CardTitle className="arabic-text flex items-center gap-2">
                  <Plus className="h-5 w-5" />
                  الإجراءات السريعة
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <Link href="/dashboard/admin/pages-management">
                    <Button className="w-full justify-start" variant="outline">
                      <FileText className="h-4 w-4 mr-2" />
                      إضافة صفحة جديدة
                    </Button>
                  </Link>
                  <Link href="/dashboard/admin/menu-management">
                    <Button className="w-full justify-start" variant="outline">
                      <Menu className="h-4 w-4 mr-2" />
                      تحرير القائمة
                    </Button>
                  </Link>
                  <Link href="/dashboard/admin/products">
                    <Button className="w-full justify-start" variant="outline">
                      <Package className="h-4 w-4 mr-2" />
                      إضافة منتج
                    </Button>
                  </Link>
                  <Link href="/dashboard/admin/orders">
                    <Button className="w-full justify-start" variant="outline">
                      <ShoppingCart className="h-4 w-4 mr-2" />
                      إدارة الطلبات
                    </Button>
                  </Link>
                  <Link href="/dashboard/admin/categories">
                    <Button className="w-full justify-start" variant="outline">
                      <Folder className="h-4 w-4 mr-2" />
                      إدارة الفئات
                    </Button>
                  </Link>
                  <Link href="/dashboard/admin/users">
                    <Button className="w-full justify-start" variant="outline">
                      <Users className="h-4 w-4 mr-2" />
                      إدارة المستخدمين
                    </Button>
                  </Link>
                  <Link href="/dashboard/admin/ai-models">
                    <Button className="w-full justify-start" variant="outline">
                      <Brain className="h-4 w-4 mr-2" />
                      نماذج الذكاء الاصطناعي
                    </Button>
                  </Link>
                  <Link href="/dashboard/admin/page-builder">
                    <Button className="w-full justify-start" variant="outline">
                      <Wand2 className="h-4 w-4 mr-2" />
                      بناء الصفحات الذكية
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Revenue Chart */}
              <Card>
                <CardHeader>
                  <CardTitle className="arabic-text">الإيرادات الشهرية</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div className="text-center">
                      <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                      <p className="text-gray-500 arabic-text">رسم بياني للإيرادات</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* User Growth Chart */}
              <Card>
                <CardHeader>
                  <CardTitle className="arabic-text">نمو المستخدمين</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div className="text-center">
                      <TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                      <p className="text-gray-500 arabic-text">رسم بياني للنمو</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle className="arabic-text">النشاط الأخير</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {activities.slice(0, 8).map((activity) => (
                    <div key={activity.id} className="flex items-center gap-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                      <div className="w-8 h-8 bg-white dark:bg-gray-700 rounded-full flex items-center justify-center">
                        {getActivityIcon(activity.type)}
                      </div>
                      <div className="flex-1">
                        <p className="text-sm arabic-text">{activity.description}</p>
                        <p className="text-xs text-gray-500 mt-1">
                          {new Date(activity.timestamp).toLocaleString('en-US')}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Users Tab */}
          <TabsContent value="users" className="space-y-6 mt-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* User Stats */}
              <Card>
                <CardHeader>
                  <CardTitle className="arabic-text">إحصائيات المستخدمين</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600 dark:text-gray-400">إجمالي المستخدمين</span>
                      <span className="font-bold">{stats.total_users.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600 dark:text-gray-400">المستخدمين النشطين</span>
                      <span className="font-bold text-green-600">892</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600 dark:text-gray-400">مستخدمين جدد هذا الشهر</span>
                      <span className="font-bold text-blue-600">156</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Quick User Actions */}
              <Card>
                <CardHeader>
                  <CardTitle className="arabic-text">إجراءات سريعة</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Link href="/dashboard/admin/users">
                    <Button className="w-full justify-start" variant="outline">
                      <Users className="h-4 w-4 mr-2" />
                      إدارة المستخدمين
                    </Button>
                  </Link>
                  <Link href="/dashboard/admin/schools">
                    <Button className="w-full justify-start" variant="outline">
                      <School className="h-4 w-4 mr-2" />
                      إدارة المدارس
                    </Button>
                  </Link>
                  <Button className="w-full justify-start" variant="outline">
                    <Plus className="h-4 w-4 mr-2" />
                    إضافة مستخدم جديد
                  </Button>
                </CardContent>
              </Card>

              {/* User Types */}
              <Card>
                <CardHeader>
                  <CardTitle className="arabic-text">أنواع المستخدمين</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm">طلاب</span>
                      <Badge variant="secondary">856</Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">مدارس</span>
                      <Badge variant="secondary">{stats.total_schools}</Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">إداريين</span>
                      <Badge variant="secondary">12</Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">شركاء توصيل</span>
                      <Badge variant="secondary">8</Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Recent Users */}
            <Card>
              <CardHeader>
                <CardTitle className="arabic-text">المستخدمين الجدد</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { name: 'أحمد محمد', email: '<EMAIL>', type: 'طالب', date: '2024-01-20' },
                    { name: 'فاطمة أحمد', email: '<EMAIL>', type: 'طالبة', date: '2024-01-19' },
                    { name: 'مدرسة النور', email: '<EMAIL>', type: 'مدرسة', date: '2024-01-18' },
                    { name: 'يوسف علي', email: '<EMAIL>', type: 'طالب', date: '2024-01-17' }
                  ].map((user, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                          <User className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                        </div>
                        <div>
                          <p className="font-medium arabic-text">{user.name}</p>
                          <p className="text-sm text-gray-500">{user.email}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <Badge variant="outline">{user.type}</Badge>
                        <p className="text-xs text-gray-500 mt-1">{user.date}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Orders Tab */}
          <TabsContent value="orders" className="space-y-6 mt-6">
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
              {/* Order Stats */}
              <Card>
                <CardHeader>
                  <CardTitle className="arabic-text">الطلبات</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-blue-600">{stats.total_orders.toLocaleString()}</div>
                    <p className="text-sm text-gray-500">إجمالي الطلبات</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="arabic-text">الإيرادات</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-600">{(stats.total_revenue / 1000).toFixed(0)}K</div>
                    <p className="text-sm text-gray-500">درهم مغربي</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="arabic-text">قيد التوصيل</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-orange-600">{stats.active_deliveries}</div>
                    <p className="text-sm text-gray-500">طلب نشط</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="arabic-text">النمو الشهري</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-purple-600">+{stats.monthly_growth}%</div>
                    <p className="text-sm text-gray-500">مقارنة بالشهر الماضي</p>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Quick Order Actions */}
            <Card>
              <CardHeader>
                <CardTitle className="arabic-text">إدارة الطلبات</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Link href="/dashboard/admin/orders">
                    <Button className="w-full justify-start" variant="outline">
                      <ShoppingCart className="h-4 w-4 mr-2" />
                      عرض جميع الطلبات
                    </Button>
                  </Link>
                  <Button className="w-full justify-start" variant="outline">
                    <Clock className="h-4 w-4 mr-2" />
                    الطلبات المعلقة
                  </Button>
                  <Button className="w-full justify-start" variant="outline">
                    <CheckCircle className="h-4 w-4 mr-2" />
                    الطلبات المكتملة
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Recent Orders */}
            <Card>
              <CardHeader>
                <CardTitle className="arabic-text">الطلبات الأخيرة</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { id: '#1234', customer: 'أحمد محمد', product: 'زي التخرج الكلاسيكي', amount: '450 Dhs', status: 'قيد التحضير' },
                    { id: '#1235', customer: 'فاطمة أحمد', product: 'طقم التخرج الفاخر', amount: '680 Dhs', status: 'تم الشحن' },
                    { id: '#1236', customer: 'مدرسة النور', product: 'طلب جماعي - 120 قطعة', amount: '54,000 Dhs', status: 'قيد المراجعة' },
                    { id: '#1237', customer: 'يوسف علي', product: 'زي التخرج المميز', amount: '520 Dhs', status: 'مكتمل' }
                  ].map((order, index) => (
                    <div key={index} className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                      <div className="flex items-center gap-4">
                        <div className="w-10 h-10 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                          <ShoppingCart className="h-5 w-5 text-green-600 dark:text-green-400" />
                        </div>
                        <div>
                          <p className="font-medium arabic-text">{order.id} - {order.customer}</p>
                          <p className="text-sm text-gray-500">{order.product}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-bold">{order.amount}</p>
                        <Badge variant={order.status === 'مكتمل' ? 'default' : 'secondary'}>
                          {order.status}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Analytics Tab */}
          <TabsContent value="analytics" className="space-y-6 mt-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Revenue Analytics */}
              <Card>
                <CardHeader>
                  <CardTitle className="arabic-text">تحليل الإيرادات</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div className="text-center">
                      <DollarSign className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                      <p className="text-gray-500 arabic-text">رسم بياني للإيرادات</p>
                      <p className="text-sm text-gray-400 mt-2">سيتم إضافة الرسوم البيانية قريباً</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* User Analytics */}
              <Card>
                <CardHeader>
                  <CardTitle className="arabic-text">تحليل المستخدمين</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div className="text-center">
                      <Users className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                      <p className="text-gray-500 arabic-text">إحصائيات المستخدمين</p>
                      <p className="text-sm text-gray-400 mt-2">تحليل نمو وسلوك المستخدمين</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Product Analytics */}
              <Card>
                <CardHeader>
                  <CardTitle className="arabic-text">تحليل المنتجات</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm">الأكثر مبيعاً</span>
                      <span className="font-medium">زي التخرج الكلاسيكي</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">الأعلى تقييماً</span>
                      <span className="font-medium">طقم التخرج الفاخر</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">الأكثر طلباً</span>
                      <span className="font-medium">زي التخرج المميز</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Performance Metrics */}
              <Card>
                <CardHeader>
                  <CardTitle className="arabic-text">مؤشرات الأداء</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>معدل التحويل</span>
                        <span>3.2%</span>
                      </div>
                      <Progress value={32} className="h-2" />
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>رضا العملاء</span>
                        <span>94%</span>
                      </div>
                      <Progress value={94} className="h-2" />
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>معدل الإرجاع</span>
                        <span>2.1%</span>
                      </div>
                      <Progress value={21} className="h-2" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Settings Tab */}
          <TabsContent value="settings" className="space-y-6 mt-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Content Management */}
              <Card>
                <CardHeader>
                  <CardTitle className="arabic-text">إدارة المحتوى</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Link href="/dashboard/admin/pages-management">
                    <Button className="w-full justify-start" variant="outline">
                      <FileText className="h-4 w-4 mr-2" />
                      إدارة الصفحات
                    </Button>
                  </Link>
                  <Link href="/dashboard/admin/menu-management">
                    <Button className="w-full justify-start" variant="outline">
                      <Menu className="h-4 w-4 mr-2" />
                      إدارة القائمة الرئيسية
                    </Button>
                  </Link>
                  <Link href="/dashboard/admin/users">
                    <Button className="w-full justify-start" variant="outline">
                      <Users className="h-4 w-4 mr-2" />
                      إدارة المستخدمين
                    </Button>
                  </Link>
                  <Link href="/dashboard/admin/products">
                    <Button className="w-full justify-start" variant="outline">
                      <Package className="h-4 w-4 mr-2" />
                      إدارة المنتجات
                    </Button>
                  </Link>
                  <Link href="/dashboard/admin/orders">
                    <Button className="w-full justify-start" variant="outline">
                      <ShoppingCart className="h-4 w-4 mr-2" />
                      إدارة الطلبات
                    </Button>
                  </Link>
                  <Link href="/dashboard/admin/categories">
                    <Button className="w-full justify-start" variant="outline">
                      <Folder className="h-4 w-4 mr-2" />
                      إدارة الفئات
                    </Button>
                  </Link>
                  <Link href="/dashboard/admin/page-builder">
                    <Button className="w-full justify-start" variant="outline">
                      <Wand2 className="h-4 w-4 mr-2" />
                      بناء الصفحات الذكية
                    </Button>
                  </Link>
                </CardContent>
              </Card>

              {/* AI & Advanced Features */}
              <Card>
                <CardHeader>
                  <CardTitle className="arabic-text">الذكاء الاصطناعي والميزات المتقدمة</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Link href="/dashboard/admin/ai-models">
                    <Button className="w-full justify-start" variant="outline">
                      <Brain className="h-4 w-4 mr-2" />
                      إدارة نماذج الذكاء الاصطناعي
                    </Button>
                  </Link>
                  <Link href="/dashboard/admin/page-builder">
                    <Button className="w-full justify-start" variant="outline">
                      <Layout className="h-4 w-4 mr-2" />
                      بناء الصفحات الذكية
                    </Button>
                  </Link>
                  <Button className="w-full justify-start" variant="outline" disabled>
                    <Palette className="h-4 w-4 mr-2" />
                    مولد المحتوى (قريباً)
                  </Button>
                </CardContent>
              </Card>

              {/* User Management */}
              <Card>
                <CardHeader>
                  <CardTitle className="arabic-text">إدارة المستخدمين</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Link href="/dashboard/admin/users">
                    <Button className="w-full justify-start" variant="outline">
                      <Users className="h-4 w-4 mr-2" />
                      إدارة المستخدمين
                    </Button>
                  </Link>
                  <Link href="/dashboard/admin/schools">
                    <Button className="w-full justify-start" variant="outline">
                      <School className="h-4 w-4 mr-2" />
                      إدارة المدارس
                    </Button>
                  </Link>
                  <Link href="/dashboard/admin/delivery">
                    <Button className="w-full justify-start" variant="outline">
                      <Truck className="h-4 w-4 mr-2" />
                      إدارة التوصيل
                    </Button>
                  </Link>
                </CardContent>
              </Card>

              {/* System Settings */}
              <Card>
                <CardHeader>
                  <CardTitle className="arabic-text">إعدادات النظام</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Link href="/dashboard/admin/settings">
                    <Button className="w-full justify-start" variant="outline">
                      <Settings className="h-4 w-4 mr-2" />
                      الإعدادات العامة
                    </Button>
                  </Link>
                  <Link href="/dashboard/admin/checkout-settings">
                    <Button className="w-full justify-start" variant="outline">
                      <ShoppingCart className="h-4 w-4 mr-2" />
                      إعدادات صفحة الدفع
                    </Button>
                  </Link>
                  <Link href="/dashboard/admin/payment-methods">
                    <Button className="w-full justify-start" variant="outline">
                      <CreditCard className="h-4 w-4 mr-2" />
                      إدارة طرق الدفع
                    </Button>
                  </Link>
                  <Button className="w-full justify-start" variant="outline">
                    <Shield className="h-4 w-4 mr-2" />
                    إعدادات الأمان
                  </Button>
                  <Button className="w-full justify-start" variant="outline">
                    <Download className="h-4 w-4 mr-2" />
                    تصدير البيانات
                  </Button>
                  <Button className="w-full justify-start" variant="outline">
                    <Upload className="h-4 w-4 mr-2" />
                    استيراد البيانات
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </main>
    </div>
  )
}
