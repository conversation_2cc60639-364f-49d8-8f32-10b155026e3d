(()=>{var e={};e.id=3419,e.ids=[3419],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3369:(e,s,a)=>{Promise.resolve().then(a.bind(a,34213))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14875:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>C});var t=a(60687),r=a(43210),i=a(63213),l=a(87801),n=a(44493),c=a(29523),d=a(89667),x=a(80013),o=a(96834),m=a(23562),p=a(5336),h=a(88059),u=a(48730),g=a(99270),j=a(93613),b=a(40228),y=a(19080),f=a(97992),N=a(48340),v=a(92200),k=a(31158),w=a(81620),A=a(41550);function C(){let{user:e,profile:s}=(0,i.A)(),[a,C]=(0,r.useState)(""),[q,T]=(0,r.useState)(null),[Z,P]=(0,r.useState)(!1),[D,_]=(0,r.useState)(""),G=async()=>{if(!a.trim())return void _("يرجى إدخال رقم الطلب أو رقم التتبع");P(!0),_(""),setTimeout(()=>{T({orderNumber:a,currentStatus:"في الطريق",estimatedDelivery:"2024-01-22T15:00:00Z",trackingNumber:"TRK-"+a,carrier:"شركة التوصيل السريع",progress:75,events:[{id:"1",status:"تم تأكيد الطلب",description:"تم استلام طلبك وتأكيده بنجاح",location:"مركز المعالجة - دبي",timestamp:"2024-01-20T09:00:00Z",isCompleted:!0},{id:"2",status:"قيد التحضير",description:"جاري تحضير منتجاتك للشحن",location:"مستودع الإنتاج - دبي",timestamp:"2024-01-20T14:30:00Z",isCompleted:!0},{id:"3",status:"تم الشحن",description:"تم شحن طلبك وهو في طريقه إليك",location:"مركز التوزيع - دبي",timestamp:"2024-01-21T08:00:00Z",isCompleted:!0},{id:"4",status:"في الطريق",description:"الطلب في طريقه للتوصيل",location:"مركز التوزيع المحلي - الشارقة",timestamp:"2024-01-21T16:45:00Z",isCompleted:!0},{id:"5",status:"خرج للتوصيل",description:"الطلب مع مندوب التوصيل",location:"في الطريق إلى العنوان",timestamp:"2024-01-22T10:00:00Z",isCompleted:!1},{id:"6",status:"تم التسليم",description:"تم تسليم الطلب بنجاح",location:"عنوان العميل",timestamp:"",isCompleted:!1}],customerInfo:{name:s?.full_name||"أحمد محمد",phone:s?.phone||"+971501234567",address:"شارع الشيخ زايد، دبي، الإمارات العربية المتحدة"},items:[{name:"زي التخرج الكلاسيكي",quantity:1,image:"/api/placeholder/80/80"},{name:"قبعة التخرج المميزة",quantity:1,image:"/api/placeholder/80/80"}]}),P(!1)},1500)},R=(e,s)=>s?(0,t.jsx)(p.A,{className:"h-5 w-5 text-green-600"}):"خرج للتوصيل"===e?(0,t.jsx)(h.A,{className:"h-5 w-5 text-blue-600"}):(0,t.jsx)(u.A,{className:"h-5 w-5 text-gray-400"});return(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",children:[(0,t.jsx)(l.V,{}),(0,t.jsxs)("main",{className:"container mx-auto px-4 py-8",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white arabic-text",children:"تتبع الطلب \uD83D\uDCE6"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-300 mt-2 arabic-text",children:"تابع حالة طلبك ومكان وصوله"})]}),(0,t.jsxs)(n.Zp,{className:"mb-8",children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsx)(n.ZB,{className:"arabic-text",children:"البحث عن طلب"}),(0,t.jsx)(n.BT,{className:"arabic-text",children:"أدخل رقم الطلب أو رقم التتبع لمعرفة حالة طلبك"})]}),(0,t.jsxs)(n.Wu,{children:[(0,t.jsxs)("div",{className:"flex gap-4",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)(x.J,{htmlFor:"tracking",className:"arabic-text",children:"رقم الطلب أو رقم التتبع"}),(0,t.jsx)(d.p,{id:"tracking",placeholder:"مثال: GT-240120-001 أو TRK-123456",value:a,onChange:e=>C(e.target.value),className:"arabic-text"})]}),(0,t.jsx)("div",{className:"flex items-end",children:(0,t.jsxs)(c.$,{onClick:G,disabled:Z,children:[Z?(0,t.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}):(0,t.jsx)(g.A,{className:"h-4 w-4 mr-2"}),(0,t.jsx)("span",{className:"arabic-text",children:"تتبع"})]})})]}),D&&(0,t.jsxs)("div",{className:"mt-3 flex items-center gap-2 text-red-600",children:[(0,t.jsx)(j.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"text-sm arabic-text",children:D})]})]})]}),q&&(0,t.jsxs)("div",{className:"grid lg:grid-cols-3 gap-8",children:[(0,t.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(n.ZB,{className:"arabic-text",children:"حالة الطلب الحالية"}),(0,t.jsxs)(n.BT,{className:"arabic-text",children:["رقم الطلب: ",q.orderNumber]})]}),(0,t.jsx)(o.E,{className:(e=>{switch(e){case"تم تأكيد الطلب":return"bg-blue-100 text-blue-800";case"قيد التحضير":return"bg-yellow-100 text-yellow-800";case"تم الشحن":return"bg-purple-100 text-purple-800";case"في الطريق":return"bg-orange-100 text-orange-800";case"خرج للتوصيل":case"تم التسليم":return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}})(q.currentStatus),children:q.currentStatus})]})}),(0,t.jsx)(n.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,t.jsx)("span",{className:"text-sm font-medium arabic-text",children:"تقدم الطلب"}),(0,t.jsxs)("span",{className:"text-sm text-gray-600",children:[q.progress,"%"]})]}),(0,t.jsx)(m.k,{value:q.progress,className:"h-3"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(b.A,{className:"h-5 w-5 text-gray-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"التوصيل المتوقع"}),(0,t.jsx)("p",{className:"font-medium",children:new Date(q.estimatedDelivery).toLocaleDateString("en-US")})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(h.A,{className:"h-5 w-5 text-gray-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"شركة الشحن"}),(0,t.jsx)("p",{className:"font-medium arabic-text",children:q.carrier})]})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(y.A,{className:"h-5 w-5 text-gray-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"رقم التتبع"}),(0,t.jsx)("p",{className:"font-medium font-mono",children:q.trackingNumber})]})]})]})})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsx)(n.ZB,{className:"arabic-text",children:"تاريخ الطلب"}),(0,t.jsx)(n.BT,{className:"arabic-text",children:"تتبع مراحل طلبك من التأكيد حتى التسليم"})]}),(0,t.jsx)(n.Wu,{children:(0,t.jsx)("div",{className:"space-y-6",children:q.events.map((e,s)=>(0,t.jsxs)("div",{className:"flex gap-4",children:[(0,t.jsxs)("div",{className:"flex flex-col items-center",children:[(0,t.jsx)("div",{className:`w-10 h-10 rounded-full border-2 flex items-center justify-center ${e.isCompleted?"bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800":"خرج للتوصيل"===e.status?"bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-800":"bg-gray-50 border-gray-200 dark:bg-gray-800 dark:border-gray-700"}`,children:R(e.status,e.isCompleted)}),s<q.events.length-1&&(0,t.jsx)("div",{className:`w-0.5 h-12 mt-2 ${e.isCompleted?"bg-green-200":"bg-gray-200"}`})]}),(0,t.jsxs)("div",{className:"flex-1 pb-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-start mb-1",children:[(0,t.jsx)("h3",{className:`font-medium arabic-text ${e.isCompleted?"text-gray-900 dark:text-white":"text-gray-500"}`,children:e.status}),e.timestamp&&(0,t.jsx)("span",{className:"text-sm text-gray-500",children:new Date(e.timestamp).toLocaleString("en-US")})]}),(0,t.jsx)("p",{className:`text-sm arabic-text ${e.isCompleted?"text-gray-600 dark:text-gray-400":"text-gray-400"}`,children:e.description}),(0,t.jsxs)("div",{className:"flex items-center gap-1 mt-1",children:[(0,t.jsx)(f.A,{className:"h-3 w-3 text-gray-400"}),(0,t.jsx)("span",{className:`text-xs arabic-text ${e.isCompleted?"text-gray-500":"text-gray-400"}`,children:e.location})]})]})]},e.id))})})]})]}),(0,t.jsxs)("div",{className:"lg:col-span-1 space-y-6",children:[(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsx)(n.ZB,{className:"arabic-text",children:"منتجات الطلب"})}),(0,t.jsx)(n.Wu,{children:(0,t.jsx)("div",{className:"space-y-3",children:q.items.map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-gray-100 dark:bg-gray-800 rounded-lg flex-shrink-0",children:(0,t.jsx)("div",{className:"w-full h-full bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900 dark:to-purple-900 rounded-lg"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium text-sm arabic-text",children:e.name}),(0,t.jsxs)("p",{className:"text-xs text-gray-600 dark:text-gray-400 arabic-text",children:["الكمية: ",e.quantity]})]})]},s))})})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsx)(n.ZB,{className:"arabic-text",children:"عنوان التوصيل"})}),(0,t.jsx)(n.Wu,{children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(f.A,{className:"h-4 w-4 text-gray-400"}),(0,t.jsx)("span",{className:"text-sm arabic-text",children:q.customerInfo.address})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(N.A,{className:"h-4 w-4 text-gray-400"}),(0,t.jsx)("span",{className:"text-sm",children:q.customerInfo.phone})]})]})})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsx)(n.ZB,{className:"arabic-text",children:"إجراءات سريعة"})}),(0,t.jsxs)(n.Wu,{className:"space-y-3",children:[(0,t.jsxs)(c.$,{variant:"outline",className:"w-full arabic-text",children:[(0,t.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"تتبع على الخريطة"]}),(0,t.jsxs)(c.$,{variant:"outline",className:"w-full arabic-text",children:[(0,t.jsx)(k.A,{className:"h-4 w-4 mr-2"}),"تحميل تفاصيل التتبع"]}),(0,t.jsxs)(c.$,{variant:"outline",className:"w-full arabic-text",children:[(0,t.jsx)(w.A,{className:"h-4 w-4 mr-2"}),"مشاركة حالة الطلب"]})]})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsx)(n.ZB,{className:"arabic-text",children:"تحتاج مساعدة؟"})}),(0,t.jsxs)(n.Wu,{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(N.A,{className:"h-4 w-4 text-blue-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium arabic-text",children:"اتصل بنا"}),(0,t.jsx)("p",{className:"text-xs text-gray-600 dark:text-gray-400",children:"+971 4 123 4567"})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(A.A,{className:"h-4 w-4 text-blue-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium arabic-text",children:"راسلنا"}),(0,t.jsx)("p",{className:"text-xs text-gray-600 dark:text-gray-400",children:"<EMAIL>"})]})]}),(0,t.jsx)(c.$,{variant:"outline",className:"w-full arabic-text",children:"تواصل مع الدعم"})]})]})]})]}),!q&&!Z&&a&&(0,t.jsx)(n.Zp,{children:(0,t.jsxs)(n.Wu,{className:"text-center py-12",children:[(0,t.jsx)(y.A,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2 arabic-text",children:"لم يتم العثور على الطلب"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-6 arabic-text",children:"تأكد من رقم الطلب أو رقم التتبع وحاول مرة أخرى"}),(0,t.jsxs)("div",{className:"flex justify-center gap-4",children:[(0,t.jsxs)(c.$,{variant:"outline",className:"arabic-text",children:[(0,t.jsx)(N.A,{className:"h-4 w-4 mr-2"}),"اتصل بالدعم"]}),(0,t.jsx)(c.$,{className:"arabic-text",asChild:!0,children:(0,t.jsx)("a",{href:"/dashboard/student",children:"لوحة التحكم"})})]})]})})]})]})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23562:(e,s,a)=>{"use strict";a.d(s,{k:()=>l});var t=a(60687);a(43210);var r=a(25177),i=a(4780);function l({className:e,value:s,...a}){return(0,t.jsx)(r.bL,{"data-slot":"progress",className:(0,i.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",e),...a,children:(0,t.jsx)(r.C1,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:`translateX(-${100-(s||0)}%)`}})})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31158:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},33873:e=>{"use strict";e.exports=require("path")},34213:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t});let t=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Graduation Toqs\\\\frontend\\\\src\\\\app\\\\track-order\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\track-order\\page.tsx","default")},41550:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78148:(e,s,a)=>{"use strict";a.d(s,{b:()=>n});var t=a(43210),r=a(14163),i=a(60687),l=t.forwardRef((e,s)=>(0,i.jsx)(r.sG.label,{...e,ref:s,onMouseDown:s=>{s.target.closest("button, input, select, textarea")||(e.onMouseDown?.(s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));l.displayName="Label";var n=l},78272:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},79551:e=>{"use strict";e.exports=require("url")},80013:(e,s,a)=>{"use strict";a.d(s,{J:()=>l});var t=a(60687);a(43210);var r=a(78148),i=a(4780);function l({className:e,...s}){return(0,t.jsx)(r.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...s})}},81620:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("share-2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]])},89530:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>l.a,__next_app__:()=>o,pages:()=>x,routeModule:()=>m,tree:()=>d});var t=a(65239),r=a(48088),i=a(88170),l=a.n(i),n=a(30893),c={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);a.d(s,c);let d={children:["",{children:["track-order",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,34213)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\track-order\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(a.bind(a,54431)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,54413)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,x=["C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\track-order\\page.tsx"],o={require:a,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/track-order/page",pathname:"/track-order",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},92200:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("navigation",[["polygon",{points:"3 11 22 2 13 21 11 13 3 11",key:"1ltx0t"}]])},93613:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},93641:(e,s,a)=>{Promise.resolve().then(a.bind(a,14875))},97992:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])}};var s=require("../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[4447,8773,6126,3932,7801],()=>a(89530));module.exports=t})();