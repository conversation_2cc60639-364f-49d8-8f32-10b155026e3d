(()=>{var e={};e.id=318,e.ids=[318],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6710:(e,t,a)=>{"use strict";a.a(e,async(e,r)=>{try{a.d(t,{AD:()=>T,P:()=>o,Rn:()=>n,wU:()=>d});var s=a(64939),i=e([s]);let u=new(s=(i.then?(await i)():i)[0]).Pool({user:process.env.POSTGRES_USER||"postgres",host:process.env.POSTGRES_HOST||"localhost",database:process.env.POSTGRES_DB||"graduation_platform",password:process.env.POSTGRES_PASSWORD||"password",port:parseInt(process.env.POSTGRES_PORT||"5432"),max:20,idleTimeoutMillis:3e4,connectionTimeoutMillis:2e3,ssl:{rejectUnauthorized:!1}});async function E(){try{return await u.connect()}catch(e){throw console.error("خطأ في الاتصال بقاعدة البيانات:",e),Error("فشل في الاتصال بقاعدة البيانات")}}async function o(e,t){let a=await E();try{Date.now();let r=await a.query(e,t);return Date.now(),r}catch(e){throw console.error("خطأ في تنفيذ الاستعلام:",e),e}finally{a.release()}}async function n(e){let t=await E();try{await t.query("BEGIN");let a=await e(t);return await t.query("COMMIT"),a}catch(e){throw await t.query("ROLLBACK"),e}finally{t.release()}}async function T(){try{return(await o("SELECT NOW() as current_time")).rows.length>0}catch(e){return console.error("فشل في فحص حالة قاعدة البيانات:",e),!1}}async function d(){try{console.log("بدء تهيئة قاعدة البيانات..."),await o(`
      CREATE TABLE IF NOT EXISTS users (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        first_name VARCHAR(100) NOT NULL,
        last_name VARCHAR(100) NOT NULL,
        phone VARCHAR(20),
        role VARCHAR(20) DEFAULT 'customer' CHECK (role IN ('admin', 'customer', 'school', 'delivery')),
        is_active BOOLEAN DEFAULT true,
        email_verified BOOLEAN DEFAULT false,
        profile_image TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `),await o(`
      CREATE TABLE IF NOT EXISTS categories (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name_ar VARCHAR(100) NOT NULL,
        name_en VARCHAR(100),
        name_fr VARCHAR(100),
        slug VARCHAR(100) UNIQUE NOT NULL,
        description TEXT,
        icon VARCHAR(50),
        parent_id UUID REFERENCES categories(id) ON DELETE CASCADE,
        order_index INTEGER DEFAULT 0,
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `),await o(`
      CREATE TABLE IF NOT EXISTS products (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name VARCHAR(255) NOT NULL,
        description TEXT,
        category_id UUID REFERENCES categories(id) ON DELETE SET NULL,
        price DECIMAL(10,2) NOT NULL,
        rental_price DECIMAL(10,2),
        colors TEXT[] DEFAULT '{}',
        sizes TEXT[] DEFAULT '{}',
        images TEXT[] DEFAULT '{}',
        stock_quantity INTEGER DEFAULT 0,
        is_available BOOLEAN DEFAULT true,
        is_published BOOLEAN DEFAULT true,
        features TEXT[] DEFAULT '{}',
        specifications JSONB DEFAULT '{}',
        rating DECIMAL(3,2) DEFAULT 0,
        reviews_count INTEGER DEFAULT 0,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `),await o(`
      CREATE TABLE IF NOT EXISTS schools (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        admin_id UUID REFERENCES users(id) ON DELETE CASCADE,
        name VARCHAR(255) NOT NULL,
        name_en VARCHAR(255),
        name_fr VARCHAR(255),
        address TEXT,
        city VARCHAR(100),
        phone VARCHAR(20),
        email VARCHAR(255),
        website VARCHAR(255),
        logo_url TEXT,
        graduation_date DATE,
        student_count INTEGER DEFAULT 0,
        is_active BOOLEAN DEFAULT true,
        settings JSONB DEFAULT '{}',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `),await o(`
      CREATE TABLE IF NOT EXISTS orders (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID REFERENCES users(id) ON DELETE CASCADE,
        school_id UUID REFERENCES schools(id) ON DELETE SET NULL,
        order_number VARCHAR(50) UNIQUE NOT NULL,
        status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled')),
        total_amount DECIMAL(10,2) NOT NULL,
        shipping_amount DECIMAL(10,2) DEFAULT 0,
        tax_amount DECIMAL(10,2) DEFAULT 0,
        discount_amount DECIMAL(10,2) DEFAULT 0,
        payment_method VARCHAR(50),
        payment_status VARCHAR(20) DEFAULT 'pending' CHECK (payment_status IN ('pending', 'paid', 'failed', 'refunded')),
        shipping_address JSONB,
        billing_address JSONB,
        notes TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `),await o(`
      CREATE TABLE IF NOT EXISTS order_items (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
        product_id UUID REFERENCES products(id) ON DELETE CASCADE,
        quantity INTEGER NOT NULL,
        unit_price DECIMAL(10,2) NOT NULL,
        total_price DECIMAL(10,2) NOT NULL,
        type VARCHAR(20) DEFAULT 'purchase' CHECK (type IN ('purchase', 'rental')),
        size VARCHAR(50),
        color VARCHAR(50),
        customizations JSONB DEFAULT '{}',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `),await o(`
      CREATE TABLE IF NOT EXISTS reviews (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID REFERENCES users(id) ON DELETE CASCADE,
        product_id UUID REFERENCES products(id) ON DELETE CASCADE,
        order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
        rating INTEGER CHECK (rating >= 1 AND rating <= 5),
        comment TEXT,
        images TEXT[] DEFAULT '{}',
        is_verified BOOLEAN DEFAULT false,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(user_id, product_id, order_id)
      )
    `),await o(`
      CREATE TABLE IF NOT EXISTS menu_items (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        title_ar VARCHAR(100) NOT NULL,
        title_en VARCHAR(100),
        title_fr VARCHAR(100),
        slug VARCHAR(100) NOT NULL,
        icon VARCHAR(50),
        parent_id UUID REFERENCES menu_items(id) ON DELETE CASCADE,
        order_index INTEGER DEFAULT 0,
        is_active BOOLEAN DEFAULT true,
        target_type VARCHAR(20) DEFAULT 'internal' CHECK (target_type IN ('internal', 'external', 'page')),
        target_value VARCHAR(255),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `),await o("CREATE INDEX IF NOT EXISTS idx_products_category ON products(category_id)"),await o("CREATE INDEX IF NOT EXISTS idx_products_published ON products(is_published)"),await o("CREATE INDEX IF NOT EXISTS idx_products_available ON products(is_available)"),await o("CREATE INDEX IF NOT EXISTS idx_orders_user ON orders(user_id)"),await o("CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status)"),await o("CREATE INDEX IF NOT EXISTS idx_order_items_order ON order_items(order_id)"),await o("CREATE INDEX IF NOT EXISTS idx_reviews_product ON reviews(product_id)"),await o("CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)"),await o("CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)"),console.log("تم تهيئة قاعدة البيانات بنجاح!")}catch(e){throw console.error("خطأ في تهيئة قاعدة البيانات:",e),e}}r()}catch(e){r(e)}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},21302:(e,t,a)=>{"use strict";a.a(e,async(e,r)=>{try{a.r(t),a.d(t,{patchFetch:()=>T,routeModule:()=>d,serverHooks:()=>c,workAsyncStorage:()=>u,workUnitAsyncStorage:()=>A});var s=a(96559),i=a(48088),E=a(37719),o=a(59890),n=e([o]);o=(n.then?(await n)():n)[0];let d=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/users/route",pathname:"/api/users",filename:"route",bundlePath:"app/api/users/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\users\\route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:u,workUnitAsyncStorage:A,serverHooks:c}=d;function T(){return(0,E.patchFetch)({workAsyncStorage:u,workUnitAsyncStorage:A})}r()}catch(e){r(e)}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},59890:(e,t,a)=>{"use strict";a.a(e,async(e,r)=>{try{a.r(t),a.d(t,{GET:()=>n,POST:()=>T});var s=a(32190),i=a(63247),E=a(6710),o=e([i,E]);async function n(e){try{if(!await (0,E.AD)())return s.NextResponse.json({error:"قاعدة البيانات غير متاحة"},{status:503});let{searchParams:t}=new URL(e.url),a={role:t.get("role")||void 0,is_active:"true"===t.get("is_active")||"false"!==t.get("is_active")&&void 0,search:t.get("search")||void 0,limit:t.get("limit")?parseInt(t.get("limit")):50,offset:t.get("offset")?parseInt(t.get("offset")):0},r=await i.F.getAll(a);return s.NextResponse.json({users:r.users,total:r.total,page:Math.floor(a.offset/a.limit)+1,totalPages:Math.ceil(r.total/a.limit),filters:a})}catch(e){return console.error("Error fetching users:",e),s.NextResponse.json({error:"فشل في جلب المستخدمين"},{status:500})}}async function T(e){try{let t=await e.json();if(!t.email||!t.password||!t.first_name||!t.last_name)return s.NextResponse.json({error:"البريد الإلكتروني وكلمة المرور والاسم الأول والأخير مطلوبة"},{status:400});if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(t.email))return s.NextResponse.json({error:"البريد الإلكتروني غير صحيح"},{status:400});if(t.password.length<8)return s.NextResponse.json({error:"كلمة المرور يجب أن تكون 8 أحرف على الأقل"},{status:400});if(await i.F.findByEmail(t.email))return s.NextResponse.json({error:"البريد الإلكتروني مستخدم بالفعل"},{status:409});let a=await i.F.create({email:t.email,password:t.password,first_name:t.first_name,last_name:t.last_name,phone:t.phone,role:t.role||"customer"});return s.NextResponse.json({message:"تم إنشاء المستخدم بنجاح",user:a},{status:201})}catch(e){return console.error("Error creating user:",e),s.NextResponse.json({error:"فشل في إنشاء المستخدم"},{status:500})}}[i,E]=o.then?(await o)():o,r()}catch(e){r(e)}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63247:(e,t,a)=>{"use strict";a.a(e,async(e,r)=>{try{a.d(t,{F:()=>n});var s=a(6710),i=a(85665),E=a.n(i),o=e([s]);s=(o.then?(await o)():o)[0];class n{static async create(e){let t=await E().hash(e.password,12);return(await (0,s.P)(`
      INSERT INTO users (
        email, password_hash, first_name, last_name, phone, role
      ) VALUES (
        $1, $2, $3, $4, $5, $6
      ) RETURNING id, email, first_name, last_name, phone, role, is_active, email_verified, profile_image, created_at, updated_at
    `,[e.email.toLowerCase(),t,e.first_name,e.last_name,e.phone,e.role||"customer"])).rows[0]}static async findByEmail(e){return(await (0,s.P)("SELECT * FROM users WHERE email = $1",[e.toLowerCase()])).rows[0]||null}static async findById(e){return(await (0,s.P)(`
      SELECT id, email, first_name, last_name, phone, role, is_active, email_verified, profile_image, created_at, updated_at
      FROM users WHERE id = $1
    `,[e])).rows[0]||null}static async verifyPassword(e,t){let a=await this.findByEmail(e);if(!a||!await E().compare(t,a.password_hash))return null;let{password_hash:r,...s}=a;return s}static async update(e,t){let a=[],r=[],i=1;if(Object.entries(t).forEach(([e,t])=>{void 0!==t&&(a.push(`${e} = $${i}`),r.push(t),i++)}),0===a.length)throw Error("لا توجد حقول للتحديث");return a.push("updated_at = NOW()"),r.push(e),(await (0,s.P)(`
      UPDATE users 
      SET ${a.join(", ")} 
      WHERE id = $${i} 
      RETURNING id, email, first_name, last_name, phone, role, is_active, email_verified, profile_image, created_at, updated_at
    `,r)).rows[0]||null}static async changePassword(e,t){let a=await E().hash(t,12);return(await (0,s.P)("UPDATE users SET password_hash = $1, updated_at = NOW() WHERE id = $2",[a,e])).rowCount>0}static async verifyEmail(e){return(await (0,s.P)("UPDATE users SET email_verified = true, updated_at = NOW() WHERE id = $1",[e])).rowCount>0}static async toggleActive(e){return(await (0,s.P)("UPDATE users SET is_active = NOT is_active, updated_at = NOW() WHERE id = $1",[e])).rowCount>0}static async delete(e){return(await (0,s.P)("DELETE FROM users WHERE id = $1",[e])).rowCount>0}static async getAll(e={}){let t=[],a=[],r=1;e.role&&(t.push(`role = $${r}`),a.push(e.role),r++),void 0!==e.is_active&&(t.push(`is_active = $${r}`),a.push(e.is_active),r++),e.search&&(t.push(`(first_name ILIKE $${r} OR last_name ILIKE $${r} OR email ILIKE $${r})`),a.push(`%${e.search}%`),r++);let i=t.length>0?`WHERE ${t.join(" AND ")}`:"",E=e.limit||50,o=e.offset||0,n=`LIMIT $${r} OFFSET $${r+1}`;a.push(E,o);let T=`SELECT COUNT(*) as total FROM users ${i}`,d=await (0,s.P)(T,a.slice(0,-2)),u=parseInt(d.rows[0].total),A=`
      SELECT id, email, first_name, last_name, phone, role, is_active, email_verified, profile_image, created_at, updated_at
      FROM users 
      ${i} 
      ORDER BY created_at DESC 
      ${n}
    `;return{users:(await (0,s.P)(A,a)).rows,total:u}}static async getStats(){let e=await (0,s.P)(`
      SELECT 
        COUNT(*) as total,
        COUNT(*) FILTER (WHERE is_active = true) as active,
        COUNT(*) FILTER (WHERE email_verified = true) as verified
      FROM users
    `),t=await (0,s.P)(`
      SELECT role, COUNT(*) as count
      FROM users
      GROUP BY role
    `),a={};return t.rows.forEach(e=>{a[e.role]=parseInt(e.count)}),{total:parseInt(e.rows[0].total),active:parseInt(e.rows[0].active),verified:parseInt(e.rows[0].verified),byRole:a}}}r()}catch(e){r(e)}})},64939:e=>{"use strict";e.exports=import("pg")},78335:()=>{},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[4447,580,5665],()=>a(21302));module.exports=r})();