(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5894],{11296:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>_});var r=t(95155),s=t(12115),n=t(30285),i=t(66695),d=t(26126),o=t(62523),l=t(85057),c=t(88539),u=t(80333),x=t(54165),m=t(85127),g=t(44838),f=t(35169),h=t(84616),p=t(5623),v=t(13717),b=t(78749),j=t(92657),y=t(62525),N=t(6874),w=t.n(N);function _(){let[e,a]=(0,s.useState)([]),[t,N]=(0,s.useState)(!0),[_,k]=(0,s.useState)(!1),[C,z]=(0,s.useState)(null),[E,A]=(0,s.useState)({name_ar:"",name_en:"",name_fr:"",slug:"",icon:"",description:"",is_active:!0,order_index:1}),T=async()=>{try{N(!0);let e=await fetch("/api/categories?include_inactive=true");if(e.ok){let t=await e.json();a(t.categories)}}catch(e){console.error("Error fetching categories:",e)}finally{N(!1)}};(0,s.useEffect)(()=>{T()},[]);let F=()=>{A({name_ar:"",name_en:"",name_fr:"",slug:"",icon:"",description:"",is_active:!0,order_index:e.length+1}),z(null)},S=e=>{A({name_ar:e.name_ar,name_en:e.name_en||"",name_fr:e.name_fr||"",slug:e.slug,icon:e.icon||"",description:e.description||"",is_active:e.is_active,order_index:e.order_index}),z(e),k(!0)},J=e=>e.toLowerCase().replace(/[أإآ]/g,"a").replace(/[ة]/g,"h").replace(/[ى]/g,"y").replace(/[ء]/g,"").replace(/\s+/g,"-").replace(/[^\w\-]/g,""),R=e=>{A(a=>({...a,name_ar:e,slug:a.slug||J(e)}))},D=async()=>{try{let e=C?"/api/categories/".concat(C.id):"/api/categories",a=C?"PUT":"POST",t=await fetch(e,{method:a,headers:{"Content-Type":"application/json"},body:JSON.stringify(E)});if(!t.ok){let e=await t.json();throw Error(e.error||"فشل في حفظ الفئة")}await T(),k(!1),F(),alert(C?"تم تحديث الفئة بنجاح!":"تم إضافة الفئة بنجاح!")}catch(e){console.error("Error saving category:",e),alert(e instanceof Error?e.message:"فشل في حفظ الفئة")}},L=async e=>{if(confirm("هل أنت متأكد من حذف هذه الفئة؟"))try{let a=await fetch("/api/categories/".concat(e),{method:"DELETE"});if(!a.ok){let e=await a.json();throw Error(e.error||"فشل في حذف الفئة")}await T(),alert("تم حذف الفئة بنجاح!")}catch(e){console.error("Error deleting category:",e),alert(e instanceof Error?e.message:"فشل في حذف الفئة")}},B=async a=>{let t=e.find(e=>e.id===a);if(t)try{let e=await fetch("/api/categories/".concat(a),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({is_active:!t.is_active})});if(!e.ok){let a=await e.json();throw Error(a.error||"فشل في تحديث حالة الفئة")}await T()}catch(e){console.error("Error toggling category status:",e),alert(e instanceof Error?e.message:"فشل في تحديث حالة الفئة")}};return t?(0,r.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600 arabic-text",children:"جاري تحميل الفئات..."})]})}):(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:(0,r.jsxs)("main",{className:"container mx-auto px-4 py-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("div",{className:"flex items-center gap-4 mb-4",children:(0,r.jsx)(n.$,{variant:"outline",size:"sm",asChild:!0,children:(0,r.jsxs)(w(),{href:"/dashboard/admin",children:[(0,r.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"العودة للوحة التحكم"]})})}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white arabic-text",children:"إدارة الفئات \uD83D\uDCC2"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-300 mt-2 arabic-text",children:"إضافة وتعديل وإدارة فئات المنتجات"})]}),(0,r.jsxs)(x.lG,{open:_,onOpenChange:k,children:[(0,r.jsx)(x.zM,{asChild:!0,children:(0,r.jsxs)(n.$,{onClick:F,children:[(0,r.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"إضافة فئة جديدة"]})}),(0,r.jsxs)(x.Cf,{className:"max-w-2xl",children:[(0,r.jsx)(x.c7,{children:(0,r.jsx)(x.L3,{className:"arabic-text",children:C?"تعديل الفئة":"إضافة فئة جديدة"})}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"category-grid grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(l.J,{htmlFor:"name_ar",className:"arabic-text",children:"الاسم بالعربية *"}),(0,r.jsx)(o.p,{id:"name_ar",value:E.name_ar,onChange:e=>R(e.target.value),placeholder:"أدخل اسم الفئة بالعربية",className:"arabic-text"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(l.J,{htmlFor:"name_en",children:"الاسم بالإنجليزية"}),(0,r.jsx)(o.p,{id:"name_en",value:E.name_en,onChange:e=>A(a=>({...a,name_en:e.target.value})),placeholder:"Enter category name in English"})]})]}),(0,r.jsxs)("div",{className:"category-grid grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(l.J,{htmlFor:"slug",className:"arabic-text",children:"الرابط المختصر *"}),(0,r.jsx)(o.p,{id:"slug",value:E.slug,onChange:e=>A(a=>({...a,slug:e.target.value})),placeholder:"category-slug"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(l.J,{htmlFor:"icon",className:"arabic-text",children:"الأيقونة"}),(0,r.jsx)(o.p,{id:"icon",value:E.icon,onChange:e=>A(a=>({...a,icon:e.target.value})),placeholder:"\uD83C\uDF93"})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(l.J,{htmlFor:"description",className:"arabic-text",children:"الوصف"}),(0,r.jsx)(c.T,{id:"description",value:E.description,onChange:e=>A(a=>({...a,description:e.target.value})),placeholder:"وصف الفئة...",className:"arabic-text"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(u.d,{id:"is_active",checked:E.is_active,onCheckedChange:e=>A(a=>({...a,is_active:e}))}),(0,r.jsx)(l.J,{htmlFor:"is_active",className:"arabic-text",children:"فئة نشطة"})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,r.jsx)(n.$,{variant:"outline",onClick:()=>k(!1),children:"إلغاء"}),(0,r.jsx)(n.$,{onClick:D,children:C?"تحديث":"إضافة"})]})]})]})]})]})]}),(0,r.jsxs)(i.Zp,{children:[(0,r.jsx)(i.aR,{children:(0,r.jsx)(i.ZB,{className:"arabic-text",children:"قائمة الفئات"})}),(0,r.jsx)(i.Wu,{children:(0,r.jsxs)(m.XI,{children:[(0,r.jsx)(m.A0,{children:(0,r.jsxs)(m.Hj,{children:[(0,r.jsx)(m.nd,{className:"arabic-text",children:"الاسم"}),(0,r.jsx)(m.nd,{className:"arabic-text",children:"الرابط المختصر"}),(0,r.jsx)(m.nd,{className:"arabic-text",children:"الحالة"}),(0,r.jsx)(m.nd,{className:"arabic-text",children:"الترتيب"}),(0,r.jsx)(m.nd,{className:"arabic-text",children:"تاريخ الإنشاء"}),(0,r.jsx)(m.nd,{className:"arabic-text",children:"الإجراءات"})]})}),(0,r.jsx)(m.BF,{children:e.map(e=>(0,r.jsxs)(m.Hj,{children:[(0,r.jsx)(m.nA,{children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[e.icon&&(0,r.jsx)("span",{className:"text-lg",children:e.icon}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium arabic-text",children:e.name_ar}),e.name_en&&(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.name_en})]})]})}),(0,r.jsx)(m.nA,{children:(0,r.jsx)("code",{className:"bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded text-sm",children:e.slug})}),(0,r.jsx)(m.nA,{children:(0,r.jsx)(d.E,{variant:e.is_active?"default":"secondary",children:e.is_active?"نشط":"غير نشط"})}),(0,r.jsx)(m.nA,{children:e.order_index}),(0,r.jsx)(m.nA,{children:new Date(e.created_at).toLocaleDateString("en-US")}),(0,r.jsx)(m.nA,{children:(0,r.jsxs)(g.rI,{children:[(0,r.jsx)(g.ty,{asChild:!0,children:(0,r.jsx)(n.$,{variant:"ghost",className:"h-8 w-8 p-0",children:(0,r.jsx)(p.A,{className:"h-4 w-4"})})}),(0,r.jsxs)(g.SQ,{align:"end",children:[(0,r.jsxs)(g._2,{onClick:()=>S(e),children:[(0,r.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"تعديل"]}),(0,r.jsx)(g._2,{onClick:()=>B(e.id),children:e.is_active?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"إلغاء التفعيل"]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"تفعيل"]})}),(0,r.jsxs)(g._2,{onClick:()=>L(e.id),className:"text-red-600",children:[(0,r.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"حذف"]})]})]})})]},e.id))})]})})]})]})})}},18454:(e,a,t)=>{Promise.resolve().then(t.bind(t,11296))},26126:(e,a,t)=>{"use strict";t.d(a,{E:()=>o});var r=t(95155);t(12115);var s=t(99708),n=t(74466),i=t(59434);let d=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o(e){let{className:a,variant:t,asChild:n=!1,...o}=e,l=n?s.DX:"span";return(0,r.jsx)(l,{"data-slot":"badge",className:(0,i.cn)(d({variant:t}),a),...o})}},30285:(e,a,t)=>{"use strict";t.d(a,{$:()=>o,r:()=>d});var r=t(95155);t(12115);var s=t(99708),n=t(74466),i=t(59434);let d=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:a,variant:t,size:n,asChild:o=!1,...l}=e,c=o?s.DX:"button";return(0,r.jsx)(c,{"data-slot":"button",className:(0,i.cn)(d({variant:t,size:n,className:a})),...l})}},44838:(e,a,t)=>{"use strict";t.d(a,{SQ:()=>o,_2:()=>l,lp:()=>c,mB:()=>u,rI:()=>i,ty:()=>d});var r=t(95155);t(12115);var s=t(48698),n=t(59434);function i(e){let{...a}=e;return(0,r.jsx)(s.bL,{"data-slot":"dropdown-menu",...a})}function d(e){let{...a}=e;return(0,r.jsx)(s.l9,{"data-slot":"dropdown-menu-trigger",...a})}function o(e){let{className:a,sideOffset:t=4,...i}=e;return(0,r.jsx)(s.ZL,{children:(0,r.jsx)(s.UC,{"data-slot":"dropdown-menu-content",sideOffset:t,className:(0,n.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",a),...i})})}function l(e){let{className:a,inset:t,variant:i="default",...d}=e;return(0,r.jsx)(s.q7,{"data-slot":"dropdown-menu-item","data-inset":t,"data-variant":i,className:(0,n.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...d})}function c(e){let{className:a,inset:t,...i}=e;return(0,r.jsx)(s.JU,{"data-slot":"dropdown-menu-label","data-inset":t,className:(0,n.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",a),...i})}function u(e){let{className:a,...t}=e;return(0,r.jsx)(s.wv,{"data-slot":"dropdown-menu-separator",className:(0,n.cn)("bg-border -mx-1 my-1 h-px",a),...t})}},54165:(e,a,t)=>{"use strict";t.d(a,{Cf:()=>u,Es:()=>m,L3:()=>g,c7:()=>x,lG:()=>d,rr:()=>f,zM:()=>o});var r=t(95155);t(12115);var s=t(15452),n=t(54416),i=t(59434);function d(e){let{...a}=e;return(0,r.jsx)(s.bL,{"data-slot":"dialog",...a})}function o(e){let{...a}=e;return(0,r.jsx)(s.l9,{"data-slot":"dialog-trigger",...a})}function l(e){let{...a}=e;return(0,r.jsx)(s.ZL,{"data-slot":"dialog-portal",...a})}function c(e){let{className:a,...t}=e;return(0,r.jsx)(s.hJ,{"data-slot":"dialog-overlay",className:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",a),...t})}function u(e){let{className:a,children:t,showCloseButton:d=!0,...o}=e;return(0,r.jsxs)(l,{"data-slot":"dialog-portal",children:[(0,r.jsx)(c,{}),(0,r.jsxs)(s.UC,{"data-slot":"dialog-content",className:(0,i.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",a),...o,children:[t,d&&(0,r.jsxs)(s.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,r.jsx)(n.A,{}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function x(e){let{className:a,...t}=e;return(0,r.jsx)("div",{"data-slot":"dialog-header",className:(0,i.cn)("flex flex-col gap-2 text-center sm:text-left",a),...t})}function m(e){let{className:a,...t}=e;return(0,r.jsx)("div",{"data-slot":"dialog-footer",className:(0,i.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",a),...t})}function g(e){let{className:a,...t}=e;return(0,r.jsx)(s.hE,{"data-slot":"dialog-title",className:(0,i.cn)("text-lg leading-none font-semibold",a),...t})}function f(e){let{className:a,...t}=e;return(0,r.jsx)(s.VY,{"data-slot":"dialog-description",className:(0,i.cn)("text-muted-foreground text-sm",a),...t})}},59434:(e,a,t)=>{"use strict";t.d(a,{cn:()=>n});var r=t(52596),s=t(39688);function n(){for(var e=arguments.length,a=Array(e),t=0;t<e;t++)a[t]=arguments[t];return(0,s.QP)((0,r.$)(a))}},62523:(e,a,t)=>{"use strict";t.d(a,{p:()=>n});var r=t(95155);t(12115);var s=t(59434);function n(e){let{className:a,type:t,...n}=e;return(0,r.jsx)("input",{type:t,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",a),...n})}},66695:(e,a,t)=>{"use strict";t.d(a,{BT:()=>o,Wu:()=>l,ZB:()=>d,Zp:()=>n,aR:()=>i});var r=t(95155);t(12115);var s=t(59434);function n(e){let{className:a,...t}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...t})}function i(e){let{className:a,...t}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...t})}function d(e){let{className:a,...t}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",a),...t})}function o(e){let{className:a,...t}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",a),...t})}function l(e){let{className:a,...t}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",a),...t})}},80333:(e,a,t)=>{"use strict";t.d(a,{d:()=>i});var r=t(95155);t(12115);var s=t(4884),n=t(59434);function i(e){let{className:a,...t}=e;return(0,r.jsx)(s.bL,{"data-slot":"switch",className:(0,n.cn)("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",a),...t,children:(0,r.jsx)(s.zi,{"data-slot":"switch-thumb",className:(0,n.cn)("bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0")})})}},85057:(e,a,t)=>{"use strict";t.d(a,{J:()=>i});var r=t(95155);t(12115);var s=t(40968),n=t(59434);function i(e){let{className:a,...t}=e;return(0,r.jsx)(s.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...t})}},85127:(e,a,t)=>{"use strict";t.d(a,{A0:()=>d,BF:()=>o,Hj:()=>l,XI:()=>i,nA:()=>u,nd:()=>c});var r=t(95155),s=t(12115),n=t(59434);let i=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("div",{className:"relative w-full overflow-auto",children:(0,r.jsx)("table",{ref:a,className:(0,n.cn)("w-full caption-bottom text-sm",t),...s})})});i.displayName="Table";let d=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("thead",{ref:a,className:(0,n.cn)("[&_tr]:border-b",t),...s})});d.displayName="TableHeader";let o=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("tbody",{ref:a,className:(0,n.cn)("[&_tr:last-child]:border-0",t),...s})});o.displayName="TableBody",s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("tfoot",{ref:a,className:(0,n.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",t),...s})}).displayName="TableFooter";let l=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("tr",{ref:a,className:(0,n.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",t),...s})});l.displayName="TableRow";let c=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("th",{ref:a,className:(0,n.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",t),...s})});c.displayName="TableHead";let u=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("td",{ref:a,className:(0,n.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",t),...s})});u.displayName="TableCell",s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("caption",{ref:a,className:(0,n.cn)("mt-4 text-sm text-muted-foreground",t),...s})}).displayName="TableCaption"},88539:(e,a,t)=>{"use strict";t.d(a,{T:()=>n});var r=t(95155);t(12115);var s=t(59434);function n(e){let{className:a,...t}=e;return(0,r.jsx)("textarea",{"data-slot":"textarea",className:(0,s.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),...t})}}},e=>{var a=a=>e(e.s=a);e.O(0,[7598,5486,380,2433,6874,8698,2166,8441,1684,7358],()=>a(18454)),_N_E=e.O()}]);