"use strict";(()=>{var e={};e.id=2802,e.ids=[2802],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},95527:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>p,serverHooks:()=>u,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>m});var i={};r.r(i),r.d(i,{POST:()=>l});var n=r(96559),o=r(48088),a=r(37719),s=r(32190),d=r(38561);async function l(e){try{let{projectId:t,format:r="html"}=await e.json();if(!t)return s.NextResponse.json({error:"معرف المشروع مطلوب"},{status:400});let i=d.CN.getPageProjects().find(e=>e.id===t);if(!i)return s.NextResponse.json({error:"المشروع غير موجود"},{status:404});let n=d.CN.getMenuItems(),o=function(e,t){let r=e.components.some(e=>"header"===e.type)?`
    <header class="bg-white border-b shadow-sm p-4 sticky top-0 z-50">
      <div class="container mx-auto flex items-center justify-between">
        <div class="flex items-center gap-4">
          <div class="text-xl font-bold text-blue-600">
            منصة أزياء التخرج
          </div>
        </div>
        <nav class="hidden md:flex items-center gap-6">
          ${t.slice(0,6).map(e=>`
            <a href="#" class="text-sm hover:text-blue-600 transition-colors">
              ${e.title_ar||e.title}
            </a>
          `).join("")}
        </nav>
        <div class="flex items-center gap-2">
          <button class="px-4 py-2 text-sm border rounded-lg hover:bg-gray-50">تسجيل الدخول</button>
          <button class="px-4 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700">إنشاء حساب</button>
        </div>
      </div>
    </header>
  `:"",i=e.components.filter(e=>"header"!==e.type).map(e=>{let t=Object.entries(e.props.style||{}).map(([e,t])=>`${e.replace(/([a-z0-9]|(?=[A-Z]))([A-Z])/g,"$1-$2").toLowerCase()}: ${t}`).join("; ");return`
        <section style="${t}" class="component-${e.type}">
          <div class="container mx-auto">
            ${function(e){let t=e.props.content||"";switch(e.type){case"hero":return`
        <div class="hero-content">
          <h1 class="hero-title">${t}</h1>
          <p class="hero-subtitle">${e.props.subtitle||""}</p>
        </div>
      `;case"features":return`
        <div class="features-content">
          <h2 style="font-size: 2rem; margin-bottom: 2rem; text-align: center;">${t}</h2>
          <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem;">
            <div style="text-align: center; padding: 2rem;">
              <h3>ميزة رائعة</h3>
              <p>وصف الميزة هنا</p>
            </div>
            <div style="text-align: center; padding: 2rem;">
              <h3>ميزة أخرى</h3>
              <p>وصف الميزة هنا</p>
            </div>
            <div style="text-align: center; padding: 2rem;">
              <h3>ميزة ثالثة</h3>
              <p>وصف الميزة هنا</p>
            </div>
          </div>
        </div>
      `;case"gallery":return`
        <div class="gallery-content">
          <h2 style="font-size: 2rem; margin-bottom: 2rem; text-align: center;">${t}</h2>
          <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem;">
            <div style="background-color: #f3f4f6; height: 200px; border-radius: 8px; display: flex; align-items: center; justify-content: center;">
              صورة 1
            </div>
            <div style="background-color: #f3f4f6; height: 200px; border-radius: 8px; display: flex; align-items: center; justify-content: center;">
              صورة 2
            </div>
            <div style="background-color: #f3f4f6; height: 200px; border-radius: 8px; display: flex; align-items: center; justify-content: center;">
              صورة 3
            </div>
          </div>
        </div>
      `;case"contact":return`
        <div class="contact-content">
          <h2 style="font-size: 2rem; margin-bottom: 2rem; text-align: center;">${t}</h2>
          <form style="max-width: 600px; margin: 0 auto;">
            <div style="margin-bottom: 1rem;">
              <label style="display: block; margin-bottom: 0.5rem;">الاسم</label>
              <input type="text" style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 4px;">
            </div>
            <div style="margin-bottom: 1rem;">
              <label style="display: block; margin-bottom: 0.5rem;">البريد الإلكتروني</label>
              <input type="email" style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 4px;">
            </div>
            <div style="margin-bottom: 1rem;">
              <label style="display: block; margin-bottom: 0.5rem;">الرسالة</label>
              <textarea rows="4" style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 4px;"></textarea>
            </div>
            <button type="submit" style="background-color: #3b82f6; color: white; padding: 0.75rem 2rem; border: none; border-radius: 4px; cursor: pointer;">
              إرسال
            </button>
          </form>
        </div>
      `;case"text":return`
        <div class="text-content">
          <div style="max-width: 800px; margin: 0 auto; text-align: center;">
            <p style="font-size: 1.125rem; line-height: 1.8;">${t}</p>
          </div>
        </div>
      `;case"footer":return`
        <div class="footer-content">
          <p>${t}</p>
        </div>
      `;default:return`<div>${t}</div>`}}(e)}
          </div>
        </section>
      `}).join("");return`
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${e.settings?.title||e.name}</title>
    <meta name="description" content="${e.settings?.description||e.description}">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            direction: rtl;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }
        
        .component-hero {
            min-height: 500px;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
        }
        
        .component-features {
            padding: 3rem 0;
        }
        
        .component-gallery {
            padding: 3rem 0;
        }
        
        .component-contact {
            padding: 3rem 0;
        }
        
        .component-text {
            padding: 3rem 0;
        }
        
        .component-footer {
            background-color: #374151;
            color: white;
            text-align: center;
            padding: 2rem 0;
        }
        
        .hero-title {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 1rem;
        }
        
        .hero-subtitle {
            font-size: 1.25rem;
            opacity: 0.9;
        }
        
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2rem;
            }
            
            .hero-subtitle {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    ${r}
    
    <main>
        ${i}
    </main>
    
    <script>
        // إضافة تفاعلات بسيطة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('تم تحميل الصفحة بنجاح');
        });
    </script>
</body>
</html>
  `.trim()}(i,n);return new s.NextResponse(o,{status:200,headers:{"Content-Type":"text/html; charset=utf-8","Content-Disposition":`attachment; filename="${i.name}.html"`}})}catch(e){return console.error("Error exporting project:",e),s.NextResponse.json({error:"خطأ في تصدير المشروع"},{status:500})}}let p=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/page-builder/export/route",pathname:"/api/page-builder/export",filename:"route",bundlePath:"app/api/page-builder/export/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\page-builder\\export\\route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:c,workUnitAsyncStorage:m,serverHooks:u}=p;function g(){return(0,a.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:m})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[4447,580,8554],()=>r(95527));module.exports=i})();