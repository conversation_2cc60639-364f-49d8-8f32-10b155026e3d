(()=>{var e={};e.id=7888,e.ids=[7888],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8819:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13943:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("rotate-ccw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26202:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t});let t=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Graduation Toqs\\\\frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\checkout-settings\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\admin\\checkout-settings\\page.tsx","default")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},43830:(e,s,a)=>{Promise.resolve().then(a.bind(a,26202))},50728:(e,s,a)=>{"use strict";a.d(s,{B:()=>r});let t={fields:[{id:"fullName",name:"fullName",label:"الاسم الكامل",type:"text",required:!0,enabled:!0,placeholder:"أدخل اسمك الكامل",order:1,section:"personal",validation:{minLength:2,maxLength:100}},{id:"email",name:"email",label:"البريد الإلكتروني",type:"email",required:!0,enabled:!0,placeholder:"<EMAIL>",order:2,section:"personal"},{id:"phone",name:"phone",label:"رقم الهاتف",type:"tel",required:!0,enabled:!0,placeholder:"+971-XX-XXX-XXXX",order:3,section:"personal"},{id:"address",name:"address",label:"العنوان",type:"textarea",required:!0,enabled:!0,placeholder:"أدخل عنوانك الكامل",order:4,section:"shipping"},{id:"city",name:"city",label:"المدينة",type:"text",required:!0,enabled:!0,placeholder:"اسم المدينة",order:5,section:"shipping"},{id:"state",name:"state",label:"الإمارة/المنطقة",type:"select",required:!0,enabled:!0,order:6,section:"shipping",options:["أبوظبي","دبي","الشارقة","عجمان","أم القيوين","رأس الخيمة","الفجيرة"]},{id:"zipCode",name:"zipCode",label:"الرمز البريدي",type:"text",required:!1,enabled:!0,placeholder:"12345",order:7,section:"shipping"},{id:"specialInstructions",name:"specialInstructions",label:"تعليمات خاصة",type:"textarea",required:!1,enabled:!0,placeholder:"أي تعليمات خاصة للتوصيل...",order:8,section:"shipping"}],paymentMethods:[{id:"card",name:"بطاقة ائتمان/خصم",description:"Visa, Mastercard, American Express",icon:"CreditCard",enabled:!0,order:1,config:{requiresCard:!0,additionalFields:[{id:"cardNumber",name:"cardNumber",label:"رقم البطاقة",type:"text",required:!0,enabled:!0,placeholder:"1234 5678 9012 3456",order:1,section:"billing"},{id:"expiryDate",name:"expiryDate",label:"تاريخ الانتهاء",type:"text",required:!0,enabled:!0,placeholder:"MM/YY",order:2,section:"billing"},{id:"cvv",name:"cvv",label:"رمز الأمان",type:"text",required:!0,enabled:!0,placeholder:"123",order:3,section:"billing"}]}},{id:"cash",name:"الدفع عند الاستلام",description:"ادفع نقداً عند وصول الطلب",icon:"Banknote",enabled:!0,order:2},{id:"bank_transfer",name:"تحويل بنكي",description:"تحويل مباشر إلى حساب البنك",icon:"Building2",enabled:!0,order:3},{id:"digital_wallet",name:"المحفظة الرقمية",description:"Apple Pay, Google Pay, Samsung Pay",icon:"Smartphone",enabled:!1,order:4}],deliveryOptions:[{id:"standard",name:"التوصيل العادي",description:"3-5 أيام عمل",price:25,estimatedDays:"3-5 أيام",enabled:!0,order:1,icon:"Truck"},{id:"express",name:"التوصيل السريع",description:"1-2 أيام عمل",price:50,estimatedDays:"1-2 أيام",enabled:!0,order:2,icon:"Zap",restrictions:{minOrderValue:100}},{id:"same_day",name:"التوصيل في نفس اليوم",description:"خلال 6 ساعات",price:100,estimatedDays:"6 ساعات",enabled:!0,order:3,icon:"Clock",restrictions:{minOrderValue:200,availableRegions:["دبي","أبوظبي"]}},{id:"pickup",name:"الاستلام من المتجر",description:"استلم طلبك من فرعنا",price:0,estimatedDays:"فوري",enabled:!1,order:4,icon:"Store"}],general:{requireTermsAcceptance:!0,allowGuestCheckout:!0,showOrderSummary:!0,enableSpecialInstructions:!0,defaultCountry:"الإمارات العربية المتحدة",currency:"AED",taxRate:.05}};class r{static getStorageKey(){return"checkoutSettings"}static getSettings(){return t}static saveSettings(e){}static resetToDefaults(){this.saveSettings(t)}static addField(e){let s=this.getSettings();s.fields.push(e),this.saveSettings(s)}static updateField(e,s){let a=this.getSettings(),t=a.fields.findIndex(s=>s.id===e);-1!==t&&(a.fields[t]={...a.fields[t],...s},this.saveSettings(a))}static removeField(e){let s=this.getSettings();s.fields=s.fields.filter(s=>s.id!==e),this.saveSettings(s)}static updatePaymentMethod(e,s){let a=this.getSettings(),t=a.paymentMethods.findIndex(s=>s.id===e);-1!==t&&(a.paymentMethods[t]={...a.paymentMethods[t],...s},this.saveSettings(a))}static updateDeliveryOption(e,s){let a=this.getSettings(),t=a.deliveryOptions.findIndex(s=>s.id===e);-1!==t&&(a.deliveryOptions[t]={...a.deliveryOptions[t],...s},this.saveSettings(a))}}},54987:(e,s,a)=>{"use strict";a.d(s,{d:()=>n});var t=a(60687);a(43210);var r=a(90270),i=a(4780);function n({className:e,...s}){return(0,t.jsx)(r.bL,{"data-slot":"switch",className:(0,i.cn)("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...s,children:(0,t.jsx)(r.zi,{"data-slot":"switch-thumb",className:(0,i.cn)("bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0")})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65954:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>c});var t=a(65239),r=a(48088),i=a(88170),n=a.n(i),d=a(30893),l={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>d[e]);a.d(s,l);let c={children:["",{children:["dashboard",{children:["admin",{children:["checkout-settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,26202)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\admin\\checkout-settings\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(a.bind(a,54431)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,54413)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\admin\\checkout-settings\\page.tsx"],x={require:a,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/dashboard/admin/checkout-settings/page",pathname:"/dashboard/admin/checkout-settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},67390:(e,s,a)=>{Promise.resolve().then(a.bind(a,87628))},78272:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},79551:e=>{"use strict";e.exports=require("url")},80013:(e,s,a)=>{"use strict";a.d(s,{J:()=>n});var t=a(60687);a(43210);var r=a(78148),i=a(4780);function n({className:e,...s}){return(0,t.jsx)(r.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...s})}},83721:(e,s,a)=>{"use strict";a.d(s,{Z:()=>r});var t=a(43210);function r(e){let s=t.useRef({value:e,previous:e});return t.useMemo(()=>(s.current.value!==e&&(s.current.previous=s.current.value,s.current.value=e),s.current.previous),[e])}},85763:(e,s,a)=>{"use strict";a.d(s,{Xi:()=>l,av:()=>c,j7:()=>d,tU:()=>n});var t=a(60687);a(43210);var r=a(55146),i=a(4780);function n({className:e,...s}){return(0,t.jsx)(r.bL,{"data-slot":"tabs",className:(0,i.cn)("flex flex-col gap-2",e),...s})}function d({className:e,...s}){return(0,t.jsx)(r.B8,{"data-slot":"tabs-list",className:(0,i.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",e),...s})}function l({className:e,...s}){return(0,t.jsx)(r.l9,{"data-slot":"tabs-trigger",className:(0,i.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...s})}function c({className:e,...s}){return(0,t.jsx)(r.UC,{"data-slot":"tabs-content",className:(0,i.cn)("flex-1 outline-none",e),...s})}},85778:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},87628:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>S});var t=a(60687),r=a(43210),i=a(87801),n=a(44493),d=a(29523),l=a(89667),c=a(80013),o=a(54987),x=a(85763),m=a(96834),h=a(35950),u=a(28559),p=a(13943),g=a(8819),b=a(58869),v=a(85778),j=a(88059),f=a(84027),y=a(81381),N=a(13861),k=a(12597),w=a(63143),C=a(88233),A=a(96474),q=a(50728);function S(){let[e,s]=(0,r.useState)(null),[a,S]=(0,r.useState)("fields"),[P,_]=(0,r.useState)(!0),[M,T]=(0,r.useState)(!1),D=a=>{if(!e)return;let t=e.fields.map(e=>e.id===a?{...e,enabled:!e.enabled}:e);s({...e,fields:t})},G=a=>{if(!e)return;let t=e.paymentMethods.map(e=>e.id===a?{...e,enabled:!e.enabled}:e);s({...e,paymentMethods:t})},z=a=>{if(!e)return;let t=e.deliveryOptions.map(e=>e.id===a?{...e,enabled:!e.enabled}:e);s({...e,deliveryOptions:t})};return P||!e?(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",children:[(0,t.jsx)(i.V,{}),(0,t.jsx)("main",{className:"container mx-auto px-4 py-8",children:(0,t.jsxs)("div",{className:"text-center py-16",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"}),(0,t.jsx)("p",{className:"mt-4 text-gray-600 dark:text-gray-400",children:"جاري تحميل الإعدادات..."})]})})]}):(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",children:[(0,t.jsx)(i.V,{}),(0,t.jsxs)("main",{className:"container mx-auto px-4 py-8",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)(d.$,{variant:"outline",size:"sm",asChild:!0,className:"mb-4",children:(0,t.jsxs)("a",{href:"/dashboard/admin",children:[(0,t.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"العودة للوحة التحكم"]})}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white arabic-text",children:"⚙️ إعدادات صفحة الدفع"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-300 mt-2 arabic-text",children:"تخصيص حقول ومعلومات صفحة إتمام الطلب"})]}),(0,t.jsxs)("div",{className:"flex gap-3",children:[(0,t.jsxs)(d.$,{variant:"outline",onClick:()=>{confirm("هل أنت متأكد من إعادة تعيين جميع الإعدادات للقيم الافتراضية؟")&&(q.B.resetToDefaults(),s(q.B.getSettings()))},children:[(0,t.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"إعادة تعيين"]}),(0,t.jsxs)(d.$,{onClick:()=>{e&&(T(!0),q.B.saveSettings(e),setTimeout(()=>T(!1),1e3))},disabled:M,children:[(0,t.jsx)(g.A,{className:"h-4 w-4 mr-2"}),M?"جاري الحفظ...":"حفظ التغييرات"]})]})]})]}),(0,t.jsxs)(x.tU,{value:a,onValueChange:S,className:"space-y-6",children:[(0,t.jsxs)(x.j7,{className:"grid w-full grid-cols-4",children:[(0,t.jsxs)(x.Xi,{value:"fields",className:"arabic-text",children:[(0,t.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"الحقول"]}),(0,t.jsxs)(x.Xi,{value:"payment",className:"arabic-text",children:[(0,t.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"طرق الدفع"]}),(0,t.jsxs)(x.Xi,{value:"delivery",className:"arabic-text",children:[(0,t.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"التوصيل"]}),(0,t.jsxs)(x.Xi,{value:"general",className:"arabic-text",children:[(0,t.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"عام"]})]}),(0,t.jsx)(x.av,{value:"fields",className:"space-y-6",children:(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsx)(n.ZB,{className:"arabic-text",children:"إدارة حقول المعلومات"}),(0,t.jsx)(n.BT,{className:"arabic-text",children:"تحكم في الحقول المطلوبة في صفحة الدفع"})]}),(0,t.jsxs)(n.Wu,{className:"space-y-4",children:[e.fields.sort((e,s)=>e.order-s.order).map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)(y.A,{className:"h-4 w-4 text-gray-400"}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("h4",{className:"font-medium arabic-text",children:e.label}),e.required&&(0,t.jsx)(m.E,{variant:"destructive",className:"text-xs",children:"مطلوب"}),(0,t.jsx)(m.E,{variant:"outline",className:"text-xs",children:e.type})]}),(0,t.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:["القسم: ","personal"===e.section?"شخصي":"shipping"===e.section?"الشحن":"الفواتير"]})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(o.d,{checked:e.enabled,onCheckedChange:()=>D(e.id)}),e.enabled?(0,t.jsx)(N.A,{className:"h-4 w-4 text-green-600"}):(0,t.jsx)(k.A,{className:"h-4 w-4 text-gray-400"}),(0,t.jsx)(d.$,{size:"sm",variant:"outline",children:(0,t.jsx)(w.A,{className:"h-4 w-4"})}),(0,t.jsx)(d.$,{size:"sm",variant:"outline",children:(0,t.jsx)(C.A,{className:"h-4 w-4"})})]})]},e.id)),(0,t.jsxs)(d.$,{variant:"outline",className:"w-full",children:[(0,t.jsx)(A.A,{className:"h-4 w-4 mr-2"}),"إضافة حقل جديد"]})]})]})}),(0,t.jsx)(x.av,{value:"payment",className:"space-y-6",children:(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsx)(n.ZB,{className:"arabic-text",children:"طرق الدفع المتاحة"}),(0,t.jsx)(n.BT,{className:"arabic-text",children:"تفعيل أو إلغاء طرق الدفع المختلفة"})]}),(0,t.jsx)(n.Wu,{className:"space-y-4",children:e.paymentMethods.sort((e,s)=>e.order-s.order).map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)(v.A,{className:"h-5 w-5 text-blue-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium arabic-text",children:e.name}),(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:e.description})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(o.d,{checked:e.enabled,onCheckedChange:()=>G(e.id)}),e.enabled?(0,t.jsx)(m.E,{variant:"default",children:"مفعل"}):(0,t.jsx)(m.E,{variant:"secondary",children:"معطل"}),(0,t.jsx)(d.$,{size:"sm",variant:"outline",children:(0,t.jsx)(w.A,{className:"h-4 w-4"})})]})]},e.id))})]})}),(0,t.jsx)(x.av,{value:"delivery",className:"space-y-6",children:(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsx)(n.ZB,{className:"arabic-text",children:"خيارات التوصيل"}),(0,t.jsx)(n.BT,{className:"arabic-text",children:"إدارة طرق وأسعار التوصيل المختلفة"})]}),(0,t.jsxs)(n.Wu,{className:"space-y-4",children:[e.deliveryOptions.sort((e,s)=>e.order-s.order).map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)(j.A,{className:"h-5 w-5 text-green-600"}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("h4",{className:"font-medium arabic-text",children:e.name}),(0,t.jsxs)(m.E,{variant:"outline",children:[e.price," درهم"]})]}),(0,t.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:[e.description," - ",e.estimatedDays]})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(o.d,{checked:e.enabled,onCheckedChange:()=>z(e.id)}),e.enabled?(0,t.jsx)(m.E,{variant:"default",children:"متاح"}):(0,t.jsx)(m.E,{variant:"secondary",children:"غير متاح"}),(0,t.jsx)(d.$,{size:"sm",variant:"outline",children:(0,t.jsx)(w.A,{className:"h-4 w-4"})})]})]},e.id)),(0,t.jsxs)(d.$,{variant:"outline",className:"w-full",children:[(0,t.jsx)(A.A,{className:"h-4 w-4 mr-2"}),"إضافة خيار توصيل جديد"]})]})]})}),(0,t.jsx)(x.av,{value:"general",className:"space-y-6",children:(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsx)(n.ZB,{className:"arabic-text",children:"الإعدادات العامة"}),(0,t.jsx)(n.BT,{className:"arabic-text",children:"إعدادات عامة لصفحة الدفع"})]}),(0,t.jsxs)(n.Wu,{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(c.J,{className:"arabic-text",children:"العملة الافتراضية"}),(0,t.jsx)(l.p,{value:e.general.currency,onChange:a=>s({...e,general:{...e.general,currency:a.target.value}})})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(c.J,{className:"arabic-text",children:"البلد الافتراضي"}),(0,t.jsx)(l.p,{value:e.general.defaultCountry,onChange:a=>s({...e,general:{...e.general,defaultCountry:a.target.value}})})]})]}),(0,t.jsx)(h.w,{}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(c.J,{className:"arabic-text",children:"طلب الموافقة على الشروط والأحكام"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"إجبار المستخدمين على الموافقة قبل إتمام الطلب"})]}),(0,t.jsx)(o.d,{checked:e.general.requireTermsAcceptance,onCheckedChange:a=>s({...e,general:{...e.general,requireTermsAcceptance:a}})})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(c.J,{className:"arabic-text",children:"السماح بالدفع كضيف"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"السماح للمستخدمين بإتمام الطلب بدون تسجيل"})]}),(0,t.jsx)(o.d,{checked:e.general.allowGuestCheckout,onCheckedChange:a=>s({...e,general:{...e.general,allowGuestCheckout:a}})})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(c.J,{className:"arabic-text",children:"عرض ملخص الطلب"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"إظهار تفاصيل الطلب والأسعار"})]}),(0,t.jsx)(o.d,{checked:e.general.showOrderSummary,onCheckedChange:a=>s({...e,general:{...e.general,showOrderSummary:a}})})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(c.J,{className:"arabic-text",children:"تفعيل التعليمات الخاصة"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"السماح للعملاء بإضافة ملاحظات خاصة"})]}),(0,t.jsx)(o.d,{checked:e.general.enableSpecialInstructions,onCheckedChange:a=>s({...e,general:{...e.general,enableSpecialInstructions:a}})})]})]})]})]})})]})]})]})}}};var s=require("../../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[4447,8773,3050,3932,7801],()=>a(65954));module.exports=t})();