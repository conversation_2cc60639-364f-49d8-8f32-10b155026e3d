exports.id=9158,exports.ids=[9158],exports.modules={6710:(e,t,a)=>{"use strict";a.a(e,async(e,E)=>{try{a.d(t,{AD:()=>T,P:()=>o,Rn:()=>d,wU:()=>n});var r=a(64939),s=e([r]);let u=new(r=(s.then?(await s)():s)[0]).Pool({user:process.env.POSTGRES_USER||"postgres",host:process.env.POSTGRES_HOST||"localhost",database:process.env.POSTGRES_DB||"graduation_platform",password:process.env.POSTGRES_PASSWORD||"password",port:parseInt(process.env.POSTGRES_PORT||"5432"),max:20,idleTimeoutMillis:3e4,connectionTimeoutMillis:2e3,ssl:{rejectUnauthorized:!1}});async function i(){try{return await u.connect()}catch(e){throw console.error("خطأ في الاتصال بقاعدة البيانات:",e),Error("فشل في الاتصال بقاعدة البيانات")}}async function o(e,t){let a=await i();try{Date.now();let E=await a.query(e,t);return Date.now(),E}catch(e){throw console.error("خطأ في تنفيذ الاستعلام:",e),e}finally{a.release()}}async function d(e){let t=await i();try{await t.query("BEGIN");let a=await e(t);return await t.query("COMMIT"),a}catch(e){throw await t.query("ROLLBACK"),e}finally{t.release()}}async function T(){try{return(await o("SELECT NOW() as current_time")).rows.length>0}catch(e){return console.error("فشل في فحص حالة قاعدة البيانات:",e),!1}}async function n(){try{console.log("بدء تهيئة قاعدة البيانات..."),await o(`
      CREATE TABLE IF NOT EXISTS users (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        first_name VARCHAR(100) NOT NULL,
        last_name VARCHAR(100) NOT NULL,
        phone VARCHAR(20),
        role VARCHAR(20) DEFAULT 'customer' CHECK (role IN ('admin', 'customer', 'school', 'delivery')),
        is_active BOOLEAN DEFAULT true,
        email_verified BOOLEAN DEFAULT false,
        profile_image TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `),await o(`
      CREATE TABLE IF NOT EXISTS categories (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name_ar VARCHAR(100) NOT NULL,
        name_en VARCHAR(100),
        name_fr VARCHAR(100),
        slug VARCHAR(100) UNIQUE NOT NULL,
        description TEXT,
        icon VARCHAR(50),
        parent_id UUID REFERENCES categories(id) ON DELETE CASCADE,
        order_index INTEGER DEFAULT 0,
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `),await o(`
      CREATE TABLE IF NOT EXISTS products (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name VARCHAR(255) NOT NULL,
        description TEXT,
        category_id UUID REFERENCES categories(id) ON DELETE SET NULL,
        price DECIMAL(10,2) NOT NULL,
        rental_price DECIMAL(10,2),
        colors TEXT[] DEFAULT '{}',
        sizes TEXT[] DEFAULT '{}',
        images TEXT[] DEFAULT '{}',
        stock_quantity INTEGER DEFAULT 0,
        is_available BOOLEAN DEFAULT true,
        is_published BOOLEAN DEFAULT true,
        features TEXT[] DEFAULT '{}',
        specifications JSONB DEFAULT '{}',
        rating DECIMAL(3,2) DEFAULT 0,
        reviews_count INTEGER DEFAULT 0,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `),await o(`
      CREATE TABLE IF NOT EXISTS schools (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        admin_id UUID REFERENCES users(id) ON DELETE CASCADE,
        name VARCHAR(255) NOT NULL,
        name_en VARCHAR(255),
        name_fr VARCHAR(255),
        address TEXT,
        city VARCHAR(100),
        phone VARCHAR(20),
        email VARCHAR(255),
        website VARCHAR(255),
        logo_url TEXT,
        graduation_date DATE,
        student_count INTEGER DEFAULT 0,
        is_active BOOLEAN DEFAULT true,
        settings JSONB DEFAULT '{}',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `),await o(`
      CREATE TABLE IF NOT EXISTS orders (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID REFERENCES users(id) ON DELETE CASCADE,
        school_id UUID REFERENCES schools(id) ON DELETE SET NULL,
        order_number VARCHAR(50) UNIQUE NOT NULL,
        status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled')),
        total_amount DECIMAL(10,2) NOT NULL,
        shipping_amount DECIMAL(10,2) DEFAULT 0,
        tax_amount DECIMAL(10,2) DEFAULT 0,
        discount_amount DECIMAL(10,2) DEFAULT 0,
        payment_method VARCHAR(50),
        payment_status VARCHAR(20) DEFAULT 'pending' CHECK (payment_status IN ('pending', 'paid', 'failed', 'refunded')),
        shipping_address JSONB,
        billing_address JSONB,
        notes TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `),await o(`
      CREATE TABLE IF NOT EXISTS order_items (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
        product_id UUID REFERENCES products(id) ON DELETE CASCADE,
        quantity INTEGER NOT NULL,
        unit_price DECIMAL(10,2) NOT NULL,
        total_price DECIMAL(10,2) NOT NULL,
        type VARCHAR(20) DEFAULT 'purchase' CHECK (type IN ('purchase', 'rental')),
        size VARCHAR(50),
        color VARCHAR(50),
        customizations JSONB DEFAULT '{}',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `),await o(`
      CREATE TABLE IF NOT EXISTS reviews (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID REFERENCES users(id) ON DELETE CASCADE,
        product_id UUID REFERENCES products(id) ON DELETE CASCADE,
        order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
        rating INTEGER CHECK (rating >= 1 AND rating <= 5),
        comment TEXT,
        images TEXT[] DEFAULT '{}',
        is_verified BOOLEAN DEFAULT false,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(user_id, product_id, order_id)
      )
    `),await o(`
      CREATE TABLE IF NOT EXISTS menu_items (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        title_ar VARCHAR(100) NOT NULL,
        title_en VARCHAR(100),
        title_fr VARCHAR(100),
        slug VARCHAR(100) NOT NULL,
        icon VARCHAR(50),
        parent_id UUID REFERENCES menu_items(id) ON DELETE CASCADE,
        order_index INTEGER DEFAULT 0,
        is_active BOOLEAN DEFAULT true,
        target_type VARCHAR(20) DEFAULT 'internal' CHECK (target_type IN ('internal', 'external', 'page')),
        target_value VARCHAR(255),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `),await o("CREATE INDEX IF NOT EXISTS idx_products_category ON products(category_id)"),await o("CREATE INDEX IF NOT EXISTS idx_products_published ON products(is_published)"),await o("CREATE INDEX IF NOT EXISTS idx_products_available ON products(is_available)"),await o("CREATE INDEX IF NOT EXISTS idx_orders_user ON orders(user_id)"),await o("CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status)"),await o("CREATE INDEX IF NOT EXISTS idx_order_items_order ON order_items(order_id)"),await o("CREATE INDEX IF NOT EXISTS idx_reviews_product ON reviews(product_id)"),await o("CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)"),await o("CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)"),console.log("تم تهيئة قاعدة البيانات بنجاح!")}catch(e){throw console.error("خطأ في تهيئة قاعدة البيانات:",e),e}}E()}catch(e){E(e)}})},32450:(e,t,a)=>{"use strict";a.a(e,async(e,E)=>{try{a.d(t,{I:()=>i});var r=a(6710),s=e([r]);r=(s.then?(await s)():s)[0];class i{static async create(e){return await (0,r.Rn)(async t=>{let a=e.items.reduce((e,t)=>e+t.unit_price*t.quantity,0),E=a>500?0:50,r=.2*a,s=`ORD-${Date.now()}-${Math.random().toString(36).substr(2,5).toUpperCase()}`,i=(await t.query(`
        INSERT INTO orders (
          user_id, school_id, order_number, total_amount, shipping_amount, tax_amount,
          shipping_address, billing_address, payment_method, notes
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10
        ) RETURNING *
      `,[e.user_id,e.school_id,s,a+E+r,E,r,JSON.stringify(e.shipping_address),JSON.stringify(e.billing_address),e.payment_method,e.notes])).rows[0],o=[];for(let a of e.items){let e=await t.query(`
          INSERT INTO order_items (
            order_id, product_id, quantity, unit_price, total_price, type, size, color, customizations
          ) VALUES (
            $1, $2, $3, $4, $5, $6, $7, $8, $9
          ) RETURNING *
        `,[i.id,a.product_id,a.quantity,a.unit_price,a.unit_price*a.quantity,a.type,a.size,a.color,JSON.stringify(a.customizations)]);o.push(e.rows[0]),await t.query("UPDATE products SET stock_quantity = stock_quantity - $1 WHERE id = $2",[a.quantity,a.product_id])}return await this.getByIdWithDetails(i.id,t)})}static async getByIdWithDetails(e,t){let a=t?t.query.bind(t):r.P,E=await a(`
      SELECT 
        o.*,
        u.first_name || ' ' || u.last_name as user_name,
        u.email as user_email
      FROM orders o
      JOIN users u ON o.user_id = u.id
      WHERE o.id = $1
    `,[e]);if(0===E.rows.length)return null;let s=E.rows[0],i=await a(`
      SELECT 
        oi.*,
        p.name as product_name,
        p.images[1] as product_image
      FROM order_items oi
      JOIN products p ON oi.product_id = p.id
      WHERE oi.order_id = $1
      ORDER BY oi.created_at
    `,[e]);return{...s,items:i.rows}}static async getByUserId(e,t=20,a=0){let E=await (0,r.P)(`
      SELECT 
        o.*,
        u.first_name || ' ' || u.last_name as user_name,
        u.email as user_email
      FROM orders o
      JOIN users u ON o.user_id = u.id
      WHERE o.user_id = $1
      ORDER BY o.created_at DESC
      LIMIT $2 OFFSET $3
    `,[e,t,a]),s=[];for(let e of E.rows){let t=await (0,r.P)(`
        SELECT 
          oi.*,
          p.name as product_name,
          p.images[1] as product_image
        FROM order_items oi
        JOIN products p ON oi.product_id = p.id
        WHERE oi.order_id = $1
        ORDER BY oi.created_at
      `,[e.id]);s.push({...e,items:t.rows})}return s}static async getAll(e={}){let t=[],a=[],E=1;e.status&&(t.push(`o.status = $${E}`),a.push(e.status),E++),e.user_id&&(t.push(`o.user_id = $${E}`),a.push(e.user_id),E++),e.school_id&&(t.push(`o.school_id = $${E}`),a.push(e.school_id),E++),e.payment_status&&(t.push(`o.payment_status = $${E}`),a.push(e.payment_status),E++),e.search&&(t.push(`(o.order_number ILIKE $${E} OR u.first_name ILIKE $${E} OR u.last_name ILIKE $${E} OR u.email ILIKE $${E})`),a.push(`%${e.search}%`),E++);let s=t.length>0?`WHERE ${t.join(" AND ")}`:"",i=e.sortBy||"created_at",o=e.sortOrder||"DESC",d=`ORDER BY o.${i} ${o}`,T=e.limit||50,n=e.offset||0,u=`LIMIT $${E} OFFSET $${E+1}`;a.push(T,n);let A=`
      SELECT COUNT(*) as total 
      FROM orders o
      JOIN users u ON o.user_id = u.id
      ${s}
    `,_=await (0,r.P)(A,a.slice(0,-2)),R=parseInt(_.rows[0].total),c=`
      SELECT 
        o.*,
        u.first_name || ' ' || u.last_name as user_name,
        u.email as user_email
      FROM orders o
      JOIN users u ON o.user_id = u.id
      ${s} 
      ${d} 
      ${u}
    `,N=await (0,r.P)(c,a),I=[];for(let e of N.rows){let t=await (0,r.P)(`
        SELECT 
          oi.*,
          p.name as product_name,
          p.images[1] as product_image
        FROM order_items oi
        JOIN products p ON oi.product_id = p.id
        WHERE oi.order_id = $1
        ORDER BY oi.created_at
      `,[e.id]);I.push({...e,items:t.rows})}return{orders:I,total:R}}static async updateStatus(e,t){return(await (0,r.P)("UPDATE orders SET status = $1, updated_at = NOW() WHERE id = $2 RETURNING *",[t,e])).rows[0]||null}static async updatePaymentStatus(e,t){return(await (0,r.P)("UPDATE orders SET payment_status = $1, updated_at = NOW() WHERE id = $2 RETURNING *",[t,e])).rows[0]||null}static async cancel(e,t){return await (0,r.Rn)(async a=>{let E=await a.query("UPDATE orders SET status = $1, notes = COALESCE(notes, '') || $2, updated_at = NOW() WHERE id = $3 RETURNING *",["cancelled",t?`
سبب الإلغاء: ${t}`:"",e]);if(0===E.rows.length)return!1;for(let t of(await a.query("SELECT product_id, quantity FROM order_items WHERE order_id = $1",[e])).rows)await a.query("UPDATE products SET stock_quantity = stock_quantity + $1 WHERE id = $2",[t.quantity,t.product_id]);return!0})}static async getStats(){let e=(await (0,r.P)(`
      SELECT 
        COUNT(*) as total,
        COUNT(*) FILTER (WHERE status = 'pending') as pending,
        COUNT(*) FILTER (WHERE status = 'confirmed') as confirmed,
        COUNT(*) FILTER (WHERE status = 'delivered') as delivered,
        COUNT(*) FILTER (WHERE status = 'cancelled') as cancelled,
        COALESCE(SUM(total_amount) FILTER (WHERE status != 'cancelled'), 0) as total_revenue,
        COALESCE(AVG(total_amount) FILTER (WHERE status != 'cancelled'), 0) as average_order_value
      FROM orders
    `)).rows[0];return{total:parseInt(e.total),pending:parseInt(e.pending),confirmed:parseInt(e.confirmed),delivered:parseInt(e.delivered),cancelled:parseInt(e.cancelled),totalRevenue:parseFloat(e.total_revenue),averageOrderValue:parseFloat(e.average_order_value)}}}E()}catch(e){E(e)}})},78335:()=>{},96487:()=>{}};