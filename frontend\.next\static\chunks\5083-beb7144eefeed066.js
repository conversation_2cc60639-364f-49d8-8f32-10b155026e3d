"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5083],{13717:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},15452:(e,t,r)=>{r.d(t,{G$:()=>Y,Hs:()=>b,UC:()=>er,VY:()=>ea,ZL:()=>ee,bL:()=>Q,bm:()=>eo,hE:()=>en,hJ:()=>et,l9:()=>X});var n=r(12115),a=r(85185),o=r(6101),l=r(46081),i=r(61285),s=r(5845),d=r(19178),c=r(25519),u=r(34378),p=r(28905),f=r(63655),g=r(92293),m=r(93795),v=r(38168),y=r(99708),h=r(95155),x="Dialog",[D,b]=(0,l.A)(x),[A,j]=D(x),w=e=>{let{__scopeDialog:t,children:r,open:a,defaultOpen:o,onOpenChange:l,modal:d=!0}=e,c=n.useRef(null),u=n.useRef(null),[p,f]=(0,s.i)({prop:a,defaultProp:null!=o&&o,onChange:l,caller:x});return(0,h.jsx)(A,{scope:t,triggerRef:c,contentRef:u,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:p,onOpenChange:f,onOpenToggle:n.useCallback(()=>f(e=>!e),[f]),modal:d,children:r})};w.displayName=x;var R="DialogTrigger",C=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,l=j(R,r),i=(0,o.s)(t,l.triggerRef);return(0,h.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":V(l.open),...n,ref:i,onClick:(0,a.m)(e.onClick,l.onOpenToggle)})});C.displayName=R;var N="DialogPortal",[k,I]=D(N,{forceMount:void 0}),O=e=>{let{__scopeDialog:t,forceMount:r,children:a,container:o}=e,l=j(N,t);return(0,h.jsx)(k,{scope:t,forceMount:r,children:n.Children.map(a,e=>(0,h.jsx)(p.C,{present:r||l.open,children:(0,h.jsx)(u.Z,{asChild:!0,container:o,children:e})}))})};O.displayName=N;var E="DialogOverlay",F=n.forwardRef((e,t)=>{let r=I(E,e.__scopeDialog),{forceMount:n=r.forceMount,...a}=e,o=j(E,e.__scopeDialog);return o.modal?(0,h.jsx)(p.C,{present:n||o.open,children:(0,h.jsx)(M,{...a,ref:t})}):null});F.displayName=E;var _=(0,y.TL)("DialogOverlay.RemoveScroll"),M=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=j(E,r);return(0,h.jsx)(m.A,{as:_,allowPinchZoom:!0,shards:[a.contentRef],children:(0,h.jsx)(f.sG.div,{"data-state":V(a.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),P="DialogContent",L=n.forwardRef((e,t)=>{let r=I(P,e.__scopeDialog),{forceMount:n=r.forceMount,...a}=e,o=j(P,e.__scopeDialog);return(0,h.jsx)(p.C,{present:n||o.open,children:o.modal?(0,h.jsx)(G,{...a,ref:t}):(0,h.jsx)(T,{...a,ref:t})})});L.displayName=P;var G=n.forwardRef((e,t)=>{let r=j(P,e.__scopeDialog),l=n.useRef(null),i=(0,o.s)(t,r.contentRef,l);return n.useEffect(()=>{let e=l.current;if(e)return(0,v.Eq)(e)},[]),(0,h.jsx)(q,{...e,ref:i,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,a.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=r.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,a.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,a.m)(e.onFocusOutside,e=>e.preventDefault())})}),T=n.forwardRef((e,t)=>{let r=j(P,e.__scopeDialog),a=n.useRef(!1),o=n.useRef(!1);return(0,h.jsx)(q,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var n,l;null==(n=e.onCloseAutoFocus)||n.call(e,t),t.defaultPrevented||(a.current||null==(l=r.triggerRef.current)||l.focus(),t.preventDefault()),a.current=!1,o.current=!1},onInteractOutside:t=>{var n,l;null==(n=e.onInteractOutside)||n.call(e,t),t.defaultPrevented||(a.current=!0,"pointerdown"===t.detail.originalEvent.type&&(o.current=!0));let i=t.target;(null==(l=r.triggerRef.current)?void 0:l.contains(i))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}})}),q=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:a,onOpenAutoFocus:l,onCloseAutoFocus:i,...s}=e,u=j(P,r),p=n.useRef(null),f=(0,o.s)(t,p);return(0,g.Oh)(),(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(c.n,{asChild:!0,loop:!0,trapped:a,onMountAutoFocus:l,onUnmountAutoFocus:i,children:(0,h.jsx)(d.qW,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":V(u.open),...s,ref:f,onDismiss:()=>u.onOpenChange(!1)})}),(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)($,{titleId:u.titleId}),(0,h.jsx)(K,{contentRef:p,descriptionId:u.descriptionId})]})]})}),B="DialogTitle",H=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=j(B,r);return(0,h.jsx)(f.sG.h2,{id:a.titleId,...n,ref:t})});H.displayName=B;var Z="DialogDescription",S=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=j(Z,r);return(0,h.jsx)(f.sG.p,{id:a.descriptionId,...n,ref:t})});S.displayName=Z;var z="DialogClose",U=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=j(z,r);return(0,h.jsx)(f.sG.button,{type:"button",...n,ref:t,onClick:(0,a.m)(e.onClick,()=>o.onOpenChange(!1))})});function V(e){return e?"open":"closed"}U.displayName=z;var W="DialogTitleWarning",[Y,J]=(0,l.q)(W,{contentName:P,titleName:B,docsSlug:"dialog"}),$=e=>{let{titleId:t}=e,r=J(W),a="`".concat(r.contentName,"` requires a `").concat(r.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(r.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(r.docsSlug);return n.useEffect(()=>{t&&(document.getElementById(t)||console.error(a))},[a,t]),null},K=e=>{let{contentRef:t,descriptionId:r}=e,a=J("DialogDescriptionWarning"),o="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(a.contentName,"}.");return n.useEffect(()=>{var e;let n=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");r&&n&&(document.getElementById(r)||console.warn(o))},[o,t,r]),null},Q=w,X=C,ee=O,et=F,er=L,en=H,ea=S,eo=U},17649:(e,t,r)=>{r.d(t,{UC:()=>P,VY:()=>q,ZD:()=>G,ZL:()=>_,bL:()=>E,hE:()=>T,hJ:()=>M,l9:()=>F,rc:()=>L});var n=r(12115),a=r(46081),o=r(6101),l=r(15452),i=r(85185),s=r(99708),d=r(95155),c="AlertDialog",[u,p]=(0,a.A)(c,[l.Hs]),f=(0,l.Hs)(),g=e=>{let{__scopeAlertDialog:t,...r}=e,n=f(t);return(0,d.jsx)(l.bL,{...n,...r,modal:!0})};g.displayName=c;var m=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,a=f(r);return(0,d.jsx)(l.l9,{...a,...n,ref:t})});m.displayName="AlertDialogTrigger";var v=e=>{let{__scopeAlertDialog:t,...r}=e,n=f(t);return(0,d.jsx)(l.ZL,{...n,...r})};v.displayName="AlertDialogPortal";var y=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,a=f(r);return(0,d.jsx)(l.hJ,{...a,...n,ref:t})});y.displayName="AlertDialogOverlay";var h="AlertDialogContent",[x,D]=u(h),b=(0,s.Dc)("AlertDialogContent"),A=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,children:a,...s}=e,c=f(r),u=n.useRef(null),p=(0,o.s)(t,u),g=n.useRef(null);return(0,d.jsx)(l.G$,{contentName:h,titleName:j,docsSlug:"alert-dialog",children:(0,d.jsx)(x,{scope:r,cancelRef:g,children:(0,d.jsxs)(l.UC,{role:"alertdialog",...c,...s,ref:p,onOpenAutoFocus:(0,i.m)(s.onOpenAutoFocus,e=>{var t;e.preventDefault(),null==(t=g.current)||t.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,d.jsx)(b,{children:a}),(0,d.jsx)(O,{contentRef:u})]})})})});A.displayName=h;var j="AlertDialogTitle",w=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,a=f(r);return(0,d.jsx)(l.hE,{...a,...n,ref:t})});w.displayName=j;var R="AlertDialogDescription",C=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,a=f(r);return(0,d.jsx)(l.VY,{...a,...n,ref:t})});C.displayName=R;var N=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,a=f(r);return(0,d.jsx)(l.bm,{...a,...n,ref:t})});N.displayName="AlertDialogAction";var k="AlertDialogCancel",I=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,{cancelRef:a}=D(k,r),i=f(r),s=(0,o.s)(t,a);return(0,d.jsx)(l.bm,{...i,...n,ref:s})});I.displayName=k;var O=e=>{let{contentRef:t}=e,r="`".concat(h,"` requires a description for the component to be accessible for screen reader users.\n\nYou can add a description to the `").concat(h,"` by passing a `").concat(R,"` component as a child, which also benefits sighted users by adding visible context to the dialog.\n\nAlternatively, you can use your own component as a description by assigning it an `id` and passing the same value to the `aria-describedby` prop in `").concat(h,"`. If the description is confusing or duplicative for sighted users, you can use the `@radix-ui/react-visually-hidden` primitive as a wrapper around your description component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/alert-dialog");return n.useEffect(()=>{var e;document.getElementById(null==(e=t.current)?void 0:e.getAttribute("aria-describedby"))||console.warn(r)},[r,t]),null},E=g,F=m,_=v,M=y,P=A,L=N,G=I,T=w,q=C},35169:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},40646:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},40968:(e,t,r)=>{r.d(t,{b:()=>i});var n=r(12115),a=r(63655),o=r(95155),l=n.forwardRef((e,t)=>(0,o.jsx)(a.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var i=l},51154:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},84616:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},85339:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])}}]);