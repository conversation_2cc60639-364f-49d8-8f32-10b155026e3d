(()=>{var e={};e.id=5894,e.ids=[5894],e.modules={1574:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Graduation Toqs\\\\frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\categories\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\admin\\categories\\page.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6211:(e,t,a)=>{"use strict";a.d(t,{A0:()=>o,BF:()=>l,Hj:()=>d,XI:()=>i,nA:()=>u,nd:()=>c});var r=a(60687),s=a(43210),n=a(4780);let i=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("div",{className:"relative w-full overflow-auto",children:(0,r.jsx)("table",{ref:a,className:(0,n.cn)("w-full caption-bottom text-sm",e),...t})}));i.displayName="Table";let o=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("thead",{ref:a,className:(0,n.cn)("[&_tr]:border-b",e),...t}));o.displayName="TableHeader";let l=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("tbody",{ref:a,className:(0,n.cn)("[&_tr:last-child]:border-0",e),...t}));l.displayName="TableBody",s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("tfoot",{ref:a,className:(0,n.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...t})).displayName="TableFooter";let d=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("tr",{ref:a,className:(0,n.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...t}));d.displayName="TableRow";let c=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("th",{ref:a,className:(0,n.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...t}));c.displayName="TableHead";let u=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("td",{ref:a,className:(0,n.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...t}));u.displayName="TableCell",s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("caption",{ref:a,className:(0,n.cn)("mt-4 text-sm text-muted-foreground",e),...t})).displayName="TableCaption"},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12597:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},13861:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26134:(e,t,a)=>{"use strict";a.d(t,{G$:()=>V,Hs:()=>y,UC:()=>ea,VY:()=>es,ZL:()=>ee,bL:()=>Y,bm:()=>en,hE:()=>er,hJ:()=>et,l9:()=>Q});var r=a(43210),s=a(70569),n=a(98599),i=a(11273),o=a(96963),l=a(65551),d=a(31355),c=a(32547),u=a(25028),p=a(46059),x=a(14163),m=a(1359),f=a(42247),h=a(63376),g=a(8730),v=a(60687),b="Dialog",[j,y]=(0,i.A)(b),[w,N]=j(b),k=e=>{let{__scopeDialog:t,children:a,open:s,defaultOpen:n,onOpenChange:i,modal:d=!0}=e,c=r.useRef(null),u=r.useRef(null),[p,x]=(0,l.i)({prop:s,defaultProp:n??!1,onChange:i,caller:b});return(0,v.jsx)(w,{scope:t,triggerRef:c,contentRef:u,contentId:(0,o.B)(),titleId:(0,o.B)(),descriptionId:(0,o.B)(),open:p,onOpenChange:x,onOpenToggle:r.useCallback(()=>x(e=>!e),[x]),modal:d,children:a})};k.displayName=b;var _="DialogTrigger",C=r.forwardRef((e,t)=>{let{__scopeDialog:a,...r}=e,i=N(_,a),o=(0,n.s)(t,i.triggerRef);return(0,v.jsx)(x.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":H(i.open),...r,ref:o,onClick:(0,s.m)(e.onClick,i.onOpenToggle)})});C.displayName=_;var D="DialogPortal",[A,R]=j(D,{forceMount:void 0}),E=e=>{let{__scopeDialog:t,forceMount:a,children:s,container:n}=e,i=N(D,t);return(0,v.jsx)(A,{scope:t,forceMount:a,children:r.Children.map(s,e=>(0,v.jsx)(p.C,{present:a||i.open,children:(0,v.jsx)(u.Z,{asChild:!0,container:n,children:e})}))})};E.displayName=D;var P="DialogOverlay",T=r.forwardRef((e,t)=>{let a=R(P,e.__scopeDialog),{forceMount:r=a.forceMount,...s}=e,n=N(P,e.__scopeDialog);return n.modal?(0,v.jsx)(p.C,{present:r||n.open,children:(0,v.jsx)(I,{...s,ref:t})}):null});T.displayName=P;var F=(0,g.TL)("DialogOverlay.RemoveScroll"),I=r.forwardRef((e,t)=>{let{__scopeDialog:a,...r}=e,s=N(P,a);return(0,v.jsx)(f.A,{as:F,allowPinchZoom:!0,shards:[s.contentRef],children:(0,v.jsx)(x.sG.div,{"data-state":H(s.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),M="DialogContent",G=r.forwardRef((e,t)=>{let a=R(M,e.__scopeDialog),{forceMount:r=a.forceMount,...s}=e,n=N(M,e.__scopeDialog);return(0,v.jsx)(p.C,{present:r||n.open,children:n.modal?(0,v.jsx)(O,{...s,ref:t}):(0,v.jsx)(q,{...s,ref:t})})});G.displayName=M;var O=r.forwardRef((e,t)=>{let a=N(M,e.__scopeDialog),i=r.useRef(null),o=(0,n.s)(t,a.contentRef,i);return r.useEffect(()=>{let e=i.current;if(e)return(0,h.Eq)(e)},[]),(0,v.jsx)(z,{...e,ref:o,trapFocus:a.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,s.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),a.triggerRef.current?.focus()}),onPointerDownOutside:(0,s.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,a=0===t.button&&!0===t.ctrlKey;(2===t.button||a)&&e.preventDefault()}),onFocusOutside:(0,s.m)(e.onFocusOutside,e=>e.preventDefault())})}),q=r.forwardRef((e,t)=>{let a=N(M,e.__scopeDialog),s=r.useRef(!1),n=r.useRef(!1);return(0,v.jsx)(z,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(s.current||a.triggerRef.current?.focus(),t.preventDefault()),s.current=!1,n.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(s.current=!0,"pointerdown"===t.detail.originalEvent.type&&(n.current=!0));let r=t.target;a.triggerRef.current?.contains(r)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&n.current&&t.preventDefault()}})}),z=r.forwardRef((e,t)=>{let{__scopeDialog:a,trapFocus:s,onOpenAutoFocus:i,onCloseAutoFocus:o,...l}=e,u=N(M,a),p=r.useRef(null),x=(0,n.s)(t,p);return(0,m.Oh)(),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(c.n,{asChild:!0,loop:!0,trapped:s,onMountAutoFocus:i,onUnmountAutoFocus:o,children:(0,v.jsx)(d.qW,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":H(u.open),...l,ref:x,onDismiss:()=>u.onOpenChange(!1)})}),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(W,{titleId:u.titleId}),(0,v.jsx)(K,{contentRef:p,descriptionId:u.descriptionId})]})]})}),S="DialogTitle",L=r.forwardRef((e,t)=>{let{__scopeDialog:a,...r}=e,s=N(S,a);return(0,v.jsx)(x.sG.h2,{id:s.titleId,...r,ref:t})});L.displayName=S;var $="DialogDescription",U=r.forwardRef((e,t)=>{let{__scopeDialog:a,...r}=e,s=N($,a);return(0,v.jsx)(x.sG.p,{id:s.descriptionId,...r,ref:t})});U.displayName=$;var J="DialogClose",B=r.forwardRef((e,t)=>{let{__scopeDialog:a,...r}=e,n=N(J,a);return(0,v.jsx)(x.sG.button,{type:"button",...r,ref:t,onClick:(0,s.m)(e.onClick,()=>n.onOpenChange(!1))})});function H(e){return e?"open":"closed"}B.displayName=J;var Z="DialogTitleWarning",[V,X]=(0,i.q)(Z,{contentName:M,titleName:S,docsSlug:"dialog"}),W=({titleId:e})=>{let t=X(Z),a=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return r.useEffect(()=>{e&&(document.getElementById(e)||console.error(a))},[a,e]),null},K=({contentRef:e,descriptionId:t})=>{let a=X("DialogDescriptionWarning"),s=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${a.contentName}}.`;return r.useEffect(()=>{let a=e.current?.getAttribute("aria-describedby");t&&a&&(document.getElementById(t)||console.warn(s))},[s,e,t]),null},Y=k,Q=C,ee=E,et=T,ea=G,er=L,es=U,en=B},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29770:(e,t,a)=>{Promise.resolve().then(a.bind(a,96920))},33873:e=>{"use strict";e.exports=require("path")},34729:(e,t,a)=>{"use strict";a.d(t,{T:()=>n});var r=a(60687);a(43210);var s=a(4780);function n({className:e,...t}){return(0,r.jsx)("textarea",{"data-slot":"textarea",className:(0,s.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...t})}},45864:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var r=a(65239),s=a(48088),n=a(88170),i=a.n(n),o=a(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);a.d(t,l);let d={children:["",{children:["dashboard",{children:["admin",{children:["categories",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,1574)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\admin\\categories\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(a.bind(a,54431)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,54413)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\admin\\categories\\page.tsx"],u={require:a,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/dashboard/admin/categories/page",pathname:"/dashboard/admin/categories",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},54987:(e,t,a)=>{"use strict";a.d(t,{d:()=>i});var r=a(60687);a(43210);var s=a(90270),n=a(4780);function i({className:e,...t}){return(0,r.jsx)(s.bL,{"data-slot":"switch",className:(0,n.cn)("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:(0,r.jsx)(s.zi,{"data-slot":"switch-thumb",className:(0,n.cn)("bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0")})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},63503:(e,t,a)=>{"use strict";a.d(t,{Cf:()=>u,Es:()=>x,L3:()=>m,c7:()=>p,lG:()=>o,rr:()=>f,zM:()=>l});var r=a(60687);a(43210);var s=a(26134),n=a(11860),i=a(4780);function o({...e}){return(0,r.jsx)(s.bL,{"data-slot":"dialog",...e})}function l({...e}){return(0,r.jsx)(s.l9,{"data-slot":"dialog-trigger",...e})}function d({...e}){return(0,r.jsx)(s.ZL,{"data-slot":"dialog-portal",...e})}function c({className:e,...t}){return(0,r.jsx)(s.hJ,{"data-slot":"dialog-overlay",className:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function u({className:e,children:t,showCloseButton:a=!0,...o}){return(0,r.jsxs)(d,{"data-slot":"dialog-portal",children:[(0,r.jsx)(c,{}),(0,r.jsxs)(s.UC,{"data-slot":"dialog-content",className:(0,i.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...o,children:[t,a&&(0,r.jsxs)(s.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,r.jsx)(n.A,{}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function p({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"dialog-header",className:(0,i.cn)("flex flex-col gap-2 text-center sm:text-left",e),...t})}function x({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"dialog-footer",className:(0,i.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...t})}function m({className:e,...t}){return(0,r.jsx)(s.hE,{"data-slot":"dialog-title",className:(0,i.cn)("text-lg leading-none font-semibold",e),...t})}function f({className:e,...t}){return(0,r.jsx)(s.VY,{"data-slot":"dialog-description",className:(0,i.cn)("text-muted-foreground text-sm",e),...t})}},78148:(e,t,a)=>{"use strict";a.d(t,{b:()=>o});var r=a(43210),s=a(14163),n=a(60687),i=r.forwardRef((e,t)=>(0,n.jsx)(s.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var o=i},79551:e=>{"use strict";e.exports=require("url")},80013:(e,t,a)=>{"use strict";a.d(t,{J:()=>i});var r=a(60687);a(43210);var s=a(78148),n=a(4780);function i({className:e,...t}){return(0,r.jsx)(s.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},83721:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});var r=a(43210);function s(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},88233:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},90270:(e,t,a)=>{"use strict";a.d(t,{bL:()=>w,zi:()=>N});var r=a(43210),s=a(70569),n=a(98599),i=a(11273),o=a(65551),l=a(83721),d=a(18853),c=a(14163),u=a(60687),p="Switch",[x,m]=(0,i.A)(p),[f,h]=x(p),g=r.forwardRef((e,t)=>{let{__scopeSwitch:a,name:i,checked:l,defaultChecked:d,required:x,disabled:m,value:h="on",onCheckedChange:g,form:v,...b}=e,[w,N]=r.useState(null),k=(0,n.s)(t,e=>N(e)),_=r.useRef(!1),C=!w||v||!!w.closest("form"),[D,A]=(0,o.i)({prop:l,defaultProp:d??!1,onChange:g,caller:p});return(0,u.jsxs)(f,{scope:a,checked:D,disabled:m,children:[(0,u.jsx)(c.sG.button,{type:"button",role:"switch","aria-checked":D,"aria-required":x,"data-state":y(D),"data-disabled":m?"":void 0,disabled:m,value:h,...b,ref:k,onClick:(0,s.m)(e.onClick,e=>{A(e=>!e),C&&(_.current=e.isPropagationStopped(),_.current||e.stopPropagation())})}),C&&(0,u.jsx)(j,{control:w,bubbles:!_.current,name:i,value:h,checked:D,required:x,disabled:m,form:v,style:{transform:"translateX(-100%)"}})]})});g.displayName=p;var v="SwitchThumb",b=r.forwardRef((e,t)=>{let{__scopeSwitch:a,...r}=e,s=h(v,a);return(0,u.jsx)(c.sG.span,{"data-state":y(s.checked),"data-disabled":s.disabled?"":void 0,...r,ref:t})});b.displayName=v;var j=r.forwardRef(({__scopeSwitch:e,control:t,checked:a,bubbles:s=!0,...i},o)=>{let c=r.useRef(null),p=(0,n.s)(c,o),x=(0,l.Z)(a),m=(0,d.X)(t);return r.useEffect(()=>{let e=c.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(x!==a&&t){let r=new Event("click",{bubbles:s});t.call(e,a),e.dispatchEvent(r)}},[x,a,s]),(0,u.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:a,...i,tabIndex:-1,ref:p,style:{...i.style,...m,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function y(e){return e?"checked":"unchecked"}j.displayName="SwitchBubbleInput";var w=g,N=b},93661:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},95266:(e,t,a)=>{Promise.resolve().then(a.bind(a,1574))},96474:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},96834:(e,t,a)=>{"use strict";a.d(t,{E:()=>l});var r=a(60687);a(43210);var s=a(8730),n=a(24224),i=a(4780);let o=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,asChild:a=!1,...n}){let l=a?s.DX:"span";return(0,r.jsx)(l,{"data-slot":"badge",className:(0,i.cn)(o({variant:t}),e),...n})}},96920:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>k});var r=a(60687),s=a(43210),n=a(29523),i=a(44493),o=a(96834),l=a(89667),d=a(80013),c=a(34729),u=a(54987),p=a(63503),x=a(6211),m=a(21342),f=a(28559),h=a(96474),g=a(93661),v=a(63143),b=a(12597),j=a(13861),y=a(88233),w=a(85814),N=a.n(w);function k(){let[e,t]=(0,s.useState)([]),[a,w]=(0,s.useState)(!0),[k,_]=(0,s.useState)(!1),[C,D]=(0,s.useState)(null),[A,R]=(0,s.useState)({name_ar:"",name_en:"",name_fr:"",slug:"",icon:"",description:"",is_active:!0,order_index:1}),E=async()=>{try{w(!0);let e=await fetch("/api/categories?include_inactive=true");if(e.ok){let a=await e.json();t(a.categories)}}catch(e){console.error("Error fetching categories:",e)}finally{w(!1)}},P=()=>{R({name_ar:"",name_en:"",name_fr:"",slug:"",icon:"",description:"",is_active:!0,order_index:e.length+1}),D(null)},T=e=>{R({name_ar:e.name_ar,name_en:e.name_en||"",name_fr:e.name_fr||"",slug:e.slug,icon:e.icon||"",description:e.description||"",is_active:e.is_active,order_index:e.order_index}),D(e),_(!0)},F=e=>e.toLowerCase().replace(/[أإآ]/g,"a").replace(/[ة]/g,"h").replace(/[ى]/g,"y").replace(/[ء]/g,"").replace(/\s+/g,"-").replace(/[^\w\-]/g,""),I=e=>{R(t=>({...t,name_ar:e,slug:t.slug||F(e)}))},M=async()=>{try{let e=C?`/api/categories/${C.id}`:"/api/categories",t=C?"PUT":"POST",a=await fetch(e,{method:t,headers:{"Content-Type":"application/json"},body:JSON.stringify(A)});if(!a.ok){let e=await a.json();throw Error(e.error||"فشل في حفظ الفئة")}await E(),_(!1),P(),alert(C?"تم تحديث الفئة بنجاح!":"تم إضافة الفئة بنجاح!")}catch(e){console.error("Error saving category:",e),alert(e instanceof Error?e.message:"فشل في حفظ الفئة")}},G=async e=>{if(confirm("هل أنت متأكد من حذف هذه الفئة؟"))try{let t=await fetch(`/api/categories/${e}`,{method:"DELETE"});if(!t.ok){let e=await t.json();throw Error(e.error||"فشل في حذف الفئة")}await E(),alert("تم حذف الفئة بنجاح!")}catch(e){console.error("Error deleting category:",e),alert(e instanceof Error?e.message:"فشل في حذف الفئة")}},O=async t=>{let a=e.find(e=>e.id===t);if(a)try{let e=await fetch(`/api/categories/${t}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({is_active:!a.is_active})});if(!e.ok){let t=await e.json();throw Error(t.error||"فشل في تحديث حالة الفئة")}await E()}catch(e){console.error("Error toggling category status:",e),alert(e instanceof Error?e.message:"فشل في تحديث حالة الفئة")}};return a?(0,r.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600 arabic-text",children:"جاري تحميل الفئات..."})]})}):(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:(0,r.jsxs)("main",{className:"container mx-auto px-4 py-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("div",{className:"flex items-center gap-4 mb-4",children:(0,r.jsx)(n.$,{variant:"outline",size:"sm",asChild:!0,children:(0,r.jsxs)(N(),{href:"/dashboard/admin",children:[(0,r.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"العودة للوحة التحكم"]})})}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white arabic-text",children:"إدارة الفئات \uD83D\uDCC2"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-300 mt-2 arabic-text",children:"إضافة وتعديل وإدارة فئات المنتجات"})]}),(0,r.jsxs)(p.lG,{open:k,onOpenChange:_,children:[(0,r.jsx)(p.zM,{asChild:!0,children:(0,r.jsxs)(n.$,{onClick:P,children:[(0,r.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"إضافة فئة جديدة"]})}),(0,r.jsxs)(p.Cf,{className:"max-w-2xl",children:[(0,r.jsx)(p.c7,{children:(0,r.jsx)(p.L3,{className:"arabic-text",children:C?"تعديل الفئة":"إضافة فئة جديدة"})}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"category-grid grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.J,{htmlFor:"name_ar",className:"arabic-text",children:"الاسم بالعربية *"}),(0,r.jsx)(l.p,{id:"name_ar",value:A.name_ar,onChange:e=>I(e.target.value),placeholder:"أدخل اسم الفئة بالعربية",className:"arabic-text"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.J,{htmlFor:"name_en",children:"الاسم بالإنجليزية"}),(0,r.jsx)(l.p,{id:"name_en",value:A.name_en,onChange:e=>R(t=>({...t,name_en:e.target.value})),placeholder:"Enter category name in English"})]})]}),(0,r.jsxs)("div",{className:"category-grid grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.J,{htmlFor:"slug",className:"arabic-text",children:"الرابط المختصر *"}),(0,r.jsx)(l.p,{id:"slug",value:A.slug,onChange:e=>R(t=>({...t,slug:e.target.value})),placeholder:"category-slug"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.J,{htmlFor:"icon",className:"arabic-text",children:"الأيقونة"}),(0,r.jsx)(l.p,{id:"icon",value:A.icon,onChange:e=>R(t=>({...t,icon:e.target.value})),placeholder:"\uD83C\uDF93"})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.J,{htmlFor:"description",className:"arabic-text",children:"الوصف"}),(0,r.jsx)(c.T,{id:"description",value:A.description,onChange:e=>R(t=>({...t,description:e.target.value})),placeholder:"وصف الفئة...",className:"arabic-text"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(u.d,{id:"is_active",checked:A.is_active,onCheckedChange:e=>R(t=>({...t,is_active:e}))}),(0,r.jsx)(d.J,{htmlFor:"is_active",className:"arabic-text",children:"فئة نشطة"})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,r.jsx)(n.$,{variant:"outline",onClick:()=>_(!1),children:"إلغاء"}),(0,r.jsx)(n.$,{onClick:M,children:C?"تحديث":"إضافة"})]})]})]})]})]})]}),(0,r.jsxs)(i.Zp,{children:[(0,r.jsx)(i.aR,{children:(0,r.jsx)(i.ZB,{className:"arabic-text",children:"قائمة الفئات"})}),(0,r.jsx)(i.Wu,{children:(0,r.jsxs)(x.XI,{children:[(0,r.jsx)(x.A0,{children:(0,r.jsxs)(x.Hj,{children:[(0,r.jsx)(x.nd,{className:"arabic-text",children:"الاسم"}),(0,r.jsx)(x.nd,{className:"arabic-text",children:"الرابط المختصر"}),(0,r.jsx)(x.nd,{className:"arabic-text",children:"الحالة"}),(0,r.jsx)(x.nd,{className:"arabic-text",children:"الترتيب"}),(0,r.jsx)(x.nd,{className:"arabic-text",children:"تاريخ الإنشاء"}),(0,r.jsx)(x.nd,{className:"arabic-text",children:"الإجراءات"})]})}),(0,r.jsx)(x.BF,{children:e.map(e=>(0,r.jsxs)(x.Hj,{children:[(0,r.jsx)(x.nA,{children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[e.icon&&(0,r.jsx)("span",{className:"text-lg",children:e.icon}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium arabic-text",children:e.name_ar}),e.name_en&&(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.name_en})]})]})}),(0,r.jsx)(x.nA,{children:(0,r.jsx)("code",{className:"bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded text-sm",children:e.slug})}),(0,r.jsx)(x.nA,{children:(0,r.jsx)(o.E,{variant:e.is_active?"default":"secondary",children:e.is_active?"نشط":"غير نشط"})}),(0,r.jsx)(x.nA,{children:e.order_index}),(0,r.jsx)(x.nA,{children:new Date(e.created_at).toLocaleDateString("en-US")}),(0,r.jsx)(x.nA,{children:(0,r.jsxs)(m.rI,{children:[(0,r.jsx)(m.ty,{asChild:!0,children:(0,r.jsx)(n.$,{variant:"ghost",className:"h-8 w-8 p-0",children:(0,r.jsx)(g.A,{className:"h-4 w-4"})})}),(0,r.jsxs)(m.SQ,{align:"end",children:[(0,r.jsxs)(m._2,{onClick:()=>T(e),children:[(0,r.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"تعديل"]}),(0,r.jsx)(m._2,{onClick:()=>O(e.id),children:e.is_active?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"إلغاء التفعيل"]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"تفعيل"]})}),(0,r.jsxs)(m._2,{onClick:()=>G(e.id),className:"text-red-600",children:[(0,r.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"حذف"]})]})]})})]},e.id))})]})})]})]})})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[4447,8773,3932],()=>a(45864));module.exports=r})();