(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5006],{12726:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>eu});var t=s(95155),r=s(12115),l=s(40283),i=s(86566),c=s(66695),n=s(30285),d=s(26126),o=s(62523),x=s(85057),m=s(59409),h=s(54165),p=s(17313),u=s(85127),j=s(44838),g=s(88539),b=s(89613),N=s(59434);let f=b.Kq,v=b.bL,y=b.l9,w=r.forwardRef((e,a)=>{let{className:s,sideOffset:r=4,...l}=e;return(0,t.jsx)(b.<PERSON>,{ref:a,sideOffset:r,className:(0,N.cn)("z-50 overflow-hidden rounded-md bg-primary px-3 py-1.5 text-xs text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",s),...l})});w.displayName=b.UC.displayName;var k=s(80333),_=s(37108),C=s(33127),A=s(27213),E=s(38564),S=s(49376),z=s(85339),T=s(84616),$=s(54416),F=s(92657),D=s(4229),O=s(24944),U=s(55365),q=s(94449),Z=s(29869),R=s(42118),P=s(62525),B=s(32568),W=s(5196);function L(e){let{images:a,onImagesChange:s,maxImages:l=10,maxSize:i=5242880,acceptedTypes:o=["image/jpeg","image/png","image/webp"],className:x=""}=e,[m,h]=(0,r.useState)(!1),[p,u]=(0,r.useState)({}),[j,g]=(0,r.useState)(!0),[b,N]=(0,r.useState)([]),[f,v]=(0,r.useState)({}),[y,w]=(0,r.useState)({}),k=(0,r.useMemo)(()=>Array.isArray(a)?a:[],[a]),_=(0,r.useCallback)(async()=>{try{let e=(await fetch("/api/health",{method:"HEAD"})).ok;return g(e),e}catch(e){return g(!1),!1}},[]),C=(0,r.useCallback)(e=>o.includes(e.type)?e.size>i?"حجم الملف كبير جداً. الحد الأقصى ".concat((i/1024/1024).toFixed(1)," ميجابايت"):null:"نوع الملف غير مدعوم. يرجى اختيار صورة بصيغة JPG, PNG, أو WebP",[o,i]),A=(0,r.useCallback)((e,a)=>new Promise((s,t)=>{let r=new FileReader;r.onload=()=>{try{let e=r.result;localStorage.setItem("fallback_image_".concat(a),e),s(e)}catch(e){t(e)}},r.onerror=t,r.readAsDataURL(e)}),[]),E=(0,r.useCallback)(async e=>new Promise(a=>{let s=document.createElement("canvas"),t=s.getContext("2d"),r=new Image;r.onload=()=>{let{width:l,height:i}=r;l>i?l>1200&&(i=1200*i/l,l=1200):i>1200&&(l=1200*l/i,i=1200),s.width=l,s.height=i,null==t||t.drawImage(r,0,0,l,i),s.toBlob(s=>{s?a(new File([s],e.name,{type:"image/jpeg",lastModified:Date.now()})):a(e)},"image/jpeg",.8)},r.src=URL.createObjectURL(e)}),[]),T=(0,r.useCallback)(async e=>{w(a=>({...a,[e]:!0}));try{await new Promise(e=>setTimeout(e,2e3+3e3*Math.random())),console.log("تم تحسين الصورة ".concat(e," باستخدام الذكاء الاصطناعي"))}catch(e){console.error("خطأ في تحسين الصورة:",e)}finally{w(a=>({...a,[e]:!1}))}},[]),F=(0,r.useCallback)(async(e,a)=>{v(e=>({...e,[a]:!0}));try{let a=await E(e);return await new Promise(e=>setTimeout(e,1e3)),a}finally{v(e=>({...e,[a]:!1}))}},[E]),D=(0,r.useCallback)(async function(e,a){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:2,t=await E(e);for(let r=1;r<=s;r++)try{u(e=>({...e,[a]:(r-1)*40}));let e=new FormData;e.append("files",t),e.append("folder","products");let s=await fetch("/api/upload",{method:"POST",body:e});if(!s.ok)throw Error("HTTP ".concat(s.status));let l=await s.json();if(u(e=>({...e,[a]:100})),l.uploadedFiles&&l.uploadedFiles.length>0)return l.uploadedFiles[0].url;throw Error("لم يتم إرجاع رابط الصورة")}catch(t){if(console.error("Upload attempt ".concat(r," failed:"),t),r===s){let s=await A(e,a);return u(e=>({...e,[a]:100})),s}await new Promise(e=>setTimeout(e,1e3*r))}throw Error("فشل في رفع الصورة")},[E,A]),L=(0,r.useCallback)(async e=>{let t=[],r=(Array.isArray(a)?a:[]).length,i=[];await _();for(let a=0;a<e.length&&r+t.length<l;a++){let s=e[a],r=C(s),l="".concat(Date.now(),"-").concat(a);if(r){i.push("".concat(s.name,": ").concat(r));continue}try{let e=await new Promise((e,a)=>{let t=new FileReader;t.onload=a=>{var s;return e(null==(s=a.target)?void 0:s.result)},t.onerror=a,t.readAsDataURL(s)}),a={file:s,preview:e,id:l,uploading:!0,uploaded:!1};t.push(a)}catch(e){console.error("Preview creation failed:",e),i.push("".concat(s.name,": فشل في إنشاء المعاينة"))}}if(t.length>0){s([...a,...t]);let e=t.map(async e=>{try{let a=await F(e.file,e.id),t=await D(a,e.id);s(a=>a.map(a=>a.id===e.id?{...a,uploading:!1,uploaded:!0,fallbackUrl:t}:a))}catch(a){console.error("Upload failed:",a),i.push("".concat(e.file.name,": فشل في الرفع")),s(a=>a.map(a=>a.id===e.id?{...a,uploading:!1,uploaded:!1,error:"فشل في الرفع"}:a))}});await Promise.allSettled(e)}i.length>0&&(N(i),setTimeout(()=>N([]),5e3))},[a,l,s,_,C,D,F]),I=(0,r.useCallback)(e=>{e.preventDefault(),e.stopPropagation(),"dragenter"===e.type||"dragover"===e.type?h(!0):"dragleave"===e.type&&h(!1)},[]),J=(0,r.useCallback)(e=>{e.preventDefault(),e.stopPropagation(),h(!1),e.dataTransfer.files&&e.dataTransfer.files[0]&&L(e.dataTransfer.files)},[L]),X=e=>{s(k.filter(a=>a.id!==e))},V=(e,a)=>{let t=[...k],[r]=t.splice(e,1);t.splice(a,0,r),s(t)};return(0,t.jsxs)("div",{className:"space-y-4 ".concat(x),children:[!j&&(0,t.jsxs)(U.Fc,{className:"border-orange-200 bg-orange-50",children:[(0,t.jsx)(q.A,{className:"h-4 w-4"}),(0,t.jsx)(U.TN,{className:"arabic-text",children:"لا يوجد اتصال بالإنترنت. سيتم حفظ الصور محلياً كنسخة احتياطية."})]}),b.length>0&&(0,t.jsxs)(U.Fc,{className:"border-red-200 bg-red-50",children:[(0,t.jsx)(z.A,{className:"h-4 w-4"}),(0,t.jsx)(U.TN,{children:(0,t.jsx)("div",{className:"space-y-1",children:b.map((e,a)=>(0,t.jsx)("div",{className:"text-sm arabic-text",children:e},a))})})]}),(0,t.jsxs)("div",{className:"border-2 border-dashed rounded-lg p-8 text-center transition-colors ".concat(m?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-300 dark:border-gray-600 hover:border-gray-400"," ").concat(a.length>=l?"opacity-50 pointer-events-none":""),onDragEnter:I,onDragLeave:I,onDragOver:I,onDrop:J,children:[(0,t.jsx)(Z.A,{className:"h-12 w-12 mx-auto mb-4 ".concat(m?"text-blue-500":"text-gray-400")}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("p",{className:"text-lg font-medium arabic-text",children:m?"أفلت الصور هنا":"اسحب الصور هنا أو"}),k.length<l&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("input",{ref:e=>{e&&(window.fileInput=e)},type:"file",multiple:!0,accept:o.join(","),className:"hidden",onChange:e=>{e.target.files&&e.target.files.length>0&&(L(e.target.files),e.target.value="")},disabled:k.length>=l}),(0,t.jsxs)(n.$,{type:"button",variant:"outline",size:"sm",disabled:k.length>=l,onClick:()=>{let e=window.fileInput;e&&e.click()},children:[(0,t.jsx)(R.A,{className:"h-4 w-4 mr-2"}),"اختر الصور"]})]})]}),(0,t.jsxs)("div",{className:"mt-4 space-y-1",children:[(0,t.jsxs)("p",{className:"text-sm text-gray-500 arabic-text",children:["يمكنك رفع حتى ",l," صور بحجم أقصى ",(i/1024/1024).toFixed(1)," ميجابايت لكل صورة"]}),(0,t.jsxs)("p",{className:"text-xs text-gray-400",children:["الصيغ المدعومة: ",o.map(e=>e.split("/")[1].toUpperCase()).join(", ")]})]}),k.length>=l&&(0,t.jsxs)("div",{className:"mt-4 flex items-center justify-center gap-2 text-orange-600",children:[(0,t.jsx)(z.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"text-sm arabic-text",children:"تم الوصول للحد الأقصى من الصور"})]})]}),k.length>0&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("h3",{className:"text-lg font-medium arabic-text",children:["الصور المرفوعة (",k.length,"/",l,")"]}),(0,t.jsxs)(n.$,{type:"button",variant:"outline",size:"sm",onClick:()=>s([]),className:"text-red-600 hover:text-red-700",children:[(0,t.jsx)(P.A,{className:"h-4 w-4 mr-2"}),"حذف الكل"]})]}),(0,t.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:k.map((e,s)=>{var r,l,i;return(0,t.jsx)(c.Zp,{className:"relative group overflow-hidden",children:(0,t.jsx)(c.Wu,{className:"p-0",children:e.error?(0,t.jsx)("div",{className:"aspect-square flex items-center justify-center bg-red-50 dark:bg-red-900/20",children:(0,t.jsxs)("div",{className:"text-center p-4",children:[(0,t.jsx)(z.A,{className:"h-8 w-8 text-red-500 mx-auto mb-2"}),(0,t.jsx)("p",{className:"text-xs text-red-600 arabic-text",children:e.error})]})}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("img",{src:e.preview,alt:"صورة ".concat(s+1),className:"w-full aspect-square object-cover"}),e.uploading&&(0,t.jsx)("div",{className:"absolute inset-0 bg-black/60 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-4 min-w-[140px] shadow-lg",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-3",children:[(0,t.jsx)(B.A,{className:"h-5 w-5 animate-spin text-blue-500"}),(0,t.jsx)("span",{className:"text-sm font-medium arabic-text",children:"جاري الرفع..."})]}),(0,t.jsx)(O.k,{value:p[e.id]||0,className:"h-3 mb-2"}),(0,t.jsxs)("div",{className:"text-xs text-center text-gray-600 dark:text-gray-400",children:[p[e.id]||0,"%"]})]})}),e.uploaded&&(0,t.jsx)("div",{className:"absolute top-2 left-2",children:(0,t.jsxs)(d.E,{className:(null==(r=e.fallbackUrl)?void 0:r.startsWith("data:"))?"bg-orange-600":"bg-green-600",children:[(0,t.jsx)(W.A,{className:"h-3 w-3 mr-1"}),(null==(l=e.fallbackUrl)?void 0:l.startsWith("data:"))?"محفوظ محلياً":"تم الرفع"]})}),!j&&e.uploaded&&(null==(i=e.fallbackUrl)?void 0:i.startsWith("data:"))&&(0,t.jsx)("div",{className:"absolute bottom-2 left-2",children:(0,t.jsxs)(d.E,{variant:"outline",className:"bg-white/90",children:[(0,t.jsx)(q.A,{className:"h-3 w-3 mr-1"}),"غير متصل"]})}),(0,t.jsx)("div",{className:"absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity",children:(0,t.jsxs)("div",{className:"flex gap-1",children:[e.uploaded&&!y[e.id]&&(0,t.jsx)(n.$,{type:"button",size:"sm",variant:"outline",onClick:()=>T(e.id),className:"h-8 w-8 p-0 bg-blue-600 text-white hover:bg-blue-700",title:"تحسين بالذكاء الاصطناعي",children:(0,t.jsx)(S.A,{className:"h-3 w-3"})}),y[e.id]&&(0,t.jsx)(n.$,{type:"button",size:"sm",variant:"outline",disabled:!0,className:"h-8 w-8 p-0 bg-blue-600 text-white",children:(0,t.jsx)(B.A,{className:"h-3 w-3 animate-spin"})}),f[e.id]&&(0,t.jsx)(n.$,{type:"button",size:"sm",variant:"outline",disabled:!0,className:"h-8 w-8 p-0 bg-orange-600 text-white",children:(0,t.jsx)(B.A,{className:"h-3 w-3 animate-spin"})}),(0,t.jsx)(n.$,{type:"button",size:"sm",variant:"destructive",onClick:()=>X(e.id),className:"h-8 w-8 p-0",disabled:e.uploading||f[e.id]||y[e.id],children:(0,t.jsx)($.A,{className:"h-3 w-3"})})]})}),0===s&&(0,t.jsx)(d.E,{className:"absolute bottom-2 left-2 bg-blue-600",children:"الصورة الرئيسية"}),(0,t.jsx)("div",{className:"absolute bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity",children:(0,t.jsxs)("div",{className:"flex gap-1",children:[s>0&&(0,t.jsx)(n.$,{type:"button",size:"sm",variant:"secondary",onClick:()=>V(s,s-1),className:"h-6 w-6 p-0",children:"←"}),s<a.length-1&&(0,t.jsx)(n.$,{type:"button",size:"sm",variant:"secondary",onClick:()=>V(s,s+1),className:"h-6 w-6 p-0",children:"→"})]})})]})})},e.id)})})]})]})}var I=s(51154),J=s(40646),X=s(57434),V=s(43332),H=s(15968),G=s(381),M=s(20690),K=s(47924),Q=s(24357);function Y(e){let{productData:a,onUpdate:s,className:l=""}=e,{model:i,available:o,loading:x}=function(){let{activeModels:e,loading:a,error:s}=function(){let[e,a]=(0,r.useState)([]),[s,t]=(0,r.useState)(!0),[l,i]=(0,r.useState)(null),c=async()=>{try{t(!0),i(null);let e=await fetch("/api/ai-models?include_inactive=true");if(!e.ok)throw Error("فشل في جلب نماذج الذكاء الاصطناعي");let s=await e.json();a(s.models||[])}catch(e){i(e instanceof Error?e.message:"خطأ غير معروف"),console.error("Error fetching AI models:",e)}finally{t(!1)}};(0,r.useEffect)(()=>{c()},[]);let n=e.filter(e=>e.isActive&&"active"===e.status&&e.apiKey&&e.baseUrl);return{models:e,activeModels:n,loading:s,error:l,refetch:c}}(),t=e.find(e=>"text"===e.type||"multimodal"===e.type||"openai"===e.provider||"anthropic"===e.provider);return{model:t,available:!!t,loading:a,error:s}}(),[m,h]=(0,r.useState)(null),[p,u]=(0,r.useState)({}),[j,b]=(0,r.useState)(""),N=async e=>{if(!o||!i)return void alert("لا توجد نماذج ذكاء اصطناعي نشطة");h(e);try{let s=await fetch("/api/ai/product-enhancement",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:e,productData:a,modelId:j||i.id,language:"ar"})});if(!s.ok)throw Error("فشل في تحسين المنتج");let t=await s.json();if(t.success)u(a=>({...a,[e]:t.result}));else throw Error(t.error||"خطأ غير معروف")}catch(e){console.error("AI Enhancement Error:",e),alert(e instanceof Error?e.message:"خطأ في تحسين المنتج")}finally{h(null)}},f=(e,a,t)=>{s(a,t),u(a=>{let s={...a};return delete s[e],s})},v=e=>{navigator.clipboard.writeText(e)};return x?(0,t.jsx)(c.Zp,{className:l,children:(0,t.jsxs)(c.Wu,{className:"flex items-center justify-center p-6",children:[(0,t.jsx)(I.A,{className:"h-6 w-6 animate-spin mr-2"}),(0,t.jsx)("span",{className:"arabic-text",children:"جاري تحميل نماذج الذكاء الاصطناعي..."})]})}):o?(0,t.jsxs)(c.Zp,{className:l,children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsxs)(c.ZB,{className:"flex items-center gap-2 arabic-text",children:[(0,t.jsx)(S.A,{className:"h-5 w-5 text-blue-600"}),"تحسين المنتج بالذكاء الاصطناعي"]}),(0,t.jsx)(c.BT,{className:"arabic-text",children:"استخدم الذكاء الاصطناعي لتحسين بيانات المنتج وتوليد محتوى احترافي"}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsxs)(d.E,{variant:"outline",className:"text-green-600",children:[(0,t.jsx)(J.A,{className:"h-3 w-3 mr-1"}),i.name]}),(0,t.jsx)(d.E,{variant:"secondary",children:i.provider})]})]}),(0,t.jsxs)(c.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-3",children:[(0,t.jsxs)(n.$,{variant:"outline",size:"sm",onClick:()=>N("generate_description"),disabled:"generate_description"===m,className:"arabic-text",children:["generate_description"===m?(0,t.jsx)(I.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(X.A,{className:"h-4 w-4 mr-2"}),"توليد وصف"]}),(0,t.jsxs)(n.$,{variant:"outline",size:"sm",onClick:()=>N("generate_title"),disabled:"generate_title"===m,className:"arabic-text",children:["generate_title"===m?(0,t.jsx)(I.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(V.A,{className:"h-4 w-4 mr-2"}),"توليد عنوان"]}),(0,t.jsxs)(n.$,{variant:"outline",size:"sm",onClick:()=>N("generate_features"),disabled:"generate_features"===m,className:"arabic-text",children:["generate_features"===m?(0,t.jsx)(I.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(H.A,{className:"h-4 w-4 mr-2"}),"توليد ميزات"]}),(0,t.jsxs)(n.$,{variant:"outline",size:"sm",onClick:()=>N("generate_specifications"),disabled:"generate_specifications"===m,className:"arabic-text",children:["generate_specifications"===m?(0,t.jsx)(I.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(G.A,{className:"h-4 w-4 mr-2"}),"توليد مواصفات"]}),(0,t.jsxs)(n.$,{variant:"outline",size:"sm",onClick:()=>N("suggest_category"),disabled:"suggest_category"===m,className:"arabic-text",children:["suggest_category"===m?(0,t.jsx)(I.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(M.A,{className:"h-4 w-4 mr-2"}),"اقتراح فئة"]}),(0,t.jsxs)(n.$,{variant:"outline",size:"sm",onClick:()=>N("optimize_seo"),disabled:"optimize_seo"===m,className:"arabic-text",children:["optimize_seo"===m?(0,t.jsx)(I.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(K.A,{className:"h-4 w-4 mr-2"}),"تحسين SEO"]})]}),Object.entries(p).map(e=>{var a;let[s,r]=e;return(0,t.jsxs)(c.Zp,{className:"border-green-200 bg-green-50 dark:bg-green-900/20",children:[(0,t.jsx)(c.aR,{className:"pb-3",children:(0,t.jsxs)(c.ZB,{className:"text-sm text-green-700 dark:text-green-300 arabic-text",children:["نتيجة ",{generate_description:"توليد الوصف",generate_title:"توليد العنوان",generate_features:"توليد الميزات",generate_specifications:"توليد المواصفات",suggest_category:"اقتراح الفئة",optimize_seo:"تحسين SEO"}[s]]})}),(0,t.jsxs)(c.Wu,{className:"space-y-3",children:[r.description&&(0,t.jsxs)("div",{children:[(0,t.jsx)(g.T,{value:r.description,readOnly:!0,className:"min-h-[100px] arabic-text"}),(0,t.jsxs)("div",{className:"flex gap-2 mt-2",children:[(0,t.jsx)(n.$,{size:"sm",onClick:()=>f(s,"description",r.description),children:"تطبيق"}),(0,t.jsx)(n.$,{size:"sm",variant:"outline",onClick:()=>v(r.description),children:(0,t.jsx)(Q.A,{className:"h-4 w-4"})})]})]}),r.title&&(0,t.jsxs)("div",{children:[(0,t.jsx)("input",{type:"text",value:r.title,readOnly:!0,className:"w-full p-2 border rounded arabic-text"}),(0,t.jsxs)("div",{className:"flex gap-2 mt-2",children:[(0,t.jsx)(n.$,{size:"sm",onClick:()=>f(s,"name",r.title),children:"تطبيق"}),(0,t.jsx)(n.$,{size:"sm",variant:"outline",onClick:()=>v(r.title),children:(0,t.jsx)(Q.A,{className:"h-4 w-4"})})]})]}),r.features&&(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"space-y-1",children:r.features.map((e,a)=>(0,t.jsx)("div",{className:"p-2 bg-white dark:bg-gray-800 rounded border arabic-text",children:e},a))}),(0,t.jsx)("div",{className:"flex gap-2 mt-2",children:(0,t.jsx)(n.$,{size:"sm",onClick:()=>f(s,"features",r.features),children:"تطبيق الكل"})})]}),r.specifications&&(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"space-y-1",children:Object.entries(r.specifications).map(e=>{let[a,s]=e;return(0,t.jsxs)("div",{className:"flex gap-2 p-2 bg-white dark:bg-gray-800 rounded border",children:[(0,t.jsxs)("span",{className:"font-medium arabic-text",children:[a,":"]}),(0,t.jsx)("span",{className:"arabic-text",children:s})]},a)})}),(0,t.jsx)("div",{className:"flex gap-2 mt-2",children:(0,t.jsx)(n.$,{size:"sm",onClick:()=>f(s,"specifications",r.specifications),children:"تطبيق الكل"})})]}),r.suggestedCategory&&(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"p-2 bg-white dark:bg-gray-800 rounded border arabic-text",children:["الفئة المقترحة: ",(0,t.jsx)("strong",{children:{gown:"ثوب التخرج",cap:"قبعة التخرج",stole:"وشاح التخرج",tassel:"شرابة التخرج",hood:"قلنسوة التخرج"}[a=r.suggestedCategory]||a}),r.confidence&&(0,t.jsxs)(d.E,{variant:"secondary",className:"mr-2",children:[Math.round(100*r.confidence),"% ثقة"]})]}),(0,t.jsx)("div",{className:"flex gap-2 mt-2",children:(0,t.jsx)(n.$,{size:"sm",onClick:()=>f(s,"category",r.suggestedCategory),children:"تطبيق"})})]}),r.keywords&&(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:r.keywords.map((e,a)=>(0,t.jsx)(d.E,{variant:"outline",className:"arabic-text",children:e},a))}),r.metaDescription&&(0,t.jsx)(g.T,{value:r.metaDescription,readOnly:!0,className:"mt-2 arabic-text",placeholder:"وصف SEO"})]})]})]},s)})]})]}):(0,t.jsx)(c.Zp,{className:l,children:(0,t.jsx)(c.Wu,{className:"flex items-center justify-center p-6 text-center",children:(0,t.jsxs)("div",{children:[(0,t.jsx)(z.A,{className:"h-12 w-12 text-orange-500 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2 arabic-text",children:"لا توجد نماذج ذكاء اصطناعي نشطة"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-400 arabic-text",children:"يرجى تفعيل نموذج ذكاء اصطناعي واحد على الأقل لاستخدام هذه الميزة"})]})})})}let ee=["أسود","أزرق داكن","بورجوندي","ذهبي","فضي","أبيض","أحمر","أخضر","بنفسجي","وردي","برتقالي","بني"],ea=["XS","S","M","L","XL","XXL","XXXL","واحد"];function es(e){var a,s;let{onSubmit:l,onCancel:i,initialData:h={},isEditing:u=!1}=e,[j,b]=(0,r.useState)({name:h.name||"",description:h.description||"",category:h.category||"",price:h.price||0,rental_price:h.rental_price||0,colors:h.colors||[],sizes:h.sizes||[],images:h.images||[],stock_quantity:h.stock_quantity||0,is_available:null==(a=h.is_available)||a,is_published:null==(s=h.is_published)||s,features:h.features||[],specifications:h.specifications||{}}),[N,f]=(0,r.useState)([]),[v,y]=(0,r.useState)(!0),[w,O]=(0,r.useState)(""),[U,q]=(0,r.useState)(""),[Z,R]=(0,r.useState)(""),[P,B]=(0,r.useState)(""),[W,I]=(0,r.useState)(""),[J,X]=(0,r.useState)({});(0,r.useEffect)(()=>{(async()=>{try{let e=await fetch("/api/categories");if(e.ok){let a=await e.json();f(a.categories)}}catch(e){console.error("Error fetching categories:",e)}finally{y(!1)}})()},[]);let V=()=>{let e={};return j.name.trim()||(e.name="اسم المنتج مطلوب"),j.description.trim()||(e.description="وصف المنتج مطلوب"),j.category||(e.category="فئة المنتج مطلوبة"),j.price<=0&&(e.price="السعر يجب أن يكون أكبر من صفر"),0===j.colors.length&&(e.colors="يجب إضافة لون واحد على الأقل"),0===j.sizes.length&&(e.sizes="يجب إضافة مقاس واحد على الأقل"),j.stock_quantity<0&&(e.stock_quantity="كمية المخزون لا يمكن أن تكون سالبة"),X(e),0===Object.keys(e).length},H=e=>{b(a=>({...a,colors:a.colors.filter(a=>a!==e)}))},G=e=>{b(a=>({...a,sizes:a.sizes.filter(a=>a!==e)}))},M=e=>{b(a=>({...a,features:a.features.filter(a=>a!==e)}))},K=e=>{b(a=>{let s={...a.specifications};return delete s[e],{...a,specifications:s}})};return(0,t.jsxs)("form",{onSubmit:e=>{e.preventDefault(),V()&&l(j)},className:"space-y-6",children:[(0,t.jsxs)(p.tU,{defaultValue:"basic",className:"w-full",children:[(0,t.jsxs)(p.j7,{className:"grid w-full grid-cols-5",children:[(0,t.jsxs)(p.Xi,{value:"basic",className:"arabic-text",children:[(0,t.jsx)(_.A,{className:"h-4 w-4 mr-2"}),"المعلومات الأساسية"]}),(0,t.jsxs)(p.Xi,{value:"details",className:"arabic-text",children:[(0,t.jsx)(C.A,{className:"h-4 w-4 mr-2"}),"التفاصيل والألوان"]}),(0,t.jsxs)(p.Xi,{value:"images",className:"arabic-text",children:[(0,t.jsx)(A.A,{className:"h-4 w-4 mr-2"}),"الصور"]}),(0,t.jsxs)(p.Xi,{value:"features",className:"arabic-text",children:[(0,t.jsx)(E.A,{className:"h-4 w-4 mr-2"}),"المميزات والمواصفات"]}),(0,t.jsxs)(p.Xi,{value:"ai",className:"arabic-text",children:[(0,t.jsx)(S.A,{className:"h-4 w-4 mr-2"}),"الذكاء الاصطناعي"]})]}),(0,t.jsxs)(p.av,{value:"basic",className:"space-y-6 mt-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(x.J,{htmlFor:"name",className:"arabic-text",children:"اسم المنتج *"}),(0,t.jsx)(o.p,{id:"name",value:j.name,onChange:e=>b(a=>({...a,name:e.target.value})),placeholder:"أدخل اسم المنتج",className:"arabic-text"}),J.name&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(z.A,{className:"h-4 w-4"}),J.name]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(x.J,{htmlFor:"category",className:"arabic-text",children:"فئة المنتج *"}),(0,t.jsxs)(m.l6,{value:j.category,onValueChange:e=>b(a=>({...a,category:e})),disabled:v,children:[(0,t.jsx)(m.bq,{children:(0,t.jsx)(m.yv,{placeholder:v?"جاري تحميل الفئات...":"اختر فئة المنتج"})}),(0,t.jsx)(m.gC,{children:N.map(e=>(0,t.jsx)(m.eb,{value:e.slug,children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[e.icon&&(0,t.jsx)("span",{children:e.icon}),(0,t.jsx)("span",{className:"arabic-text",children:e.name_ar})]})},e.slug))})]}),J.category&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(z.A,{className:"h-4 w-4"}),J.category]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(x.J,{htmlFor:"description",className:"arabic-text",children:"وصف المنتج *"}),(0,t.jsx)(g.T,{id:"description",value:j.description,onChange:e=>b(a=>({...a,description:e.target.value})),placeholder:"أدخل وصف مفصل للمنتج",className:"arabic-text min-h-[100px]"}),J.description&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(z.A,{className:"h-4 w-4"}),J.description]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(x.J,{htmlFor:"price",className:"arabic-text",children:"السعر (درهم) *"}),(0,t.jsx)(o.p,{id:"price",type:"number",min:"0",step:"0.01",value:j.price,onChange:e=>b(a=>({...a,price:parseFloat(e.target.value)||0})),placeholder:"0.00"}),J.price&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(z.A,{className:"h-4 w-4"}),J.price]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(x.J,{htmlFor:"rental_price",className:"arabic-text",children:"سعر الإيجار (درهم)"}),(0,t.jsx)(o.p,{id:"rental_price",type:"number",min:"0",step:"0.01",value:j.rental_price,onChange:e=>b(a=>({...a,rental_price:parseFloat(e.target.value)||0})),placeholder:"0.00"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(x.J,{htmlFor:"stock_quantity",className:"arabic-text",children:"كمية المخزون *"}),(0,t.jsx)(o.p,{id:"stock_quantity",type:"number",min:"0",value:j.stock_quantity,onChange:e=>b(a=>({...a,stock_quantity:parseInt(e.target.value)||0})),placeholder:"0"}),J.stock_quantity&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(z.A,{className:"h-4 w-4"}),J.stock_quantity]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(k.d,{id:"is_available",checked:j.is_available,onCheckedChange:e=>b(a=>({...a,is_available:e}))}),(0,t.jsx)(x.J,{htmlFor:"is_available",className:"arabic-text",children:"متاح للبيع"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(k.d,{id:"is_published",checked:j.is_published,onCheckedChange:e=>b(a=>({...a,is_published:e}))}),(0,t.jsx)(x.J,{htmlFor:"is_published",className:"arabic-text",children:"نشر المنتج في الكتالوج"})]})]}),(0,t.jsxs)(p.av,{value:"details",className:"space-y-6 mt-6",children:[(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsx)(c.ZB,{className:"arabic-text",children:"الألوان المتاحة"}),(0,t.jsx)(c.BT,{className:"arabic-text",children:"أضف الألوان المتاحة للمنتج"})]}),(0,t.jsxs)(c.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(m.l6,{value:w,onValueChange:O,children:[(0,t.jsx)(m.bq,{className:"flex-1",children:(0,t.jsx)(m.yv,{placeholder:"اختر لون"})}),(0,t.jsx)(m.gC,{children:ee.map(e=>(0,t.jsx)(m.eb,{value:e,children:e},e))})]}),(0,t.jsx)(o.p,{value:w,onChange:e=>O(e.target.value),placeholder:"أو أدخل لون مخصص",className:"flex-1 arabic-text"}),(0,t.jsx)(n.$,{type:"button",onClick:()=>{w.trim()&&!j.colors.includes(w.trim())&&(b(e=>({...e,colors:[...e.colors,w.trim()]})),O(""))},size:"sm",children:(0,t.jsx)(T.A,{className:"h-4 w-4"})})]}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:j.colors.map((e,a)=>(0,t.jsxs)(d.E,{variant:"secondary",className:"arabic-text",children:[e,(0,t.jsx)("button",{type:"button",onClick:()=>H(e),className:"ml-2 hover:text-red-600",children:(0,t.jsx)($.A,{className:"h-3 w-3"})})]},a))}),J.colors&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(z.A,{className:"h-4 w-4"}),J.colors]})]})]}),(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsx)(c.ZB,{className:"arabic-text",children:"المقاسات المتاحة"}),(0,t.jsx)(c.BT,{className:"arabic-text",children:"أضف المقاسات المتاحة للمنتج"})]}),(0,t.jsxs)(c.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(m.l6,{value:U,onValueChange:q,children:[(0,t.jsx)(m.bq,{className:"flex-1",children:(0,t.jsx)(m.yv,{placeholder:"اختر مقاس"})}),(0,t.jsx)(m.gC,{children:ea.map(e=>(0,t.jsx)(m.eb,{value:e,children:e},e))})]}),(0,t.jsx)(o.p,{value:U,onChange:e=>q(e.target.value),placeholder:"أو أدخل مقاس مخصص",className:"flex-1 arabic-text"}),(0,t.jsx)(n.$,{type:"button",onClick:()=>{U.trim()&&!j.sizes.includes(U.trim())&&(b(e=>({...e,sizes:[...e.sizes,U.trim()]})),q(""))},size:"sm",children:(0,t.jsx)(T.A,{className:"h-4 w-4"})})]}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:j.sizes.map((e,a)=>(0,t.jsxs)(d.E,{variant:"secondary",className:"arabic-text",children:[e,(0,t.jsx)("button",{type:"button",onClick:()=>G(e),className:"ml-2 hover:text-red-600",children:(0,t.jsx)($.A,{className:"h-3 w-3"})})]},a))}),J.sizes&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(z.A,{className:"h-4 w-4"}),J.sizes]})]})]})]}),(0,t.jsx)(p.av,{value:"images",className:"space-y-6 mt-6",children:(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsx)(c.ZB,{className:"arabic-text",children:"صور المنتج"}),(0,t.jsx)(c.BT,{className:"arabic-text",children:"أضف صور عالية الجودة للمنتج (يُفضل 500x600 بكسل أو أكبر)"})]}),(0,t.jsx)(c.Wu,{children:(0,t.jsx)(L,{images:j.images,onImagesChange:e=>b(a=>({...a,images:e})),maxImages:8,maxSize:5242880,acceptedTypes:["image/jpeg","image/png","image/webp"]})})]})}),(0,t.jsxs)(p.av,{value:"features",className:"space-y-6 mt-6",children:[(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsx)(c.ZB,{className:"arabic-text",children:"مميزات المنتج"}),(0,t.jsx)(c.BT,{className:"arabic-text",children:"أضف المميزات الرئيسية للمنتج"})]}),(0,t.jsxs)(c.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(o.p,{value:Z,onChange:e=>R(e.target.value),placeholder:"أدخل ميزة جديدة",className:"flex-1 arabic-text"}),(0,t.jsx)(n.$,{type:"button",onClick:()=>{Z.trim()&&!j.features.includes(Z.trim())&&(b(e=>({...e,features:[...e.features,Z.trim()]})),R(""))},size:"sm",children:(0,t.jsx)(T.A,{className:"h-4 w-4"})})]}),(0,t.jsx)("div",{className:"space-y-2",children:j.features.map((e,a)=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg",children:[(0,t.jsx)("span",{className:"arabic-text",children:e}),(0,t.jsx)("button",{type:"button",onClick:()=>M(e),className:"text-red-600 hover:text-red-700",children:(0,t.jsx)($.A,{className:"h-4 w-4"})})]},a))}),0===j.features.length&&(0,t.jsx)("p",{className:"text-gray-500 text-center py-4 arabic-text",children:"لم يتم إضافة أي مميزات بعد"})]})]}),(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsx)(c.ZB,{className:"arabic-text",children:"مواصفات المنتج"}),(0,t.jsx)(c.BT,{className:"arabic-text",children:"أضف المواصفات التقنية للمنتج"})]}),(0,t.jsxs)(c.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,t.jsx)(o.p,{value:P,onChange:e=>B(e.target.value),placeholder:"اسم المواصفة (مثل: المادة)",className:"arabic-text"}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(o.p,{value:W,onChange:e=>I(e.target.value),placeholder:"قيمة المواصفة",className:"flex-1 arabic-text"}),(0,t.jsx)(n.$,{type:"button",onClick:()=>{P.trim()&&W.trim()&&(b(e=>({...e,specifications:{...e.specifications,[P.trim()]:W.trim()}})),B(""),I(""))},size:"sm",children:(0,t.jsx)(T.A,{className:"h-4 w-4"})})]})]}),(0,t.jsx)("div",{className:"space-y-2",children:Object.entries(j.specifications).map(e=>{let[a,s]=e;return(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg",children:[(0,t.jsxs)("div",{className:"arabic-text",children:[(0,t.jsxs)("span",{className:"font-medium",children:[a,":"]})," ",s]}),(0,t.jsx)("button",{type:"button",onClick:()=>K(a),className:"text-red-600 hover:text-red-700",children:(0,t.jsx)($.A,{className:"h-4 w-4"})})]},a)})}),0===Object.keys(j.specifications).length&&(0,t.jsx)("p",{className:"text-gray-500 text-center py-4 arabic-text",children:"لم يتم إضافة أي مواصفات بعد"})]})]})]}),(0,t.jsx)(p.av,{value:"ai",className:"space-y-6 mt-6",children:(0,t.jsx)(Y,{productData:{name:j.name,description:j.description,category:j.category,price:j.price,features:j.features,specifications:j.specifications},onUpdate:(e,a)=>{b(s=>({...s,[e]:a}))}})})]}),(0,t.jsxs)("div",{className:"flex justify-end gap-4 pt-6 border-t",children:[(0,t.jsx)(n.$,{type:"button",variant:"outline",onClick:i,children:"إلغاء"}),(0,t.jsxs)(n.$,{type:"button",variant:"outline",children:[(0,t.jsx)(F.A,{className:"h-4 w-4 mr-2"}),"معاينة"]}),(0,t.jsxs)(n.$,{type:"submit",children:[(0,t.jsx)(D.A,{className:"h-4 w-4 mr-2"}),u?"تحديث المنتج":"إضافة المنتج"]})]})]})}function et(e){let{product:a,open:s,onOpenChange:l,onSave:i}=e,[c,n]=(0,r.useState)(!1),d=async e=>{try{n(!0),await i({...e,id:null==a?void 0:a.id}),l(!1)}catch(e){console.error("Error updating product:",e)}finally{n(!1)}};return a?(0,t.jsx)(h.lG,{open:s,onOpenChange:l,children:(0,t.jsxs)(h.Cf,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,t.jsx)(h.c7,{children:(0,t.jsxs)(h.L3,{className:"arabic-text",children:["تعديل المنتج: ",a.name]})}),(0,t.jsx)(es,{initialData:{name:a.name,description:a.description,category:a.category,price:a.price,rental_price:a.rental_price,colors:a.colors,sizes:a.sizes,images:a.images.map((e,a)=>({id:"existing-".concat(a),file:new File([],"existing-image"),preview:e,uploaded:!0,fallbackUrl:e})),stock_quantity:a.stock_quantity,is_available:a.is_available,features:a.features||[],specifications:a.specifications||{}},onSubmit:d,onCancel:()=>{l(!1)},isEditing:!0})]})}):null}var er=s(11456),el=s(6982),ei=s(6874),ec=s.n(ei),en=s(35169),ed=s(91788),eo=s(1243),ex=s(33109),em=s(13717),eh=s(5623);let ep={gown:"ثوب التخرج",cap:"قبعة التخرج",tassel:"الشرابة",stole:"الوشاح",hood:"القلنسوة"};function eu(){let{user:e,profile:a}=(0,l.A)(),s=(0,el.dj)(),[b,N]=(0,r.useState)([]),[k,C]=(0,r.useState)([]),[A,z]=(0,r.useState)(""),[$,D]=(0,r.useState)("all"),[O,U]=(0,r.useState)("all"),[q,Z]=(0,r.useState)("all"),[R,B]=(0,r.useState)("created_at"),[W,L]=(0,r.useState)("desc"),[I,J]=(0,r.useState)(!0),[Q,Y]=(0,r.useState)(!1),[ee,ea]=(0,r.useState)(null),[ei,eu]=(0,r.useState)(!1),[ej,eg]=(0,r.useState)({open:!1,productId:"",productName:""}),[eb,eN]=(0,r.useState)([]),[ef,ev]=(0,r.useState)(!1),[ey,ew]=(0,r.useState)(null),[ek,e_]=(0,r.useState)({name_ar:"",slug:"",description:"",icon:"",is_active:!0,order_index:0}),[eC,eA]=(0,r.useState)("products");(0,r.useEffect)(()=>{eE()},[]);let eE=async()=>{try{var e;J(!0);let a=await fetch("/api/products");if(!a.ok)throw Error("فشل في جلب المنتجات");let s=await a.json();console.log("Fetched products response:",s),console.log("Products loaded:",(null==(e=s.products)?void 0:e.length)||0),N(s.products||[]),C(s.products||[])}catch(e){console.error("Error fetching products:",e),s.error("فشل في جلب المنتجات")}finally{J(!1)}};(0,r.useEffect)(()=>{let e=[...b];A&&(e=e.filter(e=>e.name.toLowerCase().includes(A.toLowerCase())||e.description.toLowerCase().includes(A.toLowerCase()))),"all"!==$&&(e=e.filter(e=>e.category===$)),"all"!==O&&(e=e.filter(e=>"available"===O?e.is_available:!e.is_available)),"all"!==q&&(e=e.filter(e=>"published"===q?e.is_published:!e.is_published)),e.sort((e,a)=>{let s=e[R],t=a[R];return(("price"===R||"stock_quantity"===R||"rating"===R)&&(s=Number(s)||0,t=Number(t)||0),"asc"===W)?s>t?1:-1:s<t?1:-1}),C(e)},[b,A,$,O,q,R,W]);let eS=(e,a)=>{eg({open:!0,productId:e,productName:a})},ez=async()=>{try{console.log("Attempting to delete product with ID:",ej.productId),console.log("Delete confirm state:",ej);let e=await fetch("/api/products/".concat(ej.productId),{method:"DELETE"});if(console.log("Delete response status:",e.status),!e.ok){let a=await e.json();throw console.log("Delete error response:",a),Error(a.error||"فشل في حذف المنتج")}let a=await e.json();console.log("Delete success response:",a),eg({open:!1,productId:"",productName:""}),await eE(),s.success("تم حذف المنتج بنجاح!")}catch(e){console.error("Error deleting product:",e),s.error(e instanceof Error?e.message:"فشل في حذف المنتج"),eg({open:!1,productId:"",productName:""})}},eT=async e=>{try{let a=b.find(a=>a.id===e);if(!a)return;let t=await fetch("/api/products/".concat(e),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({is_available:!a.is_available})});if(!t.ok){let e=await t.json();throw Error(e.error||"فشل في تحديث حالة المنتج")}await eE(),s.success("تم ".concat(a.is_available?"إلغاء تفعيل":"تفعيل"," المنتج بنجاح"))}catch(e){console.error("Error toggling availability:",e),s.error(e instanceof Error?e.message:"فشل في تحديث حالة المنتج")}},e$=async e=>{try{let a=b.find(a=>a.id===e);if(!a)return;let t=await fetch("/api/products/".concat(e),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({is_published:!a.is_published})});if(!t.ok){let e=await t.json();throw Error(e.error||"فشل في تحديث حالة النشر")}await eE(),s.success("تم ".concat(a.is_published?"إلغاء نشر":"نشر"," المنتج بنجاح"))}catch(e){console.error("Error toggling published status:",e),s.error(e instanceof Error?e.message:"فشل في تحديث حالة النشر")}},eF=async e=>{try{J(!0);let a=[];e.images&&e.images.length>0&&(a=e.images.map(e=>e.uploaded&&e.fallbackUrl?e.fallbackUrl:e.preview).filter(Boolean));let t=await fetch("/api/products",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...e,images:a})});if(!t.ok){let e=await t.json();throw Error(e.error||"فشل في إضافة المنتج")}await t.json(),await eE(),Y(!1),s.success("تم إضافة المنتج بنجاح!")}catch(e){console.error("Error adding product:",e),s.error(e instanceof Error?e.message:"فشل في إضافة المنتج")}finally{J(!1)}},eD=e=>{ea(e),eu(!0)},eO=async e=>{try{J(!0);let a=[];e.images&&e.images.length>0&&(a=e.images.map(e=>e.uploaded&&e.fallbackUrl?e.fallbackUrl:e.preview).filter(Boolean));let t=await fetch("/api/products/".concat(e.id),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({...e,images:a})});if(!t.ok){let e=await t.json();throw Error(e.error||"فشل في تحديث المنتج")}await eE(),s.success("تم تحديث المنتج بنجاح!")}catch(e){console.error("Error updating product:",e),s.error(e instanceof Error?e.message:"فشل في تحديث المنتج")}finally{J(!1)}},eU=async()=>{try{let e=await fetch("/api/categories");if(e.ok){let a=await e.json();eN(a.categories||[])}}catch(e){console.error("Error fetching categories:",e)}};(0,r.useEffect)(()=>{eU()},[]);let eq=async()=>{try{let e=ey?"/api/categories/".concat(ey.id):"/api/categories",a=ey?"PUT":"POST",t=await fetch(e,{method:a,headers:{"Content-Type":"application/json"},body:JSON.stringify(ek)});if(t.ok)await eU(),eZ(),s.success(ey?"تم تحديث الفئة بنجاح!":"تم إضافة الفئة بنجاح!");else{let e=await t.json();s.error(e.error||"فشل في حفظ الفئة")}}catch(e){console.error("Error saving category:",e),s.error("فشل في حفظ الفئة")}},eZ=()=>{e_({name_ar:"",slug:"",description:"",icon:"",is_active:!0,order_index:0}),ew(null),ev(!1)},eR=e=>{e_({name_ar:e.name_ar,slug:e.slug,description:e.description||"",icon:e.icon||"",is_active:e.is_active,order_index:e.order_index}),ew(e),ev(!0)},eP=async e=>{try{let a=await fetch("/api/categories/".concat(e),{method:"DELETE"});if(a.ok)await eU(),s.success("تم حذف الفئة بنجاح!");else{let e=await a.json();s.error(e.error||"فشل في حذف الفئة")}}catch(e){console.error("Error deleting category:",e),s.error("فشل في حذف الفئة")}};return(console.log("User:",e),console.log("Profile:",a),console.log("Profile role:",null==a?void 0:a.role),e&&(null==a?void 0:a.role)==="admin")?(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",children:[(0,t.jsx)(i.V,{}),(0,t.jsxs)("main",{className:"container mx-auto px-4 py-8",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("div",{className:"flex items-center gap-4 mb-4",children:(0,t.jsx)(n.$,{variant:"outline",size:"sm",asChild:!0,children:(0,t.jsxs)(ec(),{href:"/dashboard/admin",children:[(0,t.jsx)(en.A,{className:"h-4 w-4 mr-2"}),"العودة للوحة التحكم"]})})}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white arabic-text",children:"إدارة المنتجات والفئات \uD83D\uDCE6"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-300 mt-2 arabic-text",children:"إضافة وتعديل وإدارة منتجات وفئات المنصة"})]}),(0,t.jsxs)("div",{className:"flex gap-3",children:[(0,t.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(ed.A,{className:"h-4 w-4 mr-2"}),"تصدير"]}),"products"===eC&&(0,t.jsxs)(n.$,{size:"sm",onClick:()=>Y(!0),children:[(0,t.jsx)(T.A,{className:"h-4 w-4 mr-2"}),"إضافة منتج جديد"]}),"categories"===eC&&(0,t.jsxs)(n.$,{size:"sm",onClick:()=>{eZ(),ev(!0)},children:[(0,t.jsx)(T.A,{className:"h-4 w-4 mr-2"}),"إضافة فئة جديدة"]})]})]})]}),(0,t.jsx)(c.Zp,{className:"mb-6 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border-blue-200 dark:border-blue-800",children:(0,t.jsxs)(c.Wu,{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"p-3 bg-blue-600 rounded-lg",children:(0,t.jsx)(S.A,{className:"h-6 w-6 text-white"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white arabic-text",children:"ميزات الذكاء الاصطناعي المتاحة"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-400 arabic-text",children:"استخدم الذكاء الاصطناعي لتحسين منتجاتك تلقائياً"})]})]}),(0,t.jsxs)("div",{className:"flex gap-4",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:"6"}),(0,t.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"ميزات متاحة"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-green-600",children:"نشط"}),(0,t.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"حالة النظام"})]})]})]}),(0,t.jsxs)("div",{className:"mt-4 flex flex-wrap gap-2",children:[(0,t.jsxs)(d.E,{variant:"outline",className:"bg-white dark:bg-gray-800",children:[(0,t.jsx)(X.A,{className:"h-3 w-3 mr-1"}),"توليد الأوصاف"]}),(0,t.jsxs)(d.E,{variant:"outline",className:"bg-white dark:bg-gray-800",children:[(0,t.jsx)(V.A,{className:"h-3 w-3 mr-1"}),"توليد العناوين"]}),(0,t.jsxs)(d.E,{variant:"outline",className:"bg-white dark:bg-gray-800",children:[(0,t.jsx)(H.A,{className:"h-3 w-3 mr-1"}),"توليد الميزات"]}),(0,t.jsxs)(d.E,{variant:"outline",className:"bg-white dark:bg-gray-800",children:[(0,t.jsx)(G.A,{className:"h-3 w-3 mr-1"}),"توليد المواصفات"]}),(0,t.jsxs)(d.E,{variant:"outline",className:"bg-white dark:bg-gray-800",children:[(0,t.jsx)(M.A,{className:"h-3 w-3 mr-1"}),"اقتراح الفئات"]}),(0,t.jsxs)(d.E,{variant:"outline",className:"bg-white dark:bg-gray-800",children:[(0,t.jsx)(K.A,{className:"h-3 w-3 mr-1"}),"تحسين SEO"]})]})]})}),(0,t.jsxs)(p.tU,{value:eC,onValueChange:eA,className:"space-y-6",children:[(0,t.jsxs)(p.j7,{className:"grid w-full grid-cols-2",children:[(0,t.jsx)(p.Xi,{value:"products",className:"arabic-text",children:"المنتجات"}),(0,t.jsx)(p.Xi,{value:"categories",className:"arabic-text",children:"الفئات"})]}),(0,t.jsxs)(p.av,{value:"products",className:"space-y-6",children:[(0,t.jsxs)("div",{className:"product-grid grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[(0,t.jsx)(c.Zp,{children:(0,t.jsx)(c.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text",children:"إجمالي المنتجات"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:b.length})]}),(0,t.jsx)(_.A,{className:"h-8 w-8 text-blue-600"})]})})}),(0,t.jsx)(c.Zp,{children:(0,t.jsx)(c.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text",children:"المنتجات المتاحة"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:b.filter(e=>e.is_available).length})]}),(0,t.jsx)(E.A,{className:"h-8 w-8 text-green-600"})]})})}),(0,t.jsx)(c.Zp,{children:(0,t.jsx)(c.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text",children:"المنتجات المنشورة"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:b.filter(e=>e.is_published).length})]}),(0,t.jsx)(F.A,{className:"h-8 w-8 text-blue-600"})]})})}),(0,t.jsx)(c.Zp,{children:(0,t.jsx)(c.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text",children:"مخزون منخفض"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:b.filter(e=>e.stock_quantity<20).length})]}),(0,t.jsx)(eo.A,{className:"h-8 w-8 text-orange-600"})]})})}),(0,t.jsx)(c.Zp,{children:(0,t.jsx)(c.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text",children:"متوسط التقييم"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:(b.reduce((e,a)=>e+(a.rating||0),0)/b.length).toFixed(1)})]}),(0,t.jsx)(ex.A,{className:"h-8 w-8 text-purple-600"})]})})})]}),(0,t.jsx)(c.Zp,{className:"mb-6",children:(0,t.jsx)(c.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(K.A,{className:"absolute left-3 top-3 h-4 w-4 text-gray-400"}),(0,t.jsx)(o.p,{placeholder:"البحث في المنتجات...",value:A,onChange:e=>z(e.target.value),className:"pl-10 arabic-text"})]}),(0,t.jsxs)(m.l6,{value:$,onValueChange:D,children:[(0,t.jsx)(m.bq,{children:(0,t.jsx)(m.yv,{placeholder:"جميع الفئات"})}),(0,t.jsxs)(m.gC,{children:[(0,t.jsx)(m.eb,{value:"all",children:"جميع الفئات"}),Object.entries(ep).map(e=>{let[a,s]=e;return(0,t.jsx)(m.eb,{value:a,children:s},a)})]})]}),(0,t.jsxs)(m.l6,{value:O,onValueChange:U,children:[(0,t.jsx)(m.bq,{children:(0,t.jsx)(m.yv,{placeholder:"جميع المنتجات"})}),(0,t.jsxs)(m.gC,{children:[(0,t.jsx)(m.eb,{value:"all",children:"جميع المنتجات"}),(0,t.jsx)(m.eb,{value:"available",children:"متاح"}),(0,t.jsx)(m.eb,{value:"unavailable",children:"غير متاح"})]})]}),(0,t.jsxs)(m.l6,{value:q,onValueChange:Z,children:[(0,t.jsx)(m.bq,{children:(0,t.jsx)(m.yv,{placeholder:"حالة النشر"})}),(0,t.jsxs)(m.gC,{children:[(0,t.jsx)(m.eb,{value:"all",children:"جميع المنتجات"}),(0,t.jsx)(m.eb,{value:"published",children:"منشور"}),(0,t.jsx)(m.eb,{value:"unpublished",children:"غير منشور"})]})]}),(0,t.jsxs)(m.l6,{value:R,onValueChange:B,children:[(0,t.jsx)(m.bq,{children:(0,t.jsx)(m.yv,{placeholder:"ترتيب حسب"})}),(0,t.jsxs)(m.gC,{children:[(0,t.jsx)(m.eb,{value:"created_at",children:"تاريخ الإنشاء"}),(0,t.jsx)(m.eb,{value:"name",children:"الاسم"}),(0,t.jsx)(m.eb,{value:"price",children:"السعر"}),(0,t.jsx)(m.eb,{value:"stock_quantity",children:"المخزون"}),(0,t.jsx)(m.eb,{value:"rating",children:"التقييم"})]})]}),(0,t.jsxs)(m.l6,{value:W,onValueChange:e=>L(e),children:[(0,t.jsx)(m.bq,{children:(0,t.jsx)(m.yv,{})}),(0,t.jsxs)(m.gC,{children:[(0,t.jsx)(m.eb,{value:"desc",children:"تنازلي"}),(0,t.jsx)(m.eb,{value:"asc",children:"تصاعدي"})]})]})]})})}),(0,t.jsxs)(c.Zp,{children:[(0,t.jsx)(c.aR,{children:(0,t.jsxs)(c.ZB,{className:"arabic-text",children:["قائمة المنتجات (",k.length,")"]})}),(0,t.jsx)(c.Wu,{children:I?(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,t.jsx)("p",{className:"text-gray-500 mt-2 arabic-text",children:"جاري التحميل..."})]}):0===k.length?(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)(_.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-500 arabic-text",children:"لا توجد منتجات"})]}):(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)("table",{className:"w-full",children:[(0,t.jsx)("thead",{children:(0,t.jsxs)("tr",{className:"border-b",children:[(0,t.jsx)("th",{className:"text-right py-3 px-4 font-medium text-gray-900 dark:text-white arabic-text",children:"المنتج"}),(0,t.jsx)("th",{className:"text-right py-3 px-4 font-medium text-gray-900 dark:text-white arabic-text",children:"الفئة"}),(0,t.jsx)("th",{className:"text-right py-3 px-4 font-medium text-gray-900 dark:text-white arabic-text",children:"السعر"}),(0,t.jsx)("th",{className:"text-right py-3 px-4 font-medium text-gray-900 dark:text-white arabic-text",children:"المخزون"}),(0,t.jsx)("th",{className:"text-right py-3 px-4 font-medium text-gray-900 dark:text-white arabic-text",children:"التقييم"}),(0,t.jsx)("th",{className:"text-right py-3 px-4 font-medium text-gray-900 dark:text-white arabic-text",children:"الحالة"}),(0,t.jsx)("th",{className:"text-right py-3 px-4 font-medium text-gray-900 dark:text-white arabic-text",children:"النشر"}),(0,t.jsx)("th",{className:"text-right py-3 px-4 font-medium text-gray-900 dark:text-white arabic-text",children:"الإجراءات"})]})}),(0,t.jsx)("tbody",{children:k.map(e=>{var a;return(0,t.jsxs)("tr",{className:"border-b hover:bg-gray-50 dark:hover:bg-gray-800",children:[(0,t.jsx)("td",{className:"py-4 px-4",children:(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("img",{src:e.images[0]||"/api/placeholder/80/80",alt:e.name,className:"w-16 h-16 rounded-lg object-cover border border-gray-200 dark:border-gray-700"}),e.images.length>1&&(0,t.jsx)("div",{className:"absolute -top-1 -right-1 bg-blue-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center",children:e.images.length})]}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsx)("p",{className:"font-medium text-gray-900 dark:text-white arabic-text line-clamp-1",children:e.name}),(0,t.jsx)("p",{className:"text-sm text-gray-500 arabic-text line-clamp-2 mt-1",children:e.description})]})]})}),(0,t.jsx)("td",{className:"py-4 px-4",children:(0,t.jsx)(d.E,{variant:"outline",className:"arabic-text",children:ep[e.category]})}),(0,t.jsx)("td",{className:"py-4 px-4",children:(0,t.jsxs)("div",{className:"text-sm",children:[(0,t.jsxs)("p",{className:"price font-medium text-gray-900 dark:text-white",children:[e.price," Dhs"]}),e.rental_price&&(0,t.jsxs)("p",{className:"price text-gray-500",children:["إيجار: ",e.rental_price," Dhs"]})]})}),(0,t.jsx)("td",{className:"py-4 px-4",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{className:"font-medium ".concat(e.stock_quantity<20?"text-red-600":e.stock_quantity<50?"text-orange-600":"text-green-600"),children:e.stock_quantity}),e.stock_quantity<20&&(0,t.jsx)(eo.A,{className:"h-4 w-4 text-red-600"})]})}),(0,t.jsx)("td",{className:"py-4 px-4",children:(0,t.jsxs)("div",{className:"rating flex items-center gap-1",children:[(0,t.jsx)(E.A,{className:"h-4 w-4 text-yellow-400 fill-current"}),(0,t.jsx)("span",{className:"number text-sm font-medium",children:(null==(a=e.rating)?void 0:a.toFixed(1))||"N/A"}),(0,t.jsxs)("span",{className:"number text-xs text-gray-500",children:["(",e.reviews_count||0,")"]})]})}),(0,t.jsx)("td",{className:"py-4 px-4",children:(0,t.jsx)(d.E,{variant:e.is_available?"default":"secondary",className:"arabic-text",children:e.is_available?"متاح":"غير متاح"})}),(0,t.jsx)("td",{className:"py-4 px-4",children:(0,t.jsx)(d.E,{variant:e.is_published?"default":"destructive",className:"arabic-text",children:e.is_published?"منشور":"غير منشور"})}),(0,t.jsx)("td",{className:"py-4 px-4",children:(0,t.jsx)(f,{children:(0,t.jsxs)("div",{className:"actions flex items-center gap-2",children:[(0,t.jsxs)(v,{children:[(0,t.jsx)(y,{asChild:!0,children:(0,t.jsx)(n.$,{size:"sm",variant:"outline",asChild:!0,children:(0,t.jsx)("a",{href:"/product/".concat(e.id),target:"_blank",children:(0,t.jsx)(F.A,{className:"h-4 w-4"})})})}),(0,t.jsx)(w,{children:(0,t.jsx)("p",{children:"عرض المنتج"})})]}),(0,t.jsxs)(v,{children:[(0,t.jsx)(y,{asChild:!0,children:(0,t.jsx)(n.$,{size:"sm",variant:"outline",onClick:()=>eD(e),children:(0,t.jsx)(em.A,{className:"h-4 w-4"})})}),(0,t.jsx)(w,{children:(0,t.jsx)("p",{children:"تعديل المنتج"})})]}),(0,t.jsxs)(v,{children:[(0,t.jsx)(y,{asChild:!0,children:(0,t.jsx)(n.$,{size:"sm",variant:"outline",onClick:()=>eT(e.id),children:e.is_available?"\uD83D\uDD12":"\uD83D\uDD13"})}),(0,t.jsx)(w,{children:(0,t.jsx)("p",{children:e.is_available?"إلغاء التوفر":"تفعيل التوفر"})})]}),(0,t.jsxs)(v,{children:[(0,t.jsx)(y,{asChild:!0,children:(0,t.jsx)(n.$,{size:"sm",variant:"outline",onClick:()=>e$(e.id),children:e.is_published?"\uD83D\uDC41️":"\uD83D\uDE48"})}),(0,t.jsx)(w,{children:(0,t.jsx)("p",{children:e.is_published?"إلغاء النشر":"نشر المنتج"})})]}),(0,t.jsxs)(v,{children:[(0,t.jsx)(y,{asChild:!0,children:(0,t.jsx)(n.$,{size:"sm",variant:"outline",onClick:()=>eS(e.id,e.name),className:"text-red-600 hover:text-red-700",children:(0,t.jsx)(P.A,{className:"h-4 w-4"})})}),(0,t.jsx)(w,{children:(0,t.jsx)("p",{children:"حذف المنتج"})})]})]})})})]},e.id)})})]})})})]})]}),(0,t.jsx)(p.av,{value:"categories",className:"space-y-6",children:(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsx)(c.ZB,{className:"arabic-text",children:"إدارة الفئات"}),(0,t.jsx)(c.BT,{className:"arabic-text",children:"إضافة وتعديل وإدارة فئات المنتجات"})]}),(0,t.jsx)(c.Wu,{children:(0,t.jsxs)(u.XI,{children:[(0,t.jsx)(u.A0,{children:(0,t.jsxs)(u.Hj,{children:[(0,t.jsx)(u.nd,{className:"arabic-text",children:"الاسم"}),(0,t.jsx)(u.nd,{className:"arabic-text",children:"الرابط المختصر"}),(0,t.jsx)(u.nd,{className:"arabic-text",children:"الحالة"}),(0,t.jsx)(u.nd,{className:"arabic-text",children:"الترتيب"}),(0,t.jsx)(u.nd,{className:"arabic-text",children:"تاريخ الإنشاء"}),(0,t.jsx)(u.nd,{className:"arabic-text",children:"الإجراءات"})]})}),(0,t.jsx)(u.BF,{children:eb.map(e=>(0,t.jsxs)(u.Hj,{children:[(0,t.jsx)(u.nA,{children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[e.icon&&(0,t.jsx)("span",{className:"text-lg",children:e.icon}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium arabic-text",children:e.name_ar}),e.description&&(0,t.jsx)("div",{className:"text-sm text-gray-500 arabic-text",children:e.description})]})]})}),(0,t.jsx)(u.nA,{children:(0,t.jsx)("code",{className:"bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded text-sm",children:e.slug})}),(0,t.jsx)(u.nA,{children:(0,t.jsx)(d.E,{variant:e.is_active?"default":"secondary",children:e.is_active?"نشط":"غير نشط"})}),(0,t.jsx)(u.nA,{children:e.order_index}),(0,t.jsx)(u.nA,{children:new Date(e.created_at).toLocaleDateString("en-US")}),(0,t.jsx)(u.nA,{children:(0,t.jsxs)(j.rI,{children:[(0,t.jsx)(j.ty,{asChild:!0,children:(0,t.jsx)(n.$,{variant:"ghost",className:"h-8 w-8 p-0",children:(0,t.jsx)(eh.A,{className:"h-4 w-4"})})}),(0,t.jsxs)(j.SQ,{align:"end",children:[(0,t.jsxs)(j._2,{onClick:()=>eR(e),children:[(0,t.jsx)(em.A,{className:"h-4 w-4 mr-2"}),"تعديل"]}),(0,t.jsxs)(j._2,{onClick:()=>eP(e.id),className:"text-red-600",children:[(0,t.jsx)(P.A,{className:"h-4 w-4 mr-2"}),"حذف"]})]})]})})]},e.id))})]})})]})})]})]}),(0,t.jsx)(h.lG,{open:ef,onOpenChange:ev,children:(0,t.jsxs)(h.Cf,{className:"max-w-2xl",children:[(0,t.jsxs)(h.c7,{children:[(0,t.jsx)(h.L3,{className:"arabic-text",children:ey?"تعديل الفئة":"إضافة فئة جديدة"}),(0,t.jsx)(h.rr,{className:"arabic-text",children:ey?"تعديل بيانات الفئة":"أدخل بيانات الفئة الجديدة"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(x.J,{htmlFor:"category-name",className:"arabic-text",children:"اسم الفئة (مطلوب)"}),(0,t.jsx)(o.p,{id:"category-name",value:ek.name_ar,onChange:e=>e_(a=>({...a,name_ar:e.target.value})),placeholder:"أدخل اسم الفئة",className:"arabic-text"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(x.J,{htmlFor:"category-slug",className:"arabic-text",children:"الرابط المختصر (مطلوب)"}),(0,t.jsx)(o.p,{id:"category-slug",value:ek.slug,onChange:e=>e_(a=>({...a,slug:e.target.value})),placeholder:"category-slug"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(x.J,{htmlFor:"category-description",className:"arabic-text",children:"الوصف"}),(0,t.jsx)(g.T,{id:"category-description",value:ek.description,onChange:e=>e_(a=>({...a,description:e.target.value})),placeholder:"وصف الفئة",className:"arabic-text"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(x.J,{htmlFor:"category-icon",className:"arabic-text",children:"الأيقونة"}),(0,t.jsx)(o.p,{id:"category-icon",value:ek.icon,onChange:e=>e_(a=>({...a,icon:e.target.value})),placeholder:"\uD83C\uDFF7️"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("input",{type:"checkbox",id:"category-active",checked:ek.is_active,onChange:e=>e_(a=>({...a,is_active:e.target.checked}))}),(0,t.jsx)(x.J,{htmlFor:"category-active",className:"arabic-text",children:"فئة نشطة"})]}),(0,t.jsxs)("div",{className:"flex justify-end gap-3",children:[(0,t.jsx)(n.$,{variant:"outline",onClick:eZ,children:"إلغاء"}),(0,t.jsx)(n.$,{onClick:eq,children:ey?"تحديث":"إضافة"})]})]})]})}),(0,t.jsx)(h.lG,{open:Q,onOpenChange:Y,children:(0,t.jsxs)(h.Cf,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,t.jsxs)(h.c7,{children:[(0,t.jsx)(h.L3,{className:"arabic-text",children:"إضافة منتج جديد"}),(0,t.jsx)(h.rr,{className:"arabic-text",children:"أدخل تفاصيل المنتج الجديد"})]}),(0,t.jsx)(es,{onSubmit:eF,onCancel:()=>Y(!1)})]})}),(0,t.jsx)(et,{product:ee,open:ei,onOpenChange:eu,onSave:eO}),(0,t.jsx)(er.T,{open:ej.open,onOpenChange:e=>eg(a=>({...a,open:e})),title:"تأكيد حذف المنتج",description:'هل أنت متأكد من حذف المنتج "'.concat(ej.productName,'"؟ هذا الإجراء لا يمكن التراجع عنه.'),confirmText:"حذف",cancelText:"إلغاء",variant:"destructive",onConfirm:ez}),(0,t.jsx)(el.N9,{toasts:s.toasts,onRemove:s.removeToast})]}):(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-red-600 arabic-text",children:"غير مصرح لك بالوصول"}),(0,t.jsx)("p",{className:"text-gray-600 mt-2 arabic-text",children:"هذه الصفحة مخصصة للمديرين فقط"}),(0,t.jsxs)("p",{className:"text-sm text-gray-500 mt-2",children:["User: ",e?"موجود":"غير موجود"]}),(0,t.jsxs)("p",{className:"text-sm text-gray-500",children:["Role: ",(null==a?void 0:a.role)||"غير محدد"]})]})})}},17313:(e,a,s)=>{"use strict";s.d(a,{Xi:()=>n,av:()=>d,j7:()=>c,tU:()=>i});var t=s(95155);s(12115);var r=s(60704),l=s(59434);function i(e){let{className:a,...s}=e;return(0,t.jsx)(r.bL,{"data-slot":"tabs",className:(0,l.cn)("flex flex-col gap-2",a),...s})}function c(e){let{className:a,...s}=e;return(0,t.jsx)(r.B8,{"data-slot":"tabs-list",className:(0,l.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",a),...s})}function n(e){let{className:a,...s}=e;return(0,t.jsx)(r.l9,{"data-slot":"tabs-trigger",className:(0,l.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...s})}function d(e){let{className:a,...s}=e;return(0,t.jsx)(r.UC,{"data-slot":"tabs-content",className:(0,l.cn)("flex-1 outline-none",a),...s})}},24944:(e,a,s)=>{"use strict";s.d(a,{k:()=>i});var t=s(95155);s(12115);var r=s(55863),l=s(59434);function i(e){let{className:a,value:s,...i}=e;return(0,t.jsx)(r.bL,{"data-slot":"progress",className:(0,l.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",a),...i,children:(0,t.jsx)(r.C1,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:"translateX(-".concat(100-(s||0),"%)")}})})}},34898:(e,a,s)=>{Promise.resolve().then(s.bind(s,12726))},55365:(e,a,s)=>{"use strict";s.d(a,{Fc:()=>c,TN:()=>n});var t=s(95155);s(12115);var r=s(74466),l=s(59434);let i=(0,r.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function c(e){let{className:a,variant:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,l.cn)(i({variant:s}),a),...r})}function n(e){let{className:a,...s}=e;return(0,t.jsx)("div",{"data-slot":"alert-description",className:(0,l.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",a),...s})}},85127:(e,a,s)=>{"use strict";s.d(a,{A0:()=>c,BF:()=>n,Hj:()=>d,XI:()=>i,nA:()=>x,nd:()=>o});var t=s(95155),r=s(12115),l=s(59434);let i=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("div",{className:"relative w-full overflow-auto",children:(0,t.jsx)("table",{ref:a,className:(0,l.cn)("w-full caption-bottom text-sm",s),...r})})});i.displayName="Table";let c=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("thead",{ref:a,className:(0,l.cn)("[&_tr]:border-b",s),...r})});c.displayName="TableHeader";let n=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("tbody",{ref:a,className:(0,l.cn)("[&_tr:last-child]:border-0",s),...r})});n.displayName="TableBody",r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("tfoot",{ref:a,className:(0,l.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",s),...r})}).displayName="TableFooter";let d=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("tr",{ref:a,className:(0,l.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",s),...r})});d.displayName="TableRow";let o=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("th",{ref:a,className:(0,l.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",s),...r})});o.displayName="TableHead";let x=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("td",{ref:a,className:(0,l.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",s),...r})});x.displayName="TableCell",r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("caption",{ref:a,className:(0,l.cn)("mt-4 text-sm text-muted-foreground",s),...r})}).displayName="TableCaption"}},e=>{var a=a=>e(e.s=a);e.O(0,[7598,5486,380,2433,6874,8698,6671,7889,9221,4358,5083,4775,2632,3898,6566,4601,8441,1684,7358],()=>a(34898)),_N_E=e.O()}]);