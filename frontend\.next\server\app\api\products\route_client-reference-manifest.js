globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/api/products/route"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/sonner/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/sonner/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/chat/LiveChat.tsx":{"*":{"id":"(ssr)/./src/components/chat/LiveChat.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/theme-provider.tsx":{"*":{"id":"(ssr)/./src/components/theme-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/AuthContext.tsx":{"*":{"id":"(ssr)/./src/contexts/AuthContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/CartContext.tsx":{"*":{"id":"(ssr)/./src/contexts/CartContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/MenuContext.tsx":{"*":{"id":"(ssr)/./src/contexts/MenuContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/NotificationContext.tsx":{"*":{"id":"(ssr)/./src/contexts/NotificationContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/error.tsx":{"*":{"id":"(ssr)/./src/app/error.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/not-found.tsx":{"*":{"id":"(ssr)/./src/app/not-found.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/admin/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/admin/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/page.tsx":{"*":{"id":"(ssr)/./src/app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/profile/page.tsx":{"*":{"id":"(ssr)/./src/app/profile/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/admin/products/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/admin/products/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Cairo\",\"arguments\":[{\"variable\":\"--font-cairo\",\"subsets\":[\"arabic\",\"latin\"],\"display\":\"swap\"}],\"variableName\":\"cairo\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Cairo\",\"arguments\":[{\"variable\":\"--font-cairo\",\"subsets\":[\"arabic\",\"latin\"],\"display\":\"swap\"}],\"variableName\":\"cairo\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\node_modules\\sonner\\dist\\index.mjs":{"id":"(app-pages-browser)/./node_modules/sonner/dist/index.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\chat\\LiveChat.tsx":{"id":"(app-pages-browser)/./src/components/chat/LiveChat.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\components\\theme-provider.tsx":{"id":"(app-pages-browser)/./src/components/theme-provider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\contexts\\AuthContext.tsx":{"id":"(app-pages-browser)/./src/contexts/AuthContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\contexts\\CartContext.tsx":{"id":"(app-pages-browser)/./src/contexts/CartContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\contexts\\MenuContext.tsx":{"id":"(app-pages-browser)/./src/contexts/MenuContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\contexts\\NotificationContext.tsx":{"id":"(app-pages-browser)/./src/contexts/NotificationContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\error.tsx":{"id":"(app-pages-browser)/./src/app/error.tsx","name":"*","chunks":["app/error","static/chunks/app/error.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\not-found.tsx":{"id":"(app-pages-browser)/./src/app/not-found.tsx","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\admin\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/admin/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\page.tsx":{"id":"(app-pages-browser)/./src/app/page.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\profile\\page.tsx":{"id":"(app-pages-browser)/./src/app/profile/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\dashboard\\admin\\products\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/admin/products/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\":[],"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\error":[],"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\not-found":[],"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\page":[],"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\products\\route":[]},"rscModuleMapping":{"(app-pages-browser)/./node_modules/sonner/dist/index.mjs":{"*":{"id":"(rsc)/./node_modules/sonner/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/chat/LiveChat.tsx":{"*":{"id":"(rsc)/./src/components/chat/LiveChat.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/theme-provider.tsx":{"*":{"id":"(rsc)/./src/components/theme-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/AuthContext.tsx":{"*":{"id":"(rsc)/./src/contexts/AuthContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/CartContext.tsx":{"*":{"id":"(rsc)/./src/contexts/CartContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/MenuContext.tsx":{"*":{"id":"(rsc)/./src/contexts/MenuContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/NotificationContext.tsx":{"*":{"id":"(rsc)/./src/contexts/NotificationContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/error.tsx":{"*":{"id":"(rsc)/./src/app/error.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/not-found.tsx":{"*":{"id":"(rsc)/./src/app/not-found.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/admin/page.tsx":{"*":{"id":"(rsc)/./src/app/dashboard/admin/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/page.tsx":{"*":{"id":"(rsc)/./src/app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/profile/page.tsx":{"*":{"id":"(rsc)/./src/app/profile/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/admin/products/page.tsx":{"*":{"id":"(rsc)/./src/app/dashboard/admin/products/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}