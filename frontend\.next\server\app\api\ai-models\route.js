"use strict";(()=>{var e={};e.id=6139,e.ids=[6139],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64607:(e,t,s)=>{s.r(t),s.d(t,{patchFetch:()=>A,routeModule:()=>g,serverHooks:()=>f,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>v});var r={};s.r(r),s.d(r,{DELETE:()=>p,GET:()=>l,POST:()=>u,PUT:()=>c});var n=s(96559),i=s(48088),o=s(37719),a=s(32190),d=s(38561);async function l(e){try{let{searchParams:t}=new URL(e.url),s=t.get("provider"),r=t.get("type"),n=t.get("status"),i="true"===t.get("include_inactive"),o=parseInt(t.get("page")||"1"),l=parseInt(t.get("limit")||"10"),u=d.CN.getAIModels();s&&(u=u.filter(e=>e.provider===s)),r&&(u=u.filter(e=>e.type===r)),n&&(u=u.filter(e=>e.status===n)),i||(u=u.filter(e=>e.isActive));let c=(o-1)*l,p=u.slice(c,c+l),g={total:u.length,active:u.filter(e=>e.isActive).length,inactive:u.filter(e=>!e.isActive).length,byProvider:u.reduce((e,t)=>(e[t.provider]=(e[t.provider]||0)+1,e),{}),byType:u.reduce((e,t)=>(e[t.type]=(e[t.type]||0)+1,e),{})};return a.NextResponse.json({models:p,total:u.length,page:o,limit:l,totalPages:Math.ceil(u.length/l),stats:g})}catch(e){return console.error("Error fetching AI models:",e),a.NextResponse.json({error:"خطأ في جلب نماذج الذكاء الاصطناعي"},{status:500})}}async function u(e){try{let{name:t,provider:s,type:r,description:n,apiKey:i,apiEndpoint:o,baseUrl:l,settings:u,selectedModels:c,subModels:p}=await e.json();if(!s)return a.NextResponse.json({error:"مقدم الخدمة مطلوب"},{status:400});if(!l)return a.NextResponse.json({error:"Base URL مطلوب"},{status:400});if(!c||0===c.length)return a.NextResponse.json({error:"يجب تحديد نموذج واحد على الأقل"},{status:400});let g=d.CN.getAIModels();if(g.find(e=>e.provider===s))return a.NextResponse.json({error:`مقدم الخدمة ${s} موجود بالفعل. يمكنك تحرير الإعدادات الموجودة أو حذف المقدم الحالي أولاً.`},{status:409});let m=t||({openai:"OpenAI",anthropic:"Anthropic Claude",google:"Google Gemini",meta:"Meta LLaMA",stability:"Stability AI",cohere:"Cohere",huggingface:"Hugging Face",deepseek:"DeepSeek"})[s]||s,v=r||function(e){let t=e.some(e=>e.includes("dall-e")||e.includes("stable-diffusion")||e.includes("vision")),s=e.some(e=>e.includes("whisper")||e.includes("tts")),r=e.some(e=>e.includes("embed")),n=e.some(e=>e.includes("code")||e.includes("coder"));return e.some(e=>e.includes("vision")||e.includes("multimodal"))?"multimodal":t?"image":s?"audio":r?"embedding":n?"code":"text"}(c||[]),f={id:d.CN.generateId(),name:m,provider:s,type:v,description:n||`نماذج ${m} للذكاء الاصطناعي`,subModels:p||[],selectedModels:c||[],apiKey:i,baseUrl:l,isActive:!0,status:"inactive",settings:u||{temperature:.7,maxTokens:2048,topP:1,frequencyPenalty:0,presencePenalty:0},usage:{totalRequests:0,totalTokens:0,totalCost:0,dailyUsage:[],monthlyUsage:[],averageResponseTime:0,successRate:0},createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};g.push(f),d.CN.saveAIModels(g);let A=d.CN.getModelActivities();return A.push({id:d.CN.generateId(),modelId:f.id,type:"config_change",description:`تم إضافة مقدم خدمة جديد: ${m}`,timestamp:new Date().toISOString(),success:!0}),d.CN.saveModelActivities(A),a.NextResponse.json({message:"تم إضافة النموذج بنجاح",model:f},{status:201})}catch(e){return console.error("Error creating AI model:",e),a.NextResponse.json({error:"خطأ في إنشاء النموذج"},{status:500})}}async function c(e){try{let{action:t,modelIds:s,settings:r}=await e.json();if(!t||!s||!Array.isArray(s))return a.NextResponse.json({error:"الإجراء ومعرفات النماذج مطلوبة"},{status:400});let n=d.CN.getAIModels(),i=d.CN.getModelActivities(),o=0;for(let e of s){let s=n.findIndex(t=>t.id===e);if(-1===s)continue;let a=n[s],l="";switch(t){case"activate":a.isActive=!0,a.status="active",l=`تم تفعيل النموذج: ${a.name}`;break;case"deactivate":a.isActive=!1,a.status="inactive",l=`تم إلغاء تفعيل النموذج: ${a.name}`;break;case"update_settings":r&&(a.settings={...a.settings,...r},l=`تم تحديث إعدادات النموذج: ${a.name}`);break;default:continue}a.updatedAt=new Date().toISOString(),n[s]=a,o++,i.push({id:d.CN.generateId(),modelId:a.id,type:"config_change",description:l,timestamp:new Date().toISOString(),success:!0})}return d.CN.saveAIModels(n),d.CN.saveModelActivities(i),a.NextResponse.json({message:`تم تحديث ${o} نموذج بنجاح`,updatedCount:o})}catch(e){return console.error("Error updating AI models:",e),a.NextResponse.json({error:"خطأ في تحديث النماذج"},{status:500})}}async function p(e){try{let{searchParams:t}=new URL(e.url),s=t.get("ids")?.split(",")||[];if(0===s.length)return a.NextResponse.json({error:"معرفات النماذج مطلوبة"},{status:400});let r=d.CN.getAIModels(),n=d.CN.getModelActivities(),i=0,o=r.filter(e=>!s.includes(e.id)||(n.push({id:d.CN.generateId(),modelId:e.id,type:"config_change",description:`تم حذف النموذج: ${e.name}`,timestamp:new Date().toISOString(),success:!0}),i++,!1));return d.CN.saveAIModels(o),d.CN.saveModelActivities(n),a.NextResponse.json({message:`تم حذف ${i} نموذج بنجاح`,deletedCount:i})}catch(e){return console.error("Error deleting AI models:",e),a.NextResponse.json({error:"خطأ في حذف النماذج"},{status:500})}}let g=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/ai-models/route",pathname:"/api/ai-models",filename:"route",bundlePath:"app/api/ai-models/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\ai-models\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:m,workUnitAsyncStorage:v,serverHooks:f}=g;function A(){return(0,o.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:v})}}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,580,8554],()=>s(64607));module.exports=r})();