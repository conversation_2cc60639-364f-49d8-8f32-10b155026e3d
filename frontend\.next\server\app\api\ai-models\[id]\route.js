"use strict";(()=>{var e={};e.id=3031,e.ids=[3031],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79144:(e,t,s)=>{s.r(t),s.d(t,{patchFetch:()=>x,routeModule:()=>g,serverHooks:()=>h,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>v});var r={};s.r(r),s.d(r,{DELETE:()=>p,GET:()=>u,PATCH:()=>c,PUT:()=>l});var o=s(96559),i=s(48088),n=s(37719),a=s(32190),d=s(38561);async function u(e,{params:t}){try{let{searchParams:s}=new URL(e.url),r="true"===s.get("include_usage"),o="true"===s.get("include_activities"),i=d.CN.getAIModels().find(e=>e.id===t.id);if(!i)return a.NextResponse.json({error:"النموذج غير موجود"},{status:404});let n={model:i};return r&&(n.usage=i.usage,n.usageStats={dailyAverage:i.usage.dailyUsage.length>0?i.usage.dailyUsage.reduce((e,t)=>e+t.requests,0)/i.usage.dailyUsage.length:0,monthlyTotal:i.usage.monthlyUsage.reduce((e,t)=>e+t.requests,0),costPerRequest:i.usage.totalRequests>0?i.usage.totalCost/i.usage.totalRequests:0,tokensPerRequest:i.usage.totalRequests>0?i.usage.totalTokens/i.usage.totalRequests:0}),o&&(n.activities=d.CN.getModelActivities().filter(e=>e.modelId===t.id).sort((e,t)=>new Date(t.timestamp).getTime()-new Date(e.timestamp).getTime()).slice(0,50)),a.NextResponse.json(n)}catch(e){return console.error("Error fetching AI model:",e),a.NextResponse.json({error:"خطأ في جلب النموذج"},{status:500})}}async function l(e,{params:t}){try{let{name:s,description:r,apiKey:o,apiEndpoint:i,baseUrl:n,isActive:u,settings:l,selectedModels:p,subModels:c}=await e.json(),g=d.CN.getAIModels(),m=g.findIndex(e=>e.id===t.id);if(-1===m)return a.NextResponse.json({error:"النموذج غير موجود"},{status:404});let v=g[m];if(s&&s!==v.name&&g.find(e=>e.name.toLowerCase()===s.toLowerCase()&&e.provider===v.provider&&e.id!==t.id))return a.NextResponse.json({error:"نموذج بنفس الاسم ومقدم الخدمة موجود بالفعل"},{status:400});let h={...v,...s&&{name:s},...void 0!==r&&{description:r},...void 0!==o&&{apiKey:o},...void 0!==i&&{apiEndpoint:i},...void 0!==n&&{baseUrl:n},...void 0!==u&&{isActive:u},...l&&{settings:{...v.settings,...l}},...p&&{selectedModels:p},...c&&{subModels:c},updatedAt:new Date().toISOString()};void 0!==u&&(h.status=u?"active":"inactive"),g[m]=h,d.CN.saveAIModels(g);let x=d.CN.getModelActivities(),N=[];return s&&s!==v.name&&N.push(`الاسم: ${s}`),void 0!==r&&r!==v.description&&N.push("الوصف"),void 0!==o&&N.push("مفتاح API"),void 0!==i&&N.push("نقطة النهاية"),void 0!==n&&N.push("Base URL"),void 0!==u&&u!==v.isActive&&N.push(u?"تفعيل":"إلغاء تفعيل"),l&&N.push("الإعدادات"),p&&N.push(`النماذج المحددة: ${p.length}`),c&&N.push(`النماذج الفرعية: ${c.length}`),x.push({id:d.CN.generateId(),modelId:t.id,type:"config_change",description:`تم تحديث النموذج: ${N.join(", ")}`,timestamp:new Date().toISOString(),success:!0}),d.CN.saveModelActivities(x),a.NextResponse.json({message:"تم تحديث النموذج بنجاح",model:h})}catch(e){return console.error("Error updating AI model:",e),a.NextResponse.json({error:"خطأ في تحديث النموذج"},{status:500})}}async function p(e,{params:t}){try{let e=d.CN.getAIModels(),s=e.findIndex(e=>e.id===t.id);if(-1===s)return a.NextResponse.json({error:"النموذج غير موجود"},{status:404});let r=e[s];if(r.usage.totalRequests>0&&r.usage.lastUsed&&new Date(r.usage.lastUsed).getTime()>Date.now()-6048e5)return a.NextResponse.json({error:"لا يمكن حذف النموذج لوجود استخدام حديث. يرجى إلغاء تفعيله بدلاً من ذلك.",suggestion:"deactivate"},{status:400});e.splice(s,1),d.CN.saveAIModels(e);let o=d.CN.getModelActivities();return o.push({id:d.CN.generateId(),modelId:t.id,type:"config_change",description:`تم حذف النموذج: ${r.name}`,timestamp:new Date().toISOString(),success:!0}),d.CN.saveModelActivities(o),a.NextResponse.json({message:"تم حذف النموذج بنجاح",deletedModel:{id:r.id,name:r.name,provider:r.provider}})}catch(e){return console.error("Error deleting AI model:",e),a.NextResponse.json({error:"خطأ في حذف النموذج"},{status:500})}}async function c(e,{params:t}){try{let{action:s,data:r}=await e.json();if(!s)return a.NextResponse.json({error:"الإجراء مطلوب"},{status:400});let o=d.CN.getAIModels(),i=o.findIndex(e=>e.id===t.id);if(-1===i)return a.NextResponse.json({error:"النموذج غير موجود"},{status:404});let n=o[i],u=d.CN.getModelActivities();switch(s){case"test_connection":let l=Math.random()>.1,p=Math.floor(3e3*Math.random())+500;n.lastTestedAt=new Date().toISOString(),n.testResult={success:l,responseTime:p,error:l?void 0:"فشل في الاتصال بالخدمة"},l?n.status="active":n.status="error",u.push({id:d.CN.generateId(),modelId:t.id,type:"test",description:`اختبار الاتصال: ${l?"نجح":"فشل"}`,timestamp:new Date().toISOString(),duration:p,success:l,errorMessage:l?void 0:"فشل في الاتصال"});break;case"reset_usage":n.usage={totalRequests:0,totalTokens:0,totalCost:0,dailyUsage:[],monthlyUsage:[],averageResponseTime:0,successRate:0},u.push({id:d.CN.generateId(),modelId:t.id,type:"config_change",description:"تم إعادة تعيين إحصائيات الاستخدام",timestamp:new Date().toISOString(),success:!0});break;case"add_submodel":if(!r||!r.name)return a.NextResponse.json({error:"بيانات النموذج الفرعي مطلوبة"},{status:400});let c={id:d.CN.generateId(),name:r.name,modelId:t.id,description:r.description||"",version:r.version||"1.0",capabilities:r.capabilities||[],pricing:r.pricing||{inputTokens:0,outputTokens:0,currency:"USD",unit:"1K tokens"},limits:r.limits||{maxTokens:4096,requestsPerMinute:60,requestsPerDay:1e3,contextWindow:4096},isActive:!0,isDefault:!1,tags:r.tags||[],releaseDate:new Date().toISOString()};n.subModels.push(c),u.push({id:d.CN.generateId(),modelId:t.id,type:"config_change",description:`تم إضافة نموذج فرعي: ${r.name}`,timestamp:new Date().toISOString(),success:!0});break;default:return a.NextResponse.json({error:"إجراء غير مدعوم"},{status:400})}return n.updatedAt=new Date().toISOString(),o[i]=n,d.CN.saveAIModels(o),d.CN.saveModelActivities(u),a.NextResponse.json({message:"تم تنفيذ الإجراء بنجاح",model:n,action:s})}catch(e){return console.error("Error executing model action:",e),a.NextResponse.json({error:"خطأ في تنفيذ الإجراء"},{status:500})}}let g=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/ai-models/[id]/route",pathname:"/api/ai-models/[id]",filename:"route",bundlePath:"app/api/ai-models/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\ai-models\\[id]\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:m,workUnitAsyncStorage:v,serverHooks:h}=g;function x(){return(0,n.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:v})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,580,8554],()=>s(79144));module.exports=r})();