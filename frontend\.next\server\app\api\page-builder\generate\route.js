"use strict";(()=>{var e={};e.id=2165,e.ids=[2165],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74261:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>m,routeModule:()=>d,serverHooks:()=>g,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>c});var o={};t.r(o),t.d(o,{POST:()=>u});var s=t(96559),n=t(48088),a=t(37719),i=t(32190),p=t(38561);async function u(e){try{let r,{prompt:t,language:o,category:s,style:n,colors:a,includeImages:u,includeText:d,pageType:l,targetAudience:c,businessType:g,modelId:m,includeMainHeader:h,mainMenuItems:x}=await e.json();if(!t||!o)return i.NextResponse.json({error:"الوصف واللغة مطلوبان"},{status:400});let v=null,f=null;if(!m)return i.NextResponse.json({error:"لم يتم تحديد نموذج للتوليد"},{status:400});{let e=m.split("-"),r=e[0],t=e.slice(1).join("-");v={id:r,providerName:"Google AI",status:"active",models:[t]},f=t}if(!v||!f)return i.NextResponse.json({error:"لا يوجد نموذج متاح للتوليد. يرجى إضافة وتفعيل مزود ذكاء اصطناعي أولاً."},{status:400});Date.now();let y=Math.floor(5e3*Math.random())+2e3;v.providerName;try{r=function(e,r){let t=[];return t.push({id:p.CN.generateId(),type:"hero",name:"Hero Section",props:{content:"مرحباً بكم في موقعنا",style:{backgroundColor:"#1F2937",color:"#FFFFFF",textAlign:"center",padding:"4rem 2rem"}},position:{x:0,y:0},size:{width:"100%",height:"500px"},isVisible:!0}),t}(0,0)}catch(e){return console.error("Error in generatePageComponents:",e),i.NextResponse.json({error:"خطأ في توليد مكونات الصفحة"},{status:500})}let N=Math.ceil(t.length/4)+Math.floor(1e3*Math.random())+500,j=N/1e3*.002,k=p.CN.getModelActivities();k.push({id:p.CN.generateId(),modelId:`${v.id}-${f}`,type:"request",description:`توليد صفحة: ${t.substring(0,50)}${t.length>50?"...":""}`,details:{prompt:t,language:o,category:s,pageType:l,componentsGenerated:r.length,provider:v.providerName,model:f},timestamp:new Date().toISOString(),duration:y,tokensUsed:N,cost:j,success:!0}),p.CN.saveModelActivities(k);let w=function(e,r){let t=[];return r.length<3&&t.push("يمكنك إضافة المزيد من الأقسام لجعل الصفحة أكثر تفصيلاً"),r.some(e=>"contact"===e.type)||t.push("فكر في إضافة نموذج اتصال لتسهيل التواصل مع الزوار"),r.some(e=>"testimonial"===e.type)||t.push("إضافة قسم آراء العملاء يمكن أن يزيد من الثقة"),r.some(e=>"gallery"===e.type)||t.push("معرض الصور يمكن أن يجعل الصفحة أكثر جاذبية"),t.push("تأكد من تحسين الصفحة للهواتف المحمولة"),t.push("استخدم ألوان متناسقة مع هوية علامتك التجارية"),t}(0,r),C={success:!0,components:r,suggestions:w,metadata:{tokensUsed:N,generationTime:y,modelUsed:`${v.providerName} - ${f}`,componentsCount:r.length,estimatedCost:j}};return i.NextResponse.json(C)}catch(e){return console.error("Error generating page:",e),console.error("Error details:",e instanceof Error?e.message:"Unknown error"),console.error("Error stack:",e instanceof Error?e.stack:"No stack trace"),i.NextResponse.json({error:"خطأ في توليد الصفحة: "+(e instanceof Error?e.message:"خطأ غير معروف")},{status:500})}}let d=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/page-builder/generate/route",pathname:"/api/page-builder/generate",filename:"route",bundlePath:"app/api/page-builder/generate/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\page-builder\\generate\\route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:l,workUnitAsyncStorage:c,serverHooks:g}=d;function m(){return(0,a.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:c})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[4447,580,8554],()=>t(74261));module.exports=o})();