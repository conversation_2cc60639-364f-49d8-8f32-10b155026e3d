"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7797],{4884:(e,t,n)=>{n.d(t,{bL:()=>D,zi:()=>C});var r=n(12115),l=n(85185),i=n(6101),a=n(46081),o=n(5845),u=n(45503),s=n(11275),c=n(63655),d=n(95155),f="Switch",[h,p]=(0,a.A)(f),[g,v]=h(f),b=r.forwardRef((e,t)=>{let{__scopeSwitch:n,name:a,checked:u,defaultChecked:s,required:h,disabled:p,value:v="on",onCheckedChange:b,form:y,...m}=e,[D,C]=r.useState(null),E=(0,i.s)(t,e=>C(e)),R=r.useRef(!1),k=!D||y||!!D.closest("form"),[M,S]=(0,o.i)({prop:u,defaultProp:null!=s&&s,onChange:b,caller:f});return(0,d.jsxs)(g,{scope:n,checked:M,disabled:p,children:[(0,d.jsx)(c.sG.button,{type:"button",role:"switch","aria-checked":M,"aria-required":h,"data-state":x(M),"data-disabled":p?"":void 0,disabled:p,value:v,...m,ref:E,onClick:(0,l.m)(e.onClick,e=>{S(e=>!e),k&&(R.current=e.isPropagationStopped(),R.current||e.stopPropagation())})}),k&&(0,d.jsx)(w,{control:D,bubbles:!R.current,name:a,value:v,checked:M,required:h,disabled:p,form:y,style:{transform:"translateX(-100%)"}})]})});b.displayName=f;var y="SwitchThumb",m=r.forwardRef((e,t)=>{let{__scopeSwitch:n,...r}=e,l=v(y,n);return(0,d.jsx)(c.sG.span,{"data-state":x(l.checked),"data-disabled":l.disabled?"":void 0,...r,ref:t})});m.displayName=y;var w=r.forwardRef((e,t)=>{let{__scopeSwitch:n,control:l,checked:a,bubbles:o=!0,...c}=e,f=r.useRef(null),h=(0,i.s)(f,t),p=(0,u.Z)(a),g=(0,s.X)(l);return r.useEffect(()=>{let e=f.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(p!==a&&t){let n=new Event("click",{bubbles:o});t.call(e,a),e.dispatchEvent(n)}},[p,a,o]),(0,d.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:a,...c,tabIndex:-1,ref:h,style:{...c.style,...g,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function x(e){return e?"checked":"unchecked"}w.displayName="SwitchBubbleInput";var D=b,C=m},13717:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},15452:(e,t,n)=>{n.d(t,{G$:()=>V,Hs:()=>x,UC:()=>en,VY:()=>el,ZL:()=>ee,bL:()=>Q,bm:()=>ei,hE:()=>er,hJ:()=>et,l9:()=>$});var r=n(12115),l=n(85185),i=n(6101),a=n(46081),o=n(61285),u=n(5845),s=n(19178),c=n(25519),d=n(34378),f=n(28905),h=n(63655),p=n(92293),g=n(93795),v=n(38168),b=n(99708),y=n(95155),m="Dialog",[w,x]=(0,a.A)(m),[D,C]=w(m),E=e=>{let{__scopeDialog:t,children:n,open:l,defaultOpen:i,onOpenChange:a,modal:s=!0}=e,c=r.useRef(null),d=r.useRef(null),[f,h]=(0,u.i)({prop:l,defaultProp:null!=i&&i,onChange:a,caller:m});return(0,y.jsx)(D,{scope:t,triggerRef:c,contentRef:d,contentId:(0,o.B)(),titleId:(0,o.B)(),descriptionId:(0,o.B)(),open:f,onOpenChange:h,onOpenToggle:r.useCallback(()=>h(e=>!e),[h]),modal:s,children:n})};E.displayName=m;var R="DialogTrigger",k=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=C(R,n),o=(0,i.s)(t,a.triggerRef);return(0,y.jsx)(h.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":a.open,"aria-controls":a.contentId,"data-state":X(a.open),...r,ref:o,onClick:(0,l.m)(e.onClick,a.onOpenToggle)})});k.displayName=R;var M="DialogPortal",[S,T]=w(M,{forceMount:void 0}),O=e=>{let{__scopeDialog:t,forceMount:n,children:l,container:i}=e,a=C(M,t);return(0,y.jsx)(S,{scope:t,forceMount:n,children:r.Children.map(l,e=>(0,y.jsx)(f.C,{present:n||a.open,children:(0,y.jsx)(d.Z,{asChild:!0,container:i,children:e})}))})};O.displayName=M;var j="DialogOverlay",I=r.forwardRef((e,t)=>{let n=T(j,e.__scopeDialog),{forceMount:r=n.forceMount,...l}=e,i=C(j,e.__scopeDialog);return i.modal?(0,y.jsx)(f.C,{present:r||i.open,children:(0,y.jsx)(N,{...l,ref:t})}):null});I.displayName=j;var L=(0,b.TL)("DialogOverlay.RemoveScroll"),N=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,l=C(j,n);return(0,y.jsx)(g.A,{as:L,allowPinchZoom:!0,shards:[l.contentRef],children:(0,y.jsx)(h.sG.div,{"data-state":X(l.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),A="DialogContent",P=r.forwardRef((e,t)=>{let n=T(A,e.__scopeDialog),{forceMount:r=n.forceMount,...l}=e,i=C(A,e.__scopeDialog);return(0,y.jsx)(f.C,{present:r||i.open,children:i.modal?(0,y.jsx)(z,{...l,ref:t}):(0,y.jsx)(F,{...l,ref:t})})});P.displayName=A;var z=r.forwardRef((e,t)=>{let n=C(A,e.__scopeDialog),a=r.useRef(null),o=(0,i.s)(t,n.contentRef,a);return r.useEffect(()=>{let e=a.current;if(e)return(0,v.Eq)(e)},[]),(0,y.jsx)(B,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,l.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=n.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,l.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,l.m)(e.onFocusOutside,e=>e.preventDefault())})}),F=r.forwardRef((e,t)=>{let n=C(A,e.__scopeDialog),l=r.useRef(!1),i=r.useRef(!1);return(0,y.jsx)(B,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var r,a;null==(r=e.onCloseAutoFocus)||r.call(e,t),t.defaultPrevented||(l.current||null==(a=n.triggerRef.current)||a.focus(),t.preventDefault()),l.current=!1,i.current=!1},onInteractOutside:t=>{var r,a;null==(r=e.onInteractOutside)||r.call(e,t),t.defaultPrevented||(l.current=!0,"pointerdown"===t.detail.originalEvent.type&&(i.current=!0));let o=t.target;(null==(a=n.triggerRef.current)?void 0:a.contains(o))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),B=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:l,onOpenAutoFocus:a,onCloseAutoFocus:o,...u}=e,d=C(A,n),f=r.useRef(null),h=(0,i.s)(t,f);return(0,p.Oh)(),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(c.n,{asChild:!0,loop:!0,trapped:l,onMountAutoFocus:a,onUnmountAutoFocus:o,children:(0,y.jsx)(s.qW,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":X(d.open),...u,ref:h,onDismiss:()=>d.onOpenChange(!1)})}),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(Z,{titleId:d.titleId}),(0,y.jsx)(J,{contentRef:f,descriptionId:d.descriptionId})]})]})}),W="DialogTitle",G=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,l=C(W,n);return(0,y.jsx)(h.sG.h2,{id:l.titleId,...r,ref:t})});G.displayName=W;var K="DialogDescription",Y=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,l=C(K,n);return(0,y.jsx)(h.sG.p,{id:l.descriptionId,...r,ref:t})});Y.displayName=K;var _="DialogClose",U=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=C(_,n);return(0,y.jsx)(h.sG.button,{type:"button",...r,ref:t,onClick:(0,l.m)(e.onClick,()=>i.onOpenChange(!1))})});function X(e){return e?"open":"closed"}U.displayName=_;var q="DialogTitleWarning",[V,H]=(0,a.q)(q,{contentName:A,titleName:W,docsSlug:"dialog"}),Z=e=>{let{titleId:t}=e,n=H(q),l="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return r.useEffect(()=>{t&&(document.getElementById(t)||console.error(l))},[l,t]),null},J=e=>{let{contentRef:t,descriptionId:n}=e,l=H("DialogDescriptionWarning"),i="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(l.contentName,"}.");return r.useEffect(()=>{var e;let r=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");n&&r&&(document.getElementById(n)||console.warn(i))},[i,t,n]),null},Q=E,$=k,ee=O,et=I,en=P,er=G,el=Y,ei=U},35169:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},40968:(e,t,n)=>{n.d(t,{b:()=>o});var r=n(12115),l=n(63655),i=n(95155),a=r.forwardRef((e,t)=>(0,i.jsx)(l.sG.label,{...e,ref:t,onMouseDown:t=>{var n;t.target.closest("button, input, select, textarea")||(null==(n=e.onMouseDown)||n.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));a.displayName="Label";var o=a},48021:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("grip-vertical",[["circle",{cx:"9",cy:"12",r:"1",key:"1vctgf"}],["circle",{cx:"9",cy:"5",r:"1",key:"hp0tcf"}],["circle",{cx:"9",cy:"19",r:"1",key:"fkjjf6"}],["circle",{cx:"15",cy:"12",r:"1",key:"1tmaij"}],["circle",{cx:"15",cy:"5",r:"1",key:"19l28e"}],["circle",{cx:"15",cy:"19",r:"1",key:"f4zoj3"}]])},50402:(e,t,n)=>{n.d(t,{JR:()=>C,_G:()=>c,be:()=>a,gB:()=>h,gl:()=>w});var r=n(12115),l=n(75143),i=n(78266);function a(e,t,n){let r=e.slice();return r.splice(n<0?r.length+n:n,0,r.splice(t,1)[0]),r}function o(e){return null!==e&&e>=0}let u=e=>{let{rects:t,activeIndex:n,overIndex:r,index:l}=e,i=a(t,r,n),o=t[l],u=i[l];return u&&o?{x:u.left-o.left,y:u.top-o.top,scaleX:u.width/o.width,scaleY:u.height/o.height}:null},s={scaleX:1,scaleY:1},c=e=>{var t;let{activeIndex:n,activeNodeRect:r,index:l,rects:i,overIndex:a}=e,o=null!=(t=i[n])?t:r;if(!o)return null;if(l===n){let e=i[a];return e?{x:0,y:n<a?e.top+e.height-(o.top+o.height):e.top-o.top,...s}:null}let u=function(e,t,n){let r=e[t],l=e[t-1],i=e[t+1];return r?n<t?l?r.top-(l.top+l.height):i?i.top-(r.top+r.height):0:i?i.top-(r.top+r.height):l?r.top-(l.top+l.height):0:0}(i,l,n);return l>n&&l<=a?{x:0,y:-o.height-u,...s}:l<n&&l>=a?{x:0,y:o.height+u,...s}:{x:0,y:0,...s}},d="Sortable",f=r.createContext({activeIndex:-1,containerId:d,disableTransforms:!1,items:[],overIndex:-1,useDragOverlay:!1,sortedRects:[],strategy:u,disabled:{draggable:!1,droppable:!1}});function h(e){let{children:t,id:n,items:a,strategy:o=u,disabled:s=!1}=e,{active:c,dragOverlay:h,droppableRects:p,over:g,measureDroppableContainers:v}=(0,l.fF)(),b=(0,i.YG)(d,n),y=null!==h.rect,m=(0,r.useMemo)(()=>a.map(e=>"object"==typeof e&&"id"in e?e.id:e),[a]),w=null!=c,x=c?m.indexOf(c.id):-1,D=g?m.indexOf(g.id):-1,C=(0,r.useRef)(m),E=!function(e,t){if(e===t)return!0;if(e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}(m,C.current),R=-1!==D&&-1===x||E,k="boolean"==typeof s?{draggable:s,droppable:s}:s;(0,i.Es)(()=>{E&&w&&v(m)},[E,m,w,v]),(0,r.useEffect)(()=>{C.current=m},[m]);let M=(0,r.useMemo)(()=>({activeIndex:x,containerId:b,disabled:k,disableTransforms:R,items:m,overIndex:D,useDragOverlay:y,sortedRects:m.reduce((e,t,n)=>{let r=p.get(t);return r&&(e[n]=r),e},Array(m.length)),strategy:o}),[x,b,k.draggable,k.droppable,R,m,D,p,y,o]);return r.createElement(f.Provider,{value:M},t)}let p=e=>{let{id:t,items:n,activeIndex:r,overIndex:l}=e;return a(n,r,l).indexOf(t)},g=e=>{let{containerId:t,isSorting:n,wasDragging:r,index:l,items:i,newIndex:a,previousItems:o,previousContainerId:u,transition:s}=e;return!!s&&!!r&&(o===i||l!==a)&&(!!n||a!==l&&t===u)},v={duration:200,easing:"ease"},b="transform",y=i.Ks.Transition.toString({property:b,duration:0,easing:"linear"}),m={roleDescription:"sortable"};function w(e){var t,n,a,u;let{animateLayoutChanges:s=g,attributes:c,disabled:d,data:h,getNewIndex:w=p,id:x,strategy:D,resizeObserverConfig:C,transition:E=v}=e,{items:R,containerId:k,activeIndex:M,disabled:S,disableTransforms:T,sortedRects:O,overIndex:j,useDragOverlay:I,strategy:L}=(0,r.useContext)(f),N=(t=d,n=S,"boolean"==typeof t?{draggable:t,droppable:!1}:{draggable:null!=(a=null==t?void 0:t.draggable)?a:n.draggable,droppable:null!=(u=null==t?void 0:t.droppable)?u:n.droppable}),A=R.indexOf(x),P=(0,r.useMemo)(()=>({sortable:{containerId:k,index:A,items:R},...h}),[k,h,A,R]),z=(0,r.useMemo)(()=>R.slice(R.indexOf(x)),[R,x]),{rect:F,node:B,isOver:W,setNodeRef:G}=(0,l.zM)({id:x,data:P,disabled:N.droppable,resizeObserverConfig:{updateMeasurementsFor:z,...C}}),{active:K,activatorEvent:Y,activeNodeRect:_,attributes:U,setNodeRef:X,listeners:q,isDragging:V,over:H,setActivatorNodeRef:Z,transform:J}=(0,l.PM)({id:x,data:P,attributes:{...m,...c},disabled:N.draggable}),Q=(0,i.jn)(G,X),$=!!K,ee=$&&!T&&o(M)&&o(j),et=!I&&V,en=et&&ee?J:null,er=ee?null!=en?en:(null!=D?D:L)({rects:O,activeNodeRect:_,activeIndex:M,overIndex:j,index:A}):null,el=o(M)&&o(j)?w({id:x,items:R,activeIndex:M,overIndex:j}):A,ei=null==K?void 0:K.id,ea=(0,r.useRef)({activeId:ei,items:R,newIndex:el,containerId:k}),eo=R!==ea.current.items,eu=s({active:K,containerId:k,isDragging:V,isSorting:$,id:x,index:A,items:R,newIndex:ea.current.newIndex,previousItems:ea.current.items,previousContainerId:ea.current.containerId,transition:E,wasDragging:null!=ea.current.activeId}),es=function(e){let{disabled:t,index:n,node:a,rect:o}=e,[u,s]=(0,r.useState)(null),c=(0,r.useRef)(n);return(0,i.Es)(()=>{if(!t&&n!==c.current&&a.current){let e=o.current;if(e){let t=(0,l.Sj)(a.current,{ignoreTransform:!0}),n={x:e.left-t.left,y:e.top-t.top,scaleX:e.width/t.width,scaleY:e.height/t.height};(n.x||n.y)&&s(n)}}n!==c.current&&(c.current=n)},[t,n,a,o]),(0,r.useEffect)(()=>{u&&s(null)},[u]),u}({disabled:!eu,index:A,node:B,rect:F});return(0,r.useEffect)(()=>{$&&ea.current.newIndex!==el&&(ea.current.newIndex=el),k!==ea.current.containerId&&(ea.current.containerId=k),R!==ea.current.items&&(ea.current.items=R)},[$,el,k,R]),(0,r.useEffect)(()=>{if(ei===ea.current.activeId)return;if(null!=ei&&null==ea.current.activeId){ea.current.activeId=ei;return}let e=setTimeout(()=>{ea.current.activeId=ei},50);return()=>clearTimeout(e)},[ei]),{active:K,activeIndex:M,attributes:U,data:P,rect:F,index:A,newIndex:el,items:R,isOver:W,isSorting:$,isDragging:V,listeners:q,node:B,overIndex:j,over:H,setNodeRef:Q,setActivatorNodeRef:Z,setDroppableNodeRef:G,setDraggableNodeRef:X,transform:null!=es?es:er,transition:es||eo&&ea.current.newIndex===A?y:(!et||(0,i.kx)(Y))&&E&&($||eu)?i.Ks.Transition.toString({...E,property:b}):void 0}}function x(e){if(!e)return!1;let t=e.data.current;return!!t&&"sortable"in t&&"object"==typeof t.sortable&&"containerId"in t.sortable&&"items"in t.sortable&&"index"in t.sortable}let D=[l.vL.Down,l.vL.Right,l.vL.Up,l.vL.Left],C=(e,t)=>{let{context:{active:n,collisionRect:r,droppableRects:a,droppableContainers:o,over:u,scrollableAncestors:s}}=t;if(D.includes(e.code)){if(e.preventDefault(),!n||!r)return;let t=[];o.getEnabled().forEach(n=>{if(!n||null!=n&&n.disabled)return;let i=a.get(n.id);if(i)switch(e.code){case l.vL.Down:r.top<i.top&&t.push(n);break;case l.vL.Up:r.top>i.top&&t.push(n);break;case l.vL.Left:r.left>i.left&&t.push(n);break;case l.vL.Right:r.left<i.left&&t.push(n)}});let c=(0,l.y$)({active:n,collisionRect:r,droppableRects:a,droppableContainers:t,pointerCoordinates:null}),d=(0,l.Vy)(c,"id");if(d===(null==u?void 0:u.id)&&c.length>1&&(d=c[1].id),null!=d){let e=o.get(n.id),t=o.get(d),u=t?a.get(t.id):null,c=null==t?void 0:t.node.current;if(c&&u&&e&&t){let n=(0,l.sl)(c).some((e,t)=>s[t]!==e),a=E(e,t),o=function(e,t){return!!x(e)&&!!x(t)&&!!E(e,t)&&e.data.current.sortable.index<t.data.current.sortable.index}(e,t),d=n||!a?{x:0,y:0}:{x:o?r.width-u.width:0,y:o?r.height-u.height:0},f={x:u.left,y:u.top};return d.x&&d.y?f:(0,i.Re)(f,d)}}}};function E(e,t){return!!x(e)&&!!x(t)&&e.data.current.sortable.containerId===t.data.current.sortable.containerId}},60704:(e,t,n)=>{n.d(t,{B8:()=>T,UC:()=>j,bL:()=>S,l9:()=>O});var r=n(12115),l=n(85185),i=n(46081),a=n(89196),o=n(28905),u=n(63655),s=n(94315),c=n(5845),d=n(61285),f=n(95155),h="Tabs",[p,g]=(0,i.A)(h,[a.RG]),v=(0,a.RG)(),[b,y]=p(h),m=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,onValueChange:l,defaultValue:i,orientation:a="horizontal",dir:o,activationMode:p="automatic",...g}=e,v=(0,s.jH)(o),[y,m]=(0,c.i)({prop:r,onChange:l,defaultProp:null!=i?i:"",caller:h});return(0,f.jsx)(b,{scope:n,baseId:(0,d.B)(),value:y,onValueChange:m,orientation:a,dir:v,activationMode:p,children:(0,f.jsx)(u.sG.div,{dir:v,"data-orientation":a,...g,ref:t})})});m.displayName=h;var w="TabsList",x=r.forwardRef((e,t)=>{let{__scopeTabs:n,loop:r=!0,...l}=e,i=y(w,n),o=v(n);return(0,f.jsx)(a.bL,{asChild:!0,...o,orientation:i.orientation,dir:i.dir,loop:r,children:(0,f.jsx)(u.sG.div,{role:"tablist","aria-orientation":i.orientation,...l,ref:t})})});x.displayName=w;var D="TabsTrigger",C=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,disabled:i=!1,...o}=e,s=y(D,n),c=v(n),d=k(s.baseId,r),h=M(s.baseId,r),p=r===s.value;return(0,f.jsx)(a.q7,{asChild:!0,...c,focusable:!i,active:p,children:(0,f.jsx)(u.sG.button,{type:"button",role:"tab","aria-selected":p,"aria-controls":h,"data-state":p?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:d,...o,ref:t,onMouseDown:(0,l.m)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():s.onValueChange(r)}),onKeyDown:(0,l.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&s.onValueChange(r)}),onFocus:(0,l.m)(e.onFocus,()=>{let e="manual"!==s.activationMode;p||i||!e||s.onValueChange(r)})})})});C.displayName=D;var E="TabsContent",R=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:l,forceMount:i,children:a,...s}=e,c=y(E,n),d=k(c.baseId,l),h=M(c.baseId,l),p=l===c.value,g=r.useRef(p);return r.useEffect(()=>{let e=requestAnimationFrame(()=>g.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,f.jsx)(o.C,{present:i||p,children:n=>{let{present:r}=n;return(0,f.jsx)(u.sG.div,{"data-state":p?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":d,hidden:!r,id:h,tabIndex:0,...s,ref:t,style:{...e.style,animationDuration:g.current?"0s":void 0},children:r&&a})}})});function k(e,t){return"".concat(e,"-trigger-").concat(t)}function M(e,t){return"".concat(e,"-content-").concat(t)}R.displayName=E;var S=m,T=x,O=C,j=R},74783:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},75143:(e,t,n)=>{let r;n.d(t,{Mp:()=>eF,vL:()=>o,uN:()=>ea,AN:()=>ed,fp:()=>N,y$:()=>A,Sj:()=>W,Vy:()=>I,sl:()=>K,fF:()=>eK,PM:()=>eG,zM:()=>e_,MS:()=>R,FR:()=>k});var l,i,a,o,u,s,c,d,f,h,p=n(12115),g=n(47650),v=n(78266);let b={display:"none"};function y(e){let{id:t,value:n}=e;return p.createElement("div",{id:t,style:b},n)}function m(e){let{id:t,announcement:n,ariaLiveType:r="assertive"}=e;return p.createElement("div",{id:t,style:{position:"fixed",top:0,left:0,width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0 0 0 0)",clipPath:"inset(100%)",whiteSpace:"nowrap"},role:"status","aria-live":r,"aria-atomic":!0},n)}let w=(0,p.createContext)(null),x={draggable:"\n    To pick up a draggable item, press the space bar.\n    While dragging, use the arrow keys to move the item.\n    Press space again to drop the item in its new position, or press escape to cancel.\n  "},D={onDragStart(e){let{active:t}=e;return"Picked up draggable item "+t.id+"."},onDragOver(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was moved over droppable area "+n.id+".":"Draggable item "+t.id+" is no longer over a droppable area."},onDragEnd(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was dropped over droppable area "+n.id:"Draggable item "+t.id+" was dropped."},onDragCancel(e){let{active:t}=e;return"Dragging was cancelled. Draggable item "+t.id+" was dropped."}};function C(e){let{announcements:t=D,container:n,hiddenTextDescribedById:r,screenReaderInstructions:l=x}=e,{announce:i,announcement:a}=function(){let[e,t]=(0,p.useState)("");return{announce:(0,p.useCallback)(e=>{null!=e&&t(e)},[]),announcement:e}}(),o=(0,v.YG)("DndLiveRegion"),[u,s]=(0,p.useState)(!1);(0,p.useEffect)(()=>{s(!0)},[]);var c=(0,p.useMemo)(()=>({onDragStart(e){let{active:n}=e;i(t.onDragStart({active:n}))},onDragMove(e){let{active:n,over:r}=e;t.onDragMove&&i(t.onDragMove({active:n,over:r}))},onDragOver(e){let{active:n,over:r}=e;i(t.onDragOver({active:n,over:r}))},onDragEnd(e){let{active:n,over:r}=e;i(t.onDragEnd({active:n,over:r}))},onDragCancel(e){let{active:n,over:r}=e;i(t.onDragCancel({active:n,over:r}))}}),[i,t]);let d=(0,p.useContext)(w);if((0,p.useEffect)(()=>{if(!d)throw Error("useDndMonitor must be used within a children of <DndContext>");return d(c)},[c,d]),!u)return null;let f=p.createElement(p.Fragment,null,p.createElement(y,{id:r,value:l.draggable}),p.createElement(m,{id:o,announcement:a}));return n?(0,g.createPortal)(f,n):f}function E(){}function R(e,t){return(0,p.useMemo)(()=>({sensor:e,options:null!=t?t:{}}),[e,t])}function k(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,p.useMemo)(()=>[...t].filter(e=>null!=e),[...t])}!function(e){e.DragStart="dragStart",e.DragMove="dragMove",e.DragEnd="dragEnd",e.DragCancel="dragCancel",e.DragOver="dragOver",e.RegisterDroppable="registerDroppable",e.SetDroppableDisabled="setDroppableDisabled",e.UnregisterDroppable="unregisterDroppable"}(l||(l={}));let M=Object.freeze({x:0,y:0});function S(e,t){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}function T(e,t){let{data:{value:n}}=e,{data:{value:r}}=t;return n-r}function O(e,t){let{data:{value:n}}=e,{data:{value:r}}=t;return r-n}function j(e){let{left:t,top:n,height:r,width:l}=e;return[{x:t,y:n},{x:t+l,y:n},{x:t,y:n+r},{x:t+l,y:n+r}]}function I(e,t){if(!e||0===e.length)return null;let[n]=e;return t?n[t]:n}function L(e,t,n){return void 0===t&&(t=e.left),void 0===n&&(n=e.top),{x:t+.5*e.width,y:n+.5*e.height}}let N=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e,l=L(t,t.left,t.top),i=[];for(let e of r){let{id:t}=e,r=n.get(t);if(r){let n=S(L(r),l);i.push({id:t,data:{droppableContainer:e,value:n}})}}return i.sort(T)},A=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e,l=j(t),i=[];for(let e of r){let{id:t}=e,r=n.get(t);if(r){let n=j(r),a=Number((l.reduce((e,t,r)=>e+S(n[r],t),0)/4).toFixed(4));i.push({id:t,data:{droppableContainer:e,value:a}})}}return i.sort(T)},P=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e,l=[];for(let e of r){let{id:r}=e,i=n.get(r);if(i){let n=function(e,t){let n=Math.max(t.top,e.top),r=Math.max(t.left,e.left),l=Math.min(t.left+t.width,e.left+e.width),i=Math.min(t.top+t.height,e.top+e.height);if(r<l&&n<i){let a=t.width*t.height,o=e.width*e.height,u=(l-r)*(i-n);return Number((u/(a+o-u)).toFixed(4))}return 0}(i,t);n>0&&l.push({id:r,data:{droppableContainer:e,value:n}})}}return l.sort(O)};function z(e,t){return e&&t?{x:e.left-t.left,y:e.top-t.top}:M}let F=function(e){return function(t){for(var n=arguments.length,r=Array(n>1?n-1:0),l=1;l<n;l++)r[l-1]=arguments[l];return r.reduce((t,n)=>({...t,top:t.top+e*n.y,bottom:t.bottom+e*n.y,left:t.left+e*n.x,right:t.right+e*n.x}),{...t})}}(1),B={ignoreTransform:!1};function W(e,t){void 0===t&&(t=B);let n=e.getBoundingClientRect();if(t.ignoreTransform){let{transform:t,transformOrigin:r}=(0,v.zk)(e).getComputedStyle(e);t&&(n=function(e,t,n){let r=function(e){if(e.startsWith("matrix3d(")){let t=e.slice(9,-1).split(/, /);return{x:+t[12],y:+t[13],scaleX:+t[0],scaleY:+t[5]}}if(e.startsWith("matrix(")){let t=e.slice(7,-1).split(/, /);return{x:+t[4],y:+t[5],scaleX:+t[0],scaleY:+t[3]}}return null}(t);if(!r)return e;let{scaleX:l,scaleY:i,x:a,y:o}=r,u=e.left-a-(1-l)*parseFloat(n),s=e.top-o-(1-i)*parseFloat(n.slice(n.indexOf(" ")+1)),c=l?e.width/l:e.width,d=i?e.height/i:e.height;return{width:c,height:d,top:s,right:u+c,bottom:s+d,left:u}}(n,t,r))}let{top:r,left:l,width:i,height:a,bottom:o,right:u}=n;return{top:r,left:l,width:i,height:a,bottom:o,right:u}}function G(e){return W(e,{ignoreTransform:!0})}function K(e,t){let n=[];return e?function r(l){var i;if(null!=t&&n.length>=t||!l)return n;if((0,v.wz)(l)&&null!=l.scrollingElement&&!n.includes(l.scrollingElement))return n.push(l.scrollingElement),n;if(!(0,v.sb)(l)||(0,v.xZ)(l)||n.includes(l))return n;let a=(0,v.zk)(e).getComputedStyle(l);return(l!==e&&function(e,t){void 0===t&&(t=(0,v.zk)(e).getComputedStyle(e));let n=/(auto|scroll|overlay)/;return["overflow","overflowX","overflowY"].some(e=>{let r=t[e];return"string"==typeof r&&n.test(r)})}(l,a)&&n.push(l),void 0===(i=a)&&(i=(0,v.zk)(l).getComputedStyle(l)),"fixed"===i.position)?n:r(l.parentNode)}(e):n}function Y(e){let[t]=K(e,1);return null!=t?t:null}function _(e){return v.Sw&&e?(0,v.l6)(e)?e:(0,v.Ll)(e)?(0,v.wz)(e)||e===(0,v.TW)(e).scrollingElement?window:(0,v.sb)(e)?e:null:null:null}function U(e){return(0,v.l6)(e)?e.scrollX:e.scrollLeft}function X(e){return(0,v.l6)(e)?e.scrollY:e.scrollTop}function q(e){return{x:U(e),y:X(e)}}function V(e){return!!v.Sw&&!!e&&e===document.scrollingElement}function H(e){let t={x:0,y:0},n=V(e)?{height:window.innerHeight,width:window.innerWidth}:{height:e.clientHeight,width:e.clientWidth},r={x:e.scrollWidth-n.width,y:e.scrollHeight-n.height},l=e.scrollTop<=t.y,i=e.scrollLeft<=t.x;return{isTop:l,isLeft:i,isBottom:e.scrollTop>=r.y,isRight:e.scrollLeft>=r.x,maxScroll:r,minScroll:t}}!function(e){e[e.Forward=1]="Forward",e[e.Backward=-1]="Backward"}(i||(i={}));let Z={x:.2,y:.2};function J(e){return e.reduce((e,t)=>(0,v.WQ)(e,q(t)),M)}let Q=[["x",["left","right"],function(e){return e.reduce((e,t)=>e+U(t),0)}],["y",["top","bottom"],function(e){return e.reduce((e,t)=>e+X(t),0)}]];class ${constructor(e,t){this.rect=void 0,this.width=void 0,this.height=void 0,this.top=void 0,this.bottom=void 0,this.right=void 0,this.left=void 0;let n=K(t),r=J(n);for(let[t,l,i]of(this.rect={...e},this.width=e.width,this.height=e.height,Q))for(let e of l)Object.defineProperty(this,e,{get:()=>{let l=i(n),a=r[t]-l;return this.rect[e]+a},enumerable:!0});Object.defineProperty(this,"rect",{enumerable:!1})}}class ee{constructor(e){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach(e=>{var t;return null==(t=this.target)?void 0:t.removeEventListener(...e)})},this.target=e}add(e,t,n){var r;null==(r=this.target)||r.addEventListener(e,t,n),this.listeners.push([e,t,n])}}function et(e,t){let n=Math.abs(e.x),r=Math.abs(e.y);return"number"==typeof t?Math.sqrt(n**2+r**2)>t:"x"in t&&"y"in t?n>t.x&&r>t.y:"x"in t?n>t.x:"y"in t&&r>t.y}function en(e){e.preventDefault()}function er(e){e.stopPropagation()}!function(e){e.Click="click",e.DragStart="dragstart",e.Keydown="keydown",e.ContextMenu="contextmenu",e.Resize="resize",e.SelectionChange="selectionchange",e.VisibilityChange="visibilitychange"}(a||(a={})),function(e){e.Space="Space",e.Down="ArrowDown",e.Right="ArrowRight",e.Left="ArrowLeft",e.Up="ArrowUp",e.Esc="Escape",e.Enter="Enter",e.Tab="Tab"}(o||(o={}));let el={start:[o.Space,o.Enter],cancel:[o.Esc],end:[o.Space,o.Enter,o.Tab]},ei=(e,t)=>{let{currentCoordinates:n}=t;switch(e.code){case o.Right:return{...n,x:n.x+25};case o.Left:return{...n,x:n.x-25};case o.Down:return{...n,y:n.y+25};case o.Up:return{...n,y:n.y-25}}};class ea{constructor(e){this.props=void 0,this.autoScrollEnabled=!1,this.referenceCoordinates=void 0,this.listeners=void 0,this.windowListeners=void 0,this.props=e;let{event:{target:t}}=e;this.props=e,this.listeners=new ee((0,v.TW)(t)),this.windowListeners=new ee((0,v.zk)(t)),this.handleKeyDown=this.handleKeyDown.bind(this),this.handleCancel=this.handleCancel.bind(this),this.attach()}attach(){this.handleStart(),this.windowListeners.add(a.Resize,this.handleCancel),this.windowListeners.add(a.VisibilityChange,this.handleCancel),setTimeout(()=>this.listeners.add(a.Keydown,this.handleKeyDown))}handleStart(){let{activeNode:e,onStart:t}=this.props,n=e.node.current;n&&function(e,t){if(void 0===t&&(t=W),!e)return;let{top:n,left:r,bottom:l,right:i}=t(e);Y(e)&&(l<=0||i<=0||n>=window.innerHeight||r>=window.innerWidth)&&e.scrollIntoView({block:"center",inline:"center"})}(n),t(M)}handleKeyDown(e){if((0,v.kx)(e)){let{active:t,context:n,options:r}=this.props,{keyboardCodes:l=el,coordinateGetter:i=ei,scrollBehavior:a="smooth"}=r,{code:u}=e;if(l.end.includes(u))return void this.handleEnd(e);if(l.cancel.includes(u))return void this.handleCancel(e);let{collisionRect:s}=n.current,c=s?{x:s.left,y:s.top}:M;this.referenceCoordinates||(this.referenceCoordinates=c);let d=i(e,{active:t,context:n.current,currentCoordinates:c});if(d){let t=(0,v.Re)(d,c),r={x:0,y:0},{scrollableAncestors:l}=n.current;for(let n of l){let l=e.code,{isTop:i,isRight:u,isLeft:s,isBottom:c,maxScroll:f,minScroll:h}=H(n),p=function(e){if(e===document.scrollingElement){let{innerWidth:e,innerHeight:t}=window;return{top:0,left:0,right:e,bottom:t,width:e,height:t}}let{top:t,left:n,right:r,bottom:l}=e.getBoundingClientRect();return{top:t,left:n,right:r,bottom:l,width:e.clientWidth,height:e.clientHeight}}(n),g={x:Math.min(l===o.Right?p.right-p.width/2:p.right,Math.max(l===o.Right?p.left:p.left+p.width/2,d.x)),y:Math.min(l===o.Down?p.bottom-p.height/2:p.bottom,Math.max(l===o.Down?p.top:p.top+p.height/2,d.y))},v=l===o.Right&&!u||l===o.Left&&!s,b=l===o.Down&&!c||l===o.Up&&!i;if(v&&g.x!==d.x){let e=n.scrollLeft+t.x,i=l===o.Right&&e<=f.x||l===o.Left&&e>=h.x;if(i&&!t.y)return void n.scrollTo({left:e,behavior:a});i?r.x=n.scrollLeft-e:r.x=l===o.Right?n.scrollLeft-f.x:n.scrollLeft-h.x,r.x&&n.scrollBy({left:-r.x,behavior:a});break}if(b&&g.y!==d.y){let e=n.scrollTop+t.y,i=l===o.Down&&e<=f.y||l===o.Up&&e>=h.y;if(i&&!t.x)return void n.scrollTo({top:e,behavior:a});i?r.y=n.scrollTop-e:r.y=l===o.Down?n.scrollTop-f.y:n.scrollTop-h.y,r.y&&n.scrollBy({top:-r.y,behavior:a});break}}this.handleMove(e,(0,v.WQ)((0,v.Re)(d,this.referenceCoordinates),r))}}}handleMove(e,t){let{onMove:n}=this.props;e.preventDefault(),n(t)}handleEnd(e){let{onEnd:t}=this.props;e.preventDefault(),this.detach(),t()}handleCancel(e){let{onCancel:t}=this.props;e.preventDefault(),this.detach(),t()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll()}}function eo(e){return!!(e&&"distance"in e)}function eu(e){return!!(e&&"delay"in e)}ea.activators=[{eventName:"onKeyDown",handler:(e,t,n)=>{let{keyboardCodes:r=el,onActivation:l}=t,{active:i}=n,{code:a}=e.nativeEvent;if(r.start.includes(a)){let t=i.activatorNode.current;return(!t||e.target===t)&&(e.preventDefault(),null==l||l({event:e.nativeEvent}),!0)}return!1}}];class es{constructor(e,t,n){var r;void 0===n&&(n=function(e){let{EventTarget:t}=(0,v.zk)(e);return e instanceof t?e:(0,v.TW)(e)}(e.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=e,this.events=t;let{event:l}=e,{target:i}=l;this.props=e,this.events=t,this.document=(0,v.TW)(i),this.documentListeners=new ee(this.document),this.listeners=new ee(n),this.windowListeners=new ee((0,v.zk)(i)),this.initialCoordinates=null!=(r=(0,v.e_)(l))?r:M,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){let{events:e,props:{options:{activationConstraint:t,bypassActivationConstraint:n}}}=this;if(this.listeners.add(e.move.name,this.handleMove,{passive:!1}),this.listeners.add(e.end.name,this.handleEnd),e.cancel&&this.listeners.add(e.cancel.name,this.handleCancel),this.windowListeners.add(a.Resize,this.handleCancel),this.windowListeners.add(a.DragStart,en),this.windowListeners.add(a.VisibilityChange,this.handleCancel),this.windowListeners.add(a.ContextMenu,en),this.documentListeners.add(a.Keydown,this.handleKeydown),t){if(null!=n&&n({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(eu(t)){this.timeoutId=setTimeout(this.handleStart,t.delay),this.handlePending(t);return}if(eo(t))return void this.handlePending(t)}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),null!==this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handlePending(e,t){let{active:n,onPending:r}=this.props;r(n,e,this.initialCoordinates,t)}handleStart(){let{initialCoordinates:e}=this,{onStart:t}=this.props;e&&(this.activated=!0,this.documentListeners.add(a.Click,er,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(a.SelectionChange,this.removeTextSelection),t(e))}handleMove(e){var t;let{activated:n,initialCoordinates:r,props:l}=this,{onMove:i,options:{activationConstraint:a}}=l;if(!r)return;let o=null!=(t=(0,v.e_)(e))?t:M,u=(0,v.Re)(r,o);if(!n&&a){if(eo(a)){if(null!=a.tolerance&&et(u,a.tolerance))return this.handleCancel();if(et(u,a.distance))return this.handleStart()}return eu(a)&&et(u,a.tolerance)?this.handleCancel():void this.handlePending(a,u)}e.cancelable&&e.preventDefault(),i(o)}handleEnd(){let{onAbort:e,onEnd:t}=this.props;this.detach(),this.activated||e(this.props.active),t()}handleCancel(){let{onAbort:e,onCancel:t}=this.props;this.detach(),this.activated||e(this.props.active),t()}handleKeydown(e){e.code===o.Esc&&this.handleCancel()}removeTextSelection(){var e;null==(e=this.document.getSelection())||e.removeAllRanges()}}let ec={cancel:{name:"pointercancel"},move:{name:"pointermove"},end:{name:"pointerup"}};class ed extends es{constructor(e){let{event:t}=e;super(e,ec,(0,v.TW)(t.target))}}ed.activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return!!n.isPrimary&&0===n.button&&(null==r||r({event:n}),!0)}}];let ef={move:{name:"mousemove"},end:{name:"mouseup"}};!function(e){e[e.RightClick=2]="RightClick"}(u||(u={}));class eh extends es{constructor(e){super(e,ef,(0,v.TW)(e.event.target))}}eh.activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return n.button!==u.RightClick&&(null==r||r({event:n}),!0)}}];let ep={cancel:{name:"touchcancel"},move:{name:"touchmove"},end:{name:"touchend"}};class eg extends es{constructor(e){super(e,ep)}static setup(){return window.addEventListener(ep.move.name,e,{capture:!1,passive:!1}),function(){window.removeEventListener(ep.move.name,e)};function e(){}}}eg.activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t,{touches:l}=n;return!(l.length>1)&&(null==r||r({event:n}),!0)}}],function(e){e[e.Pointer=0]="Pointer",e[e.DraggableRect=1]="DraggableRect"}(s||(s={})),function(e){e[e.TreeOrder=0]="TreeOrder",e[e.ReversedTreeOrder=1]="ReversedTreeOrder"}(c||(c={}));let ev={x:{[i.Backward]:!1,[i.Forward]:!1},y:{[i.Backward]:!1,[i.Forward]:!1}};!function(e){e[e.Always=0]="Always",e[e.BeforeDragging=1]="BeforeDragging",e[e.WhileDragging=2]="WhileDragging"}(d||(d={})),(f||(f={})).Optimized="optimized";let eb=new Map;function ey(e,t){return(0,v.KG)(n=>e?n||("function"==typeof t?t(e):e):null,[t,e])}function em(e){let{callback:t,disabled:n}=e,r=(0,v._q)(t),l=(0,p.useMemo)(()=>{if(n||"undefined"==typeof window||void 0===window.ResizeObserver)return;let{ResizeObserver:e}=window;return new e(r)},[n]);return(0,p.useEffect)(()=>()=>null==l?void 0:l.disconnect(),[l]),l}function ew(e){return new $(W(e),e)}function ex(e,t,n){void 0===t&&(t=ew);let[r,l]=(0,p.useState)(null);function i(){l(r=>{if(!e)return null;if(!1===e.isConnected){var l;return null!=(l=null!=r?r:n)?l:null}let i=t(e);return JSON.stringify(r)===JSON.stringify(i)?r:i})}let a=function(e){let{callback:t,disabled:n}=e,r=(0,v._q)(t),l=(0,p.useMemo)(()=>{if(n||"undefined"==typeof window||void 0===window.MutationObserver)return;let{MutationObserver:e}=window;return new e(r)},[r,n]);return(0,p.useEffect)(()=>()=>null==l?void 0:l.disconnect(),[l]),l}({callback(t){if(e)for(let n of t){let{type:t,target:r}=n;if("childList"===t&&r instanceof HTMLElement&&r.contains(e)){i();break}}}}),o=em({callback:i});return(0,v.Es)(()=>{i(),e?(null==o||o.observe(e),null==a||a.observe(document.body,{childList:!0,subtree:!0})):(null==o||o.disconnect(),null==a||a.disconnect())},[e]),r}let eD=[];function eC(e,t){void 0===t&&(t=[]);let n=(0,p.useRef)(null);return(0,p.useEffect)(()=>{n.current=null},t),(0,p.useEffect)(()=>{let t=e!==M;t&&!n.current&&(n.current=e),!t&&n.current&&(n.current=null)},[e]),n.current?(0,v.Re)(e,n.current):M}function eE(e){return(0,p.useMemo)(()=>e?function(e){let t=e.innerWidth,n=e.innerHeight;return{top:0,left:0,right:t,bottom:n,width:t,height:n}}(e):null,[e])}let eR=[],ek=[{sensor:ed,options:{}},{sensor:ea,options:{}}],eM={current:{}},eS={draggable:{measure:G},droppable:{measure:G,strategy:d.WhileDragging,frequency:f.Optimized},dragOverlay:{measure:W}};class eT extends Map{get(e){var t;return null!=e&&null!=(t=super.get(e))?t:void 0}toArray(){return Array.from(this.values())}getEnabled(){return this.toArray().filter(e=>{let{disabled:t}=e;return!t})}getNodeFor(e){var t,n;return null!=(t=null==(n=this.get(e))?void 0:n.node.current)?t:void 0}}let eO={activatorEvent:null,active:null,activeNode:null,activeNodeRect:null,collisions:null,containerNodeRect:null,draggableNodes:new Map,droppableRects:new Map,droppableContainers:new eT,over:null,dragOverlay:{nodeRef:{current:null},rect:null,setRef:E},scrollableAncestors:[],scrollableAncestorRects:[],measuringConfiguration:eS,measureDroppableContainers:E,windowRect:null,measuringScheduled:!1},ej={activatorEvent:null,activators:[],active:null,activeNodeRect:null,ariaDescribedById:{draggable:""},dispatch:E,draggableNodes:new Map,over:null,measureDroppableContainers:E},eI=(0,p.createContext)(ej),eL=(0,p.createContext)(eO);function eN(){return{draggable:{active:null,initialCoordinates:{x:0,y:0},nodes:new Map,translate:{x:0,y:0}},droppable:{containers:new eT}}}function eA(e,t){switch(t.type){case l.DragStart:return{...e,draggable:{...e.draggable,initialCoordinates:t.initialCoordinates,active:t.active}};case l.DragMove:if(null==e.draggable.active)return e;return{...e,draggable:{...e.draggable,translate:{x:t.coordinates.x-e.draggable.initialCoordinates.x,y:t.coordinates.y-e.draggable.initialCoordinates.y}}};case l.DragEnd:case l.DragCancel:return{...e,draggable:{...e.draggable,active:null,initialCoordinates:{x:0,y:0},translate:{x:0,y:0}}};case l.RegisterDroppable:{let{element:n}=t,{id:r}=n,l=new eT(e.droppable.containers);return l.set(r,n),{...e,droppable:{...e.droppable,containers:l}}}case l.SetDroppableDisabled:{let{id:n,key:r,disabled:l}=t,i=e.droppable.containers.get(n);if(!i||r!==i.key)return e;let a=new eT(e.droppable.containers);return a.set(n,{...i,disabled:l}),{...e,droppable:{...e.droppable,containers:a}}}case l.UnregisterDroppable:{let{id:n,key:r}=t,l=e.droppable.containers.get(n);if(!l||r!==l.key)return e;let i=new eT(e.droppable.containers);return i.delete(n),{...e,droppable:{...e.droppable,containers:i}}}default:return e}}function eP(e){let{disabled:t}=e,{active:n,activatorEvent:r,draggableNodes:l}=(0,p.useContext)(eI),i=(0,v.ZC)(r),a=(0,v.ZC)(null==n?void 0:n.id);return(0,p.useEffect)(()=>{if(!t&&!r&&i&&null!=a){if(!(0,v.kx)(i)||document.activeElement===i.target)return;let e=l.get(a);if(!e)return;let{activatorNode:t,node:n}=e;(t.current||n.current)&&requestAnimationFrame(()=>{for(let e of[t.current,n.current]){if(!e)continue;let t=(0,v.ag)(e);if(t){t.focus();break}}})}},[r,t,l,a,i]),null}let ez=(0,p.createContext)({...M,scaleX:1,scaleY:1});!function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Initializing=1]="Initializing",e[e.Initialized=2]="Initialized"}(h||(h={}));let eF=(0,p.memo)(function(e){var t,n,r,a,o,u;let{id:f,accessibility:b,autoScroll:y=!0,children:m,sensors:x=ek,collisionDetection:D=P,measuring:E,modifiers:R,...k}=e,[S,T]=(0,p.useReducer)(eA,void 0,eN),[O,j]=function(){let[e]=(0,p.useState)(()=>new Set),t=(0,p.useCallback)(t=>(e.add(t),()=>e.delete(t)),[e]);return[(0,p.useCallback)(t=>{let{type:n,event:r}=t;e.forEach(e=>{var t;return null==(t=e[n])?void 0:t.call(e,r)})},[e]),t]}(),[L,N]=(0,p.useState)(h.Uninitialized),A=L===h.Initialized,{draggable:{active:B,nodes:G,translate:U},droppable:{containers:X}}=S,Q=null!=B?G.get(B):null,ee=(0,p.useRef)({initial:null,translated:null}),et=(0,p.useMemo)(()=>{var e;return null!=B?{id:B,data:null!=(e=null==Q?void 0:Q.data)?e:eM,rect:ee}:null},[B,Q]),en=(0,p.useRef)(null),[er,el]=(0,p.useState)(null),[ei,ea]=(0,p.useState)(null),eo=(0,v.YN)(k,Object.values(k)),eu=(0,v.YG)("DndDescribedBy",f),es=(0,p.useMemo)(()=>X.getEnabled(),[X]),ec=(0,p.useMemo)(()=>({draggable:{...eS.draggable,...null==E?void 0:E.draggable},droppable:{...eS.droppable,...null==E?void 0:E.droppable},dragOverlay:{...eS.dragOverlay,...null==E?void 0:E.dragOverlay}}),[null==E?void 0:E.draggable,null==E?void 0:E.droppable,null==E?void 0:E.dragOverlay]),{droppableRects:ed,measureDroppableContainers:ef,measuringScheduled:eh}=function(e,t){let{dragging:n,dependencies:r,config:l}=t,[i,a]=(0,p.useState)(null),{frequency:o,measure:u,strategy:s}=l,c=(0,p.useRef)(e),f=function(){switch(s){case d.Always:return!1;case d.BeforeDragging:return n;default:return!n}}(),h=(0,v.YN)(f),g=(0,p.useCallback)(function(e){void 0===e&&(e=[]),h.current||a(t=>null===t?e:t.concat(e.filter(e=>!t.includes(e))))},[h]),b=(0,p.useRef)(null),y=(0,v.KG)(t=>{if(f&&!n)return eb;if(!t||t===eb||c.current!==e||null!=i){let t=new Map;for(let n of e){if(!n)continue;if(i&&i.length>0&&!i.includes(n.id)&&n.rect.current){t.set(n.id,n.rect.current);continue}let e=n.node.current,r=e?new $(u(e),e):null;n.rect.current=r,r&&t.set(n.id,r)}return t}return t},[e,i,n,f,u]);return(0,p.useEffect)(()=>{c.current=e},[e]),(0,p.useEffect)(()=>{f||g()},[n,f]),(0,p.useEffect)(()=>{i&&i.length>0&&a(null)},[JSON.stringify(i)]),(0,p.useEffect)(()=>{f||"number"!=typeof o||null!==b.current||(b.current=setTimeout(()=>{g(),b.current=null},o))},[o,f,g,...r]),{droppableRects:y,measureDroppableContainers:g,measuringScheduled:null!=i}}(es,{dragging:A,dependencies:[U.x,U.y],config:ec.droppable}),ep=function(e,t){let n=null!=t?e.get(t):void 0,r=n?n.node.current:null;return(0,v.KG)(e=>{var n;return null==t?null:null!=(n=null!=r?r:e)?n:null},[r,t])}(G,B),eg=(0,p.useMemo)(()=>ei?(0,v.e_)(ei):null,[ei]),ew=function(){let e=(null==er?void 0:er.autoScrollEnabled)===!1,t="object"==typeof y?!1===y.enabled:!1===y,n=A&&!e&&!t;return"object"==typeof y?{...y,enabled:n}:{enabled:n}}(),eT=ey(ep,ec.draggable.measure);!function(e){let{activeNode:t,measure:n,initialRect:r,config:l=!0}=e,i=(0,p.useRef)(!1),{x:a,y:o}="boolean"==typeof l?{x:l,y:l}:l;(0,v.Es)(()=>{if(!a&&!o||!t){i.current=!1;return}if(i.current||!r)return;let e=null==t?void 0:t.node.current;if(!e||!1===e.isConnected)return;let l=z(n(e),r);if(a||(l.x=0),o||(l.y=0),i.current=!0,Math.abs(l.x)>0||Math.abs(l.y)>0){let t=Y(e);t&&t.scrollBy({top:l.y,left:l.x})}},[t,a,o,r,n])}({activeNode:null!=B?G.get(B):null,config:ew.layoutShiftCompensation,initialRect:eT,measure:ec.draggable.measure});let eO=ex(ep,ec.draggable.measure,eT),ej=ex(ep?ep.parentElement:null),eF=(0,p.useRef)({activatorEvent:null,active:null,activeNode:ep,collisionRect:null,collisions:null,droppableRects:ed,draggableNodes:G,draggingNode:null,draggingNodeRect:null,droppableContainers:X,over:null,scrollableAncestors:[],scrollAdjustedTranslate:null}),eB=X.getNodeFor(null==(t=eF.current.over)?void 0:t.id),eW=function(e){let{measure:t}=e,[n,r]=(0,p.useState)(null),l=em({callback:(0,p.useCallback)(e=>{for(let{target:n}of e)if((0,v.sb)(n)){r(e=>{let r=t(n);return e?{...e,width:r.width,height:r.height}:r});break}},[t])}),i=(0,p.useCallback)(e=>{let n=function(e){if(!e)return null;if(e.children.length>1)return e;let t=e.children[0];return(0,v.sb)(t)?t:e}(e);null==l||l.disconnect(),n&&(null==l||l.observe(n)),r(n?t(n):null)},[t,l]),[a,o]=(0,v.lk)(i);return(0,p.useMemo)(()=>({nodeRef:a,rect:n,setRef:o}),[n,a,o])}({measure:ec.dragOverlay.measure}),eG=null!=(n=eW.nodeRef.current)?n:ep,eK=A?null!=(r=eW.rect)?r:eO:null,eY=!!(eW.nodeRef.current&&eW.rect),e_=function(e){let t=ey(e);return z(e,t)}(eY?null:eO),eU=eE(eG?(0,v.zk)(eG):null),eX=function(e){let t=(0,p.useRef)(e),n=(0,v.KG)(n=>e?n&&n!==eD&&e&&t.current&&e.parentNode===t.current.parentNode?n:K(e):eD,[e]);return(0,p.useEffect)(()=>{t.current=e},[e]),n}(A?null!=eB?eB:ep:null),eq=function(e,t){void 0===t&&(t=W);let[n]=e,r=eE(n?(0,v.zk)(n):null),[l,i]=(0,p.useState)(eR);function a(){i(()=>e.length?e.map(e=>V(e)?r:new $(t(e),e)):eR)}let o=em({callback:a});return(0,v.Es)(()=>{null==o||o.disconnect(),a(),e.forEach(e=>null==o?void 0:o.observe(e))},[e]),l}(eX),eV=function(e,t){let{transform:n,...r}=t;return null!=e&&e.length?e.reduce((e,t)=>t({transform:e,...r}),n):n}(R,{transform:{x:U.x-e_.x,y:U.y-e_.y,scaleX:1,scaleY:1},activatorEvent:ei,active:et,activeNodeRect:eO,containerNodeRect:ej,draggingNodeRect:eK,over:eF.current.over,overlayNodeRect:eW.rect,scrollableAncestors:eX,scrollableAncestorRects:eq,windowRect:eU}),eH=eg?(0,v.WQ)(eg,U):null,eZ=function(e){let[t,n]=(0,p.useState)(null),r=(0,p.useRef)(e),l=(0,p.useCallback)(e=>{let t=_(e.target);t&&n(e=>e?(e.set(t,q(t)),new Map(e)):null)},[]);return(0,p.useEffect)(()=>{let t=r.current;if(e!==t){i(t);let a=e.map(e=>{let t=_(e);return t?(t.addEventListener("scroll",l,{passive:!0}),[t,q(t)]):null}).filter(e=>null!=e);n(a.length?new Map(a):null),r.current=e}return()=>{i(e),i(t)};function i(e){e.forEach(e=>{let t=_(e);null==t||t.removeEventListener("scroll",l)})}},[l,e]),(0,p.useMemo)(()=>e.length?t?Array.from(t.values()).reduce((e,t)=>(0,v.WQ)(e,t),M):J(e):M,[e,t])}(eX),eJ=eC(eZ),eQ=eC(eZ,[eO]),e$=(0,v.WQ)(eV,eJ),e0=eK?F(eK,eV):null,e1=et&&e0?D({active:et,collisionRect:e0,droppableRects:ed,droppableContainers:es,pointerCoordinates:eH}):null,e5=I(e1,"id"),[e2,e4]=(0,p.useState)(null),e6=(o=eY?eV:(0,v.WQ)(eV,eQ),u=null!=(a=null==e2?void 0:e2.rect)?a:null,{...o,scaleX:u&&eO?u.width/eO.width:1,scaleY:u&&eO?u.height/eO.height:1}),e9=(0,p.useRef)(null),e7=(0,p.useCallback)((e,t)=>{let{sensor:n,options:r}=t;if(null==en.current)return;let i=G.get(en.current);if(!i)return;let a=e.nativeEvent,o=new n({active:en.current,activeNode:i,event:a,options:r,context:eF,onAbort(e){if(!G.get(e))return;let{onDragAbort:t}=eo.current,n={id:e};null==t||t(n),O({type:"onDragAbort",event:n})},onPending(e,t,n,r){if(!G.get(e))return;let{onDragPending:l}=eo.current,i={id:e,constraint:t,initialCoordinates:n,offset:r};null==l||l(i),O({type:"onDragPending",event:i})},onStart(e){let t=en.current;if(null==t)return;let n=G.get(t);if(!n)return;let{onDragStart:r}=eo.current,i={activatorEvent:a,active:{id:t,data:n.data,rect:ee}};(0,g.unstable_batchedUpdates)(()=>{null==r||r(i),N(h.Initializing),T({type:l.DragStart,initialCoordinates:e,active:t}),O({type:"onDragStart",event:i}),el(e9.current),ea(a)})},onMove(e){T({type:l.DragMove,coordinates:e})},onEnd:u(l.DragEnd),onCancel:u(l.DragCancel)});function u(e){return async function(){let{active:t,collisions:n,over:r,scrollAdjustedTranslate:i}=eF.current,o=null;if(t&&i){let{cancelDrop:u}=eo.current;o={activatorEvent:a,active:t,collisions:n,delta:i,over:r},e===l.DragEnd&&"function"==typeof u&&await Promise.resolve(u(o))&&(e=l.DragCancel)}en.current=null,(0,g.unstable_batchedUpdates)(()=>{T({type:e}),N(h.Uninitialized),e4(null),el(null),ea(null),e9.current=null;let t=e===l.DragEnd?"onDragEnd":"onDragCancel";if(o){let e=eo.current[t];null==e||e(o),O({type:t,event:o})}})}}e9.current=o},[G]),e8=(0,p.useCallback)((e,t)=>(n,r)=>{let l=n.nativeEvent,i=G.get(r);null!==en.current||!i||l.dndKit||l.defaultPrevented||!0===e(n,t.options,{active:i})&&(l.dndKit={capturedBy:t.sensor},en.current=r,e7(n,t))},[G,e7]),e3=(0,p.useMemo)(()=>x.reduce((e,t)=>{let{sensor:n}=t;return[...e,...n.activators.map(e=>({eventName:e.eventName,handler:e8(e.handler,t)}))]},[]),[x,e8]);(0,p.useEffect)(()=>{if(!v.Sw)return;let e=x.map(e=>{let{sensor:t}=e;return null==t.setup?void 0:t.setup()});return()=>{for(let t of e)null==t||t()}},x.map(e=>{let{sensor:t}=e;return t})),(0,v.Es)(()=>{eO&&L===h.Initializing&&N(h.Initialized)},[eO,L]),(0,p.useEffect)(()=>{let{onDragMove:e}=eo.current,{active:t,activatorEvent:n,collisions:r,over:l}=eF.current;if(!t||!n)return;let i={active:t,activatorEvent:n,collisions:r,delta:{x:e$.x,y:e$.y},over:l};(0,g.unstable_batchedUpdates)(()=>{null==e||e(i),O({type:"onDragMove",event:i})})},[e$.x,e$.y]),(0,p.useEffect)(()=>{let{active:e,activatorEvent:t,collisions:n,droppableContainers:r,scrollAdjustedTranslate:l}=eF.current;if(!e||null==en.current||!t||!l)return;let{onDragOver:i}=eo.current,a=r.get(e5),o=a&&a.rect.current?{id:a.id,rect:a.rect.current,data:a.data,disabled:a.disabled}:null,u={active:e,activatorEvent:t,collisions:n,delta:{x:l.x,y:l.y},over:o};(0,g.unstable_batchedUpdates)(()=>{e4(o),null==i||i(u),O({type:"onDragOver",event:u})})},[e5]),(0,v.Es)(()=>{eF.current={activatorEvent:ei,active:et,activeNode:ep,collisionRect:e0,collisions:e1,droppableRects:ed,draggableNodes:G,draggingNode:eG,draggingNodeRect:eK,droppableContainers:X,over:e2,scrollableAncestors:eX,scrollAdjustedTranslate:e$},ee.current={initial:eK,translated:e0}},[et,ep,e1,e0,G,eG,eK,ed,X,e2,eX,e$]),function(e){let{acceleration:t,activator:n=s.Pointer,canScroll:r,draggingRect:l,enabled:a,interval:o=5,order:u=c.TreeOrder,pointerCoordinates:d,scrollableAncestors:f,scrollableAncestorRects:h,delta:g,threshold:b}=e,y=function(e){let{delta:t,disabled:n}=e,r=(0,v.ZC)(t);return(0,v.KG)(e=>{if(n||!r||!e)return ev;let l={x:Math.sign(t.x-r.x),y:Math.sign(t.y-r.y)};return{x:{[i.Backward]:e.x[i.Backward]||-1===l.x,[i.Forward]:e.x[i.Forward]||1===l.x},y:{[i.Backward]:e.y[i.Backward]||-1===l.y,[i.Forward]:e.y[i.Forward]||1===l.y}}},[n,t,r])}({delta:g,disabled:!a}),[m,w]=(0,v.$$)(),x=(0,p.useRef)({x:0,y:0}),D=(0,p.useRef)({x:0,y:0}),C=(0,p.useMemo)(()=>{switch(n){case s.Pointer:return d?{top:d.y,bottom:d.y,left:d.x,right:d.x}:null;case s.DraggableRect:return l}},[n,l,d]),E=(0,p.useRef)(null),R=(0,p.useCallback)(()=>{let e=E.current;if(!e)return;let t=x.current.x*D.current.x,n=x.current.y*D.current.y;e.scrollBy(t,n)},[]),k=(0,p.useMemo)(()=>u===c.TreeOrder?[...f].reverse():f,[u,f]);(0,p.useEffect)(()=>{if(!a||!f.length||!C)return void w();for(let e of k){if((null==r?void 0:r(e))===!1)continue;let n=h[f.indexOf(e)];if(!n)continue;let{direction:l,speed:a}=function(e,t,n,r,l){let{top:a,left:o,right:u,bottom:s}=n;void 0===r&&(r=10),void 0===l&&(l=Z);let{isTop:c,isBottom:d,isLeft:f,isRight:h}=H(e),p={x:0,y:0},g={x:0,y:0},v={height:t.height*l.y,width:t.width*l.x};return!c&&a<=t.top+v.height?(p.y=i.Backward,g.y=r*Math.abs((t.top+v.height-a)/v.height)):!d&&s>=t.bottom-v.height&&(p.y=i.Forward,g.y=r*Math.abs((t.bottom-v.height-s)/v.height)),!h&&u>=t.right-v.width?(p.x=i.Forward,g.x=r*Math.abs((t.right-v.width-u)/v.width)):!f&&o<=t.left+v.width&&(p.x=i.Backward,g.x=r*Math.abs((t.left+v.width-o)/v.width)),{direction:p,speed:g}}(e,n,C,t,b);for(let e of["x","y"])y[e][l[e]]||(a[e]=0,l[e]=0);if(a.x>0||a.y>0){w(),E.current=e,m(R,o),x.current=a,D.current=l;return}}x.current={x:0,y:0},D.current={x:0,y:0},w()},[t,R,r,w,a,o,JSON.stringify(C),JSON.stringify(y),m,f,k,h,JSON.stringify(b)])}({...ew,delta:U,draggingRect:e0,pointerCoordinates:eH,scrollableAncestors:eX,scrollableAncestorRects:eq});let te=(0,p.useMemo)(()=>({active:et,activeNode:ep,activeNodeRect:eO,activatorEvent:ei,collisions:e1,containerNodeRect:ej,dragOverlay:eW,draggableNodes:G,droppableContainers:X,droppableRects:ed,over:e2,measureDroppableContainers:ef,scrollableAncestors:eX,scrollableAncestorRects:eq,measuringConfiguration:ec,measuringScheduled:eh,windowRect:eU}),[et,ep,eO,ei,e1,ej,eW,G,X,ed,e2,ef,eX,eq,ec,eh,eU]),tt=(0,p.useMemo)(()=>({activatorEvent:ei,activators:e3,active:et,activeNodeRect:eO,ariaDescribedById:{draggable:eu},dispatch:T,draggableNodes:G,over:e2,measureDroppableContainers:ef}),[ei,e3,et,eO,T,eu,G,e2,ef]);return p.createElement(w.Provider,{value:j},p.createElement(eI.Provider,{value:tt},p.createElement(eL.Provider,{value:te},p.createElement(ez.Provider,{value:e6},m)),p.createElement(eP,{disabled:(null==b?void 0:b.restoreFocus)===!1})),p.createElement(C,{...b,hiddenTextDescribedById:eu}))}),eB=(0,p.createContext)(null),eW="button";function eG(e){let{id:t,data:n,disabled:r=!1,attributes:l}=e,i=(0,v.YG)("Draggable"),{activators:a,activatorEvent:o,active:u,activeNodeRect:s,ariaDescribedById:c,draggableNodes:d,over:f}=(0,p.useContext)(eI),{role:h=eW,roleDescription:g="draggable",tabIndex:b=0}=null!=l?l:{},y=(null==u?void 0:u.id)===t,m=(0,p.useContext)(y?ez:eB),[w,x]=(0,v.lk)(),[D,C]=(0,v.lk)(),E=(0,p.useMemo)(()=>a.reduce((e,n)=>{let{eventName:r,handler:l}=n;return e[r]=e=>{l(e,t)},e},{}),[a,t]),R=(0,v.YN)(n);return(0,v.Es)(()=>(d.set(t,{id:t,key:i,node:w,activatorNode:D,data:R}),()=>{let e=d.get(t);e&&e.key===i&&d.delete(t)}),[d,t]),{active:u,activatorEvent:o,activeNodeRect:s,attributes:(0,p.useMemo)(()=>({role:h,tabIndex:b,"aria-disabled":r,"aria-pressed":!!y&&h===eW||void 0,"aria-roledescription":g,"aria-describedby":c.draggable}),[r,h,b,y,g,c.draggable]),isDragging:y,listeners:r?void 0:E,node:w,over:f,setNodeRef:x,setActivatorNodeRef:C,transform:m}}function eK(){return(0,p.useContext)(eL)}let eY={timeout:25};function e_(e){let{data:t,disabled:n=!1,id:r,resizeObserverConfig:i}=e,a=(0,v.YG)("Droppable"),{active:o,dispatch:u,over:s,measureDroppableContainers:c}=(0,p.useContext)(eI),d=(0,p.useRef)({disabled:n}),f=(0,p.useRef)(!1),h=(0,p.useRef)(null),g=(0,p.useRef)(null),{disabled:b,updateMeasurementsFor:y,timeout:m}={...eY,...i},w=(0,v.YN)(null!=y?y:r),x=em({callback:(0,p.useCallback)(()=>{if(!f.current){f.current=!0;return}null!=g.current&&clearTimeout(g.current),g.current=setTimeout(()=>{c(Array.isArray(w.current)?w.current:[w.current]),g.current=null},m)},[m]),disabled:b||!o}),D=(0,p.useCallback)((e,t)=>{x&&(t&&(x.unobserve(t),f.current=!1),e&&x.observe(e))},[x]),[C,E]=(0,v.lk)(D),R=(0,v.YN)(t);return(0,p.useEffect)(()=>{x&&C.current&&(x.disconnect(),f.current=!1,x.observe(C.current))},[C,x]),(0,p.useEffect)(()=>(u({type:l.RegisterDroppable,element:{id:r,key:a,disabled:n,node:C,rect:h,data:R}}),()=>u({type:l.UnregisterDroppable,key:a,id:r})),[r]),(0,p.useEffect)(()=>{n!==d.current.disabled&&(u({type:l.SetDroppableDisabled,id:r,key:a,disabled:n}),d.current.disabled=n)},[r,a,n,u]),{active:o,rect:h,isOver:(null==s?void 0:s.id)===r,node:C,over:s,setNodeRef:E}}r={styles:{active:{opacity:"0"}}},e=>{let{active:t,dragOverlay:n}=e,l={},{styles:i,className:a}=r;if(null!=i&&i.active)for(let[e,n]of Object.entries(i.active))void 0!==n&&(l[e]=t.node.style.getPropertyValue(e),t.node.style.setProperty(e,n));if(null!=i&&i.dragOverlay)for(let[e,t]of Object.entries(i.dragOverlay))void 0!==t&&n.node.style.setProperty(e,t);return null!=a&&a.active&&t.node.classList.add(a.active),null!=a&&a.dragOverlay&&n.node.classList.add(a.dragOverlay),function(){for(let[e,n]of Object.entries(l))t.node.style.setProperty(e,n);null!=a&&a.active&&t.node.classList.remove(a.active)}}},78266:(e,t,n)=>{n.d(t,{$$:()=>g,Es:()=>h,KG:()=>b,Ks:()=>M,Ll:()=>o,Re:()=>E,Sw:()=>i,TW:()=>f,WQ:()=>C,YG:()=>x,YN:()=>v,ZC:()=>m,_q:()=>p,ag:()=>T,e_:()=>k,jn:()=>l,kx:()=>R,l6:()=>a,lk:()=>y,sb:()=>c,wz:()=>s,xZ:()=>d,zk:()=>u});var r=n(12115);function l(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,r.useMemo)(()=>e=>{t.forEach(t=>t(e))},t)}let i="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement;function a(e){let t=Object.prototype.toString.call(e);return"[object Window]"===t||"[object global]"===t}function o(e){return"nodeType"in e}function u(e){var t,n;return e?a(e)?e:o(e)&&null!=(t=null==(n=e.ownerDocument)?void 0:n.defaultView)?t:window:window}function s(e){let{Document:t}=u(e);return e instanceof t}function c(e){return!a(e)&&e instanceof u(e).HTMLElement}function d(e){return e instanceof u(e).SVGElement}function f(e){return e?a(e)?e.document:o(e)?s(e)?e:c(e)||d(e)?e.ownerDocument:document:document:document}let h=i?r.useLayoutEffect:r.useEffect;function p(e){let t=(0,r.useRef)(e);return h(()=>{t.current=e}),(0,r.useCallback)(function(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];return null==t.current?void 0:t.current(...n)},[])}function g(){let e=(0,r.useRef)(null);return[(0,r.useCallback)((t,n)=>{e.current=setInterval(t,n)},[]),(0,r.useCallback)(()=>{null!==e.current&&(clearInterval(e.current),e.current=null)},[])]}function v(e,t){void 0===t&&(t=[e]);let n=(0,r.useRef)(e);return h(()=>{n.current!==e&&(n.current=e)},t),n}function b(e,t){let n=(0,r.useRef)();return(0,r.useMemo)(()=>{let t=e(n.current);return n.current=t,t},[...t])}function y(e){let t=p(e),n=(0,r.useRef)(null),l=(0,r.useCallback)(e=>{e!==n.current&&(null==t||t(e,n.current)),n.current=e},[]);return[n,l]}function m(e){let t=(0,r.useRef)();return(0,r.useEffect)(()=>{t.current=e},[e]),t.current}let w={};function x(e,t){return(0,r.useMemo)(()=>{if(t)return t;let n=null==w[e]?0:w[e]+1;return w[e]=n,e+"-"+n},[e,t])}function D(e){return function(t){for(var n=arguments.length,r=Array(n>1?n-1:0),l=1;l<n;l++)r[l-1]=arguments[l];return r.reduce((t,n)=>{for(let[r,l]of Object.entries(n)){let n=t[r];null!=n&&(t[r]=n+e*l)}return t},{...t})}}let C=D(1),E=D(-1);function R(e){if(!e)return!1;let{KeyboardEvent:t}=u(e.target);return t&&e instanceof t}function k(e){if(function(e){if(!e)return!1;let{TouchEvent:t}=u(e.target);return t&&e instanceof t}(e)){if(e.touches&&e.touches.length){let{clientX:t,clientY:n}=e.touches[0];return{x:t,y:n}}else if(e.changedTouches&&e.changedTouches.length){let{clientX:t,clientY:n}=e.changedTouches[0];return{x:t,y:n}}}return"clientX"in e&&"clientY"in e?{x:e.clientX,y:e.clientY}:null}let M=Object.freeze({Translate:{toString(e){if(!e)return;let{x:t,y:n}=e;return"translate3d("+(t?Math.round(t):0)+"px, "+(n?Math.round(n):0)+"px, 0)"}},Scale:{toString(e){if(!e)return;let{scaleX:t,scaleY:n}=e;return"scaleX("+t+") scaleY("+n+")"}},Transform:{toString(e){if(e)return[M.Translate.toString(e),M.Scale.toString(e)].join(" ")}},Transition:{toString(e){let{property:t,duration:n,easing:r}=e;return t+" "+n+"ms "+r}}}),S="a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]";function T(e){return e.matches(S)?e:e.querySelector(S)}},78749:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},84616:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},92657:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}}]);