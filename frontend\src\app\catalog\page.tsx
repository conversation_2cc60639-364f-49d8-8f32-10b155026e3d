"use client"

import { useState, useEffect } from 'react'
import { useTranslation } from '@/hooks/useTranslation'
import { MockProduct } from '@/lib/mockData'
import { useCart } from '@/contexts/CartContext'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { PageLayout } from '@/components/layouts/PageLayout'
import { EnhancedImage } from '@/components/ui/enhanced-image'
import {
  GraduationCap,
  Search,
  Filter,
  Heart,
  ShoppingCart,
  Star,
  Eye,
  Loader2
} from 'lucide-react'

// فئات المنتجات
const productCategories = [
  { value: 'all', label: 'جميع المنتجات', icon: '🎓' },
  { value: 'أثواب التخرج', label: 'أثواب التخرج', icon: '👘' },
  { value: 'قبعات التخرج', label: 'قبعات التخرج', icon: '🎩' },
  { value: 'أوشحة التخرج', label: 'أوشحة التخرج', icon: '🧣' },
  { value: 'إكسسوارات التخرج', label: 'إكسسوارات التخرج', icon: '🏷️' }
]

export default function CatalogPage() {
  const { t } = useTranslation()
  const { addToCart, addToWishlist, removeFromWishlist, isInWishlist, cartItems } = useCart()
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [sortBy, setSortBy] = useState('name')
  const [products, setProducts] = useState<MockProduct[]>([])
  const [loading, setLoading] = useState(true)

  // جلب المنتجات من API
  const fetchProducts = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams()
      
      if (selectedCategory && selectedCategory !== 'all') {
        params.append('category', selectedCategory)
      }
      
      // عرض المنتجات المنشورة فقط في الكتالوج العام
      params.append('published', 'true')
      
      const response = await fetch(`/api/products?${params}`)
      const data = await response.json()
      
      if (response.ok) {
        setProducts(data.products || [])
      } else {
        console.error('Error fetching products:', data.error)
      }
    } catch (error) {
      console.error('Error fetching products:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchProducts()
  }, [selectedCategory])



  const toggleFavorite = (productId: string) => {
    const product = products.find(p => p.id === productId)
    if (!product) return

    if (isInWishlist(productId)) {
      removeFromWishlist(productId)
    } else {
      addToWishlist(productId, product)
    }
  }

  const handleAddToCart = (productId: string, type: 'purchase' | 'rental' = 'purchase') => {
    const product = products.find(p => p.id === productId)
    if (!product) return

    addToCart(productId, product, type)
  }

  // فلترة المنتجات حسب البحث
  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         product.description.toLowerCase().includes(searchQuery.toLowerCase())
    return matchesSearch
  })

  // ترتيب المنتجات
  const sortedProducts = [...filteredProducts].sort((a, b) => {
    switch (sortBy) {
      case 'price-low':
        return a.price - b.price
      case 'price-high':
        return b.price - a.price
      case 'rating':
        return (b.rating || 0) - (a.rating || 0)
      case 'name':
      default:
        return a.name.localeCompare(b.name, 'ar')
    }
  })

  return (
    <PageLayout containerClassName="mobile-container py-6 sm:py-8">
        {/* Page Title */}
        <div className="text-center mb-6 sm:mb-8">
          <h2 className="mobile-text-2xl sm:text-4xl font-bold text-gray-900 dark:text-white mb-3 sm:mb-4 arabic-text">
            🎓 كتالوج أزياء التخرج
          </h2>
          <p className="mobile-text-base sm:text-xl text-gray-600 dark:text-gray-300 arabic-text">
            اكتشف مجموعتنا المتميزة من أزياء التخرج الأنيقة
          </p>
        </div>

        {/* Filters */}
        <div className="mobile-form bg-white dark:bg-gray-800 rounded-lg shadow-sm mobile-spacing mb-6 sm:mb-8">
          <div className="grid grid-cols-1 gap-4 sm:gap-6">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="ابحث عن المنتجات..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="mobile-form-input pl-10 arabic-text"
              />
            </div>

            {/* Filters Row */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              {/* Category Filter */}
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="mobile-form-input arabic-text">
                  <SelectValue placeholder="اختر الفئة" />
                </SelectTrigger>
                <SelectContent>
                  {productCategories.map((category) => (
                    <SelectItem key={category.value} value={category.value} className="arabic-text">
                      {category.icon} {category.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              {/* Sort */}
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="mobile-form-input arabic-text">
                  <SelectValue placeholder="ترتيب حسب" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="name" className="arabic-text">الاسم</SelectItem>
                  <SelectItem value="price-low" className="arabic-text">السعر: من الأقل للأعلى</SelectItem>
                  <SelectItem value="price-high" className="arabic-text">السعر: من الأعلى للأقل</SelectItem>
                  <SelectItem value="rating" className="arabic-text">التقييم</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="mobile-loading py-12 sm:py-16">
            <div className="mobile-spinner"></div>
            <span className="mr-3 mobile-text-base text-gray-600 dark:text-gray-400 arabic-text">جاري تحميل المنتجات...</span>
          </div>
        )}

        {/* Products Grid */}
        {!loading && (
          <>
            {sortedProducts.length === 0 ? (
              <div className="text-center py-12 sm:py-16">
                <GraduationCap className="h-12 w-12 sm:h-16 sm:w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="mobile-text-lg sm:text-xl font-semibold text-gray-900 dark:text-white mb-2 arabic-text">
                  لا توجد منتجات
                </h3>
                <p className="mobile-text-base text-gray-600 dark:text-gray-400 arabic-text">
                  لم يتم العثور على منتجات تطابق معايير البحث
                </p>
              </div>
            ) : (
              <div className="mobile-grid gap-4 sm:gap-6">
                {sortedProducts.map((product) => (
                  <Card key={product.id} className="mobile-card group hover:shadow-lg transition-all duration-300">
                    <CardHeader className="p-0">
                      <div className="relative overflow-hidden rounded-t-lg">
                        <EnhancedImage
                          src={product.images[0] || '/images/products/placeholder.jpg'}
                          alt={product.name}
                          width={300}
                          height={300}
                          className="w-full h-48 sm:h-56 md:h-64 object-cover group-hover:scale-105 transition-transform duration-300"
                        />

                        {/* Favorite Button */}
                        <Button
                          size="sm"
                          variant="ghost"
                          className="absolute top-2 right-2 bg-white/80 hover:bg-white touch-target mobile-button"
                          onClick={() => toggleFavorite(product.id)}
                        >
                          <Heart
                            className={`h-4 w-4 ${
                              isInWishlist(product.id)
                                ? 'fill-red-500 text-red-500'
                                : 'text-gray-600'
                            }`}
                          />
                        </Button>

                        {/* Badges */}
                        <div className="absolute top-2 left-2 space-y-1">
                          {!product.is_available && (
                            <Badge variant="destructive" className="text-xs">
                              غير متوفر
                            </Badge>
                          )}
                          {product.rating && product.rating > 4.5 && (
                            <Badge variant="secondary" className="text-xs">
                              الأكثر تقييماً
                            </Badge>
                          )}
                        </div>
                      </div>
                    </CardHeader>

                    <CardContent className="p-4">
                      <CardTitle className="text-lg font-semibold mb-2 arabic-text line-clamp-1">
                        {product.name}
                      </CardTitle>
                      <CardDescription className="text-sm text-gray-600 dark:text-gray-400 mb-3 arabic-text line-clamp-2">
                        {product.description}
                      </CardDescription>

                      {/* Rating */}
                      {product.rating && (
                        <div className="flex items-center gap-1 mb-3">
                          <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                          <span className="text-sm font-medium">{product.rating}</span>
                          <span className="text-xs text-gray-500">
                            ({product.reviews_count || 0} تقييم)
                          </span>
                        </div>
                      )}

                      {/* Prices */}
                      <div className="mb-4">
                        <div className="flex items-center gap-2">
                          <span className="text-lg font-bold text-blue-600">
                            {product.price} Dhs
                          </span>
                          {product.rental_price && (
                            <span className="text-sm text-gray-500">
                              إيجار: {product.rental_price} Dhs
                            </span>
                          )}
                        </div>
                      </div>

                      {/* Actions */}
                      <div className="space-y-2">
                        {/* أزرار الشراء والإيجار */}
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            className="flex-1"
                            onClick={() => handleAddToCart(product.id, 'purchase')}
                            disabled={!product.is_available}
                            variant={cartItems.some(item => item.id === product.id && item.type === 'purchase') ? "secondary" : "default"}
                          >
                            <ShoppingCart className="h-4 w-4 mr-2" />
                            شراء {product.price} Dhs
                          </Button>

                          {product.rental_price && (
                            <Button
                              size="sm"
                              className="flex-1"
                              onClick={() => handleAddToCart(product.id, 'rental')}
                              disabled={!product.is_available}
                              variant={cartItems.some(item => item.id === product.id && item.type === 'rental') ? "secondary" : "outline"}
                            >
                              <ShoppingCart className="h-4 w-4 mr-2" />
                              إيجار {product.rental_price} Dhs
                            </Button>
                          )}
                        </div>

                        {/* زر عرض التفاصيل */}
                        <Button size="sm" variant="outline" className="w-full" asChild>
                          <a href={`/product/${product.id}`}>
                            <Eye className="h-4 w-4 mr-2" />
                            عرض التفاصيل
                          </a>
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </>
        )}
    </PageLayout>
  )
}
