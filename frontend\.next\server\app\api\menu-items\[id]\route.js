/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/menu-items/[id]/route";
exports.ids = ["app/api/menu-items/[id]/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmenu-items%2F%5Bid%5D%2Froute&page=%2Fapi%2Fmenu-items%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmenu-items%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CGraduation%20Toqs%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CGraduation%20Toqs%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmenu-items%2F%5Bid%5D%2Froute&page=%2Fapi%2Fmenu-items%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmenu-items%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CGraduation%20Toqs%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CGraduation%20Toqs%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_faiss_Desktop_Graduation_Toqs_frontend_src_app_api_menu_items_id_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/menu-items/[id]/route.ts */ \"(rsc)/./src/app/api/menu-items/[id]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/menu-items/[id]/route\",\n        pathname: \"/api/menu-items/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/menu-items/[id]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Graduation Toqs\\\\frontend\\\\src\\\\app\\\\api\\\\menu-items\\\\[id]\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_faiss_Desktop_Graduation_Toqs_frontend_src_app_api_menu_items_id_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZtZW51LWl0ZW1zJTJGJTVCaWQlNUQlMkZyb3V0ZSZwYWdlPSUyRmFwaSUyRm1lbnUtaXRlbXMlMkYlNUJpZCU1RCUyRnJvdXRlJmFwcFBhdGhzPSZwYWdlUGF0aD1wcml2YXRlLW5leHQtYXBwLWRpciUyRmFwaSUyRm1lbnUtaXRlbXMlMkYlNUJpZCU1RCUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDVXNlcnMlNUNmYWlzcyU1Q0Rlc2t0b3AlNUNHcmFkdWF0aW9uJTIwVG9xcyU1Q2Zyb250ZW5kJTVDc3JjJTVDYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj1DJTNBJTVDVXNlcnMlNUNmYWlzcyU1Q0Rlc2t0b3AlNUNHcmFkdWF0aW9uJTIwVG9xcyU1Q2Zyb250ZW5kJmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUErRjtBQUN2QztBQUNxQjtBQUMrQztBQUM1SDtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IseUdBQW1CO0FBQzNDO0FBQ0EsY0FBYyxrRUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsWUFBWTtBQUNaLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSxRQUFRLHNEQUFzRDtBQUM5RDtBQUNBLFdBQVcsNEVBQVc7QUFDdEI7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUMwRjs7QUFFMUYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBcHBSb3V0ZVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUtbW9kdWxlcy9hcHAtcm91dGUvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgeyBwYXRjaEZldGNoIGFzIF9wYXRjaEZldGNoIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvbGliL3BhdGNoLWZldGNoXCI7XG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiQzpcXFxcVXNlcnNcXFxcZmFpc3NcXFxcRGVza3RvcFxcXFxHcmFkdWF0aW9uIFRvcXNcXFxcZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxhcGlcXFxcbWVudS1pdGVtc1xcXFxbaWRdXFxcXHJvdXRlLnRzXCI7XG4vLyBXZSBpbmplY3QgdGhlIG5leHRDb25maWdPdXRwdXQgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IG5leHRDb25maWdPdXRwdXQgPSBcIlwiXG5jb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBSb3V0ZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUk9VVEUsXG4gICAgICAgIHBhZ2U6IFwiL2FwaS9tZW51LWl0ZW1zL1tpZF0vcm91dGVcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FwaS9tZW51LWl0ZW1zL1tpZF1cIixcbiAgICAgICAgZmlsZW5hbWU6IFwicm91dGVcIixcbiAgICAgICAgYnVuZGxlUGF0aDogXCJhcHAvYXBpL21lbnUtaXRlbXMvW2lkXS9yb3V0ZVwiXG4gICAgfSxcbiAgICByZXNvbHZlZFBhZ2VQYXRoOiBcIkM6XFxcXFVzZXJzXFxcXGZhaXNzXFxcXERlc2t0b3BcXFxcR3JhZHVhdGlvbiBUb3FzXFxcXGZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxcYXBpXFxcXG1lbnUtaXRlbXNcXFxcW2lkXVxcXFxyb3V0ZS50c1wiLFxuICAgIG5leHRDb25maWdPdXRwdXQsXG4gICAgdXNlcmxhbmRcbn0pO1xuLy8gUHVsbCBvdXQgdGhlIGV4cG9ydHMgdGhhdCB3ZSBuZWVkIHRvIGV4cG9zZSBmcm9tIHRoZSBtb2R1bGUuIFRoaXMgc2hvdWxkXG4vLyBiZSBlbGltaW5hdGVkIHdoZW4gd2UndmUgbW92ZWQgdGhlIG90aGVyIHJvdXRlcyB0byB0aGUgbmV3IGZvcm1hdC4gVGhlc2Vcbi8vIGFyZSB1c2VkIHRvIGhvb2sgaW50byB0aGUgcm91dGUuXG5jb25zdCB7IHdvcmtBc3luY1N0b3JhZ2UsIHdvcmtVbml0QXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcyB9ID0gcm91dGVNb2R1bGU7XG5mdW5jdGlvbiBwYXRjaEZldGNoKCkge1xuICAgIHJldHVybiBfcGF0Y2hGZXRjaCh7XG4gICAgICAgIHdvcmtBc3luY1N0b3JhZ2UsXG4gICAgICAgIHdvcmtVbml0QXN5bmNTdG9yYWdlXG4gICAgfSk7XG59XG5leHBvcnQgeyByb3V0ZU1vZHVsZSwgd29ya0FzeW5jU3RvcmFnZSwgd29ya1VuaXRBc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzLCBwYXRjaEZldGNoLCAgfTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXJvdXRlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmenu-items%2F%5Bid%5D%2Froute&page=%2Fapi%2Fmenu-items%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmenu-items%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CGraduation%20Toqs%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CGraduation%20Toqs%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/menu-items/[id]/route.ts":
/*!**********************************************!*\
  !*** ./src/app/api/menu-items/[id]/route.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_mockData__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/mockData */ \"(rsc)/./src/lib/mockData.ts\");\n\n\n// GET - جلب عنصر قائمة واحد\nasync function GET(request, { params }) {\n    try {\n        const { id } = await params;\n        const menuItems = _lib_mockData__WEBPACK_IMPORTED_MODULE_1__.MockDataManager.getMenuItems();\n        const menuItem = menuItems.find((item)=>item.id === id);\n        if (!menuItem) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'عنصر القائمة غير موجود'\n            }, {\n                status: 404\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            menuItem\n        });\n    } catch (error) {\n        console.error('Unexpected error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'خطأ غير متوقع'\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT - تحديث عنصر قائمة\nasync function PUT(request, { params }) {\n    try {\n        const { id } = await params;\n        const body = await request.json();\n        const { title_ar, title_en, title_fr, slug, icon, parent_id, order_index, is_active, target_type, target_value } = body;\n        // جلب العناصر الحالية\n        const menuItems = _lib_mockData__WEBPACK_IMPORTED_MODULE_1__.MockDataManager.getMenuItems();\n        const itemIndex = menuItems.findIndex((item)=>item.id === id);\n        if (itemIndex === -1) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'عنصر القائمة غير موجود'\n            }, {\n                status: 404\n            });\n        }\n        // التحقق من عدم تكرار الـ slug (إذا تم تغييره)\n        if (slug && slug !== menuItems[itemIndex].slug) {\n            const existingItem = menuItems.find((item)=>item.slug === slug && item.id !== id);\n            if (existingItem) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'الرابط المختصر موجود بالفعل'\n                }, {\n                    status: 400\n                });\n            }\n        }\n        // تحديث العنصر\n        const updatedMenuItem = {\n            ...menuItems[itemIndex],\n            title_ar: title_ar || menuItems[itemIndex].title_ar,\n            title_en: title_en !== undefined ? title_en : menuItems[itemIndex].title_en,\n            title_fr: title_fr !== undefined ? title_fr : menuItems[itemIndex].title_fr,\n            slug: slug || menuItems[itemIndex].slug,\n            icon: icon !== undefined ? icon : menuItems[itemIndex].icon,\n            parent_id: parent_id !== undefined ? parent_id : menuItems[itemIndex].parent_id,\n            order_index: order_index !== undefined ? order_index : menuItems[itemIndex].order_index,\n            is_active: is_active !== undefined ? is_active : menuItems[itemIndex].is_active,\n            target_type: target_type || menuItems[itemIndex].target_type,\n            target_value: target_value || menuItems[itemIndex].target_value,\n            updated_at: new Date().toISOString()\n        };\n        // حفظ التحديثات\n        menuItems[itemIndex] = updatedMenuItem;\n        _lib_mockData__WEBPACK_IMPORTED_MODULE_1__.MockDataManager.saveMenuItems(menuItems);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: 'تم تحديث عنصر القائمة بنجاح',\n            menuItem: updatedMenuItem\n        });\n    } catch (error) {\n        console.error('Unexpected error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'خطأ غير متوقع'\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE - حذف عنصر قائمة\nasync function DELETE(request, { params }) {\n    try {\n        const { id } = await params;\n        // جلب العناصر الحالية\n        const menuItems = _lib_mockData__WEBPACK_IMPORTED_MODULE_1__.MockDataManager.getMenuItems();\n        const itemIndex = menuItems.findIndex((item)=>item.id === id);\n        if (itemIndex === -1) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'عنصر القائمة غير موجود'\n            }, {\n                status: 404\n            });\n        }\n        // التحقق من وجود عناصر فرعية\n        const childItems = menuItems.filter((item)=>item.parent_id === id);\n        if (childItems.length > 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'لا يمكن حذف عنصر يحتوي على عناصر فرعية'\n            }, {\n                status: 400\n            });\n        }\n        // حذف العنصر\n        menuItems.splice(itemIndex, 1);\n        _lib_mockData__WEBPACK_IMPORTED_MODULE_1__.MockDataManager.saveMenuItems(menuItems);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: 'تم حذف عنصر القائمة بنجاح'\n        });\n    } catch (error) {\n        console.error('Unexpected error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'خطأ غير متوقع'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/menu-items/[id]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/mockData.ts":
/*!*****************************!*\
  !*** ./src/lib/mockData.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MockDataManager: () => (/* binding */ MockDataManager),\n/* harmony export */   mockCategories: () => (/* binding */ mockCategories),\n/* harmony export */   mockMenuItems: () => (/* binding */ mockMenuItems),\n/* harmony export */   mockOrders: () => (/* binding */ mockOrders),\n/* harmony export */   mockPages: () => (/* binding */ mockPages),\n/* harmony export */   mockProducts: () => (/* binding */ mockProducts),\n/* harmony export */   mockSchools: () => (/* binding */ mockSchools)\n/* harmony export */ });\n// بيانات وهمية للتطوير والاختبار\n// بيانات وهمية للصفحات\nconst mockPages = [\n    {\n        id: '1',\n        slug: 'about-us',\n        is_published: true,\n        author_id: 'admin-1',\n        featured_image: '/images/about-hero.jpg',\n        created_at: '2024-01-15T10:00:00Z',\n        updated_at: '2024-01-20T14:30:00Z',\n        profiles: {\n            full_name: 'مدير النظام'\n        },\n        page_content: [\n            {\n                id: '1-ar',\n                page_id: '1',\n                language: 'ar',\n                title: 'من نحن',\n                content: '<h2>مرحباً بكم في منصة أزياء التخرج</h2><p>نحن منصة متخصصة في تأجير وبيع أزياء التخرج المغربية الأصيلة. نهدف إلى جعل يوم تخرجكم لا يُنسى من خلال توفير أجمل الأزياء التقليدية.</p><p>تأسست منصتنا عام 2024 بهدف خدمة الطلاب والطالبات في جميع أنحاء المغرب، ونفتخر بتقديم خدمات عالية الجودة وأسعار مناسبة.</p>',\n                meta_description: 'تعرف على منصة أزياء التخرج المغربية - خدمات تأجير وبيع الأزياء التقليدية',\n                meta_keywords: 'أزياء التخرج، المغرب، تأجير، بيع، تقليدي'\n            },\n            {\n                id: '1-en',\n                page_id: '1',\n                language: 'en',\n                title: 'About Us',\n                content: '<h2>Welcome to Graduation Attire Platform</h2><p>We are a specialized platform for renting and selling authentic Moroccan graduation attire. We aim to make your graduation day unforgettable by providing the most beautiful traditional outfits.</p><p>Our platform was founded in 2024 to serve students throughout Morocco, and we pride ourselves on providing high-quality services at affordable prices.</p>',\n                meta_description: 'Learn about the Moroccan Graduation Attire Platform - traditional outfit rental and sales services',\n                meta_keywords: 'graduation attire, Morocco, rental, sales, traditional'\n            }\n        ]\n    },\n    {\n        id: '2',\n        slug: 'services',\n        is_published: true,\n        author_id: 'admin-1',\n        created_at: '2024-01-16T09:00:00Z',\n        updated_at: '2024-01-22T16:45:00Z',\n        profiles: {\n            full_name: 'مدير النظام'\n        },\n        page_content: [\n            {\n                id: '2-ar',\n                page_id: '2',\n                language: 'ar',\n                title: 'خدماتنا',\n                content: '<h2>خدماتنا المتميزة</h2><h3>تأجير الأزياء</h3><p>نوفر خدمة تأجير أزياء التخرج لفترات مرنة مع ضمان النظافة والجودة.</p><h3>بيع الأزياء</h3><p>إمكانية شراء الأزياء للاحتفاظ بها كذكرى جميلة من يوم التخرج.</p><h3>التخصيص</h3><p>خدمات تخصيص الأزياء حسب المقاسات والتفضيلات الشخصية.</p>',\n                meta_description: 'خدمات منصة أزياء التخرج - تأجير وبيع وتخصيص الأزياء التقليدية المغربية',\n                meta_keywords: 'خدمات، تأجير، بيع، تخصيص، أزياء التخرج'\n            }\n        ]\n    },\n    {\n        id: '3',\n        slug: 'contact',\n        is_published: true,\n        author_id: 'admin-1',\n        created_at: '2024-01-17T11:00:00Z',\n        updated_at: '2024-01-17T11:00:00Z',\n        profiles: {\n            full_name: 'مدير النظام'\n        },\n        page_content: [\n            {\n                id: '3-ar',\n                page_id: '3',\n                language: 'ar',\n                title: 'اتصل بنا',\n                content: '<h2>تواصل معنا</h2><p>نحن هنا لخدمتكم في أي وقت. يمكنكم التواصل معنا عبر:</p><ul><li>الهاتف: +212 5XX-XXXXXX</li><li>البريد الإلكتروني: <EMAIL></li><li>العنوان: الدار البيضاء، المغرب</li></ul>',\n                meta_description: 'تواصل مع منصة أزياء التخرج المغربية',\n                meta_keywords: 'اتصال، تواصل، خدمة العملاء'\n            }\n        ]\n    }\n];\n// بيانات وهمية للقوائم\nconst mockMenuItems = [\n    {\n        id: '1',\n        title_ar: 'الرئيسية',\n        title_en: 'Home',\n        title_fr: 'Accueil',\n        slug: 'home',\n        icon: 'Home',\n        order_index: 1,\n        is_active: true,\n        target_type: 'internal',\n        target_value: '/',\n        created_at: '2024-01-15T10:00:00Z',\n        updated_at: '2024-01-15T10:00:00Z'\n    },\n    {\n        id: '2',\n        title_ar: 'من نحن',\n        title_en: 'About Us',\n        title_fr: 'À propos',\n        slug: 'about',\n        icon: 'Info',\n        order_index: 2,\n        is_active: true,\n        target_type: 'internal',\n        target_value: '/about',\n        created_at: '2024-01-15T10:05:00Z',\n        updated_at: '2024-01-15T10:05:00Z'\n    },\n    {\n        id: '3',\n        title_ar: 'خدماتنا',\n        title_en: 'Services',\n        title_fr: 'Services',\n        slug: 'services',\n        icon: 'Settings',\n        order_index: 3,\n        is_active: true,\n        target_type: 'internal',\n        target_value: '/services',\n        created_at: '2024-01-15T10:10:00Z',\n        updated_at: '2024-01-15T10:10:00Z'\n    },\n    {\n        id: '4',\n        title_ar: 'المنتجات',\n        title_en: 'Products',\n        title_fr: 'Produits',\n        slug: 'products',\n        icon: 'Package',\n        order_index: 4,\n        is_active: true,\n        target_type: 'internal',\n        target_value: '/catalog',\n        created_at: '2024-01-15T10:15:00Z',\n        updated_at: '2024-01-15T10:15:00Z'\n    },\n    {\n        id: '5',\n        title_ar: 'تأجير الأزياء',\n        title_en: 'Rental',\n        title_fr: 'Location',\n        slug: 'rental',\n        parent_id: '4',\n        icon: 'Calendar',\n        order_index: 1,\n        is_active: true,\n        target_type: 'internal',\n        target_value: '/catalog?type=rental',\n        created_at: '2024-01-15T10:20:00Z',\n        updated_at: '2024-01-15T10:20:00Z'\n    },\n    {\n        id: '6',\n        title_ar: 'بيع الأزياء',\n        title_en: 'Sales',\n        title_fr: 'Vente',\n        slug: 'sales',\n        parent_id: '4',\n        icon: 'ShoppingCart',\n        order_index: 2,\n        is_active: true,\n        target_type: 'internal',\n        target_value: '/catalog?type=sale',\n        created_at: '2024-01-15T10:25:00Z',\n        updated_at: '2024-01-15T10:25:00Z'\n    },\n    {\n        id: '8',\n        title_ar: 'اتصل بنا',\n        title_en: 'Contact',\n        title_fr: 'Contact',\n        slug: 'contact',\n        icon: 'Phone',\n        order_index: 6,\n        is_active: true,\n        target_type: 'internal',\n        target_value: '/contact',\n        created_at: '2024-01-15T10:35:00Z',\n        updated_at: '2024-01-15T10:35:00Z'\n    }\n];\n// بيانات وهمية للفئات\nconst mockCategories = [\n    {\n        id: '1',\n        name_ar: 'أثواب التخرج',\n        name_en: 'Graduation Gowns',\n        name_fr: 'Robes de Graduation',\n        slug: 'gown',\n        icon: '👘',\n        description: 'أثواب التخرج الأكاديمية التقليدية',\n        is_active: true,\n        order_index: 1,\n        created_at: '2024-01-15T10:00:00Z',\n        updated_at: '2024-01-15T10:00:00Z'\n    },\n    {\n        id: '2',\n        name_ar: 'قبعات التخرج',\n        name_en: 'Graduation Caps',\n        name_fr: 'Chapeaux de Graduation',\n        slug: 'cap',\n        icon: '🎩',\n        description: 'قبعات التخرج الأكاديمية',\n        is_active: true,\n        order_index: 2,\n        created_at: '2024-01-15T10:05:00Z',\n        updated_at: '2024-01-15T10:05:00Z'\n    },\n    {\n        id: '3',\n        name_ar: 'شرابات التخرج',\n        name_en: 'Graduation Tassels',\n        name_fr: 'Glands de Graduation',\n        slug: 'tassel',\n        icon: '🏷️',\n        description: 'شرابات التخرج الملونة',\n        is_active: true,\n        order_index: 3,\n        created_at: '2024-01-15T10:10:00Z',\n        updated_at: '2024-01-15T10:10:00Z'\n    },\n    {\n        id: '4',\n        name_ar: 'أوشحة التخرج',\n        name_en: 'Graduation Stoles',\n        name_fr: 'Étoles de Graduation',\n        slug: 'stole',\n        icon: '🧣',\n        description: 'أوشحة التخرج المميزة',\n        is_active: true,\n        order_index: 4,\n        created_at: '2024-01-15T10:15:00Z',\n        updated_at: '2024-01-15T10:15:00Z'\n    },\n    {\n        id: '5',\n        name_ar: 'القلانس الأكاديمية',\n        name_en: 'Academic Hoods',\n        name_fr: 'Capuches Académiques',\n        slug: 'hood',\n        icon: '🎓',\n        description: 'القلانس الأكاديمية للدرجات العليا',\n        is_active: true,\n        order_index: 5,\n        created_at: '2024-01-15T10:20:00Z',\n        updated_at: '2024-01-15T10:20:00Z'\n    }\n];\n// بيانات وهمية للمنتجات\nconst mockProducts = [\n    {\n        id: '1',\n        name: 'ثوب التخرج الكلاسيكي',\n        description: 'ثوب تخرج أنيق مصنوع من أجود الخامات، مناسب لجميع المناسبات الأكاديمية',\n        category: 'gown',\n        price: 299.99,\n        rental_price: 99.99,\n        colors: [\n            'أسود',\n            'أزرق داكن',\n            'بورجوندي'\n        ],\n        sizes: [\n            'S',\n            'M',\n            'L',\n            'XL',\n            'XXL'\n        ],\n        images: [\n            '/images/products/gown-classic-1.jpg',\n            '/images/products/gown-classic-2.jpg'\n        ],\n        stock_quantity: 25,\n        is_available: true,\n        is_published: true,\n        created_at: '2024-01-15T10:00:00Z',\n        updated_at: '2024-01-20T14:30:00Z',\n        rating: 4.8,\n        reviews_count: 42,\n        features: [\n            'مقاوم للتجاعيد',\n            'قابل للغسيل',\n            'خامة عالية الجودة'\n        ],\n        specifications: {\n            material: 'بوليستر عالي الجودة',\n            weight: '0.8 كيلو',\n            care: 'غسيل جاف أو غسيل عادي'\n        }\n    },\n    {\n        id: '2',\n        name: 'قبعة التخرج التقليدية',\n        description: 'قبعة تخرج تقليدية مع شرابة ذهبية، رمز الإنجاز الأكاديمي',\n        category: 'cap',\n        price: 79.99,\n        rental_price: 29.99,\n        colors: [\n            'أسود',\n            'أزرق داكن'\n        ],\n        sizes: [\n            'One Size'\n        ],\n        images: [\n            '/images/products/cap-traditional-1.jpg'\n        ],\n        stock_quantity: 50,\n        is_available: true,\n        is_published: true,\n        created_at: '2024-01-16T09:00:00Z',\n        updated_at: '2024-01-22T16:45:00Z',\n        rating: 4.6,\n        reviews_count: 28,\n        features: [\n            'مقاس واحد يناسب الجميع',\n            'شرابة ذهبية',\n            'تصميم تقليدي'\n        ],\n        specifications: {\n            material: 'قطن مخلوط',\n            tassel_color: 'ذهبي',\n            adjustable: 'نعم'\n        }\n    },\n    {\n        id: '3',\n        name: 'وشاح التخرج المطرز',\n        description: 'وشاح تخرج مطرز بخيوط ذهبية، يضيف لمسة من الأناقة والتميز',\n        category: 'stole',\n        price: 149.99,\n        rental_price: 49.99,\n        colors: [\n            'أبيض مع ذهبي',\n            'أزرق مع فضي',\n            'أحمر مع ذهبي'\n        ],\n        sizes: [\n            'One Size'\n        ],\n        images: [\n            '/images/products/stole-embroidered-1.jpg',\n            '/images/products/stole-embroidered-2.jpg'\n        ],\n        stock_quantity: 15,\n        is_available: true,\n        is_published: true,\n        created_at: '2024-01-17T11:00:00Z',\n        updated_at: '2024-01-25T10:15:00Z',\n        rating: 4.9,\n        reviews_count: 18,\n        features: [\n            'تطريز يدوي',\n            'خيوط ذهبية',\n            'تصميم فاخر'\n        ],\n        specifications: {\n            material: 'حرير طبيعي',\n            embroidery: 'خيوط ذهبية وفضية',\n            length: '150 سم'\n        }\n    },\n    {\n        id: '4',\n        name: 'شرابة التخرج الذهبية',\n        description: 'شرابة تخرج ذهبية اللون، رمز التفوق والإنجاز الأكاديمي',\n        category: 'tassel',\n        price: 39.99,\n        rental_price: 15.99,\n        colors: [\n            'ذهبي',\n            'فضي',\n            'أزرق',\n            'أحمر'\n        ],\n        sizes: [\n            'One Size'\n        ],\n        images: [\n            '/images/products/tassel-gold-1.jpg'\n        ],\n        stock_quantity: 100,\n        is_available: true,\n        is_published: true,\n        created_at: '2024-01-18T14:00:00Z',\n        updated_at: '2024-01-26T09:30:00Z',\n        rating: 4.7,\n        reviews_count: 35,\n        features: [\n            'خيوط عالية الجودة',\n            'ألوان ثابتة',\n            'سهل التركيب'\n        ],\n        specifications: {\n            material: 'خيوط حريرية',\n            length: '23 سم',\n            attachment: 'مشبك معدني'\n        }\n    },\n    {\n        id: '5',\n        name: 'قلنسوة الدكتوراه الفاخرة',\n        description: 'قلنسوة دكتوراه فاخرة مصممة خصيصاً لحفلات التخرج الأكاديمية العليا',\n        category: 'hood',\n        price: 199.99,\n        rental_price: 79.99,\n        colors: [\n            'أسود مع ذهبي',\n            'أزرق مع فضي'\n        ],\n        sizes: [\n            'M',\n            'L',\n            'XL'\n        ],\n        images: [\n            '/images/products/hood-doctorate-1.jpg',\n            '/images/products/hood-doctorate-2.jpg'\n        ],\n        stock_quantity: 8,\n        is_available: true,\n        is_published: true,\n        created_at: '2024-01-19T16:00:00Z',\n        updated_at: '2024-01-27T12:00:00Z',\n        rating: 5.0,\n        reviews_count: 12,\n        features: [\n            'تصميم أكاديمي أصيل',\n            'خامة فاخرة',\n            'مناسب للدكتوراه'\n        ],\n        specifications: {\n            material: 'مخمل عالي الجودة',\n            lining: 'حرير ملون',\n            academic_level: 'دكتوراه'\n        }\n    }\n];\n// بيانات وهمية للمدارس\nconst mockSchools = [\n    {\n        id: '1',\n        admin_id: 'admin-school-1',\n        name: 'جامعة الإمارات العربية المتحدة',\n        name_en: 'United Arab Emirates University',\n        name_fr: 'Université des Émirats Arabes Unis',\n        address: 'شارع الجامعة، العين',\n        city: 'العين',\n        phone: '+971-3-713-5000',\n        email: '<EMAIL>',\n        website: 'https://www.uaeu.ac.ae',\n        logo_url: '/images/schools/uaeu-logo.png',\n        graduation_date: '2024-06-15',\n        student_count: 14500,\n        is_active: true,\n        settings: {\n            graduation_ceremony_location: 'قاعة الاحتفالات الكبرى',\n            dress_code: 'formal',\n            photography_allowed: true\n        },\n        created_at: '2024-01-10T08:00:00Z',\n        updated_at: '2024-01-25T10:30:00Z'\n    },\n    {\n        id: '2',\n        admin_id: 'admin-school-2',\n        name: 'الجامعة الأمريكية في الشارقة',\n        name_en: 'American University of Sharjah',\n        name_fr: 'Université Américaine de Sharjah',\n        address: 'شارع الجامعة، الشارقة',\n        city: 'الشارقة',\n        phone: '+971-6-515-5555',\n        email: '<EMAIL>',\n        website: 'https://www.aus.edu',\n        logo_url: '/images/schools/aus-logo.png',\n        graduation_date: '2024-05-20',\n        student_count: 6200,\n        is_active: true,\n        settings: {\n            graduation_ceremony_location: 'مسرح الجامعة',\n            dress_code: 'academic',\n            photography_allowed: true\n        },\n        created_at: '2024-01-12T09:15:00Z',\n        updated_at: '2024-01-28T14:20:00Z'\n    },\n    {\n        id: '3',\n        admin_id: 'admin-school-3',\n        name: 'جامعة زايد',\n        name_en: 'Zayed University',\n        name_fr: 'Université Zayed',\n        address: 'شارع الشيخ زايد، دبي',\n        city: 'دبي',\n        phone: '+971-4-402-1111',\n        email: '<EMAIL>',\n        website: 'https://www.zu.ac.ae',\n        logo_url: '/images/schools/zu-logo.png',\n        graduation_date: '2024-06-10',\n        student_count: 9800,\n        is_active: true,\n        settings: {\n            graduation_ceremony_location: 'مركز المؤتمرات',\n            dress_code: 'formal',\n            photography_allowed: false\n        },\n        created_at: '2024-01-15T11:00:00Z',\n        updated_at: '2024-02-01T16:45:00Z'\n    },\n    {\n        id: '4',\n        admin_id: 'admin-school-4',\n        name: 'كلية الإمارات للتكنولوجيا',\n        name_en: 'Emirates Institute of Technology',\n        name_fr: 'Institut de Technologie des Émirats',\n        address: 'المنطقة الأكاديمية، أبوظبي',\n        city: 'أبوظبي',\n        phone: '+971-2-401-4000',\n        email: '<EMAIL>',\n        website: 'https://www.eit.ac.ae',\n        logo_url: '/images/schools/eit-logo.png',\n        graduation_date: '2024-07-05',\n        student_count: 3500,\n        is_active: true,\n        settings: {\n            graduation_ceremony_location: 'القاعة الرئيسية',\n            dress_code: 'business',\n            photography_allowed: true\n        },\n        created_at: '2024-01-18T13:30:00Z',\n        updated_at: '2024-02-05T09:15:00Z'\n    },\n    {\n        id: '5',\n        admin_id: 'admin-school-5',\n        name: 'معهد أبوظبي للتعليم التقني',\n        name_en: 'Abu Dhabi Technical Institute',\n        name_fr: 'Institut Technique d\\'Abu Dhabi',\n        address: 'المنطقة الصناعية، أبوظبي',\n        city: 'أبوظبي',\n        phone: '+971-2-505-2000',\n        email: '<EMAIL>',\n        website: 'https://www.adti.ac.ae',\n        graduation_date: '2024-06-25',\n        student_count: 2800,\n        is_active: false,\n        settings: {\n            graduation_ceremony_location: 'مركز التدريب',\n            dress_code: 'casual',\n            photography_allowed: true\n        },\n        created_at: '2024-01-20T15:45:00Z',\n        updated_at: '2024-02-10T12:00:00Z'\n    }\n];\n// بيانات وهمية للطلبات\nconst mockOrders = [\n    {\n        id: '1',\n        order_number: 'GT-240120-001',\n        customer_id: 'student-1',\n        customer_name: 'أحمد محمد علي',\n        customer_email: '<EMAIL>',\n        customer_phone: '+971-50-123-4567',\n        status: 'in_production',\n        items: [\n            {\n                id: '1',\n                order_id: '1',\n                product_id: '1',\n                product_name: 'ثوب التخرج الكلاسيكي',\n                product_image: '/images/products/gown-classic-1.jpg',\n                category: 'gown',\n                quantity: 1,\n                unit_price: 299.99,\n                total_price: 299.99,\n                customizations: {\n                    color: 'أسود',\n                    size: 'L',\n                    embroidery: 'أحمد علي - بكالوريوس هندسة'\n                }\n            },\n            {\n                id: '2',\n                order_id: '1',\n                product_id: '2',\n                product_name: 'قبعة التخرج الأكاديمية',\n                product_image: '/images/products/cap-academic-1.jpg',\n                category: 'cap',\n                quantity: 1,\n                unit_price: 89.99,\n                total_price: 89.99,\n                customizations: {\n                    color: 'أسود',\n                    size: 'M'\n                }\n            }\n        ],\n        subtotal: 389.98,\n        tax: 19.50,\n        shipping_cost: 25.00,\n        total: 434.48,\n        payment_status: 'paid',\n        payment_method: 'credit_card',\n        shipping_address: {\n            street: 'شارع الجامعة، مبنى 12، شقة 304',\n            city: 'العين',\n            state: 'أبوظبي',\n            postal_code: '17666',\n            country: 'الإمارات العربية المتحدة'\n        },\n        tracking_number: 'TRK-GT-001-2024',\n        notes: 'يرجى التسليم قبل حفل التخرج',\n        created_at: '2024-01-20T10:30:00Z',\n        updated_at: '2024-01-22T14:15:00Z',\n        delivery_date: '2024-02-15T00:00:00Z',\n        school_id: '1',\n        school_name: 'جامعة الإمارات العربية المتحدة'\n    },\n    {\n        id: '2',\n        order_number: 'GT-**********',\n        customer_id: 'student-2',\n        customer_name: 'فاطمة سالم الزهراني',\n        customer_email: '<EMAIL>',\n        customer_phone: '+971-56-789-0123',\n        status: 'delivered',\n        items: [\n            {\n                id: '3',\n                order_id: '2',\n                product_id: '3',\n                product_name: 'ثوب التخرج المميز',\n                product_image: '/images/products/gown-premium-1.jpg',\n                category: 'gown',\n                quantity: 1,\n                unit_price: 399.99,\n                total_price: 399.99,\n                customizations: {\n                    color: 'أزرق داكن',\n                    size: 'M',\n                    embroidery: 'فاطمة الزهراني - ماجستير إدارة أعمال'\n                }\n            }\n        ],\n        subtotal: 399.99,\n        tax: 20.00,\n        shipping_cost: 30.00,\n        total: 449.99,\n        payment_status: 'paid',\n        payment_method: 'bank_transfer',\n        shipping_address: {\n            street: 'شارع الكورنيش، برج الإمارات، الطابق 15',\n            city: 'الشارقة',\n            state: 'الشارقة',\n            postal_code: '27272',\n            country: 'الإمارات العربية المتحدة'\n        },\n        tracking_number: 'TRK-GT-002-2024',\n        created_at: '2024-01-21T09:15:00Z',\n        updated_at: '2024-01-25T16:30:00Z',\n        delivery_date: '2024-01-28T00:00:00Z',\n        school_id: '2',\n        school_name: 'الجامعة الأمريكية في الشارقة'\n    },\n    {\n        id: '3',\n        order_number: 'GT-**********',\n        customer_id: 'student-3',\n        customer_name: 'خالد عبدالله المنصوري',\n        customer_email: '<EMAIL>',\n        customer_phone: '+971-52-456-7890',\n        status: 'pending',\n        items: [\n            {\n                id: '4',\n                order_id: '3',\n                product_id: '1',\n                product_name: 'ثوب التخرج الكلاسيكي',\n                product_image: '/images/products/gown-classic-1.jpg',\n                category: 'gown',\n                quantity: 1,\n                unit_price: 299.99,\n                total_price: 299.99,\n                customizations: {\n                    color: 'بورجوندي',\n                    size: 'XL'\n                }\n            },\n            {\n                id: '5',\n                order_id: '3',\n                product_id: '4',\n                product_name: 'وشاح التخرج المطرز',\n                product_image: '/images/products/stole-embroidered-1.jpg',\n                category: 'stole',\n                quantity: 1,\n                unit_price: 149.99,\n                total_price: 149.99,\n                customizations: {\n                    color: 'ذهبي',\n                    embroidery: 'كلية الهندسة'\n                }\n            }\n        ],\n        subtotal: 449.98,\n        tax: 22.50,\n        shipping_cost: 25.00,\n        total: 497.48,\n        payment_status: 'pending',\n        shipping_address: {\n            street: 'شارع الشيخ زايد، مجمع دبي الأكاديمي',\n            city: 'دبي',\n            state: 'دبي',\n            postal_code: '391186',\n            country: 'الإمارات العربية المتحدة'\n        },\n        created_at: '2024-01-22T14:45:00Z',\n        updated_at: '2024-01-22T14:45:00Z',\n        school_id: '3',\n        school_name: 'جامعة زايد'\n    }\n];\n// مساعدات للتعامل مع البيانات الوهمية\nclass MockDataManager {\n    static getStorageKey(type) {\n        return `mockData_${type}`;\n    }\n    static getPages() {\n        if (true) return mockPages;\n        const stored = localStorage.getItem(this.getStorageKey('pages'));\n        return stored ? JSON.parse(stored) : mockPages;\n    }\n    static getMenuItems() {\n        if (true) return mockMenuItems;\n        const stored = localStorage.getItem(this.getStorageKey('menuItems'));\n        return stored ? JSON.parse(stored) : mockMenuItems;\n    }\n    static getProducts() {\n        if (true) {\n            console.log('Server side - returning mock products:', mockProducts.length);\n            return mockProducts;\n        }\n        const stored = localStorage.getItem(this.getStorageKey('products'));\n        const products = stored ? JSON.parse(stored) : mockProducts;\n        console.log('Client side - loaded products:', products.length);\n        console.log('Product IDs:', products.map((p)=>p.id));\n        return products;\n    }\n    static getCategories() {\n        if (true) return mockCategories;\n        const stored = localStorage.getItem(this.getStorageKey('categories'));\n        return stored ? JSON.parse(stored) : mockCategories;\n    }\n    static getSchools() {\n        if (true) return mockSchools;\n        const stored = localStorage.getItem(this.getStorageKey('schools'));\n        return stored ? JSON.parse(stored) : mockSchools;\n    }\n    static getOrders() {\n        if (true) return mockOrders;\n        const stored = localStorage.getItem(this.getStorageKey('orders'));\n        return stored ? JSON.parse(stored) : mockOrders;\n    }\n    static savePages(pages) {\n        if (false) {}\n    }\n    static saveMenuItems(items) {\n        if (false) {}\n    }\n    static saveProducts(products) {\n        if (false) {}\n    }\n    static saveCategories(categories) {\n        if (false) {}\n    }\n    static saveSchools(schools) {\n        if (false) {}\n    }\n    static saveOrders(orders) {\n        if (false) {}\n    }\n    static generateId() {\n        return Date.now().toString() + Math.random().toString(36).substring(2, 11);\n    }\n    // مسح جميع البيانات المحفوظة (للاختبار)\n    static clearAllData() {\n        if (false) {}\n    }\n    static generateOrderNumber() {\n        const date = new Date();\n        const year = date.getFullYear().toString().slice(-2);\n        const month = (date.getMonth() + 1).toString().padStart(2, '0');\n        const day = date.getDate().toString().padStart(2, '0');\n        const orders = this.getOrders();\n        const todayOrders = orders.filter((order)=>order.created_at.startsWith(`${date.getFullYear()}-${month}-${day}`));\n        const orderCount = (todayOrders.length + 1).toString().padStart(3, '0');\n        return `GT-${year}${month}${day}-${orderCount}`;\n    }\n    // إدارة نماذج الذكاء الاصطناعي\n    static getAIModels() {\n        if (true) return this.defaultAIModels;\n        const stored = localStorage.getItem('mockAIModels');\n        if (stored) {\n            return JSON.parse(stored);\n        }\n        return this.defaultAIModels;\n    }\n    static saveAIModels(models) {\n        if (false) {}\n    }\n    static getModelActivities() {\n        if (true) return this.defaultModelActivities;\n        const stored = localStorage.getItem('mockModelActivities');\n        if (stored) {\n            return JSON.parse(stored);\n        }\n        return this.defaultModelActivities;\n    }\n    static saveModelActivities(activities) {\n        if (false) {}\n    }\n    // إدارة قوالب الصفحات\n    static getPageTemplates() {\n        if (true) return this.defaultPageTemplates;\n        const stored = localStorage.getItem('mockPageTemplates');\n        if (stored) {\n            return JSON.parse(stored);\n        }\n        return this.defaultPageTemplates;\n    }\n    static savePageTemplates(templates) {\n        if (false) {}\n    }\n    static getPageProjects() {\n        if (true) return this.defaultPageProjects;\n        const stored = localStorage.getItem('mockPageProjects');\n        if (stored) {\n            return JSON.parse(stored);\n        }\n        return this.defaultPageProjects;\n    }\n    static savePageProjects(projects) {\n        if (false) {}\n    }\n    static getComponentLibrary() {\n        if (true) return this.defaultComponentLibrary;\n        const stored = localStorage.getItem('mockComponentLibrary');\n        if (stored) {\n            return JSON.parse(stored);\n        }\n        return this.defaultComponentLibrary;\n    }\n    static saveComponentLibrary(components) {\n        if (false) {}\n    }\n    static{\n        // البيانات الافتراضية لنماذج الذكاء الاصطناعي (فارغة للبداية)\n        this.defaultAIModels = [];\n    }\n    static{\n        this.defaultModelActivities = [];\n    }\n    static{\n        // البيانات الافتراضية لقوالب الصفحات\n        this.defaultPageTemplates = [\n            {\n                id: 'template-landing-1',\n                name: 'Landing Page - Modern',\n                nameAr: 'صفحة هبوط - عصرية',\n                nameEn: 'Landing Page - Modern',\n                nameFr: 'Page d\\'atterrissage - Moderne',\n                description: 'قالب صفحة هبوط عصرية مع تصميم جذاب',\n                category: 'landing',\n                components: [\n                    {\n                        id: 'hero-1',\n                        type: 'hero',\n                        name: 'Hero Section',\n                        props: {\n                            content: 'مرحباً بكم في منصة أزياء التخرج',\n                            style: {\n                                backgroundColor: '#1F2937',\n                                color: '#FFFFFF'\n                            }\n                        },\n                        position: {\n                            x: 0,\n                            y: 0\n                        },\n                        size: {\n                            width: '100%',\n                            height: '500px'\n                        },\n                        isVisible: true\n                    }\n                ],\n                preview: '/images/templates/landing-modern.jpg',\n                thumbnail: '/images/templates/landing-modern-thumb.jpg',\n                isAIGenerated: false,\n                isPremium: false,\n                tags: [\n                    'landing',\n                    'modern',\n                    'business'\n                ],\n                createdAt: '2024-01-01T00:00:00Z',\n                updatedAt: '2024-01-01T00:00:00Z',\n                usageCount: 45,\n                rating: 4.8,\n                metadata: {\n                    colors: [\n                        '#1F2937',\n                        '#FFFFFF',\n                        '#3B82F6'\n                    ],\n                    fonts: [\n                        'Inter',\n                        'Cairo'\n                    ],\n                    layout: 'single-page',\n                    responsive: true\n                }\n            }\n        ];\n    }\n    static{\n        // البيانات الافتراضية لمشاريع الصفحات\n        this.defaultPageProjects = [\n            {\n                id: 'project-1',\n                name: 'موقع أزياء التخرج الرئيسي',\n                description: 'الموقع الرئيسي لعرض منتجات أزياء التخرج',\n                components: [],\n                templateId: 'template-landing-1',\n                generationMode: 'template',\n                settings: {\n                    title: 'أزياء التخرج - منصة مغربية متخصصة',\n                    description: 'أول منصة مغربية لتأجير وبيع أزياء التخرج',\n                    keywords: [\n                        'أزياء التخرج',\n                        'تأجير',\n                        'المغرب'\n                    ],\n                    language: 'ar',\n                    direction: 'rtl'\n                },\n                isPublished: false,\n                createdAt: '2024-01-15T00:00:00Z',\n                updatedAt: '2024-01-20T10:30:00Z',\n                createdBy: 'admin-1',\n                version: 1\n            }\n        ];\n    }\n    static{\n        // البيانات الافتراضية لمكتبة المكونات\n        this.defaultComponentLibrary = [\n            {\n                id: 'comp-hero',\n                name: 'Hero Section',\n                nameAr: 'قسم البطل',\n                type: 'hero',\n                category: 'layout',\n                description: 'قسم رئيسي جذاب في أعلى الصفحة',\n                icon: 'Layout',\n                preview: '/images/components/hero-preview.jpg',\n                defaultProps: {\n                    content: 'عنوان رئيسي جذاب',\n                    style: {\n                        backgroundColor: '#1F2937',\n                        color: '#FFFFFF'\n                    }\n                },\n                defaultSize: {\n                    width: '100%',\n                    height: '500px'\n                },\n                isCustom: false,\n                isPremium: false,\n                tags: [\n                    'layout',\n                    'header',\n                    'hero'\n                ],\n                usageCount: 156\n            },\n            {\n                id: 'comp-button',\n                name: 'Button',\n                nameAr: 'زر',\n                type: 'button',\n                category: 'interactive',\n                description: 'زر تفاعلي قابل للتخصيص',\n                icon: 'MousePointer',\n                preview: '/images/components/button-preview.jpg',\n                defaultProps: {\n                    content: 'انقر هنا',\n                    style: {\n                        backgroundColor: '#3B82F6',\n                        color: '#FFFFFF'\n                    }\n                },\n                defaultSize: {\n                    width: '120px',\n                    height: '40px'\n                },\n                isCustom: false,\n                isPremium: false,\n                tags: [\n                    'interactive',\n                    'button',\n                    'cta'\n                ],\n                usageCount: 234\n            }\n        ];\n    }\n    // إدارة مزودي الذكاء الاصطناعي\n    static getAIProviders() {\n        if (true) return [];\n        const stored = localStorage.getItem(this.getStorageKey('aiProviders'));\n        return stored ? JSON.parse(stored) : [];\n    }\n    static addAIProvider(provider) {\n        if (true) return provider;\n        const providers = this.getAIProviders();\n        providers.push(provider);\n        localStorage.setItem(this.getStorageKey('aiProviders'), JSON.stringify(providers));\n        return provider;\n    }\n    static updateAIProvider(id, updatedProvider) {\n        if (true) return null;\n        const providers = this.getAIProviders();\n        const index = providers.findIndex((p)=>p.id === id);\n        if (index === -1) return null;\n        providers[index] = {\n            ...providers[index],\n            ...updatedProvider\n        };\n        localStorage.setItem(this.getStorageKey('aiProviders'), JSON.stringify(providers));\n        return providers[index];\n    }\n    static deleteAIProvider(id) {\n        if (true) return false;\n        const providers = this.getAIProviders();\n        const filteredProviders = providers.filter((p)=>p.id !== id);\n        if (filteredProviders.length === providers.length) return false;\n        localStorage.setItem(this.getStorageKey('aiProviders'), JSON.stringify(filteredProviders));\n        return true;\n    }\n    static getAIProviderById(id) {\n        if (true) return null;\n        const providers = this.getAIProviders();\n        return providers.find((p)=>p.id === id) || null;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL21vY2tEYXRhLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBQSxpQ0FBaUM7QUFpS2pDLHVCQUF1QjtBQUNoQixNQUFNQSxZQUF3QjtJQUNuQztRQUNFQyxJQUFJO1FBQ0pDLE1BQU07UUFDTkMsY0FBYztRQUNkQyxXQUFXO1FBQ1hDLGdCQUFnQjtRQUNoQkMsWUFBWTtRQUNaQyxZQUFZO1FBQ1pDLFVBQVU7WUFDUkMsV0FBVztRQUNiO1FBQ0FDLGNBQWM7WUFDWjtnQkFDRVQsSUFBSTtnQkFDSlUsU0FBUztnQkFDVEMsVUFBVTtnQkFDVkMsT0FBTztnQkFDUEMsU0FBUztnQkFDVEMsa0JBQWtCO2dCQUNsQkMsZUFBZTtZQUNqQjtZQUNBO2dCQUNFZixJQUFJO2dCQUNKVSxTQUFTO2dCQUNUQyxVQUFVO2dCQUNWQyxPQUFPO2dCQUNQQyxTQUFTO2dCQUNUQyxrQkFBa0I7Z0JBQ2xCQyxlQUFlO1lBQ2pCO1NBQ0Q7SUFDSDtJQUNBO1FBQ0VmLElBQUk7UUFDSkMsTUFBTTtRQUNOQyxjQUFjO1FBQ2RDLFdBQVc7UUFDWEUsWUFBWTtRQUNaQyxZQUFZO1FBQ1pDLFVBQVU7WUFDUkMsV0FBVztRQUNiO1FBQ0FDLGNBQWM7WUFDWjtnQkFDRVQsSUFBSTtnQkFDSlUsU0FBUztnQkFDVEMsVUFBVTtnQkFDVkMsT0FBTztnQkFDUEMsU0FBUztnQkFDVEMsa0JBQWtCO2dCQUNsQkMsZUFBZTtZQUNqQjtTQUNEO0lBQ0g7SUFDQTtRQUNFZixJQUFJO1FBQ0pDLE1BQU07UUFDTkMsY0FBYztRQUNkQyxXQUFXO1FBQ1hFLFlBQVk7UUFDWkMsWUFBWTtRQUNaQyxVQUFVO1lBQ1JDLFdBQVc7UUFDYjtRQUNBQyxjQUFjO1lBQ1o7Z0JBQ0VULElBQUk7Z0JBQ0pVLFNBQVM7Z0JBQ1RDLFVBQVU7Z0JBQ1ZDLE9BQU87Z0JBQ1BDLFNBQVM7Z0JBQ1RDLGtCQUFrQjtnQkFDbEJDLGVBQWU7WUFDakI7U0FDRDtJQUNIO0NBQ0Q7QUFFRCx1QkFBdUI7QUFDaEIsTUFBTUMsZ0JBQWdDO0lBQzNDO1FBQ0VoQixJQUFJO1FBQ0ppQixVQUFVO1FBQ1ZDLFVBQVU7UUFDVkMsVUFBVTtRQUNWbEIsTUFBTTtRQUNObUIsTUFBTTtRQUNOQyxhQUFhO1FBQ2JDLFdBQVc7UUFDWEMsYUFBYTtRQUNiQyxjQUFjO1FBQ2RuQixZQUFZO1FBQ1pDLFlBQVk7SUFDZDtJQUNBO1FBQ0VOLElBQUk7UUFDSmlCLFVBQVU7UUFDVkMsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZsQixNQUFNO1FBQ05tQixNQUFNO1FBQ05DLGFBQWE7UUFDYkMsV0FBVztRQUNYQyxhQUFhO1FBQ2JDLGNBQWM7UUFDZG5CLFlBQVk7UUFDWkMsWUFBWTtJQUNkO0lBQ0E7UUFDRU4sSUFBSTtRQUNKaUIsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZDLFVBQVU7UUFDVmxCLE1BQU07UUFDTm1CLE1BQU07UUFDTkMsYUFBYTtRQUNiQyxXQUFXO1FBQ1hDLGFBQWE7UUFDYkMsY0FBYztRQUNkbkIsWUFBWTtRQUNaQyxZQUFZO0lBQ2Q7SUFDQTtRQUNFTixJQUFJO1FBQ0ppQixVQUFVO1FBQ1ZDLFVBQVU7UUFDVkMsVUFBVTtRQUNWbEIsTUFBTTtRQUNObUIsTUFBTTtRQUNOQyxhQUFhO1FBQ2JDLFdBQVc7UUFDWEMsYUFBYTtRQUNiQyxjQUFjO1FBQ2RuQixZQUFZO1FBQ1pDLFlBQVk7SUFDZDtJQUNBO1FBQ0VOLElBQUk7UUFDSmlCLFVBQVU7UUFDVkMsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZsQixNQUFNO1FBQ053QixXQUFXO1FBQ1hMLE1BQU07UUFDTkMsYUFBYTtRQUNiQyxXQUFXO1FBQ1hDLGFBQWE7UUFDYkMsY0FBYztRQUNkbkIsWUFBWTtRQUNaQyxZQUFZO0lBQ2Q7SUFDQTtRQUNFTixJQUFJO1FBQ0ppQixVQUFVO1FBQ1ZDLFVBQVU7UUFDVkMsVUFBVTtRQUNWbEIsTUFBTTtRQUNOd0IsV0FBVztRQUNYTCxNQUFNO1FBQ05DLGFBQWE7UUFDYkMsV0FBVztRQUNYQyxhQUFhO1FBQ2JDLGNBQWM7UUFDZG5CLFlBQVk7UUFDWkMsWUFBWTtJQUNkO0lBRUE7UUFDRU4sSUFBSTtRQUNKaUIsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZDLFVBQVU7UUFDVmxCLE1BQU07UUFDTm1CLE1BQU07UUFDTkMsYUFBYTtRQUNiQyxXQUFXO1FBQ1hDLGFBQWE7UUFDYkMsY0FBYztRQUNkbkIsWUFBWTtRQUNaQyxZQUFZO0lBQ2Q7Q0FDRDtBQUVELHNCQUFzQjtBQUNmLE1BQU1vQixpQkFBaUM7SUFDNUM7UUFDRTFCLElBQUk7UUFDSjJCLFNBQVM7UUFDVEMsU0FBUztRQUNUQyxTQUFTO1FBQ1Q1QixNQUFNO1FBQ05tQixNQUFNO1FBQ05VLGFBQWE7UUFDYlIsV0FBVztRQUNYRCxhQUFhO1FBQ2JoQixZQUFZO1FBQ1pDLFlBQVk7SUFDZDtJQUNBO1FBQ0VOLElBQUk7UUFDSjJCLFNBQVM7UUFDVEMsU0FBUztRQUNUQyxTQUFTO1FBQ1Q1QixNQUFNO1FBQ05tQixNQUFNO1FBQ05VLGFBQWE7UUFDYlIsV0FBVztRQUNYRCxhQUFhO1FBQ2JoQixZQUFZO1FBQ1pDLFlBQVk7SUFDZDtJQUNBO1FBQ0VOLElBQUk7UUFDSjJCLFNBQVM7UUFDVEMsU0FBUztRQUNUQyxTQUFTO1FBQ1Q1QixNQUFNO1FBQ05tQixNQUFNO1FBQ05VLGFBQWE7UUFDYlIsV0FBVztRQUNYRCxhQUFhO1FBQ2JoQixZQUFZO1FBQ1pDLFlBQVk7SUFDZDtJQUNBO1FBQ0VOLElBQUk7UUFDSjJCLFNBQVM7UUFDVEMsU0FBUztRQUNUQyxTQUFTO1FBQ1Q1QixNQUFNO1FBQ05tQixNQUFNO1FBQ05VLGFBQWE7UUFDYlIsV0FBVztRQUNYRCxhQUFhO1FBQ2JoQixZQUFZO1FBQ1pDLFlBQVk7SUFDZDtJQUNBO1FBQ0VOLElBQUk7UUFDSjJCLFNBQVM7UUFDVEMsU0FBUztRQUNUQyxTQUFTO1FBQ1Q1QixNQUFNO1FBQ05tQixNQUFNO1FBQ05VLGFBQWE7UUFDYlIsV0FBVztRQUNYRCxhQUFhO1FBQ2JoQixZQUFZO1FBQ1pDLFlBQVk7SUFDZDtDQUNEO0FBRUQsd0JBQXdCO0FBQ2pCLE1BQU15QixlQUE4QjtJQUN6QztRQUNFL0IsSUFBSTtRQUNKZ0MsTUFBTTtRQUNORixhQUFhO1FBQ2JHLFVBQVU7UUFDVkMsT0FBTztRQUNQQyxjQUFjO1FBQ2RDLFFBQVE7WUFBQztZQUFRO1lBQWE7U0FBVztRQUN6Q0MsT0FBTztZQUFDO1lBQUs7WUFBSztZQUFLO1lBQU07U0FBTTtRQUNuQ0MsUUFBUTtZQUFDO1lBQXVDO1NBQXNDO1FBQ3RGQyxnQkFBZ0I7UUFDaEJDLGNBQWM7UUFDZHRDLGNBQWM7UUFDZEcsWUFBWTtRQUNaQyxZQUFZO1FBQ1ptQyxRQUFRO1FBQ1JDLGVBQWU7UUFDZkMsVUFBVTtZQUFDO1lBQWtCO1lBQWU7U0FBb0I7UUFDaEVDLGdCQUFnQjtZQUNkQyxVQUFVO1lBQ1ZDLFFBQVE7WUFDUkMsTUFBTTtRQUNSO0lBQ0Y7SUFDQTtRQUNFL0MsSUFBSTtRQUNKZ0MsTUFBTTtRQUNORixhQUFhO1FBQ2JHLFVBQVU7UUFDVkMsT0FBTztRQUNQQyxjQUFjO1FBQ2RDLFFBQVE7WUFBQztZQUFRO1NBQVk7UUFDN0JDLE9BQU87WUFBQztTQUFXO1FBQ25CQyxRQUFRO1lBQUM7U0FBeUM7UUFDbERDLGdCQUFnQjtRQUNoQkMsY0FBYztRQUNkdEMsY0FBYztRQUNkRyxZQUFZO1FBQ1pDLFlBQVk7UUFDWm1DLFFBQVE7UUFDUkMsZUFBZTtRQUNmQyxVQUFVO1lBQUM7WUFBMEI7WUFBZTtTQUFlO1FBQ25FQyxnQkFBZ0I7WUFDZEMsVUFBVTtZQUNWRyxjQUFjO1lBQ2RDLFlBQVk7UUFDZDtJQUNGO0lBQ0E7UUFDRWpELElBQUk7UUFDSmdDLE1BQU07UUFDTkYsYUFBYTtRQUNiRyxVQUFVO1FBQ1ZDLE9BQU87UUFDUEMsY0FBYztRQUNkQyxRQUFRO1lBQUM7WUFBZ0I7WUFBZTtTQUFlO1FBQ3ZEQyxPQUFPO1lBQUM7U0FBVztRQUNuQkMsUUFBUTtZQUFDO1lBQTRDO1NBQTJDO1FBQ2hHQyxnQkFBZ0I7UUFDaEJDLGNBQWM7UUFDZHRDLGNBQWM7UUFDZEcsWUFBWTtRQUNaQyxZQUFZO1FBQ1ptQyxRQUFRO1FBQ1JDLGVBQWU7UUFDZkMsVUFBVTtZQUFDO1lBQWM7WUFBYztTQUFhO1FBQ3BEQyxnQkFBZ0I7WUFDZEMsVUFBVTtZQUNWSyxZQUFZO1lBQ1pDLFFBQVE7UUFDVjtJQUNGO0lBQ0E7UUFDRW5ELElBQUk7UUFDSmdDLE1BQU07UUFDTkYsYUFBYTtRQUNiRyxVQUFVO1FBQ1ZDLE9BQU87UUFDUEMsY0FBYztRQUNkQyxRQUFRO1lBQUM7WUFBUTtZQUFPO1lBQVE7U0FBTztRQUN2Q0MsT0FBTztZQUFDO1NBQVc7UUFDbkJDLFFBQVE7WUFBQztTQUFxQztRQUM5Q0MsZ0JBQWdCO1FBQ2hCQyxjQUFjO1FBQ2R0QyxjQUFjO1FBQ2RHLFlBQVk7UUFDWkMsWUFBWTtRQUNabUMsUUFBUTtRQUNSQyxlQUFlO1FBQ2ZDLFVBQVU7WUFBQztZQUFxQjtZQUFlO1NBQWM7UUFDN0RDLGdCQUFnQjtZQUNkQyxVQUFVO1lBQ1ZNLFFBQVE7WUFDUkMsWUFBWTtRQUNkO0lBQ0Y7SUFDQTtRQUNFcEQsSUFBSTtRQUNKZ0MsTUFBTTtRQUNORixhQUFhO1FBQ2JHLFVBQVU7UUFDVkMsT0FBTztRQUNQQyxjQUFjO1FBQ2RDLFFBQVE7WUFBQztZQUFnQjtTQUFjO1FBQ3ZDQyxPQUFPO1lBQUM7WUFBSztZQUFLO1NBQUs7UUFDdkJDLFFBQVE7WUFBQztZQUF5QztTQUF3QztRQUMxRkMsZ0JBQWdCO1FBQ2hCQyxjQUFjO1FBQ2R0QyxjQUFjO1FBQ2RHLFlBQVk7UUFDWkMsWUFBWTtRQUNabUMsUUFBUTtRQUNSQyxlQUFlO1FBQ2ZDLFVBQVU7WUFBQztZQUFzQjtZQUFjO1NBQWtCO1FBQ2pFQyxnQkFBZ0I7WUFDZEMsVUFBVTtZQUNWUSxRQUFRO1lBQ1JDLGdCQUFnQjtRQUNsQjtJQUNGO0NBQ0Q7QUFFRCx1QkFBdUI7QUFDaEIsTUFBTUMsY0FBNEI7SUFDdkM7UUFDRXZELElBQUk7UUFDSndELFVBQVU7UUFDVnhCLE1BQU07UUFDTkosU0FBUztRQUNUQyxTQUFTO1FBQ1Q0QixTQUFTO1FBQ1RDLE1BQU07UUFDTkMsT0FBTztRQUNQQyxPQUFPO1FBQ1BDLFNBQVM7UUFDVEMsVUFBVTtRQUNWQyxpQkFBaUI7UUFDakJDLGVBQWU7UUFDZjFDLFdBQVc7UUFDWDJDLFVBQVU7WUFDUkMsOEJBQThCO1lBQzlCQyxZQUFZO1lBQ1pDLHFCQUFxQjtRQUN2QjtRQUNBL0QsWUFBWTtRQUNaQyxZQUFZO0lBQ2Q7SUFDQTtRQUNFTixJQUFJO1FBQ0p3RCxVQUFVO1FBQ1Z4QixNQUFNO1FBQ05KLFNBQVM7UUFDVEMsU0FBUztRQUNUNEIsU0FBUztRQUNUQyxNQUFNO1FBQ05DLE9BQU87UUFDUEMsT0FBTztRQUNQQyxTQUFTO1FBQ1RDLFVBQVU7UUFDVkMsaUJBQWlCO1FBQ2pCQyxlQUFlO1FBQ2YxQyxXQUFXO1FBQ1gyQyxVQUFVO1lBQ1JDLDhCQUE4QjtZQUM5QkMsWUFBWTtZQUNaQyxxQkFBcUI7UUFDdkI7UUFDQS9ELFlBQVk7UUFDWkMsWUFBWTtJQUNkO0lBQ0E7UUFDRU4sSUFBSTtRQUNKd0QsVUFBVTtRQUNWeEIsTUFBTTtRQUNOSixTQUFTO1FBQ1RDLFNBQVM7UUFDVDRCLFNBQVM7UUFDVEMsTUFBTTtRQUNOQyxPQUFPO1FBQ1BDLE9BQU87UUFDUEMsU0FBUztRQUNUQyxVQUFVO1FBQ1ZDLGlCQUFpQjtRQUNqQkMsZUFBZTtRQUNmMUMsV0FBVztRQUNYMkMsVUFBVTtZQUNSQyw4QkFBOEI7WUFDOUJDLFlBQVk7WUFDWkMscUJBQXFCO1FBQ3ZCO1FBQ0EvRCxZQUFZO1FBQ1pDLFlBQVk7SUFDZDtJQUNBO1FBQ0VOLElBQUk7UUFDSndELFVBQVU7UUFDVnhCLE1BQU07UUFDTkosU0FBUztRQUNUQyxTQUFTO1FBQ1Q0QixTQUFTO1FBQ1RDLE1BQU07UUFDTkMsT0FBTztRQUNQQyxPQUFPO1FBQ1BDLFNBQVM7UUFDVEMsVUFBVTtRQUNWQyxpQkFBaUI7UUFDakJDLGVBQWU7UUFDZjFDLFdBQVc7UUFDWDJDLFVBQVU7WUFDUkMsOEJBQThCO1lBQzlCQyxZQUFZO1lBQ1pDLHFCQUFxQjtRQUN2QjtRQUNBL0QsWUFBWTtRQUNaQyxZQUFZO0lBQ2Q7SUFDQTtRQUNFTixJQUFJO1FBQ0p3RCxVQUFVO1FBQ1Z4QixNQUFNO1FBQ05KLFNBQVM7UUFDVEMsU0FBUztRQUNUNEIsU0FBUztRQUNUQyxNQUFNO1FBQ05DLE9BQU87UUFDUEMsT0FBTztRQUNQQyxTQUFTO1FBQ1RFLGlCQUFpQjtRQUNqQkMsZUFBZTtRQUNmMUMsV0FBVztRQUNYMkMsVUFBVTtZQUNSQyw4QkFBOEI7WUFDOUJDLFlBQVk7WUFDWkMscUJBQXFCO1FBQ3ZCO1FBQ0EvRCxZQUFZO1FBQ1pDLFlBQVk7SUFDZDtDQUNEO0FBRUQsdUJBQXVCO0FBQ2hCLE1BQU0rRCxhQUEwQjtJQUNyQztRQUNFckUsSUFBSTtRQUNKc0UsY0FBYztRQUNkQyxhQUFhO1FBQ2JDLGVBQWU7UUFDZkMsZ0JBQWdCO1FBQ2hCQyxnQkFBZ0I7UUFDaEJDLFFBQVE7UUFDUkMsT0FBTztZQUNMO2dCQUNFNUUsSUFBSTtnQkFDSjZFLFVBQVU7Z0JBQ1ZDLFlBQVk7Z0JBQ1pDLGNBQWM7Z0JBQ2RDLGVBQWU7Z0JBQ2YvQyxVQUFVO2dCQUNWZ0QsVUFBVTtnQkFDVkMsWUFBWTtnQkFDWkMsYUFBYTtnQkFDYkMsZ0JBQWdCO29CQUNkQyxPQUFPO29CQUNQQyxNQUFNO29CQUNOcEMsWUFBWTtnQkFDZDtZQUNGO1lBQ0E7Z0JBQ0VsRCxJQUFJO2dCQUNKNkUsVUFBVTtnQkFDVkMsWUFBWTtnQkFDWkMsY0FBYztnQkFDZEMsZUFBZTtnQkFDZi9DLFVBQVU7Z0JBQ1ZnRCxVQUFVO2dCQUNWQyxZQUFZO2dCQUNaQyxhQUFhO2dCQUNiQyxnQkFBZ0I7b0JBQ2RDLE9BQU87b0JBQ1BDLE1BQU07Z0JBQ1I7WUFDRjtTQUNEO1FBQ0RDLFVBQVU7UUFDVkMsS0FBSztRQUNMQyxlQUFlO1FBQ2ZDLE9BQU87UUFDUEMsZ0JBQWdCO1FBQ2hCQyxnQkFBZ0I7UUFDaEJDLGtCQUFrQjtZQUNoQkMsUUFBUTtZQUNScEMsTUFBTTtZQUNOcUMsT0FBTztZQUNQQyxhQUFhO1lBQ2JDLFNBQVM7UUFDWDtRQUNBQyxpQkFBaUI7UUFDakJDLE9BQU87UUFDUDlGLFlBQVk7UUFDWkMsWUFBWTtRQUNaOEYsZUFBZTtRQUNmQyxXQUFXO1FBQ1hDLGFBQWE7SUFDZjtJQUNBO1FBQ0V0RyxJQUFJO1FBQ0pzRSxjQUFjO1FBQ2RDLGFBQWE7UUFDYkMsZUFBZTtRQUNmQyxnQkFBZ0I7UUFDaEJDLGdCQUFnQjtRQUNoQkMsUUFBUTtRQUNSQyxPQUFPO1lBQ0w7Z0JBQ0U1RSxJQUFJO2dCQUNKNkUsVUFBVTtnQkFDVkMsWUFBWTtnQkFDWkMsY0FBYztnQkFDZEMsZUFBZTtnQkFDZi9DLFVBQVU7Z0JBQ1ZnRCxVQUFVO2dCQUNWQyxZQUFZO2dCQUNaQyxhQUFhO2dCQUNiQyxnQkFBZ0I7b0JBQ2RDLE9BQU87b0JBQ1BDLE1BQU07b0JBQ05wQyxZQUFZO2dCQUNkO1lBQ0Y7U0FDRDtRQUNEcUMsVUFBVTtRQUNWQyxLQUFLO1FBQ0xDLGVBQWU7UUFDZkMsT0FBTztRQUNQQyxnQkFBZ0I7UUFDaEJDLGdCQUFnQjtRQUNoQkMsa0JBQWtCO1lBQ2hCQyxRQUFRO1lBQ1JwQyxNQUFNO1lBQ05xQyxPQUFPO1lBQ1BDLGFBQWE7WUFDYkMsU0FBUztRQUNYO1FBQ0FDLGlCQUFpQjtRQUNqQjdGLFlBQVk7UUFDWkMsWUFBWTtRQUNaOEYsZUFBZTtRQUNmQyxXQUFXO1FBQ1hDLGFBQWE7SUFDZjtJQUNBO1FBQ0V0RyxJQUFJO1FBQ0pzRSxjQUFjO1FBQ2RDLGFBQWE7UUFDYkMsZUFBZTtRQUNmQyxnQkFBZ0I7UUFDaEJDLGdCQUFnQjtRQUNoQkMsUUFBUTtRQUNSQyxPQUFPO1lBQ0w7Z0JBQ0U1RSxJQUFJO2dCQUNKNkUsVUFBVTtnQkFDVkMsWUFBWTtnQkFDWkMsY0FBYztnQkFDZEMsZUFBZTtnQkFDZi9DLFVBQVU7Z0JBQ1ZnRCxVQUFVO2dCQUNWQyxZQUFZO2dCQUNaQyxhQUFhO2dCQUNiQyxnQkFBZ0I7b0JBQ2RDLE9BQU87b0JBQ1BDLE1BQU07Z0JBQ1I7WUFDRjtZQUNBO2dCQUNFdEYsSUFBSTtnQkFDSjZFLFVBQVU7Z0JBQ1ZDLFlBQVk7Z0JBQ1pDLGNBQWM7Z0JBQ2RDLGVBQWU7Z0JBQ2YvQyxVQUFVO2dCQUNWZ0QsVUFBVTtnQkFDVkMsWUFBWTtnQkFDWkMsYUFBYTtnQkFDYkMsZ0JBQWdCO29CQUNkQyxPQUFPO29CQUNQbkMsWUFBWTtnQkFDZDtZQUNGO1NBQ0Q7UUFDRHFDLFVBQVU7UUFDVkMsS0FBSztRQUNMQyxlQUFlO1FBQ2ZDLE9BQU87UUFDUEMsZ0JBQWdCO1FBQ2hCRSxrQkFBa0I7WUFDaEJDLFFBQVE7WUFDUnBDLE1BQU07WUFDTnFDLE9BQU87WUFDUEMsYUFBYTtZQUNiQyxTQUFTO1FBQ1g7UUFDQTVGLFlBQVk7UUFDWkMsWUFBWTtRQUNaK0YsV0FBVztRQUNYQyxhQUFhO0lBQ2Y7Q0FDRDtBQUVELHNDQUFzQztBQUMvQixNQUFNQztJQUNYLE9BQWVDLGNBQWNDLElBQThGLEVBQVU7UUFDbkksT0FBTyxDQUFDLFNBQVMsRUFBRUEsTUFBTTtJQUMzQjtJQUVBLE9BQU9DLFdBQXVCO1FBQzVCLElBQUksSUFBNkIsRUFBRSxPQUFPM0c7UUFFMUMsTUFBTTRHLFNBQVNDLGFBQWFDLE9BQU8sQ0FBQyxJQUFJLENBQUNMLGFBQWEsQ0FBQztRQUN2RCxPQUFPRyxTQUFTRyxLQUFLQyxLQUFLLENBQUNKLFVBQVU1RztJQUN2QztJQUVBLE9BQU9pSCxlQUErQjtRQUNwQyxJQUFJLElBQTZCLEVBQUUsT0FBT2hHO1FBRTFDLE1BQU0yRixTQUFTQyxhQUFhQyxPQUFPLENBQUMsSUFBSSxDQUFDTCxhQUFhLENBQUM7UUFDdkQsT0FBT0csU0FBU0csS0FBS0MsS0FBSyxDQUFDSixVQUFVM0Y7SUFDdkM7SUFFQSxPQUFPaUcsY0FBNkI7UUFDbEMsSUFBSSxJQUE2QixFQUFFO1lBQ2pDQyxRQUFRQyxHQUFHLENBQUMsMENBQTBDcEYsYUFBYW9CLE1BQU07WUFDekUsT0FBT3BCO1FBQ1Q7UUFFQSxNQUFNNEUsU0FBU0MsYUFBYUMsT0FBTyxDQUFDLElBQUksQ0FBQ0wsYUFBYSxDQUFDO1FBQ3ZELE1BQU1ZLFdBQVdULFNBQVNHLEtBQUtDLEtBQUssQ0FBQ0osVUFBVTVFO1FBQy9DbUYsUUFBUUMsR0FBRyxDQUFDLGtDQUFrQ0MsU0FBU2pFLE1BQU07UUFDN0QrRCxRQUFRQyxHQUFHLENBQUMsZ0JBQWdCQyxTQUFTQyxHQUFHLENBQUMsQ0FBQ0MsSUFBbUJBLEVBQUV0SCxFQUFFO1FBQ2pFLE9BQU9vSDtJQUNUO0lBRUEsT0FBT0csZ0JBQWdDO1FBQ3JDLElBQUksSUFBNkIsRUFBRSxPQUFPN0Y7UUFFMUMsTUFBTWlGLFNBQVNDLGFBQWFDLE9BQU8sQ0FBQyxJQUFJLENBQUNMLGFBQWEsQ0FBQztRQUN2RCxPQUFPRyxTQUFTRyxLQUFLQyxLQUFLLENBQUNKLFVBQVVqRjtJQUN2QztJQUVBLE9BQU84RixhQUEyQjtRQUNoQyxJQUFJLElBQTZCLEVBQUUsT0FBT2pFO1FBRTFDLE1BQU1vRCxTQUFTQyxhQUFhQyxPQUFPLENBQUMsSUFBSSxDQUFDTCxhQUFhLENBQUM7UUFDdkQsT0FBT0csU0FBU0csS0FBS0MsS0FBSyxDQUFDSixVQUFVcEQ7SUFDdkM7SUFFQSxPQUFPa0UsWUFBeUI7UUFDOUIsSUFBSSxJQUE2QixFQUFFLE9BQU9wRDtRQUUxQyxNQUFNc0MsU0FBU0MsYUFBYUMsT0FBTyxDQUFDLElBQUksQ0FBQ0wsYUFBYSxDQUFDO1FBQ3ZELE9BQU9HLFNBQVNHLEtBQUtDLEtBQUssQ0FBQ0osVUFBVXRDO0lBQ3ZDO0lBRUEsT0FBT3FELFVBQVVDLEtBQWlCLEVBQVE7UUFDeEMsSUFBSSxLQUE2QixFQUFFLEVBRWxDO0lBQ0g7SUFFQSxPQUFPRyxjQUFjbEQsS0FBcUIsRUFBUTtRQUNoRCxJQUFJLEtBQTZCLEVBQUUsRUFFbEM7SUFDSDtJQUVBLE9BQU9tRCxhQUFhWCxRQUF1QixFQUFRO1FBQ2pELElBQUksS0FBNkIsRUFBRSxFQVFsQztJQUNIO0lBRUEsT0FBT2MsZUFBZUMsVUFBMEIsRUFBUTtRQUN0RCxJQUFJLEtBQTZCLEVBQUUsRUFFbEM7SUFDSDtJQUVBLE9BQU9DLFlBQVlDLE9BQXFCLEVBQVE7UUFDOUMsSUFBSSxLQUE2QixFQUFFLEVBRWxDO0lBQ0g7SUFFQSxPQUFPQyxXQUFXQyxNQUFtQixFQUFRO1FBQzNDLElBQUksS0FBNkIsRUFBRSxFQUVsQztJQUNIO0lBRUEsT0FBT0MsYUFBcUI7UUFDMUIsT0FBT0MsS0FBS0MsR0FBRyxHQUFHQyxRQUFRLEtBQUtDLEtBQUtDLE1BQU0sR0FBR0YsUUFBUSxDQUFDLElBQUlHLFNBQVMsQ0FBQyxHQUFHO0lBQ3pFO0lBRUEsd0NBQXdDO0lBQ3hDLE9BQU9DLGVBQXFCO1FBQzFCLElBQUksS0FBNkIsRUFBRSxFQU9sQztJQUNIO0lBRUEsT0FBT0Usc0JBQThCO1FBQ25DLE1BQU1DLE9BQU8sSUFBSVQ7UUFDakIsTUFBTVUsT0FBT0QsS0FBS0UsV0FBVyxHQUFHVCxRQUFRLEdBQUdVLEtBQUssQ0FBQyxDQUFDO1FBQ2xELE1BQU1DLFFBQVEsQ0FBQ0osS0FBS0ssUUFBUSxLQUFLLEdBQUdaLFFBQVEsR0FBR2EsUUFBUSxDQUFDLEdBQUc7UUFDM0QsTUFBTUMsTUFBTVAsS0FBS1EsT0FBTyxHQUFHZixRQUFRLEdBQUdhLFFBQVEsQ0FBQyxHQUFHO1FBQ2xELE1BQU1qQixTQUFTLElBQUksQ0FBQ2QsU0FBUztRQUM3QixNQUFNa0MsY0FBY3BCLE9BQU9xQixNQUFNLENBQUNDLENBQUFBLFFBQ2hDQSxNQUFNeEosVUFBVSxDQUFDeUosVUFBVSxDQUFDLEdBQUdaLEtBQUtFLFdBQVcsR0FBRyxDQUFDLEVBQUVFLE1BQU0sQ0FBQyxFQUFFRyxLQUFLO1FBRXJFLE1BQU1NLGFBQWEsQ0FBQ0osWUFBWXhHLE1BQU0sR0FBRyxHQUFHd0YsUUFBUSxHQUFHYSxRQUFRLENBQUMsR0FBRztRQUNuRSxPQUFPLENBQUMsR0FBRyxFQUFFTCxPQUFPRyxRQUFRRyxJQUFJLENBQUMsRUFBRU0sWUFBWTtJQUNqRDtJQUVBLCtCQUErQjtJQUMvQixPQUFPQyxjQUF5QjtRQUM5QixJQUFJLElBQTZCLEVBQUUsT0FBTyxJQUFJLENBQUNDLGVBQWU7UUFFOUQsTUFBTXRELFNBQVNDLGFBQWFDLE9BQU8sQ0FBQztRQUNwQyxJQUFJRixRQUFRO1lBQ1YsT0FBT0csS0FBS0MsS0FBSyxDQUFDSjtRQUNwQjtRQUNBLE9BQU8sSUFBSSxDQUFDc0QsZUFBZTtJQUM3QjtJQUVBLE9BQU9DLGFBQWFDLE1BQWlCLEVBQVE7UUFDM0MsSUFBSSxLQUE2QixFQUFFLEVBRWxDO0lBQ0g7SUFFQSxPQUFPQyxxQkFBc0M7UUFDM0MsSUFBSSxJQUE2QixFQUFFLE9BQU8sSUFBSSxDQUFDQyxzQkFBc0I7UUFFckUsTUFBTTFELFNBQVNDLGFBQWFDLE9BQU8sQ0FBQztRQUNwQyxJQUFJRixRQUFRO1lBQ1YsT0FBT0csS0FBS0MsS0FBSyxDQUFDSjtRQUNwQjtRQUNBLE9BQU8sSUFBSSxDQUFDMEQsc0JBQXNCO0lBQ3BDO0lBRUEsT0FBT0Msb0JBQW9CQyxVQUEyQixFQUFRO1FBQzVELElBQUksS0FBNkIsRUFBRSxFQUVsQztJQUNIO0lBRUEsc0JBQXNCO0lBQ3RCLE9BQU9DLG1CQUFtQztRQUN4QyxJQUFJLElBQTZCLEVBQUUsT0FBTyxJQUFJLENBQUNDLG9CQUFvQjtRQUVuRSxNQUFNOUQsU0FBU0MsYUFBYUMsT0FBTyxDQUFDO1FBQ3BDLElBQUlGLFFBQVE7WUFDVixPQUFPRyxLQUFLQyxLQUFLLENBQUNKO1FBQ3BCO1FBQ0EsT0FBTyxJQUFJLENBQUM4RCxvQkFBb0I7SUFDbEM7SUFFQSxPQUFPQyxrQkFBa0JDLFNBQXlCLEVBQVE7UUFDeEQsSUFBSSxLQUE2QixFQUFFLEVBRWxDO0lBQ0g7SUFFQSxPQUFPQyxrQkFBaUM7UUFDdEMsSUFBSSxJQUE2QixFQUFFLE9BQU8sSUFBSSxDQUFDQyxtQkFBbUI7UUFFbEUsTUFBTWxFLFNBQVNDLGFBQWFDLE9BQU8sQ0FBQztRQUNwQyxJQUFJRixRQUFRO1lBQ1YsT0FBT0csS0FBS0MsS0FBSyxDQUFDSjtRQUNwQjtRQUNBLE9BQU8sSUFBSSxDQUFDa0UsbUJBQW1CO0lBQ2pDO0lBRUEsT0FBT0MsaUJBQWlCQyxRQUF1QixFQUFRO1FBQ3JELElBQUksS0FBNkIsRUFBRSxFQUVsQztJQUNIO0lBRUEsT0FBT0Msc0JBQThDO1FBQ25ELElBQUksSUFBNkIsRUFBRSxPQUFPLElBQUksQ0FBQ0MsdUJBQXVCO1FBRXRFLE1BQU10RSxTQUFTQyxhQUFhQyxPQUFPLENBQUM7UUFDcEMsSUFBSUYsUUFBUTtZQUNWLE9BQU9HLEtBQUtDLEtBQUssQ0FBQ0o7UUFDcEI7UUFDQSxPQUFPLElBQUksQ0FBQ3NFLHVCQUF1QjtJQUNyQztJQUVBLE9BQU9DLHFCQUFxQkMsVUFBa0MsRUFBUTtRQUNwRSxJQUFJLEtBQTZCLEVBQUUsRUFFbEM7SUFDSDs7UUFFQSw4REFBOEQ7YUFDdkRsQixrQkFBNkIsRUFBRTs7O2FBRS9CSSx5QkFBMEMsRUFBRTs7O1FBRW5ELHFDQUFxQzthQUM5QkksdUJBQXVDO1lBQzVDO2dCQUNFekssSUFBSTtnQkFDSmdDLE1BQU07Z0JBQ05vSixRQUFRO2dCQUNSQyxRQUFRO2dCQUNSQyxRQUFRO2dCQUNSeEosYUFBYTtnQkFDYkcsVUFBVTtnQkFDVmtKLFlBQVk7b0JBQ1Y7d0JBQ0VuTCxJQUFJO3dCQUNKeUcsTUFBTTt3QkFDTnpFLE1BQU07d0JBQ051SixPQUFPOzRCQUNMMUssU0FBUzs0QkFDVDJLLE9BQU87Z0NBQUVDLGlCQUFpQjtnQ0FBV3BHLE9BQU87NEJBQVU7d0JBQ3hEO3dCQUNBcUcsVUFBVTs0QkFBRUMsR0FBRzs0QkFBR0MsR0FBRzt3QkFBRTt3QkFDdkJ0RyxNQUFNOzRCQUFFdUcsT0FBTzs0QkFBUUMsUUFBUTt3QkFBUTt3QkFDdkNDLFdBQVc7b0JBQ2I7aUJBQ0Q7Z0JBQ0RDLFNBQVM7Z0JBQ1RDLFdBQVc7Z0JBQ1hDLGVBQWU7Z0JBQ2ZDLFdBQVc7Z0JBQ1hDLE1BQU07b0JBQUM7b0JBQVc7b0JBQVU7aUJBQVc7Z0JBQ3ZDQyxXQUFXO2dCQUNYQyxXQUFXO2dCQUNYQyxZQUFZO2dCQUNaOUosUUFBUTtnQkFDUitKLFVBQVU7b0JBQ1JwSyxRQUFRO3dCQUFDO3dCQUFXO3dCQUFXO3FCQUFVO29CQUN6Q3FLLE9BQU87d0JBQUM7d0JBQVM7cUJBQVE7b0JBQ3pCQyxRQUFRO29CQUNSQyxZQUFZO2dCQUNkO1lBQ0Y7U0FDRDs7O1FBRUQsc0NBQXNDO2FBQy9COUIsc0JBQXFDO1lBQzFDO2dCQUNFN0ssSUFBSTtnQkFDSmdDLE1BQU07Z0JBQ05GLGFBQWE7Z0JBQ2JxSixZQUFZLEVBQUU7Z0JBQ2R5QixZQUFZO2dCQUNaQyxnQkFBZ0I7Z0JBQ2hCNUksVUFBVTtvQkFDUnJELE9BQU87b0JBQ1BrQixhQUFhO29CQUNiZ0wsVUFBVTt3QkFBQzt3QkFBZ0I7d0JBQVM7cUJBQVM7b0JBQzdDbk0sVUFBVTtvQkFDVm9NLFdBQVc7Z0JBQ2I7Z0JBQ0FDLGFBQWE7Z0JBQ2JYLFdBQVc7Z0JBQ1hDLFdBQVc7Z0JBQ1hXLFdBQVc7Z0JBQ1hDLFNBQVM7WUFDWDtTQUNEOzs7UUFFRCxzQ0FBc0M7YUFDL0JqQywwQkFBa0Q7WUFDdkQ7Z0JBQ0VqTCxJQUFJO2dCQUNKZ0MsTUFBTTtnQkFDTm9KLFFBQVE7Z0JBQ1IzRSxNQUFNO2dCQUNOeEUsVUFBVTtnQkFDVkgsYUFBYTtnQkFDYlYsTUFBTTtnQkFDTjRLLFNBQVM7Z0JBQ1RtQixjQUFjO29CQUNadE0sU0FBUztvQkFDVDJLLE9BQU87d0JBQUVDLGlCQUFpQjt3QkFBV3BHLE9BQU87b0JBQVU7Z0JBQ3hEO2dCQUNBK0gsYUFBYTtvQkFBRXZCLE9BQU87b0JBQVFDLFFBQVE7Z0JBQVE7Z0JBQzlDdUIsVUFBVTtnQkFDVmxCLFdBQVc7Z0JBQ1hDLE1BQU07b0JBQUM7b0JBQVU7b0JBQVU7aUJBQU87Z0JBQ2xDRyxZQUFZO1lBQ2Q7WUFDQTtnQkFDRXZNLElBQUk7Z0JBQ0pnQyxNQUFNO2dCQUNOb0osUUFBUTtnQkFDUjNFLE1BQU07Z0JBQ054RSxVQUFVO2dCQUNWSCxhQUFhO2dCQUNiVixNQUFNO2dCQUNONEssU0FBUztnQkFDVG1CLGNBQWM7b0JBQ1p0TSxTQUFTO29CQUNUMkssT0FBTzt3QkFBRUMsaUJBQWlCO3dCQUFXcEcsT0FBTztvQkFBVTtnQkFDeEQ7Z0JBQ0ErSCxhQUFhO29CQUFFdkIsT0FBTztvQkFBU0MsUUFBUTtnQkFBTztnQkFDOUN1QixVQUFVO2dCQUNWbEIsV0FBVztnQkFDWEMsTUFBTTtvQkFBQztvQkFBZTtvQkFBVTtpQkFBTTtnQkFDdENHLFlBQVk7WUFDZDtTQUNEOztJQUVELCtCQUErQjtJQUMvQixPQUFPZSxpQkFBbUM7UUFDeEMsSUFBSSxJQUE2QixFQUFFLE9BQU8sRUFBRTtRQUU1QyxNQUFNM0csU0FBU0MsYUFBYUMsT0FBTyxDQUFDLElBQUksQ0FBQ0wsYUFBYSxDQUFDO1FBQ3ZELE9BQU9HLFNBQVNHLEtBQUtDLEtBQUssQ0FBQ0osVUFBVSxFQUFFO0lBQ3pDO0lBRUEsT0FBTzRHLGNBQWNDLFFBQXdCLEVBQWtCO1FBQzdELElBQUksSUFBNkIsRUFBRSxPQUFPQTtRQUUxQyxNQUFNQyxZQUFZLElBQUksQ0FBQ0gsY0FBYztRQUNyQ0csVUFBVUMsSUFBSSxDQUFDRjtRQUNmNUcsYUFBYWdCLE9BQU8sQ0FBQyxJQUFJLENBQUNwQixhQUFhLENBQUMsZ0JBQWdCTSxLQUFLZSxTQUFTLENBQUM0RjtRQUN2RSxPQUFPRDtJQUNUO0lBRUEsT0FBT0csaUJBQWlCM04sRUFBVSxFQUFFNE4sZUFBd0MsRUFBeUI7UUFDbkcsSUFBSSxJQUE2QixFQUFFLE9BQU87UUFFMUMsTUFBTUgsWUFBWSxJQUFJLENBQUNILGNBQWM7UUFDckMsTUFBTU8sUUFBUUosVUFBVUssU0FBUyxDQUFDeEcsQ0FBQUEsSUFBS0EsRUFBRXRILEVBQUUsS0FBS0E7UUFFaEQsSUFBSTZOLFVBQVUsQ0FBQyxHQUFHLE9BQU87UUFFekJKLFNBQVMsQ0FBQ0ksTUFBTSxHQUFHO1lBQUUsR0FBR0osU0FBUyxDQUFDSSxNQUFNO1lBQUUsR0FBR0QsZUFBZTtRQUFDO1FBQzdEaEgsYUFBYWdCLE9BQU8sQ0FBQyxJQUFJLENBQUNwQixhQUFhLENBQUMsZ0JBQWdCTSxLQUFLZSxTQUFTLENBQUM0RjtRQUN2RSxPQUFPQSxTQUFTLENBQUNJLE1BQU07SUFDekI7SUFFQSxPQUFPRSxpQkFBaUIvTixFQUFVLEVBQVc7UUFDM0MsSUFBSSxJQUE2QixFQUFFLE9BQU87UUFFMUMsTUFBTXlOLFlBQVksSUFBSSxDQUFDSCxjQUFjO1FBQ3JDLE1BQU1VLG9CQUFvQlAsVUFBVTdELE1BQU0sQ0FBQ3RDLENBQUFBLElBQUtBLEVBQUV0SCxFQUFFLEtBQUtBO1FBRXpELElBQUlnTyxrQkFBa0I3SyxNQUFNLEtBQUtzSyxVQUFVdEssTUFBTSxFQUFFLE9BQU87UUFFMUR5RCxhQUFhZ0IsT0FBTyxDQUFDLElBQUksQ0FBQ3BCLGFBQWEsQ0FBQyxnQkFBZ0JNLEtBQUtlLFNBQVMsQ0FBQ21HO1FBQ3ZFLE9BQU87SUFDVDtJQUVBLE9BQU9DLGtCQUFrQmpPLEVBQVUsRUFBeUI7UUFDMUQsSUFBSSxJQUE2QixFQUFFLE9BQU87UUFFMUMsTUFBTXlOLFlBQVksSUFBSSxDQUFDSCxjQUFjO1FBQ3JDLE9BQU9HLFVBQVVTLElBQUksQ0FBQzVHLENBQUFBLElBQUtBLEVBQUV0SCxFQUFFLEtBQUtBLE9BQU87SUFDN0M7QUFDRiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxmYWlzc1xcRGVza3RvcFxcR3JhZHVhdGlvbiBUb3FzXFxmcm9udGVuZFxcc3JjXFxsaWJcXG1vY2tEYXRhLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vINio2YrYp9mG2KfYqiDZiNmH2YXZitipINmE2YTYqti32YjZitixINmI2KfZhNin2K7Yqtio2KfYsVxuaW1wb3J0IHsgQUlNb2RlbCwgQUlTdWJNb2RlbCwgTW9kZWxBY3Rpdml0eSB9IGZyb20gJ0AvdHlwZXMvYWktbW9kZWxzJ1xuaW1wb3J0IHsgUGFnZVRlbXBsYXRlLCBQYWdlUHJvamVjdCwgQ29tcG9uZW50TGlicmFyeUl0ZW0sIFBhZ2VDb21wb25lbnQgfSBmcm9tICdAL3R5cGVzL3BhZ2UtYnVpbGRlcidcblxuZXhwb3J0IGludGVyZmFjZSBNb2NrUGFnZSB7XG4gIGlkOiBzdHJpbmdcbiAgc2x1Zzogc3RyaW5nXG4gIGlzX3B1Ymxpc2hlZDogYm9vbGVhblxuICBhdXRob3JfaWQ6IHN0cmluZ1xuICBmZWF0dXJlZF9pbWFnZT86IHN0cmluZ1xuICBjcmVhdGVkX2F0OiBzdHJpbmdcbiAgdXBkYXRlZF9hdDogc3RyaW5nXG4gIHBhZ2VfY29udGVudDogTW9ja1BhZ2VDb250ZW50W11cbiAgcHJvZmlsZXM/OiB7XG4gICAgZnVsbF9uYW1lOiBzdHJpbmdcbiAgfVxufVxuXG5leHBvcnQgaW50ZXJmYWNlIE1vY2tQYWdlQ29udGVudCB7XG4gIGlkOiBzdHJpbmdcbiAgcGFnZV9pZDogc3RyaW5nXG4gIGxhbmd1YWdlOiAnYXInIHwgJ2VuJyB8ICdmcidcbiAgdGl0bGU6IHN0cmluZ1xuICBjb250ZW50OiBzdHJpbmdcbiAgbWV0YV9kZXNjcmlwdGlvbj86IHN0cmluZ1xuICBtZXRhX2tleXdvcmRzPzogc3RyaW5nXG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgTW9ja01lbnVJdGVtIHtcbiAgaWQ6IHN0cmluZ1xuICB0aXRsZV9hcjogc3RyaW5nXG4gIHRpdGxlX2VuPzogc3RyaW5nXG4gIHRpdGxlX2ZyPzogc3RyaW5nXG4gIHNsdWc6IHN0cmluZ1xuICBpY29uPzogc3RyaW5nXG4gIHBhcmVudF9pZD86IHN0cmluZ1xuICBvcmRlcl9pbmRleDogbnVtYmVyXG4gIGlzX2FjdGl2ZTogYm9vbGVhblxuICB0YXJnZXRfdHlwZTogJ2ludGVybmFsJyB8ICdleHRlcm5hbCcgfCAncGFnZSdcbiAgdGFyZ2V0X3ZhbHVlOiBzdHJpbmdcbiAgY3JlYXRlZF9hdDogc3RyaW5nXG4gIHVwZGF0ZWRfYXQ6IHN0cmluZ1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIE1vY2tBSVByb3ZpZGVyIHtcbiAgaWQ6IHN0cmluZ1xuICBwcm92aWRlcjogc3RyaW5nXG4gIHByb3ZpZGVyTmFtZTogc3RyaW5nXG4gIGJhc2VVcmw6IHN0cmluZ1xuICBhcGlLZXk6IHN0cmluZ1xuICBtb2RlbHM6IHN0cmluZ1tdXG4gIGRlc2NyaXB0aW9uPzogc3RyaW5nXG4gIHN0YXR1czogJ2FjdGl2ZScgfCAnaW5hY3RpdmUnXG4gIGNyZWF0ZWRBdDogc3RyaW5nXG4gIHVwZGF0ZWRBdDogc3RyaW5nXG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgTW9ja0NhdGVnb3J5IHtcbiAgaWQ6IHN0cmluZ1xuICBuYW1lX2FyOiBzdHJpbmdcbiAgbmFtZV9lbj86IHN0cmluZ1xuICBuYW1lX2ZyPzogc3RyaW5nXG4gIHNsdWc6IHN0cmluZ1xuICBpY29uPzogc3RyaW5nXG4gIGRlc2NyaXB0aW9uPzogc3RyaW5nXG4gIGlzX2FjdGl2ZTogYm9vbGVhblxuICBvcmRlcl9pbmRleDogbnVtYmVyXG4gIGNyZWF0ZWRfYXQ6IHN0cmluZ1xuICB1cGRhdGVkX2F0OiBzdHJpbmdcbn1cblxuZXhwb3J0IGludGVyZmFjZSBNb2NrUHJvZHVjdCB7XG4gIGlkOiBzdHJpbmdcbiAgbmFtZTogc3RyaW5nXG4gIGRlc2NyaXB0aW9uOiBzdHJpbmdcbiAgY2F0ZWdvcnk6IHN0cmluZyAvLyDYqti62YrZitixINmF2YYgdW5pb24gdHlwZSDYpdmE2Ykgc3RyaW5nINmE2YTZhdix2YjZhtipXG4gIHByaWNlOiBudW1iZXJcbiAgcmVudGFsX3ByaWNlPzogbnVtYmVyXG4gIGNvbG9yczogc3RyaW5nW11cbiAgc2l6ZXM6IHN0cmluZ1tdXG4gIGltYWdlczogc3RyaW5nW11cbiAgc3RvY2tfcXVhbnRpdHk6IG51bWJlclxuICBpc19hdmFpbGFibGU6IGJvb2xlYW5cbiAgaXNfcHVibGlzaGVkOiBib29sZWFuIC8vINit2YLZhCDYrNiv2YrYryDZhNmE2KrYrdmD2YUg2YHZiiDYp9mE2YbYtNixXG4gIGNyZWF0ZWRfYXQ6IHN0cmluZ1xuICB1cGRhdGVkX2F0OiBzdHJpbmdcbiAgcmF0aW5nPzogbnVtYmVyXG4gIHJldmlld3NfY291bnQ/OiBudW1iZXJcbiAgZmVhdHVyZXM/OiBzdHJpbmdbXVxuICBzcGVjaWZpY2F0aW9ucz86IFJlY29yZDxzdHJpbmcsIGFueT5cbn1cblxuZXhwb3J0IGludGVyZmFjZSBNb2NrU2Nob29sIHtcbiAgaWQ6IHN0cmluZ1xuICBhZG1pbl9pZD86IHN0cmluZ1xuICBuYW1lOiBzdHJpbmdcbiAgbmFtZV9lbj86IHN0cmluZ1xuICBuYW1lX2ZyPzogc3RyaW5nXG4gIGFkZHJlc3M/OiBzdHJpbmdcbiAgY2l0eT86IHN0cmluZ1xuICBwaG9uZT86IHN0cmluZ1xuICBlbWFpbD86IHN0cmluZ1xuICB3ZWJzaXRlPzogc3RyaW5nXG4gIGxvZ29fdXJsPzogc3RyaW5nXG4gIGdyYWR1YXRpb25fZGF0ZT86IHN0cmluZ1xuICBzdHVkZW50X2NvdW50OiBudW1iZXJcbiAgaXNfYWN0aXZlOiBib29sZWFuXG4gIHNldHRpbmdzPzogUmVjb3JkPHN0cmluZywgYW55PlxuICBjcmVhdGVkX2F0OiBzdHJpbmdcbiAgdXBkYXRlZF9hdDogc3RyaW5nXG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgTW9ja09yZGVyIHtcbiAgaWQ6IHN0cmluZ1xuICBvcmRlcl9udW1iZXI6IHN0cmluZ1xuICBjdXN0b21lcl9pZDogc3RyaW5nXG4gIGN1c3RvbWVyX25hbWU6IHN0cmluZ1xuICBjdXN0b21lcl9lbWFpbDogc3RyaW5nXG4gIGN1c3RvbWVyX3Bob25lPzogc3RyaW5nXG4gIHN0YXR1czogJ3BlbmRpbmcnIHwgJ2NvbmZpcm1lZCcgfCAnaW5fcHJvZHVjdGlvbicgfCAnc2hpcHBlZCcgfCAnZGVsaXZlcmVkJyB8ICdjYW5jZWxsZWQnXG4gIGl0ZW1zOiBNb2NrT3JkZXJJdGVtW11cbiAgc3VidG90YWw6IG51bWJlclxuICB0YXg6IG51bWJlclxuICBzaGlwcGluZ19jb3N0OiBudW1iZXJcbiAgdG90YWw6IG51bWJlclxuICBwYXltZW50X3N0YXR1czogJ3BlbmRpbmcnIHwgJ3BhaWQnIHwgJ2ZhaWxlZCcgfCAncmVmdW5kZWQnXG4gIHBheW1lbnRfbWV0aG9kPzogc3RyaW5nXG4gIHNoaXBwaW5nX2FkZHJlc3M6IHtcbiAgICBzdHJlZXQ6IHN0cmluZ1xuICAgIGNpdHk6IHN0cmluZ1xuICAgIHN0YXRlOiBzdHJpbmdcbiAgICBwb3N0YWxfY29kZTogc3RyaW5nXG4gICAgY291bnRyeTogc3RyaW5nXG4gIH1cbiAgdHJhY2tpbmdfbnVtYmVyPzogc3RyaW5nXG4gIG5vdGVzPzogc3RyaW5nXG4gIGNyZWF0ZWRfYXQ6IHN0cmluZ1xuICB1cGRhdGVkX2F0OiBzdHJpbmdcbiAgZGVsaXZlcnlfZGF0ZT86IHN0cmluZ1xuICBzY2hvb2xfaWQ/OiBzdHJpbmdcbiAgc2Nob29sX25hbWU/OiBzdHJpbmdcbn1cblxuZXhwb3J0IGludGVyZmFjZSBNb2NrT3JkZXJJdGVtIHtcbiAgaWQ6IHN0cmluZ1xuICBvcmRlcl9pZDogc3RyaW5nXG4gIHByb2R1Y3RfaWQ6IHN0cmluZ1xuICBwcm9kdWN0X25hbWU6IHN0cmluZ1xuICBwcm9kdWN0X2ltYWdlOiBzdHJpbmdcbiAgY2F0ZWdvcnk6IHN0cmluZ1xuICBxdWFudGl0eTogbnVtYmVyXG4gIHVuaXRfcHJpY2U6IG51bWJlclxuICB0b3RhbF9wcmljZTogbnVtYmVyXG4gIGN1c3RvbWl6YXRpb25zPzoge1xuICAgIGNvbG9yPzogc3RyaW5nXG4gICAgc2l6ZT86IHN0cmluZ1xuICAgIGVtYnJvaWRlcnk/OiBzdHJpbmdcbiAgICBzcGVjaWFsX3JlcXVlc3RzPzogc3RyaW5nXG4gIH1cbn1cblxuLy8g2KjZitin2YbYp9iqINmI2YfZhdmK2Kkg2YTZhNi12YHYrdin2KpcbmV4cG9ydCBjb25zdCBtb2NrUGFnZXM6IE1vY2tQYWdlW10gPSBbXG4gIHtcbiAgICBpZDogJzEnLFxuICAgIHNsdWc6ICdhYm91dC11cycsXG4gICAgaXNfcHVibGlzaGVkOiB0cnVlLFxuICAgIGF1dGhvcl9pZDogJ2FkbWluLTEnLFxuICAgIGZlYXR1cmVkX2ltYWdlOiAnL2ltYWdlcy9hYm91dC1oZXJvLmpwZycsXG4gICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMTVUMTA6MDA6MDBaJyxcbiAgICB1cGRhdGVkX2F0OiAnMjAyNC0wMS0yMFQxNDozMDowMFonLFxuICAgIHByb2ZpbGVzOiB7XG4gICAgICBmdWxsX25hbWU6ICfZhdiv2YrYsSDYp9mE2YbYuNin2YUnXG4gICAgfSxcbiAgICBwYWdlX2NvbnRlbnQ6IFtcbiAgICAgIHtcbiAgICAgICAgaWQ6ICcxLWFyJyxcbiAgICAgICAgcGFnZV9pZDogJzEnLFxuICAgICAgICBsYW5ndWFnZTogJ2FyJyxcbiAgICAgICAgdGl0bGU6ICfZhdmGINmG2K3ZhicsXG4gICAgICAgIGNvbnRlbnQ6ICc8aDI+2YXYsdit2KjYp9mLINio2YPZhSDZgdmKINmF2YbYtdipINij2LLZitin2KEg2KfZhNiq2K7YsdisPC9oMj48cD7Zhtit2YYg2YXZhti12Kkg2YXYqtiu2LXYtdipINmB2Yog2KrYo9is2YrYsSDZiNio2YrYuSDYo9iy2YrYp9ihINin2YTYqtiu2LHYrCDYp9mE2YXYutix2KjZitipINin2YTYo9i12YrZhNipLiDZhtmH2K/ZgSDYpdmE2Ykg2KzYudmEINmK2YjZhSDYqtiu2LHYrNmD2YUg2YTYpyDZitmP2YbYs9mJINmF2YYg2K7ZhNin2YQg2KrZiNmB2YrYsSDYo9is2YXZhCDYp9mE2KPYstmK2KfYoSDYp9mE2KrZgtmE2YrYr9mK2KkuPC9wPjxwPtiq2KPYs9iz2Kog2YXZhti12KrZhtinINi52KfZhSAyMDI0INio2YfYr9mBINiu2K/ZhdipINin2YTYt9mE2KfYqCDZiNin2YTYt9in2YTYqNin2Kog2YHZiiDYrNmF2YrYuSDYo9mG2K3Yp9ihINin2YTZhdi62LHYqNiMINmI2YbZgdiq2K7YsSDYqNiq2YLYr9mK2YUg2K7Yr9mF2KfYqiDYudin2YTZitipINin2YTYrNmI2K/YqSDZiNij2LPYudin2LEg2YXZhtin2LPYqNipLjwvcD4nLFxuICAgICAgICBtZXRhX2Rlc2NyaXB0aW9uOiAn2KrYudix2YEg2LnZhNmJINmF2YbYtdipINij2LLZitin2KEg2KfZhNiq2K7YsdisINin2YTZhdi62LHYqNmK2KkgLSDYrtiv2YXYp9iqINiq2KPYrNmK2LEg2YjYqNmK2Lkg2KfZhNij2LLZitin2KEg2KfZhNiq2YLZhNmK2K/ZitipJyxcbiAgICAgICAgbWV0YV9rZXl3b3JkczogJ9ij2LLZitin2KEg2KfZhNiq2K7Ysdis2Iwg2KfZhNmF2LrYsdio2Iwg2KrYo9is2YrYsdiMINio2YrYudiMINiq2YLZhNmK2K/ZiidcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIGlkOiAnMS1lbicsXG4gICAgICAgIHBhZ2VfaWQ6ICcxJyxcbiAgICAgICAgbGFuZ3VhZ2U6ICdlbicsXG4gICAgICAgIHRpdGxlOiAnQWJvdXQgVXMnLFxuICAgICAgICBjb250ZW50OiAnPGgyPldlbGNvbWUgdG8gR3JhZHVhdGlvbiBBdHRpcmUgUGxhdGZvcm08L2gyPjxwPldlIGFyZSBhIHNwZWNpYWxpemVkIHBsYXRmb3JtIGZvciByZW50aW5nIGFuZCBzZWxsaW5nIGF1dGhlbnRpYyBNb3JvY2NhbiBncmFkdWF0aW9uIGF0dGlyZS4gV2UgYWltIHRvIG1ha2UgeW91ciBncmFkdWF0aW9uIGRheSB1bmZvcmdldHRhYmxlIGJ5IHByb3ZpZGluZyB0aGUgbW9zdCBiZWF1dGlmdWwgdHJhZGl0aW9uYWwgb3V0Zml0cy48L3A+PHA+T3VyIHBsYXRmb3JtIHdhcyBmb3VuZGVkIGluIDIwMjQgdG8gc2VydmUgc3R1ZGVudHMgdGhyb3VnaG91dCBNb3JvY2NvLCBhbmQgd2UgcHJpZGUgb3Vyc2VsdmVzIG9uIHByb3ZpZGluZyBoaWdoLXF1YWxpdHkgc2VydmljZXMgYXQgYWZmb3JkYWJsZSBwcmljZXMuPC9wPicsXG4gICAgICAgIG1ldGFfZGVzY3JpcHRpb246ICdMZWFybiBhYm91dCB0aGUgTW9yb2NjYW4gR3JhZHVhdGlvbiBBdHRpcmUgUGxhdGZvcm0gLSB0cmFkaXRpb25hbCBvdXRmaXQgcmVudGFsIGFuZCBzYWxlcyBzZXJ2aWNlcycsXG4gICAgICAgIG1ldGFfa2V5d29yZHM6ICdncmFkdWF0aW9uIGF0dGlyZSwgTW9yb2NjbywgcmVudGFsLCBzYWxlcywgdHJhZGl0aW9uYWwnXG4gICAgICB9XG4gICAgXVxuICB9LFxuICB7XG4gICAgaWQ6ICcyJyxcbiAgICBzbHVnOiAnc2VydmljZXMnLFxuICAgIGlzX3B1Ymxpc2hlZDogdHJ1ZSxcbiAgICBhdXRob3JfaWQ6ICdhZG1pbi0xJyxcbiAgICBjcmVhdGVkX2F0OiAnMjAyNC0wMS0xNlQwOTowMDowMFonLFxuICAgIHVwZGF0ZWRfYXQ6ICcyMDI0LTAxLTIyVDE2OjQ1OjAwWicsXG4gICAgcHJvZmlsZXM6IHtcbiAgICAgIGZ1bGxfbmFtZTogJ9mF2K/ZitixINin2YTZhti42KfZhSdcbiAgICB9LFxuICAgIHBhZ2VfY29udGVudDogW1xuICAgICAge1xuICAgICAgICBpZDogJzItYXInLFxuICAgICAgICBwYWdlX2lkOiAnMicsXG4gICAgICAgIGxhbmd1YWdlOiAnYXInLFxuICAgICAgICB0aXRsZTogJ9iu2K/Zhdin2KrZhtinJyxcbiAgICAgICAgY29udGVudDogJzxoMj7Yrtiv2YXYp9iq2YbYpyDYp9mE2YXYqtmF2YrYstipPC9oMj48aDM+2KrYo9is2YrYsSDYp9mE2KPYstmK2KfYoTwvaDM+PHA+2YbZiNmB2LEg2K7Yr9mF2Kkg2KrYo9is2YrYsSDYo9iy2YrYp9ihINin2YTYqtiu2LHYrCDZhNmB2KrYsdin2Kog2YXYsdmG2Kkg2YXYuSDYttmF2KfZhiDYp9mE2YbYuNin2YHYqSDZiNin2YTYrNmI2K/YqS48L3A+PGgzPtio2YrYuSDYp9mE2KPYstmK2KfYoTwvaDM+PHA+2KXZhdmD2KfZhtmK2Kkg2LTYsdin2KEg2KfZhNij2LLZitin2KEg2YTZhNin2K3YqtmB2KfYuCDYqNmH2Kcg2YPYsNmD2LHZiSDYrNmF2YrZhNipINmF2YYg2YrZiNmFINin2YTYqtiu2LHYrC48L3A+PGgzPtin2YTYqtiu2LXZiti1PC9oMz48cD7Yrtiv2YXYp9iqINiq2K7YtdmK2LUg2KfZhNij2LLZitin2KEg2K3Ys9ioINin2YTZhdmC2KfYs9in2Kog2YjYp9mE2KrZgdi22YrZhNin2Kog2KfZhNi02K7YtdmK2KkuPC9wPicsXG4gICAgICAgIG1ldGFfZGVzY3JpcHRpb246ICfYrtiv2YXYp9iqINmF2YbYtdipINij2LLZitin2KEg2KfZhNiq2K7YsdisIC0g2KrYo9is2YrYsSDZiNio2YrYuSDZiNiq2K7YtdmK2LUg2KfZhNij2LLZitin2KEg2KfZhNiq2YLZhNmK2K/ZitipINin2YTZhdi62LHYqNmK2KknLFxuICAgICAgICBtZXRhX2tleXdvcmRzOiAn2K7Yr9mF2KfYqtiMINiq2KPYrNmK2LHYjCDYqNmK2LnYjCDYqtiu2LXZiti12Iwg2KPYstmK2KfYoSDYp9mE2KrYrtix2KwnXG4gICAgICB9XG4gICAgXVxuICB9LFxuICB7XG4gICAgaWQ6ICczJyxcbiAgICBzbHVnOiAnY29udGFjdCcsXG4gICAgaXNfcHVibGlzaGVkOiB0cnVlLFxuICAgIGF1dGhvcl9pZDogJ2FkbWluLTEnLFxuICAgIGNyZWF0ZWRfYXQ6ICcyMDI0LTAxLTE3VDExOjAwOjAwWicsXG4gICAgdXBkYXRlZF9hdDogJzIwMjQtMDEtMTdUMTE6MDA6MDBaJyxcbiAgICBwcm9maWxlczoge1xuICAgICAgZnVsbF9uYW1lOiAn2YXYr9mK2LEg2KfZhNmG2LjYp9mFJ1xuICAgIH0sXG4gICAgcGFnZV9jb250ZW50OiBbXG4gICAgICB7XG4gICAgICAgIGlkOiAnMy1hcicsXG4gICAgICAgIHBhZ2VfaWQ6ICczJyxcbiAgICAgICAgbGFuZ3VhZ2U6ICdhcicsXG4gICAgICAgIHRpdGxlOiAn2KfYqti12YQg2KjZhtinJyxcbiAgICAgICAgY29udGVudDogJzxoMj7YqtmI2KfYtdmEINmF2LnZhtinPC9oMj48cD7Zhtit2YYg2YfZhtinINmE2K7Yr9mF2KrZg9mFINmB2Yog2KPZiiDZiNmC2KouINmK2YXZg9mG2YPZhSDYp9mE2KrZiNin2LXZhCDZhdi52YbYpyDYudio2LE6PC9wPjx1bD48bGk+2KfZhNmH2KfYqtmBOiArMjEyIDVYWC1YWFhYWFg8L2xpPjxsaT7Yp9mE2KjYsdmK2K8g2KfZhNil2YTZg9iq2LHZiNmG2Yo6IGluZm9AZ3JhZHVhdGlvbnRvcXMuY29tPC9saT48bGk+2KfZhNi52YbZiNin2YY6INin2YTYr9in2LEg2KfZhNio2YrYttin2KHYjCDYp9mE2YXYutix2Kg8L2xpPjwvdWw+JyxcbiAgICAgICAgbWV0YV9kZXNjcmlwdGlvbjogJ9iq2YjYp9i12YQg2YXYuSDZhdmG2LXYqSDYo9iy2YrYp9ihINin2YTYqtiu2LHYrCDYp9mE2YXYutix2KjZitipJyxcbiAgICAgICAgbWV0YV9rZXl3b3JkczogJ9in2KrYtdin2YTYjCDYqtmI2KfYtdmE2Iwg2K7Yr9mF2Kkg2KfZhNi52YXZhNin2KEnXG4gICAgICB9XG4gICAgXVxuICB9XG5dXG5cbi8vINio2YrYp9mG2KfYqiDZiNmH2YXZitipINmE2YTZgtmI2KfYptmFXG5leHBvcnQgY29uc3QgbW9ja01lbnVJdGVtczogTW9ja01lbnVJdGVtW10gPSBbXG4gIHtcbiAgICBpZDogJzEnLFxuICAgIHRpdGxlX2FyOiAn2KfZhNix2KbZitiz2YrYqScsXG4gICAgdGl0bGVfZW46ICdIb21lJyxcbiAgICB0aXRsZV9mcjogJ0FjY3VlaWwnLFxuICAgIHNsdWc6ICdob21lJyxcbiAgICBpY29uOiAnSG9tZScsXG4gICAgb3JkZXJfaW5kZXg6IDEsXG4gICAgaXNfYWN0aXZlOiB0cnVlLFxuICAgIHRhcmdldF90eXBlOiAnaW50ZXJuYWwnLFxuICAgIHRhcmdldF92YWx1ZTogJy8nLFxuICAgIGNyZWF0ZWRfYXQ6ICcyMDI0LTAxLTE1VDEwOjAwOjAwWicsXG4gICAgdXBkYXRlZF9hdDogJzIwMjQtMDEtMTVUMTA6MDA6MDBaJ1xuICB9LFxuICB7XG4gICAgaWQ6ICcyJyxcbiAgICB0aXRsZV9hcjogJ9mF2YYg2YbYrdmGJyxcbiAgICB0aXRsZV9lbjogJ0Fib3V0IFVzJyxcbiAgICB0aXRsZV9mcjogJ8OAIHByb3BvcycsXG4gICAgc2x1ZzogJ2Fib3V0JyxcbiAgICBpY29uOiAnSW5mbycsXG4gICAgb3JkZXJfaW5kZXg6IDIsXG4gICAgaXNfYWN0aXZlOiB0cnVlLFxuICAgIHRhcmdldF90eXBlOiAnaW50ZXJuYWwnLFxuICAgIHRhcmdldF92YWx1ZTogJy9hYm91dCcsXG4gICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMTVUMTA6MDU6MDBaJyxcbiAgICB1cGRhdGVkX2F0OiAnMjAyNC0wMS0xNVQxMDowNTowMFonXG4gIH0sXG4gIHtcbiAgICBpZDogJzMnLFxuICAgIHRpdGxlX2FyOiAn2K7Yr9mF2KfYqtmG2KcnLFxuICAgIHRpdGxlX2VuOiAnU2VydmljZXMnLFxuICAgIHRpdGxlX2ZyOiAnU2VydmljZXMnLFxuICAgIHNsdWc6ICdzZXJ2aWNlcycsXG4gICAgaWNvbjogJ1NldHRpbmdzJyxcbiAgICBvcmRlcl9pbmRleDogMyxcbiAgICBpc19hY3RpdmU6IHRydWUsXG4gICAgdGFyZ2V0X3R5cGU6ICdpbnRlcm5hbCcsXG4gICAgdGFyZ2V0X3ZhbHVlOiAnL3NlcnZpY2VzJyxcbiAgICBjcmVhdGVkX2F0OiAnMjAyNC0wMS0xNVQxMDoxMDowMFonLFxuICAgIHVwZGF0ZWRfYXQ6ICcyMDI0LTAxLTE1VDEwOjEwOjAwWidcbiAgfSxcbiAge1xuICAgIGlkOiAnNCcsXG4gICAgdGl0bGVfYXI6ICfYp9mE2YXZhtiq2KzYp9iqJyxcbiAgICB0aXRsZV9lbjogJ1Byb2R1Y3RzJyxcbiAgICB0aXRsZV9mcjogJ1Byb2R1aXRzJyxcbiAgICBzbHVnOiAncHJvZHVjdHMnLFxuICAgIGljb246ICdQYWNrYWdlJyxcbiAgICBvcmRlcl9pbmRleDogNCxcbiAgICBpc19hY3RpdmU6IHRydWUsXG4gICAgdGFyZ2V0X3R5cGU6ICdpbnRlcm5hbCcsXG4gICAgdGFyZ2V0X3ZhbHVlOiAnL2NhdGFsb2cnLFxuICAgIGNyZWF0ZWRfYXQ6ICcyMDI0LTAxLTE1VDEwOjE1OjAwWicsXG4gICAgdXBkYXRlZF9hdDogJzIwMjQtMDEtMTVUMTA6MTU6MDBaJ1xuICB9LFxuICB7XG4gICAgaWQ6ICc1JyxcbiAgICB0aXRsZV9hcjogJ9iq2KPYrNmK2LEg2KfZhNij2LLZitin2KEnLFxuICAgIHRpdGxlX2VuOiAnUmVudGFsJyxcbiAgICB0aXRsZV9mcjogJ0xvY2F0aW9uJyxcbiAgICBzbHVnOiAncmVudGFsJyxcbiAgICBwYXJlbnRfaWQ6ICc0JyxcbiAgICBpY29uOiAnQ2FsZW5kYXInLFxuICAgIG9yZGVyX2luZGV4OiAxLFxuICAgIGlzX2FjdGl2ZTogdHJ1ZSxcbiAgICB0YXJnZXRfdHlwZTogJ2ludGVybmFsJyxcbiAgICB0YXJnZXRfdmFsdWU6ICcvY2F0YWxvZz90eXBlPXJlbnRhbCcsXG4gICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMTVUMTA6MjA6MDBaJyxcbiAgICB1cGRhdGVkX2F0OiAnMjAyNC0wMS0xNVQxMDoyMDowMFonXG4gIH0sXG4gIHtcbiAgICBpZDogJzYnLFxuICAgIHRpdGxlX2FyOiAn2KjZiti5INin2YTYo9iy2YrYp9ihJyxcbiAgICB0aXRsZV9lbjogJ1NhbGVzJyxcbiAgICB0aXRsZV9mcjogJ1ZlbnRlJyxcbiAgICBzbHVnOiAnc2FsZXMnLFxuICAgIHBhcmVudF9pZDogJzQnLFxuICAgIGljb246ICdTaG9wcGluZ0NhcnQnLFxuICAgIG9yZGVyX2luZGV4OiAyLFxuICAgIGlzX2FjdGl2ZTogdHJ1ZSxcbiAgICB0YXJnZXRfdHlwZTogJ2ludGVybmFsJyxcbiAgICB0YXJnZXRfdmFsdWU6ICcvY2F0YWxvZz90eXBlPXNhbGUnLFxuICAgIGNyZWF0ZWRfYXQ6ICcyMDI0LTAxLTE1VDEwOjI1OjAwWicsXG4gICAgdXBkYXRlZF9hdDogJzIwMjQtMDEtMTVUMTA6MjU6MDBaJ1xuICB9LFxuXG4gIHtcbiAgICBpZDogJzgnLFxuICAgIHRpdGxlX2FyOiAn2KfYqti12YQg2KjZhtinJyxcbiAgICB0aXRsZV9lbjogJ0NvbnRhY3QnLFxuICAgIHRpdGxlX2ZyOiAnQ29udGFjdCcsXG4gICAgc2x1ZzogJ2NvbnRhY3QnLFxuICAgIGljb246ICdQaG9uZScsXG4gICAgb3JkZXJfaW5kZXg6IDYsXG4gICAgaXNfYWN0aXZlOiB0cnVlLFxuICAgIHRhcmdldF90eXBlOiAnaW50ZXJuYWwnLFxuICAgIHRhcmdldF92YWx1ZTogJy9jb250YWN0JyxcbiAgICBjcmVhdGVkX2F0OiAnMjAyNC0wMS0xNVQxMDozNTowMFonLFxuICAgIHVwZGF0ZWRfYXQ6ICcyMDI0LTAxLTE1VDEwOjM1OjAwWidcbiAgfVxuXVxuXG4vLyDYqNmK2KfZhtin2Kog2YjZh9mF2YrYqSDZhNmE2YHYptin2KpcbmV4cG9ydCBjb25zdCBtb2NrQ2F0ZWdvcmllczogTW9ja0NhdGVnb3J5W10gPSBbXG4gIHtcbiAgICBpZDogJzEnLFxuICAgIG5hbWVfYXI6ICfYo9ir2YjYp9ioINin2YTYqtiu2LHYrCcsXG4gICAgbmFtZV9lbjogJ0dyYWR1YXRpb24gR293bnMnLFxuICAgIG5hbWVfZnI6ICdSb2JlcyBkZSBHcmFkdWF0aW9uJyxcbiAgICBzbHVnOiAnZ293bicsXG4gICAgaWNvbjogJ/CfkZgnLFxuICAgIGRlc2NyaXB0aW9uOiAn2KPYq9mI2KfYqCDYp9mE2KrYrtix2Kwg2KfZhNij2YPYp9iv2YrZhdmK2Kkg2KfZhNiq2YLZhNmK2K/ZitipJyxcbiAgICBpc19hY3RpdmU6IHRydWUsXG4gICAgb3JkZXJfaW5kZXg6IDEsXG4gICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMTVUMTA6MDA6MDBaJyxcbiAgICB1cGRhdGVkX2F0OiAnMjAyNC0wMS0xNVQxMDowMDowMFonXG4gIH0sXG4gIHtcbiAgICBpZDogJzInLFxuICAgIG5hbWVfYXI6ICfZgtio2LnYp9iqINin2YTYqtiu2LHYrCcsXG4gICAgbmFtZV9lbjogJ0dyYWR1YXRpb24gQ2FwcycsXG4gICAgbmFtZV9mcjogJ0NoYXBlYXV4IGRlIEdyYWR1YXRpb24nLFxuICAgIHNsdWc6ICdjYXAnLFxuICAgIGljb246ICfwn46pJyxcbiAgICBkZXNjcmlwdGlvbjogJ9mC2KjYudin2Kog2KfZhNiq2K7YsdisINin2YTYo9mD2KfYr9mK2YXZitipJyxcbiAgICBpc19hY3RpdmU6IHRydWUsXG4gICAgb3JkZXJfaW5kZXg6IDIsXG4gICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMTVUMTA6MDU6MDBaJyxcbiAgICB1cGRhdGVkX2F0OiAnMjAyNC0wMS0xNVQxMDowNTowMFonXG4gIH0sXG4gIHtcbiAgICBpZDogJzMnLFxuICAgIG5hbWVfYXI6ICfYtNix2KfYqNin2Kog2KfZhNiq2K7YsdisJyxcbiAgICBuYW1lX2VuOiAnR3JhZHVhdGlvbiBUYXNzZWxzJyxcbiAgICBuYW1lX2ZyOiAnR2xhbmRzIGRlIEdyYWR1YXRpb24nLFxuICAgIHNsdWc6ICd0YXNzZWwnLFxuICAgIGljb246ICfwn4+377iPJyxcbiAgICBkZXNjcmlwdGlvbjogJ9i02LHYp9io2KfYqiDYp9mE2KrYrtix2Kwg2KfZhNmF2YTZiNmG2KknLFxuICAgIGlzX2FjdGl2ZTogdHJ1ZSxcbiAgICBvcmRlcl9pbmRleDogMyxcbiAgICBjcmVhdGVkX2F0OiAnMjAyNC0wMS0xNVQxMDoxMDowMFonLFxuICAgIHVwZGF0ZWRfYXQ6ICcyMDI0LTAxLTE1VDEwOjEwOjAwWidcbiAgfSxcbiAge1xuICAgIGlkOiAnNCcsXG4gICAgbmFtZV9hcjogJ9ij2YjYtNit2Kkg2KfZhNiq2K7YsdisJyxcbiAgICBuYW1lX2VuOiAnR3JhZHVhdGlvbiBTdG9sZXMnLFxuICAgIG5hbWVfZnI6ICfDiXRvbGVzIGRlIEdyYWR1YXRpb24nLFxuICAgIHNsdWc6ICdzdG9sZScsXG4gICAgaWNvbjogJ/Cfp6MnLFxuICAgIGRlc2NyaXB0aW9uOiAn2KPZiNi02K3YqSDYp9mE2KrYrtix2Kwg2KfZhNmF2YXZitiy2KknLFxuICAgIGlzX2FjdGl2ZTogdHJ1ZSxcbiAgICBvcmRlcl9pbmRleDogNCxcbiAgICBjcmVhdGVkX2F0OiAnMjAyNC0wMS0xNVQxMDoxNTowMFonLFxuICAgIHVwZGF0ZWRfYXQ6ICcyMDI0LTAxLTE1VDEwOjE1OjAwWidcbiAgfSxcbiAge1xuICAgIGlkOiAnNScsXG4gICAgbmFtZV9hcjogJ9in2YTZgtmE2KfZhtizINin2YTYo9mD2KfYr9mK2YXZitipJyxcbiAgICBuYW1lX2VuOiAnQWNhZGVtaWMgSG9vZHMnLFxuICAgIG5hbWVfZnI6ICdDYXB1Y2hlcyBBY2Fkw6ltaXF1ZXMnLFxuICAgIHNsdWc6ICdob29kJyxcbiAgICBpY29uOiAn8J+OkycsXG4gICAgZGVzY3JpcHRpb246ICfYp9mE2YLZhNin2YbYsyDYp9mE2KPZg9in2K/ZitmF2YrYqSDZhNmE2K/Ysdis2KfYqiDYp9mE2LnZhNmK2KcnLFxuICAgIGlzX2FjdGl2ZTogdHJ1ZSxcbiAgICBvcmRlcl9pbmRleDogNSxcbiAgICBjcmVhdGVkX2F0OiAnMjAyNC0wMS0xNVQxMDoyMDowMFonLFxuICAgIHVwZGF0ZWRfYXQ6ICcyMDI0LTAxLTE1VDEwOjIwOjAwWidcbiAgfVxuXVxuXG4vLyDYqNmK2KfZhtin2Kog2YjZh9mF2YrYqSDZhNmE2YXZhtiq2KzYp9iqXG5leHBvcnQgY29uc3QgbW9ja1Byb2R1Y3RzOiBNb2NrUHJvZHVjdFtdID0gW1xuICB7XG4gICAgaWQ6ICcxJyxcbiAgICBuYW1lOiAn2KvZiNioINin2YTYqtiu2LHYrCDYp9mE2YPZhNin2LPZitmD2YonLFxuICAgIGRlc2NyaXB0aW9uOiAn2KvZiNioINiq2K7YsdisINij2YbZitmCINmF2LXZhtmI2Lkg2YXZhiDYo9is2YjYryDYp9mE2K7Yp9mF2KfYqtiMINmF2YbYp9iz2Kgg2YTYrNmF2YrYuSDYp9mE2YXZhtin2LPYqNin2Kog2KfZhNij2YPYp9iv2YrZhdmK2KknLFxuICAgIGNhdGVnb3J5OiAnZ293bicsXG4gICAgcHJpY2U6IDI5OS45OSxcbiAgICByZW50YWxfcHJpY2U6IDk5Ljk5LFxuICAgIGNvbG9yczogWyfYo9iz2YjYrycsICfYo9iy2LHZgiDYr9in2YPZhicsICfYqNmI2LHYrNmI2YbYr9mKJ10sXG4gICAgc2l6ZXM6IFsnUycsICdNJywgJ0wnLCAnWEwnLCAnWFhMJ10sXG4gICAgaW1hZ2VzOiBbJy9pbWFnZXMvcHJvZHVjdHMvZ293bi1jbGFzc2ljLTEuanBnJywgJy9pbWFnZXMvcHJvZHVjdHMvZ293bi1jbGFzc2ljLTIuanBnJ10sXG4gICAgc3RvY2tfcXVhbnRpdHk6IDI1LFxuICAgIGlzX2F2YWlsYWJsZTogdHJ1ZSxcbiAgICBpc19wdWJsaXNoZWQ6IHRydWUsXG4gICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMTVUMTA6MDA6MDBaJyxcbiAgICB1cGRhdGVkX2F0OiAnMjAyNC0wMS0yMFQxNDozMDowMFonLFxuICAgIHJhdGluZzogNC44LFxuICAgIHJldmlld3NfY291bnQ6IDQyLFxuICAgIGZlYXR1cmVzOiBbJ9mF2YLYp9mI2YUg2YTZhNiq2KzYp9i52YrYrycsICfZgtin2KjZhCDZhNmE2LrYs9mK2YQnLCAn2K7Yp9mF2Kkg2LnYp9mE2YrYqSDYp9mE2KzZiNiv2KknXSxcbiAgICBzcGVjaWZpY2F0aW9uczoge1xuICAgICAgbWF0ZXJpYWw6ICfYqNmI2YTZitiz2KrYsSDYudin2YTZiiDYp9mE2KzZiNiv2KknLFxuICAgICAgd2VpZ2h0OiAnMC44INmD2YrZhNmIJyxcbiAgICAgIGNhcmU6ICfYutiz2YrZhCDYrNin2YEg2KPZiCDYutiz2YrZhCDYudin2K/ZiidcbiAgICB9XG4gIH0sXG4gIHtcbiAgICBpZDogJzInLFxuICAgIG5hbWU6ICfZgtio2LnYqSDYp9mE2KrYrtix2Kwg2KfZhNiq2YLZhNmK2K/ZitipJyxcbiAgICBkZXNjcmlwdGlvbjogJ9mC2KjYudipINiq2K7YsdisINiq2YLZhNmK2K/ZitipINmF2Lkg2LTYsdin2KjYqSDYsNmH2KjZitip2Iwg2LHZhdiyINin2YTYpdmG2KzYp9iyINin2YTYo9mD2KfYr9mK2YXZiicsXG4gICAgY2F0ZWdvcnk6ICdjYXAnLFxuICAgIHByaWNlOiA3OS45OSxcbiAgICByZW50YWxfcHJpY2U6IDI5Ljk5LFxuICAgIGNvbG9yczogWyfYo9iz2YjYrycsICfYo9iy2LHZgiDYr9in2YPZhiddLFxuICAgIHNpemVzOiBbJ09uZSBTaXplJ10sXG4gICAgaW1hZ2VzOiBbJy9pbWFnZXMvcHJvZHVjdHMvY2FwLXRyYWRpdGlvbmFsLTEuanBnJ10sXG4gICAgc3RvY2tfcXVhbnRpdHk6IDUwLFxuICAgIGlzX2F2YWlsYWJsZTogdHJ1ZSxcbiAgICBpc19wdWJsaXNoZWQ6IHRydWUsXG4gICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMTZUMDk6MDA6MDBaJyxcbiAgICB1cGRhdGVkX2F0OiAnMjAyNC0wMS0yMlQxNjo0NTowMFonLFxuICAgIHJhdGluZzogNC42LFxuICAgIHJldmlld3NfY291bnQ6IDI4LFxuICAgIGZlYXR1cmVzOiBbJ9mF2YLYp9izINmI2KfYrdivINmK2YbYp9iz2Kgg2KfZhNis2YXZiti5JywgJ9i02LHYp9io2Kkg2LDZh9io2YrYqScsICfYqti12YXZitmFINiq2YLZhNmK2K/ZiiddLFxuICAgIHNwZWNpZmljYXRpb25zOiB7XG4gICAgICBtYXRlcmlhbDogJ9mC2LfZhiDZhdiu2YTZiNi3JyxcbiAgICAgIHRhc3NlbF9jb2xvcjogJ9iw2YfYqNmKJyxcbiAgICAgIGFkanVzdGFibGU6ICfZhti52YUnXG4gICAgfVxuICB9LFxuICB7XG4gICAgaWQ6ICczJyxcbiAgICBuYW1lOiAn2YjYtNin2K0g2KfZhNiq2K7YsdisINin2YTZhdi32LHYsicsXG4gICAgZGVzY3JpcHRpb246ICfZiNi02KfYrSDYqtiu2LHYrCDZhdi32LHYsiDYqNiu2YrZiNi3INiw2YfYqNmK2KnYjCDZiti22YrZgSDZhNmF2LPYqSDZhdmGINin2YTYo9mG2KfZgtipINmI2KfZhNiq2YXZitiyJyxcbiAgICBjYXRlZ29yeTogJ3N0b2xlJyxcbiAgICBwcmljZTogMTQ5Ljk5LFxuICAgIHJlbnRhbF9wcmljZTogNDkuOTksXG4gICAgY29sb3JzOiBbJ9ij2KjZiti2INmF2Lkg2LDZh9io2YonLCAn2KPYstix2YIg2YXYuSDZgdi22YonLCAn2KPYrdmF2LEg2YXYuSDYsNmH2KjZiiddLFxuICAgIHNpemVzOiBbJ09uZSBTaXplJ10sXG4gICAgaW1hZ2VzOiBbJy9pbWFnZXMvcHJvZHVjdHMvc3RvbGUtZW1icm9pZGVyZWQtMS5qcGcnLCAnL2ltYWdlcy9wcm9kdWN0cy9zdG9sZS1lbWJyb2lkZXJlZC0yLmpwZyddLFxuICAgIHN0b2NrX3F1YW50aXR5OiAxNSxcbiAgICBpc19hdmFpbGFibGU6IHRydWUsXG4gICAgaXNfcHVibGlzaGVkOiB0cnVlLFxuICAgIGNyZWF0ZWRfYXQ6ICcyMDI0LTAxLTE3VDExOjAwOjAwWicsXG4gICAgdXBkYXRlZF9hdDogJzIwMjQtMDEtMjVUMTA6MTU6MDBaJyxcbiAgICByYXRpbmc6IDQuOSxcbiAgICByZXZpZXdzX2NvdW50OiAxOCxcbiAgICBmZWF0dXJlczogWyfYqti32LHZitiyINmK2K/ZiNmKJywgJ9iu2YrZiNi3INiw2YfYqNmK2KknLCAn2KrYtdmF2YrZhSDZgdin2K7YsSddLFxuICAgIHNwZWNpZmljYXRpb25zOiB7XG4gICAgICBtYXRlcmlhbDogJ9it2LHZitixINi32KjZiti52YonLFxuICAgICAgZW1icm9pZGVyeTogJ9iu2YrZiNi3INiw2YfYqNmK2Kkg2YjZgdi22YrYqScsXG4gICAgICBsZW5ndGg6ICcxNTAg2LPZhSdcbiAgICB9XG4gIH0sXG4gIHtcbiAgICBpZDogJzQnLFxuICAgIG5hbWU6ICfYtNix2KfYqNipINin2YTYqtiu2LHYrCDYp9mE2LDZh9io2YrYqScsXG4gICAgZGVzY3JpcHRpb246ICfYtNix2KfYqNipINiq2K7YsdisINiw2YfYqNmK2Kkg2KfZhNmE2YjZhtiMINix2YXYsiDYp9mE2KrZgdmI2YIg2YjYp9mE2KXZhtis2KfYsiDYp9mE2KPZg9in2K/ZitmF2YonLFxuICAgIGNhdGVnb3J5OiAndGFzc2VsJyxcbiAgICBwcmljZTogMzkuOTksXG4gICAgcmVudGFsX3ByaWNlOiAxNS45OSxcbiAgICBjb2xvcnM6IFsn2LDZh9io2YonLCAn2YHYttmKJywgJ9ij2LLYsdmCJywgJ9ij2K3ZhdixJ10sXG4gICAgc2l6ZXM6IFsnT25lIFNpemUnXSxcbiAgICBpbWFnZXM6IFsnL2ltYWdlcy9wcm9kdWN0cy90YXNzZWwtZ29sZC0xLmpwZyddLFxuICAgIHN0b2NrX3F1YW50aXR5OiAxMDAsXG4gICAgaXNfYXZhaWxhYmxlOiB0cnVlLFxuICAgIGlzX3B1Ymxpc2hlZDogdHJ1ZSxcbiAgICBjcmVhdGVkX2F0OiAnMjAyNC0wMS0xOFQxNDowMDowMFonLFxuICAgIHVwZGF0ZWRfYXQ6ICcyMDI0LTAxLTI2VDA5OjMwOjAwWicsXG4gICAgcmF0aW5nOiA0LjcsXG4gICAgcmV2aWV3c19jb3VudDogMzUsXG4gICAgZmVhdHVyZXM6IFsn2K7ZitmI2Lcg2LnYp9mE2YrYqSDYp9mE2KzZiNiv2KknLCAn2KPZhNmI2KfZhiDYq9in2KjYqtipJywgJ9iz2YfZhCDYp9mE2KrYsdmD2YrYqCddLFxuICAgIHNwZWNpZmljYXRpb25zOiB7XG4gICAgICBtYXRlcmlhbDogJ9iu2YrZiNi3INit2LHZitix2YrYqScsXG4gICAgICBsZW5ndGg6ICcyMyDYs9mFJyxcbiAgICAgIGF0dGFjaG1lbnQ6ICfZhdi02KjZgyDZhdi52K/ZhtmKJ1xuICAgIH1cbiAgfSxcbiAge1xuICAgIGlkOiAnNScsXG4gICAgbmFtZTogJ9mC2YTZhtiz2YjYqSDYp9mE2K/Zg9iq2YjYsdin2Ycg2KfZhNmB2KfYrtix2KknLFxuICAgIGRlc2NyaXB0aW9uOiAn2YLZhNmG2LPZiNipINiv2YPYqtmI2LHYp9mHINmB2KfYrtix2Kkg2YXYtdmF2YXYqSDYrti12YrYtdin2Ysg2YTYrdmB2YTYp9iqINin2YTYqtiu2LHYrCDYp9mE2KPZg9in2K/ZitmF2YrYqSDYp9mE2LnZhNmK2KcnLFxuICAgIGNhdGVnb3J5OiAnaG9vZCcsXG4gICAgcHJpY2U6IDE5OS45OSxcbiAgICByZW50YWxfcHJpY2U6IDc5Ljk5LFxuICAgIGNvbG9yczogWyfYo9iz2YjYryDZhdi5INiw2YfYqNmKJywgJ9ij2LLYsdmCINmF2Lkg2YHYttmKJ10sXG4gICAgc2l6ZXM6IFsnTScsICdMJywgJ1hMJ10sXG4gICAgaW1hZ2VzOiBbJy9pbWFnZXMvcHJvZHVjdHMvaG9vZC1kb2N0b3JhdGUtMS5qcGcnLCAnL2ltYWdlcy9wcm9kdWN0cy9ob29kLWRvY3RvcmF0ZS0yLmpwZyddLFxuICAgIHN0b2NrX3F1YW50aXR5OiA4LFxuICAgIGlzX2F2YWlsYWJsZTogdHJ1ZSxcbiAgICBpc19wdWJsaXNoZWQ6IHRydWUsXG4gICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMTlUMTY6MDA6MDBaJyxcbiAgICB1cGRhdGVkX2F0OiAnMjAyNC0wMS0yN1QxMjowMDowMFonLFxuICAgIHJhdGluZzogNS4wLFxuICAgIHJldmlld3NfY291bnQ6IDEyLFxuICAgIGZlYXR1cmVzOiBbJ9iq2LXZhdmK2YUg2KPZg9in2K/ZitmF2Yog2KPYtdmK2YQnLCAn2K7Yp9mF2Kkg2YHYp9iu2LHYqScsICfZhdmG2KfYs9ioINmE2YTYr9mD2KrZiNix2KfZhyddLFxuICAgIHNwZWNpZmljYXRpb25zOiB7XG4gICAgICBtYXRlcmlhbDogJ9mF2K7ZhdmEINi52KfZhNmKINin2YTYrNmI2K/YqScsXG4gICAgICBsaW5pbmc6ICfYrdix2YrYsSDZhdmE2YjZhicsXG4gICAgICBhY2FkZW1pY19sZXZlbDogJ9iv2YPYqtmI2LHYp9mHJ1xuICAgIH1cbiAgfVxuXVxuXG4vLyDYqNmK2KfZhtin2Kog2YjZh9mF2YrYqSDZhNmE2YXYr9in2LHYs1xuZXhwb3J0IGNvbnN0IG1vY2tTY2hvb2xzOiBNb2NrU2Nob29sW10gPSBbXG4gIHtcbiAgICBpZDogJzEnLFxuICAgIGFkbWluX2lkOiAnYWRtaW4tc2Nob29sLTEnLFxuICAgIG5hbWU6ICfYrNin2YXYudipINin2YTYpdmF2KfYsdin2Kog2KfZhNi52LHYqNmK2Kkg2KfZhNmF2KrYrdiv2KknLFxuICAgIG5hbWVfZW46ICdVbml0ZWQgQXJhYiBFbWlyYXRlcyBVbml2ZXJzaXR5JyxcbiAgICBuYW1lX2ZyOiAnVW5pdmVyc2l0w6kgZGVzIMOJbWlyYXRzIEFyYWJlcyBVbmlzJyxcbiAgICBhZGRyZXNzOiAn2LTYp9ix2Lkg2KfZhNis2KfZhdi52KnYjCDYp9mE2LnZitmGJyxcbiAgICBjaXR5OiAn2KfZhNi52YrZhicsXG4gICAgcGhvbmU6ICcrOTcxLTMtNzEzLTUwMDAnLFxuICAgIGVtYWlsOiAnaW5mb0B1YWV1LmFjLmFlJyxcbiAgICB3ZWJzaXRlOiAnaHR0cHM6Ly93d3cudWFldS5hYy5hZScsXG4gICAgbG9nb191cmw6ICcvaW1hZ2VzL3NjaG9vbHMvdWFldS1sb2dvLnBuZycsXG4gICAgZ3JhZHVhdGlvbl9kYXRlOiAnMjAyNC0wNi0xNScsXG4gICAgc3R1ZGVudF9jb3VudDogMTQ1MDAsXG4gICAgaXNfYWN0aXZlOiB0cnVlLFxuICAgIHNldHRpbmdzOiB7XG4gICAgICBncmFkdWF0aW9uX2NlcmVtb255X2xvY2F0aW9uOiAn2YLYp9i52Kkg2KfZhNin2K3YqtmB2KfZhNin2Kog2KfZhNmD2KjYsdmJJyxcbiAgICAgIGRyZXNzX2NvZGU6ICdmb3JtYWwnLFxuICAgICAgcGhvdG9ncmFwaHlfYWxsb3dlZDogdHJ1ZVxuICAgIH0sXG4gICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMTBUMDg6MDA6MDBaJyxcbiAgICB1cGRhdGVkX2F0OiAnMjAyNC0wMS0yNVQxMDozMDowMFonXG4gIH0sXG4gIHtcbiAgICBpZDogJzInLFxuICAgIGFkbWluX2lkOiAnYWRtaW4tc2Nob29sLTInLFxuICAgIG5hbWU6ICfYp9mE2KzYp9mF2LnYqSDYp9mE2KPZhdix2YrZg9mK2Kkg2YHZiiDYp9mE2LTYp9ix2YLYqScsXG4gICAgbmFtZV9lbjogJ0FtZXJpY2FuIFVuaXZlcnNpdHkgb2YgU2hhcmphaCcsXG4gICAgbmFtZV9mcjogJ1VuaXZlcnNpdMOpIEFtw6lyaWNhaW5lIGRlIFNoYXJqYWgnLFxuICAgIGFkZHJlc3M6ICfYtNin2LHYuSDYp9mE2KzYp9mF2LnYqdiMINin2YTYtNin2LHZgtipJyxcbiAgICBjaXR5OiAn2KfZhNi02KfYsdmC2KknLFxuICAgIHBob25lOiAnKzk3MS02LTUxNS01NTU1JyxcbiAgICBlbWFpbDogJ2luZm9AYXVzLmVkdScsXG4gICAgd2Vic2l0ZTogJ2h0dHBzOi8vd3d3LmF1cy5lZHUnLFxuICAgIGxvZ29fdXJsOiAnL2ltYWdlcy9zY2hvb2xzL2F1cy1sb2dvLnBuZycsXG4gICAgZ3JhZHVhdGlvbl9kYXRlOiAnMjAyNC0wNS0yMCcsXG4gICAgc3R1ZGVudF9jb3VudDogNjIwMCxcbiAgICBpc19hY3RpdmU6IHRydWUsXG4gICAgc2V0dGluZ3M6IHtcbiAgICAgIGdyYWR1YXRpb25fY2VyZW1vbnlfbG9jYXRpb246ICfZhdiz2LHYrSDYp9mE2KzYp9mF2LnYqScsXG4gICAgICBkcmVzc19jb2RlOiAnYWNhZGVtaWMnLFxuICAgICAgcGhvdG9ncmFwaHlfYWxsb3dlZDogdHJ1ZVxuICAgIH0sXG4gICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMTJUMDk6MTU6MDBaJyxcbiAgICB1cGRhdGVkX2F0OiAnMjAyNC0wMS0yOFQxNDoyMDowMFonXG4gIH0sXG4gIHtcbiAgICBpZDogJzMnLFxuICAgIGFkbWluX2lkOiAnYWRtaW4tc2Nob29sLTMnLFxuICAgIG5hbWU6ICfYrNin2YXYudipINiy2KfZitivJyxcbiAgICBuYW1lX2VuOiAnWmF5ZWQgVW5pdmVyc2l0eScsXG4gICAgbmFtZV9mcjogJ1VuaXZlcnNpdMOpIFpheWVkJyxcbiAgICBhZGRyZXNzOiAn2LTYp9ix2Lkg2KfZhNi02YrYriDYstin2YrYr9iMINiv2KjZiicsXG4gICAgY2l0eTogJ9iv2KjZiicsXG4gICAgcGhvbmU6ICcrOTcxLTQtNDAyLTExMTEnLFxuICAgIGVtYWlsOiAnaW5mb0B6dS5hYy5hZScsXG4gICAgd2Vic2l0ZTogJ2h0dHBzOi8vd3d3Lnp1LmFjLmFlJyxcbiAgICBsb2dvX3VybDogJy9pbWFnZXMvc2Nob29scy96dS1sb2dvLnBuZycsXG4gICAgZ3JhZHVhdGlvbl9kYXRlOiAnMjAyNC0wNi0xMCcsXG4gICAgc3R1ZGVudF9jb3VudDogOTgwMCxcbiAgICBpc19hY3RpdmU6IHRydWUsXG4gICAgc2V0dGluZ3M6IHtcbiAgICAgIGdyYWR1YXRpb25fY2VyZW1vbnlfbG9jYXRpb246ICfZhdix2YPYsiDYp9mE2YXYpNiq2YXYsdin2KonLFxuICAgICAgZHJlc3NfY29kZTogJ2Zvcm1hbCcsXG4gICAgICBwaG90b2dyYXBoeV9hbGxvd2VkOiBmYWxzZVxuICAgIH0sXG4gICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMTVUMTE6MDA6MDBaJyxcbiAgICB1cGRhdGVkX2F0OiAnMjAyNC0wMi0wMVQxNjo0NTowMFonXG4gIH0sXG4gIHtcbiAgICBpZDogJzQnLFxuICAgIGFkbWluX2lkOiAnYWRtaW4tc2Nob29sLTQnLFxuICAgIG5hbWU6ICfZg9mE2YrYqSDYp9mE2KXZhdin2LHYp9iqINmE2YTYqtmD2YbZiNmE2YjYrNmK2KcnLFxuICAgIG5hbWVfZW46ICdFbWlyYXRlcyBJbnN0aXR1dGUgb2YgVGVjaG5vbG9neScsXG4gICAgbmFtZV9mcjogJ0luc3RpdHV0IGRlIFRlY2hub2xvZ2llIGRlcyDDiW1pcmF0cycsXG4gICAgYWRkcmVzczogJ9in2YTZhdmG2LfZgtipINin2YTYo9mD2KfYr9mK2YXZitip2Iwg2KPYqNmI2LjYqNmKJyxcbiAgICBjaXR5OiAn2KPYqNmI2LjYqNmKJyxcbiAgICBwaG9uZTogJys5NzEtMi00MDEtNDAwMCcsXG4gICAgZW1haWw6ICdpbmZvQGVpdC5hYy5hZScsXG4gICAgd2Vic2l0ZTogJ2h0dHBzOi8vd3d3LmVpdC5hYy5hZScsXG4gICAgbG9nb191cmw6ICcvaW1hZ2VzL3NjaG9vbHMvZWl0LWxvZ28ucG5nJyxcbiAgICBncmFkdWF0aW9uX2RhdGU6ICcyMDI0LTA3LTA1JyxcbiAgICBzdHVkZW50X2NvdW50OiAzNTAwLFxuICAgIGlzX2FjdGl2ZTogdHJ1ZSxcbiAgICBzZXR0aW5nczoge1xuICAgICAgZ3JhZHVhdGlvbl9jZXJlbW9ueV9sb2NhdGlvbjogJ9in2YTZgtin2LnYqSDYp9mE2LHYptmK2LPZitipJyxcbiAgICAgIGRyZXNzX2NvZGU6ICdidXNpbmVzcycsXG4gICAgICBwaG90b2dyYXBoeV9hbGxvd2VkOiB0cnVlXG4gICAgfSxcbiAgICBjcmVhdGVkX2F0OiAnMjAyNC0wMS0xOFQxMzozMDowMFonLFxuICAgIHVwZGF0ZWRfYXQ6ICcyMDI0LTAyLTA1VDA5OjE1OjAwWidcbiAgfSxcbiAge1xuICAgIGlkOiAnNScsXG4gICAgYWRtaW5faWQ6ICdhZG1pbi1zY2hvb2wtNScsXG4gICAgbmFtZTogJ9mF2LnZh9ivINij2KjZiNi42KjZiiDZhNmE2KrYudmE2YrZhSDYp9mE2KrZgtmG2YonLFxuICAgIG5hbWVfZW46ICdBYnUgRGhhYmkgVGVjaG5pY2FsIEluc3RpdHV0ZScsXG4gICAgbmFtZV9mcjogJ0luc3RpdHV0IFRlY2huaXF1ZSBkXFwnQWJ1IERoYWJpJyxcbiAgICBhZGRyZXNzOiAn2KfZhNmF2YbYt9mC2Kkg2KfZhNi12YbYp9i52YrYqdiMINij2KjZiNi42KjZiicsXG4gICAgY2l0eTogJ9ij2KjZiNi42KjZiicsXG4gICAgcGhvbmU6ICcrOTcxLTItNTA1LTIwMDAnLFxuICAgIGVtYWlsOiAnaW5mb0BhZHRpLmFjLmFlJyxcbiAgICB3ZWJzaXRlOiAnaHR0cHM6Ly93d3cuYWR0aS5hYy5hZScsXG4gICAgZ3JhZHVhdGlvbl9kYXRlOiAnMjAyNC0wNi0yNScsXG4gICAgc3R1ZGVudF9jb3VudDogMjgwMCxcbiAgICBpc19hY3RpdmU6IGZhbHNlLFxuICAgIHNldHRpbmdzOiB7XG4gICAgICBncmFkdWF0aW9uX2NlcmVtb255X2xvY2F0aW9uOiAn2YXYsdmD2LIg2KfZhNiq2K/YsdmK2KgnLFxuICAgICAgZHJlc3NfY29kZTogJ2Nhc3VhbCcsXG4gICAgICBwaG90b2dyYXBoeV9hbGxvd2VkOiB0cnVlXG4gICAgfSxcbiAgICBjcmVhdGVkX2F0OiAnMjAyNC0wMS0yMFQxNTo0NTowMFonLFxuICAgIHVwZGF0ZWRfYXQ6ICcyMDI0LTAyLTEwVDEyOjAwOjAwWidcbiAgfVxuXVxuXG4vLyDYqNmK2KfZhtin2Kog2YjZh9mF2YrYqSDZhNmE2LfZhNio2KfYqlxuZXhwb3J0IGNvbnN0IG1vY2tPcmRlcnM6IE1vY2tPcmRlcltdID0gW1xuICB7XG4gICAgaWQ6ICcxJyxcbiAgICBvcmRlcl9udW1iZXI6ICdHVC0yNDAxMjAtMDAxJyxcbiAgICBjdXN0b21lcl9pZDogJ3N0dWRlbnQtMScsXG4gICAgY3VzdG9tZXJfbmFtZTogJ9ij2K3ZhdivINmF2K3ZhdivINi52YTZiicsXG4gICAgY3VzdG9tZXJfZW1haWw6ICdhaG1lZC5hbGlAc3R1ZGVudC51YWV1LmFjLmFlJyxcbiAgICBjdXN0b21lcl9waG9uZTogJys5NzEtNTAtMTIzLTQ1NjcnLFxuICAgIHN0YXR1czogJ2luX3Byb2R1Y3Rpb24nLFxuICAgIGl0ZW1zOiBbXG4gICAgICB7XG4gICAgICAgIGlkOiAnMScsXG4gICAgICAgIG9yZGVyX2lkOiAnMScsXG4gICAgICAgIHByb2R1Y3RfaWQ6ICcxJyxcbiAgICAgICAgcHJvZHVjdF9uYW1lOiAn2KvZiNioINin2YTYqtiu2LHYrCDYp9mE2YPZhNin2LPZitmD2YonLFxuICAgICAgICBwcm9kdWN0X2ltYWdlOiAnL2ltYWdlcy9wcm9kdWN0cy9nb3duLWNsYXNzaWMtMS5qcGcnLFxuICAgICAgICBjYXRlZ29yeTogJ2dvd24nLFxuICAgICAgICBxdWFudGl0eTogMSxcbiAgICAgICAgdW5pdF9wcmljZTogMjk5Ljk5LFxuICAgICAgICB0b3RhbF9wcmljZTogMjk5Ljk5LFxuICAgICAgICBjdXN0b21pemF0aW9uczoge1xuICAgICAgICAgIGNvbG9yOiAn2KPYs9mI2K8nLFxuICAgICAgICAgIHNpemU6ICdMJyxcbiAgICAgICAgICBlbWJyb2lkZXJ5OiAn2KPYrdmF2K8g2LnZhNmKIC0g2KjZg9in2YTZiNix2YrZiNizINmH2YbYr9iz2KknXG4gICAgICAgIH1cbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIGlkOiAnMicsXG4gICAgICAgIG9yZGVyX2lkOiAnMScsXG4gICAgICAgIHByb2R1Y3RfaWQ6ICcyJyxcbiAgICAgICAgcHJvZHVjdF9uYW1lOiAn2YLYqNi52Kkg2KfZhNiq2K7YsdisINin2YTYo9mD2KfYr9mK2YXZitipJyxcbiAgICAgICAgcHJvZHVjdF9pbWFnZTogJy9pbWFnZXMvcHJvZHVjdHMvY2FwLWFjYWRlbWljLTEuanBnJyxcbiAgICAgICAgY2F0ZWdvcnk6ICdjYXAnLFxuICAgICAgICBxdWFudGl0eTogMSxcbiAgICAgICAgdW5pdF9wcmljZTogODkuOTksXG4gICAgICAgIHRvdGFsX3ByaWNlOiA4OS45OSxcbiAgICAgICAgY3VzdG9taXphdGlvbnM6IHtcbiAgICAgICAgICBjb2xvcjogJ9ij2LPZiNivJyxcbiAgICAgICAgICBzaXplOiAnTSdcbiAgICAgICAgfVxuICAgICAgfVxuICAgIF0sXG4gICAgc3VidG90YWw6IDM4OS45OCxcbiAgICB0YXg6IDE5LjUwLFxuICAgIHNoaXBwaW5nX2Nvc3Q6IDI1LjAwLFxuICAgIHRvdGFsOiA0MzQuNDgsXG4gICAgcGF5bWVudF9zdGF0dXM6ICdwYWlkJyxcbiAgICBwYXltZW50X21ldGhvZDogJ2NyZWRpdF9jYXJkJyxcbiAgICBzaGlwcGluZ19hZGRyZXNzOiB7XG4gICAgICBzdHJlZXQ6ICfYtNin2LHYuSDYp9mE2KzYp9mF2LnYqdiMINmF2KjZhtmJIDEy2Iwg2LTZgtipIDMwNCcsXG4gICAgICBjaXR5OiAn2KfZhNi52YrZhicsXG4gICAgICBzdGF0ZTogJ9ij2KjZiNi42KjZiicsXG4gICAgICBwb3N0YWxfY29kZTogJzE3NjY2JyxcbiAgICAgIGNvdW50cnk6ICfYp9mE2KXZhdin2LHYp9iqINin2YTYudix2KjZitipINin2YTZhdiq2K3Yr9ipJ1xuICAgIH0sXG4gICAgdHJhY2tpbmdfbnVtYmVyOiAnVFJLLUdULTAwMS0yMDI0JyxcbiAgICBub3RlczogJ9mK2LHYrNmJINin2YTYqtiz2YTZitmFINmC2KjZhCDYrdmB2YQg2KfZhNiq2K7YsdisJyxcbiAgICBjcmVhdGVkX2F0OiAnMjAyNC0wMS0yMFQxMDozMDowMFonLFxuICAgIHVwZGF0ZWRfYXQ6ICcyMDI0LTAxLTIyVDE0OjE1OjAwWicsXG4gICAgZGVsaXZlcnlfZGF0ZTogJzIwMjQtMDItMTVUMDA6MDA6MDBaJyxcbiAgICBzY2hvb2xfaWQ6ICcxJyxcbiAgICBzY2hvb2xfbmFtZTogJ9is2KfZhdi52Kkg2KfZhNil2YXYp9ix2KfYqiDYp9mE2LnYsdio2YrYqSDYp9mE2YXYqtit2K/YqSdcbiAgfSxcbiAge1xuICAgIGlkOiAnMicsXG4gICAgb3JkZXJfbnVtYmVyOiAnR1QtMjQwMTIxLTAwMicsXG4gICAgY3VzdG9tZXJfaWQ6ICdzdHVkZW50LTInLFxuICAgIGN1c3RvbWVyX25hbWU6ICfZgdin2LfZhdipINiz2KfZhNmFINin2YTYstmH2LHYp9mG2YonLFxuICAgIGN1c3RvbWVyX2VtYWlsOiAnZmF0aW1hLmFsemFocmFuaUBhdXMuZWR1JyxcbiAgICBjdXN0b21lcl9waG9uZTogJys5NzEtNTYtNzg5LTAxMjMnLFxuICAgIHN0YXR1czogJ2RlbGl2ZXJlZCcsXG4gICAgaXRlbXM6IFtcbiAgICAgIHtcbiAgICAgICAgaWQ6ICczJyxcbiAgICAgICAgb3JkZXJfaWQ6ICcyJyxcbiAgICAgICAgcHJvZHVjdF9pZDogJzMnLFxuICAgICAgICBwcm9kdWN0X25hbWU6ICfYq9mI2Kgg2KfZhNiq2K7YsdisINin2YTZhdmF2YrYsicsXG4gICAgICAgIHByb2R1Y3RfaW1hZ2U6ICcvaW1hZ2VzL3Byb2R1Y3RzL2dvd24tcHJlbWl1bS0xLmpwZycsXG4gICAgICAgIGNhdGVnb3J5OiAnZ293bicsXG4gICAgICAgIHF1YW50aXR5OiAxLFxuICAgICAgICB1bml0X3ByaWNlOiAzOTkuOTksXG4gICAgICAgIHRvdGFsX3ByaWNlOiAzOTkuOTksXG4gICAgICAgIGN1c3RvbWl6YXRpb25zOiB7XG4gICAgICAgICAgY29sb3I6ICfYo9iy2LHZgiDYr9in2YPZhicsXG4gICAgICAgICAgc2l6ZTogJ00nLFxuICAgICAgICAgIGVtYnJvaWRlcnk6ICfZgdin2LfZhdipINin2YTYstmH2LHYp9mG2YogLSDZhdin2KzYs9iq2YrYsSDYpdiv2KfYsdipINij2LnZhdin2YQnXG4gICAgICAgIH1cbiAgICAgIH1cbiAgICBdLFxuICAgIHN1YnRvdGFsOiAzOTkuOTksXG4gICAgdGF4OiAyMC4wMCxcbiAgICBzaGlwcGluZ19jb3N0OiAzMC4wMCxcbiAgICB0b3RhbDogNDQ5Ljk5LFxuICAgIHBheW1lbnRfc3RhdHVzOiAncGFpZCcsXG4gICAgcGF5bWVudF9tZXRob2Q6ICdiYW5rX3RyYW5zZmVyJyxcbiAgICBzaGlwcGluZ19hZGRyZXNzOiB7XG4gICAgICBzdHJlZXQ6ICfYtNin2LHYuSDYp9mE2YPZiNix2YbZiti02Iwg2KjYsdisINin2YTYpdmF2KfYsdin2KrYjCDYp9mE2LfYp9io2YIgMTUnLFxuICAgICAgY2l0eTogJ9in2YTYtNin2LHZgtipJyxcbiAgICAgIHN0YXRlOiAn2KfZhNi02KfYsdmC2KknLFxuICAgICAgcG9zdGFsX2NvZGU6ICcyNzI3MicsXG4gICAgICBjb3VudHJ5OiAn2KfZhNil2YXYp9ix2KfYqiDYp9mE2LnYsdio2YrYqSDYp9mE2YXYqtit2K/YqSdcbiAgICB9LFxuICAgIHRyYWNraW5nX251bWJlcjogJ1RSSy1HVC0wMDItMjAyNCcsXG4gICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMjFUMDk6MTU6MDBaJyxcbiAgICB1cGRhdGVkX2F0OiAnMjAyNC0wMS0yNVQxNjozMDowMFonLFxuICAgIGRlbGl2ZXJ5X2RhdGU6ICcyMDI0LTAxLTI4VDAwOjAwOjAwWicsXG4gICAgc2Nob29sX2lkOiAnMicsXG4gICAgc2Nob29sX25hbWU6ICfYp9mE2KzYp9mF2LnYqSDYp9mE2KPZhdix2YrZg9mK2Kkg2YHZiiDYp9mE2LTYp9ix2YLYqSdcbiAgfSxcbiAge1xuICAgIGlkOiAnMycsXG4gICAgb3JkZXJfbnVtYmVyOiAnR1QtMjQwMTIyLTAwMycsXG4gICAgY3VzdG9tZXJfaWQ6ICdzdHVkZW50LTMnLFxuICAgIGN1c3RvbWVyX25hbWU6ICfYrtin2YTYryDYudio2K/Yp9mE2YTZhyDYp9mE2YXZhti12YjYsdmKJyxcbiAgICBjdXN0b21lcl9lbWFpbDogJ2toYWxpZC5hbG1hbnNvdXJpQHp1LmFjLmFlJyxcbiAgICBjdXN0b21lcl9waG9uZTogJys5NzEtNTItNDU2LTc4OTAnLFxuICAgIHN0YXR1czogJ3BlbmRpbmcnLFxuICAgIGl0ZW1zOiBbXG4gICAgICB7XG4gICAgICAgIGlkOiAnNCcsXG4gICAgICAgIG9yZGVyX2lkOiAnMycsXG4gICAgICAgIHByb2R1Y3RfaWQ6ICcxJyxcbiAgICAgICAgcHJvZHVjdF9uYW1lOiAn2KvZiNioINin2YTYqtiu2LHYrCDYp9mE2YPZhNin2LPZitmD2YonLFxuICAgICAgICBwcm9kdWN0X2ltYWdlOiAnL2ltYWdlcy9wcm9kdWN0cy9nb3duLWNsYXNzaWMtMS5qcGcnLFxuICAgICAgICBjYXRlZ29yeTogJ2dvd24nLFxuICAgICAgICBxdWFudGl0eTogMSxcbiAgICAgICAgdW5pdF9wcmljZTogMjk5Ljk5LFxuICAgICAgICB0b3RhbF9wcmljZTogMjk5Ljk5LFxuICAgICAgICBjdXN0b21pemF0aW9uczoge1xuICAgICAgICAgIGNvbG9yOiAn2KjZiNix2KzZiNmG2K/ZiicsXG4gICAgICAgICAgc2l6ZTogJ1hMJ1xuICAgICAgICB9XG4gICAgICB9LFxuICAgICAge1xuICAgICAgICBpZDogJzUnLFxuICAgICAgICBvcmRlcl9pZDogJzMnLFxuICAgICAgICBwcm9kdWN0X2lkOiAnNCcsXG4gICAgICAgIHByb2R1Y3RfbmFtZTogJ9mI2LTYp9itINin2YTYqtiu2LHYrCDYp9mE2YXYt9ix2LInLFxuICAgICAgICBwcm9kdWN0X2ltYWdlOiAnL2ltYWdlcy9wcm9kdWN0cy9zdG9sZS1lbWJyb2lkZXJlZC0xLmpwZycsXG4gICAgICAgIGNhdGVnb3J5OiAnc3RvbGUnLFxuICAgICAgICBxdWFudGl0eTogMSxcbiAgICAgICAgdW5pdF9wcmljZTogMTQ5Ljk5LFxuICAgICAgICB0b3RhbF9wcmljZTogMTQ5Ljk5LFxuICAgICAgICBjdXN0b21pemF0aW9uczoge1xuICAgICAgICAgIGNvbG9yOiAn2LDZh9io2YonLFxuICAgICAgICAgIGVtYnJvaWRlcnk6ICfZg9mE2YrYqSDYp9mE2YfZhtiv2LPYqSdcbiAgICAgICAgfVxuICAgICAgfVxuICAgIF0sXG4gICAgc3VidG90YWw6IDQ0OS45OCxcbiAgICB0YXg6IDIyLjUwLFxuICAgIHNoaXBwaW5nX2Nvc3Q6IDI1LjAwLFxuICAgIHRvdGFsOiA0OTcuNDgsXG4gICAgcGF5bWVudF9zdGF0dXM6ICdwZW5kaW5nJyxcbiAgICBzaGlwcGluZ19hZGRyZXNzOiB7XG4gICAgICBzdHJlZXQ6ICfYtNin2LHYuSDYp9mE2LTZitiuINiy2KfZitiv2Iwg2YXYrNmF2Lkg2K/YqNmKINin2YTYo9mD2KfYr9mK2YXZiicsXG4gICAgICBjaXR5OiAn2K/YqNmKJyxcbiAgICAgIHN0YXRlOiAn2K/YqNmKJyxcbiAgICAgIHBvc3RhbF9jb2RlOiAnMzkxMTg2JyxcbiAgICAgIGNvdW50cnk6ICfYp9mE2KXZhdin2LHYp9iqINin2YTYudix2KjZitipINin2YTZhdiq2K3Yr9ipJ1xuICAgIH0sXG4gICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMjJUMTQ6NDU6MDBaJyxcbiAgICB1cGRhdGVkX2F0OiAnMjAyNC0wMS0yMlQxNDo0NTowMFonLFxuICAgIHNjaG9vbF9pZDogJzMnLFxuICAgIHNjaG9vbF9uYW1lOiAn2KzYp9mF2LnYqSDYstin2YrYrydcbiAgfVxuXVxuXG4vLyDZhdiz2KfYudiv2KfYqiDZhNmE2KrYudin2YXZhCDZhdi5INin2YTYqNmK2KfZhtin2Kog2KfZhNmI2YfZhdmK2KlcbmV4cG9ydCBjbGFzcyBNb2NrRGF0YU1hbmFnZXIge1xuICBwcml2YXRlIHN0YXRpYyBnZXRTdG9yYWdlS2V5KHR5cGU6ICdwYWdlcycgfCAnbWVudUl0ZW1zJyB8ICdwcm9kdWN0cycgfCAnY2F0ZWdvcmllcycgfCAnc2Nob29scycgfCAnb3JkZXJzJyB8ICdhaVByb3ZpZGVycycpOiBzdHJpbmcge1xuICAgIHJldHVybiBgbW9ja0RhdGFfJHt0eXBlfWBcbiAgfVxuXG4gIHN0YXRpYyBnZXRQYWdlcygpOiBNb2NrUGFnZVtdIHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHJldHVybiBtb2NrUGFnZXNcblxuICAgIGNvbnN0IHN0b3JlZCA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKHRoaXMuZ2V0U3RvcmFnZUtleSgncGFnZXMnKSlcbiAgICByZXR1cm4gc3RvcmVkID8gSlNPTi5wYXJzZShzdG9yZWQpIDogbW9ja1BhZ2VzXG4gIH1cblxuICBzdGF0aWMgZ2V0TWVudUl0ZW1zKCk6IE1vY2tNZW51SXRlbVtdIHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHJldHVybiBtb2NrTWVudUl0ZW1zXG5cbiAgICBjb25zdCBzdG9yZWQgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSh0aGlzLmdldFN0b3JhZ2VLZXkoJ21lbnVJdGVtcycpKVxuICAgIHJldHVybiBzdG9yZWQgPyBKU09OLnBhcnNlKHN0b3JlZCkgOiBtb2NrTWVudUl0ZW1zXG4gIH1cblxuICBzdGF0aWMgZ2V0UHJvZHVjdHMoKTogTW9ja1Byb2R1Y3RbXSB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICBjb25zb2xlLmxvZygnU2VydmVyIHNpZGUgLSByZXR1cm5pbmcgbW9jayBwcm9kdWN0czonLCBtb2NrUHJvZHVjdHMubGVuZ3RoKVxuICAgICAgcmV0dXJuIG1vY2tQcm9kdWN0c1xuICAgIH1cblxuICAgIGNvbnN0IHN0b3JlZCA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKHRoaXMuZ2V0U3RvcmFnZUtleSgncHJvZHVjdHMnKSlcbiAgICBjb25zdCBwcm9kdWN0cyA9IHN0b3JlZCA/IEpTT04ucGFyc2Uoc3RvcmVkKSA6IG1vY2tQcm9kdWN0c1xuICAgIGNvbnNvbGUubG9nKCdDbGllbnQgc2lkZSAtIGxvYWRlZCBwcm9kdWN0czonLCBwcm9kdWN0cy5sZW5ndGgpXG4gICAgY29uc29sZS5sb2coJ1Byb2R1Y3QgSURzOicsIHByb2R1Y3RzLm1hcCgocDogTW9ja1Byb2R1Y3QpID0+IHAuaWQpKVxuICAgIHJldHVybiBwcm9kdWN0c1xuICB9XG5cbiAgc3RhdGljIGdldENhdGVnb3JpZXMoKTogTW9ja0NhdGVnb3J5W10ge1xuICAgIGlmICh0eXBlb2Ygd2luZG93ID09PSAndW5kZWZpbmVkJykgcmV0dXJuIG1vY2tDYXRlZ29yaWVzXG5cbiAgICBjb25zdCBzdG9yZWQgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSh0aGlzLmdldFN0b3JhZ2VLZXkoJ2NhdGVnb3JpZXMnKSlcbiAgICByZXR1cm4gc3RvcmVkID8gSlNPTi5wYXJzZShzdG9yZWQpIDogbW9ja0NhdGVnb3JpZXNcbiAgfVxuXG4gIHN0YXRpYyBnZXRTY2hvb2xzKCk6IE1vY2tTY2hvb2xbXSB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSByZXR1cm4gbW9ja1NjaG9vbHNcblxuICAgIGNvbnN0IHN0b3JlZCA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKHRoaXMuZ2V0U3RvcmFnZUtleSgnc2Nob29scycpKVxuICAgIHJldHVybiBzdG9yZWQgPyBKU09OLnBhcnNlKHN0b3JlZCkgOiBtb2NrU2Nob29sc1xuICB9XG5cbiAgc3RhdGljIGdldE9yZGVycygpOiBNb2NrT3JkZXJbXSB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSByZXR1cm4gbW9ja09yZGVyc1xuXG4gICAgY29uc3Qgc3RvcmVkID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0odGhpcy5nZXRTdG9yYWdlS2V5KCdvcmRlcnMnKSlcbiAgICByZXR1cm4gc3RvcmVkID8gSlNPTi5wYXJzZShzdG9yZWQpIDogbW9ja09yZGVyc1xuICB9XG5cbiAgc3RhdGljIHNhdmVQYWdlcyhwYWdlczogTW9ja1BhZ2VbXSk6IHZvaWQge1xuICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0odGhpcy5nZXRTdG9yYWdlS2V5KCdwYWdlcycpLCBKU09OLnN0cmluZ2lmeShwYWdlcykpXG4gICAgfVxuICB9XG5cbiAgc3RhdGljIHNhdmVNZW51SXRlbXMoaXRlbXM6IE1vY2tNZW51SXRlbVtdKTogdm9pZCB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSh0aGlzLmdldFN0b3JhZ2VLZXkoJ21lbnVJdGVtcycpLCBKU09OLnN0cmluZ2lmeShpdGVtcykpXG4gICAgfVxuICB9XG5cbiAgc3RhdGljIHNhdmVQcm9kdWN0cyhwcm9kdWN0czogTW9ja1Byb2R1Y3RbXSk6IHZvaWQge1xuICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgY29uc29sZS5sb2coJ1NhdmluZyBwcm9kdWN0cyB0byBsb2NhbFN0b3JhZ2U6JywgcHJvZHVjdHMubGVuZ3RoKVxuICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0odGhpcy5nZXRTdG9yYWdlS2V5KCdwcm9kdWN0cycpLCBKU09OLnN0cmluZ2lmeShwcm9kdWN0cykpXG5cbiAgICAgIC8vINin2YTYqtit2YLZgiDZhdmGINij2YYg2KfZhNio2YrYp9mG2KfYqiDYrdmP2YHYuNiqINio2LTZg9mEINi12K3ZititXG4gICAgICBjb25zdCBzYXZlZCA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKHRoaXMuZ2V0U3RvcmFnZUtleSgncHJvZHVjdHMnKSlcbiAgICAgIGNvbnN0IHBhcnNlZFNhdmVkID0gc2F2ZWQgPyBKU09OLnBhcnNlKHNhdmVkKSA6IFtdXG4gICAgICBjb25zb2xlLmxvZygnVmVyaWZpZWQgc2F2ZWQgcHJvZHVjdHMgY291bnQ6JywgcGFyc2VkU2F2ZWQubGVuZ3RoKVxuICAgIH1cbiAgfVxuXG4gIHN0YXRpYyBzYXZlQ2F0ZWdvcmllcyhjYXRlZ29yaWVzOiBNb2NrQ2F0ZWdvcnlbXSk6IHZvaWQge1xuICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0odGhpcy5nZXRTdG9yYWdlS2V5KCdjYXRlZ29yaWVzJyksIEpTT04uc3RyaW5naWZ5KGNhdGVnb3JpZXMpKVxuICAgIH1cbiAgfVxuXG4gIHN0YXRpYyBzYXZlU2Nob29scyhzY2hvb2xzOiBNb2NrU2Nob29sW10pOiB2b2lkIHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKHRoaXMuZ2V0U3RvcmFnZUtleSgnc2Nob29scycpLCBKU09OLnN0cmluZ2lmeShzY2hvb2xzKSlcbiAgICB9XG4gIH1cblxuICBzdGF0aWMgc2F2ZU9yZGVycyhvcmRlcnM6IE1vY2tPcmRlcltdKTogdm9pZCB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSh0aGlzLmdldFN0b3JhZ2VLZXkoJ29yZGVycycpLCBKU09OLnN0cmluZ2lmeShvcmRlcnMpKVxuICAgIH1cbiAgfVxuXG4gIHN0YXRpYyBnZW5lcmF0ZUlkKCk6IHN0cmluZyB7XG4gICAgcmV0dXJuIERhdGUubm93KCkudG9TdHJpbmcoKSArIE1hdGgucmFuZG9tKCkudG9TdHJpbmcoMzYpLnN1YnN0cmluZygyLCAxMSlcbiAgfVxuXG4gIC8vINmF2LPYrSDYrNmF2YrYuSDYp9mE2KjZitin2YbYp9iqINin2YTZhdit2YHZiNi42KkgKNmE2YTYp9iu2KrYqNin2LEpXG4gIHN0YXRpYyBjbGVhckFsbERhdGEoKTogdm9pZCB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgnbW9ja0FJTW9kZWxzJylcbiAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdtb2NrTW9kZWxBY3Rpdml0aWVzJylcbiAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdtb2NrUGFnZXMnKVxuICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ21vY2tQYWdlVGVtcGxhdGVzJylcbiAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdtb2NrUGFnZVByb2plY3RzJylcbiAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdtb2NrQ29tcG9uZW50TGlicmFyeScpXG4gICAgfVxuICB9XG5cbiAgc3RhdGljIGdlbmVyYXRlT3JkZXJOdW1iZXIoKTogc3RyaW5nIHtcbiAgICBjb25zdCBkYXRlID0gbmV3IERhdGUoKVxuICAgIGNvbnN0IHllYXIgPSBkYXRlLmdldEZ1bGxZZWFyKCkudG9TdHJpbmcoKS5zbGljZSgtMilcbiAgICBjb25zdCBtb250aCA9IChkYXRlLmdldE1vbnRoKCkgKyAxKS50b1N0cmluZygpLnBhZFN0YXJ0KDIsICcwJylcbiAgICBjb25zdCBkYXkgPSBkYXRlLmdldERhdGUoKS50b1N0cmluZygpLnBhZFN0YXJ0KDIsICcwJylcbiAgICBjb25zdCBvcmRlcnMgPSB0aGlzLmdldE9yZGVycygpXG4gICAgY29uc3QgdG9kYXlPcmRlcnMgPSBvcmRlcnMuZmlsdGVyKG9yZGVyID0+XG4gICAgICBvcmRlci5jcmVhdGVkX2F0LnN0YXJ0c1dpdGgoYCR7ZGF0ZS5nZXRGdWxsWWVhcigpfS0ke21vbnRofS0ke2RheX1gKVxuICAgIClcbiAgICBjb25zdCBvcmRlckNvdW50ID0gKHRvZGF5T3JkZXJzLmxlbmd0aCArIDEpLnRvU3RyaW5nKCkucGFkU3RhcnQoMywgJzAnKVxuICAgIHJldHVybiBgR1QtJHt5ZWFyfSR7bW9udGh9JHtkYXl9LSR7b3JkZXJDb3VudH1gXG4gIH1cblxuICAvLyDYpdiv2KfYsdipINmG2YXYp9iw2Kwg2KfZhNiw2YPYp9ihINin2YTYp9i12LfZhtin2LnZilxuICBzdGF0aWMgZ2V0QUlNb2RlbHMoKTogQUlNb2RlbFtdIHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHJldHVybiB0aGlzLmRlZmF1bHRBSU1vZGVsc1xuXG4gICAgY29uc3Qgc3RvcmVkID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ21vY2tBSU1vZGVscycpXG4gICAgaWYgKHN0b3JlZCkge1xuICAgICAgcmV0dXJuIEpTT04ucGFyc2Uoc3RvcmVkKVxuICAgIH1cbiAgICByZXR1cm4gdGhpcy5kZWZhdWx0QUlNb2RlbHNcbiAgfVxuXG4gIHN0YXRpYyBzYXZlQUlNb2RlbHMobW9kZWxzOiBBSU1vZGVsW10pOiB2b2lkIHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdtb2NrQUlNb2RlbHMnLCBKU09OLnN0cmluZ2lmeShtb2RlbHMpKVxuICAgIH1cbiAgfVxuXG4gIHN0YXRpYyBnZXRNb2RlbEFjdGl2aXRpZXMoKTogTW9kZWxBY3Rpdml0eVtdIHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHJldHVybiB0aGlzLmRlZmF1bHRNb2RlbEFjdGl2aXRpZXNcblxuICAgIGNvbnN0IHN0b3JlZCA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdtb2NrTW9kZWxBY3Rpdml0aWVzJylcbiAgICBpZiAoc3RvcmVkKSB7XG4gICAgICByZXR1cm4gSlNPTi5wYXJzZShzdG9yZWQpXG4gICAgfVxuICAgIHJldHVybiB0aGlzLmRlZmF1bHRNb2RlbEFjdGl2aXRpZXNcbiAgfVxuXG4gIHN0YXRpYyBzYXZlTW9kZWxBY3Rpdml0aWVzKGFjdGl2aXRpZXM6IE1vZGVsQWN0aXZpdHlbXSk6IHZvaWQge1xuICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ21vY2tNb2RlbEFjdGl2aXRpZXMnLCBKU09OLnN0cmluZ2lmeShhY3Rpdml0aWVzKSlcbiAgICB9XG4gIH1cblxuICAvLyDYpdiv2KfYsdipINmC2YjYp9mE2Kgg2KfZhNi12YHYrdin2KpcbiAgc3RhdGljIGdldFBhZ2VUZW1wbGF0ZXMoKTogUGFnZVRlbXBsYXRlW10ge1xuICAgIGlmICh0eXBlb2Ygd2luZG93ID09PSAndW5kZWZpbmVkJykgcmV0dXJuIHRoaXMuZGVmYXVsdFBhZ2VUZW1wbGF0ZXNcblxuICAgIGNvbnN0IHN0b3JlZCA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdtb2NrUGFnZVRlbXBsYXRlcycpXG4gICAgaWYgKHN0b3JlZCkge1xuICAgICAgcmV0dXJuIEpTT04ucGFyc2Uoc3RvcmVkKVxuICAgIH1cbiAgICByZXR1cm4gdGhpcy5kZWZhdWx0UGFnZVRlbXBsYXRlc1xuICB9XG5cbiAgc3RhdGljIHNhdmVQYWdlVGVtcGxhdGVzKHRlbXBsYXRlczogUGFnZVRlbXBsYXRlW10pOiB2b2lkIHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdtb2NrUGFnZVRlbXBsYXRlcycsIEpTT04uc3RyaW5naWZ5KHRlbXBsYXRlcykpXG4gICAgfVxuICB9XG5cbiAgc3RhdGljIGdldFBhZ2VQcm9qZWN0cygpOiBQYWdlUHJvamVjdFtdIHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHJldHVybiB0aGlzLmRlZmF1bHRQYWdlUHJvamVjdHNcblxuICAgIGNvbnN0IHN0b3JlZCA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdtb2NrUGFnZVByb2plY3RzJylcbiAgICBpZiAoc3RvcmVkKSB7XG4gICAgICByZXR1cm4gSlNPTi5wYXJzZShzdG9yZWQpXG4gICAgfVxuICAgIHJldHVybiB0aGlzLmRlZmF1bHRQYWdlUHJvamVjdHNcbiAgfVxuXG4gIHN0YXRpYyBzYXZlUGFnZVByb2plY3RzKHByb2plY3RzOiBQYWdlUHJvamVjdFtdKTogdm9pZCB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgnbW9ja1BhZ2VQcm9qZWN0cycsIEpTT04uc3RyaW5naWZ5KHByb2plY3RzKSlcbiAgICB9XG4gIH1cblxuICBzdGF0aWMgZ2V0Q29tcG9uZW50TGlicmFyeSgpOiBDb21wb25lbnRMaWJyYXJ5SXRlbVtdIHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHJldHVybiB0aGlzLmRlZmF1bHRDb21wb25lbnRMaWJyYXJ5XG5cbiAgICBjb25zdCBzdG9yZWQgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnbW9ja0NvbXBvbmVudExpYnJhcnknKVxuICAgIGlmIChzdG9yZWQpIHtcbiAgICAgIHJldHVybiBKU09OLnBhcnNlKHN0b3JlZClcbiAgICB9XG4gICAgcmV0dXJuIHRoaXMuZGVmYXVsdENvbXBvbmVudExpYnJhcnlcbiAgfVxuXG4gIHN0YXRpYyBzYXZlQ29tcG9uZW50TGlicmFyeShjb21wb25lbnRzOiBDb21wb25lbnRMaWJyYXJ5SXRlbVtdKTogdm9pZCB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgnbW9ja0NvbXBvbmVudExpYnJhcnknLCBKU09OLnN0cmluZ2lmeShjb21wb25lbnRzKSlcbiAgICB9XG4gIH1cblxuICAvLyDYp9mE2KjZitin2YbYp9iqINin2YTYp9mB2KrYsdin2LbZitipINmE2YbZhdin2LDYrCDYp9mE2LDZg9in2KEg2KfZhNin2LXYt9mG2KfYudmKICjZgdin2LHYutipINmE2YTYqNiv2KfZitipKVxuICBzdGF0aWMgZGVmYXVsdEFJTW9kZWxzOiBBSU1vZGVsW10gPSBbXVxuXG4gIHN0YXRpYyBkZWZhdWx0TW9kZWxBY3Rpdml0aWVzOiBNb2RlbEFjdGl2aXR5W10gPSBbXVxuXG4gIC8vINin2YTYqNmK2KfZhtin2Kog2KfZhNin2YHYqtix2KfYttmK2Kkg2YTZgtmI2KfZhNioINin2YTYtdmB2K3Yp9iqXG4gIHN0YXRpYyBkZWZhdWx0UGFnZVRlbXBsYXRlczogUGFnZVRlbXBsYXRlW10gPSBbXG4gICAge1xuICAgICAgaWQ6ICd0ZW1wbGF0ZS1sYW5kaW5nLTEnLFxuICAgICAgbmFtZTogJ0xhbmRpbmcgUGFnZSAtIE1vZGVybicsXG4gICAgICBuYW1lQXI6ICfYtdmB2K3YqSDZh9io2YjYtyAtINi52LXYsdmK2KknLFxuICAgICAgbmFtZUVuOiAnTGFuZGluZyBQYWdlIC0gTW9kZXJuJyxcbiAgICAgIG5hbWVGcjogJ1BhZ2UgZFxcJ2F0dGVycmlzc2FnZSAtIE1vZGVybmUnLFxuICAgICAgZGVzY3JpcHRpb246ICfZgtin2YTYqCDYtdmB2K3YqSDZh9io2YjYtyDYudi12LHZitipINmF2Lkg2KrYtdmF2YrZhSDYrNiw2KfYqCcsXG4gICAgICBjYXRlZ29yeTogJ2xhbmRpbmcnLFxuICAgICAgY29tcG9uZW50czogW1xuICAgICAgICB7XG4gICAgICAgICAgaWQ6ICdoZXJvLTEnLFxuICAgICAgICAgIHR5cGU6ICdoZXJvJyxcbiAgICAgICAgICBuYW1lOiAnSGVybyBTZWN0aW9uJyxcbiAgICAgICAgICBwcm9wczoge1xuICAgICAgICAgICAgY29udGVudDogJ9mF2LHYrdio2KfZiyDYqNmD2YUg2YHZiiDZhdmG2LXYqSDYo9iy2YrYp9ihINin2YTYqtiu2LHYrCcsXG4gICAgICAgICAgICBzdHlsZTogeyBiYWNrZ3JvdW5kQ29sb3I6ICcjMUYyOTM3JywgY29sb3I6ICcjRkZGRkZGJyB9XG4gICAgICAgICAgfSxcbiAgICAgICAgICBwb3NpdGlvbjogeyB4OiAwLCB5OiAwIH0sXG4gICAgICAgICAgc2l6ZTogeyB3aWR0aDogJzEwMCUnLCBoZWlnaHQ6ICc1MDBweCcgfSxcbiAgICAgICAgICBpc1Zpc2libGU6IHRydWVcbiAgICAgICAgfVxuICAgICAgXSxcbiAgICAgIHByZXZpZXc6ICcvaW1hZ2VzL3RlbXBsYXRlcy9sYW5kaW5nLW1vZGVybi5qcGcnLFxuICAgICAgdGh1bWJuYWlsOiAnL2ltYWdlcy90ZW1wbGF0ZXMvbGFuZGluZy1tb2Rlcm4tdGh1bWIuanBnJyxcbiAgICAgIGlzQUlHZW5lcmF0ZWQ6IGZhbHNlLFxuICAgICAgaXNQcmVtaXVtOiBmYWxzZSxcbiAgICAgIHRhZ3M6IFsnbGFuZGluZycsICdtb2Rlcm4nLCAnYnVzaW5lc3MnXSxcbiAgICAgIGNyZWF0ZWRBdDogJzIwMjQtMDEtMDFUMDA6MDA6MDBaJyxcbiAgICAgIHVwZGF0ZWRBdDogJzIwMjQtMDEtMDFUMDA6MDA6MDBaJyxcbiAgICAgIHVzYWdlQ291bnQ6IDQ1LFxuICAgICAgcmF0aW5nOiA0LjgsXG4gICAgICBtZXRhZGF0YToge1xuICAgICAgICBjb2xvcnM6IFsnIzFGMjkzNycsICcjRkZGRkZGJywgJyMzQjgyRjYnXSxcbiAgICAgICAgZm9udHM6IFsnSW50ZXInLCAnQ2Fpcm8nXSxcbiAgICAgICAgbGF5b3V0OiAnc2luZ2xlLXBhZ2UnLFxuICAgICAgICByZXNwb25zaXZlOiB0cnVlXG4gICAgICB9XG4gICAgfVxuICBdXG5cbiAgLy8g2KfZhNio2YrYp9mG2KfYqiDYp9mE2KfZgdiq2LHYp9i22YrYqSDZhNmF2LTYp9ix2YrYuSDYp9mE2LXZgdit2KfYqlxuICBzdGF0aWMgZGVmYXVsdFBhZ2VQcm9qZWN0czogUGFnZVByb2plY3RbXSA9IFtcbiAgICB7XG4gICAgICBpZDogJ3Byb2plY3QtMScsXG4gICAgICBuYW1lOiAn2YXZiNmC2Lkg2KPYstmK2KfYoSDYp9mE2KrYrtix2Kwg2KfZhNix2KbZitiz2YonLFxuICAgICAgZGVzY3JpcHRpb246ICfYp9mE2YXZiNmC2Lkg2KfZhNix2KbZitiz2Yog2YTYudix2LYg2YXZhtiq2KzYp9iqINij2LLZitin2KEg2KfZhNiq2K7YsdisJyxcbiAgICAgIGNvbXBvbmVudHM6IFtdLFxuICAgICAgdGVtcGxhdGVJZDogJ3RlbXBsYXRlLWxhbmRpbmctMScsXG4gICAgICBnZW5lcmF0aW9uTW9kZTogJ3RlbXBsYXRlJyxcbiAgICAgIHNldHRpbmdzOiB7XG4gICAgICAgIHRpdGxlOiAn2KPYstmK2KfYoSDYp9mE2KrYrtix2KwgLSDZhdmG2LXYqSDZhdi62LHYqNmK2Kkg2YXYqtiu2LXYtdipJyxcbiAgICAgICAgZGVzY3JpcHRpb246ICfYo9mI2YQg2YXZhti12Kkg2YXYutix2KjZitipINmE2KrYo9is2YrYsSDZiNio2YrYuSDYo9iy2YrYp9ihINin2YTYqtiu2LHYrCcsXG4gICAgICAgIGtleXdvcmRzOiBbJ9ij2LLZitin2KEg2KfZhNiq2K7YsdisJywgJ9iq2KPYrNmK2LEnLCAn2KfZhNmF2LrYsdioJ10sXG4gICAgICAgIGxhbmd1YWdlOiAnYXInLFxuICAgICAgICBkaXJlY3Rpb246ICdydGwnXG4gICAgICB9LFxuICAgICAgaXNQdWJsaXNoZWQ6IGZhbHNlLFxuICAgICAgY3JlYXRlZEF0OiAnMjAyNC0wMS0xNVQwMDowMDowMFonLFxuICAgICAgdXBkYXRlZEF0OiAnMjAyNC0wMS0yMFQxMDozMDowMFonLFxuICAgICAgY3JlYXRlZEJ5OiAnYWRtaW4tMScsXG4gICAgICB2ZXJzaW9uOiAxXG4gICAgfVxuICBdXG5cbiAgLy8g2KfZhNio2YrYp9mG2KfYqiDYp9mE2KfZgdiq2LHYp9i22YrYqSDZhNmF2YPYqtio2Kkg2KfZhNmF2YPZiNmG2KfYqlxuICBzdGF0aWMgZGVmYXVsdENvbXBvbmVudExpYnJhcnk6IENvbXBvbmVudExpYnJhcnlJdGVtW10gPSBbXG4gICAge1xuICAgICAgaWQ6ICdjb21wLWhlcm8nLFxuICAgICAgbmFtZTogJ0hlcm8gU2VjdGlvbicsXG4gICAgICBuYW1lQXI6ICfZgtiz2YUg2KfZhNio2LfZhCcsXG4gICAgICB0eXBlOiAnaGVybycsXG4gICAgICBjYXRlZ29yeTogJ2xheW91dCcsXG4gICAgICBkZXNjcmlwdGlvbjogJ9mC2LPZhSDYsdim2YrYs9mKINis2LDYp9ioINmB2Yog2KPYudmE2Ykg2KfZhNi12YHYrdipJyxcbiAgICAgIGljb246ICdMYXlvdXQnLFxuICAgICAgcHJldmlldzogJy9pbWFnZXMvY29tcG9uZW50cy9oZXJvLXByZXZpZXcuanBnJyxcbiAgICAgIGRlZmF1bHRQcm9wczoge1xuICAgICAgICBjb250ZW50OiAn2LnZhtmI2KfZhiDYsdim2YrYs9mKINis2LDYp9ioJyxcbiAgICAgICAgc3R5bGU6IHsgYmFja2dyb3VuZENvbG9yOiAnIzFGMjkzNycsIGNvbG9yOiAnI0ZGRkZGRicgfVxuICAgICAgfSxcbiAgICAgIGRlZmF1bHRTaXplOiB7IHdpZHRoOiAnMTAwJScsIGhlaWdodDogJzUwMHB4JyB9LFxuICAgICAgaXNDdXN0b206IGZhbHNlLFxuICAgICAgaXNQcmVtaXVtOiBmYWxzZSxcbiAgICAgIHRhZ3M6IFsnbGF5b3V0JywgJ2hlYWRlcicsICdoZXJvJ10sXG4gICAgICB1c2FnZUNvdW50OiAxNTZcbiAgICB9LFxuICAgIHtcbiAgICAgIGlkOiAnY29tcC1idXR0b24nLFxuICAgICAgbmFtZTogJ0J1dHRvbicsXG4gICAgICBuYW1lQXI6ICfYstixJyxcbiAgICAgIHR5cGU6ICdidXR0b24nLFxuICAgICAgY2F0ZWdvcnk6ICdpbnRlcmFjdGl2ZScsXG4gICAgICBkZXNjcmlwdGlvbjogJ9iy2LEg2KrZgdin2LnZhNmKINmC2KfYqNmEINmE2YTYqtiu2LXZiti1JyxcbiAgICAgIGljb246ICdNb3VzZVBvaW50ZXInLFxuICAgICAgcHJldmlldzogJy9pbWFnZXMvY29tcG9uZW50cy9idXR0b24tcHJldmlldy5qcGcnLFxuICAgICAgZGVmYXVsdFByb3BzOiB7XG4gICAgICAgIGNvbnRlbnQ6ICfYp9mG2YLYsSDZh9mG2KcnLFxuICAgICAgICBzdHlsZTogeyBiYWNrZ3JvdW5kQ29sb3I6ICcjM0I4MkY2JywgY29sb3I6ICcjRkZGRkZGJyB9XG4gICAgICB9LFxuICAgICAgZGVmYXVsdFNpemU6IHsgd2lkdGg6ICcxMjBweCcsIGhlaWdodDogJzQwcHgnIH0sXG4gICAgICBpc0N1c3RvbTogZmFsc2UsXG4gICAgICBpc1ByZW1pdW06IGZhbHNlLFxuICAgICAgdGFnczogWydpbnRlcmFjdGl2ZScsICdidXR0b24nLCAnY3RhJ10sXG4gICAgICB1c2FnZUNvdW50OiAyMzRcbiAgICB9XG4gIF1cblxuICAvLyDYpdiv2KfYsdipINmF2LLZiNiv2Yog2KfZhNiw2YPYp9ihINin2YTYp9i12LfZhtin2LnZilxuICBzdGF0aWMgZ2V0QUlQcm92aWRlcnMoKTogTW9ja0FJUHJvdmlkZXJbXSB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSByZXR1cm4gW11cblxuICAgIGNvbnN0IHN0b3JlZCA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKHRoaXMuZ2V0U3RvcmFnZUtleSgnYWlQcm92aWRlcnMnKSlcbiAgICByZXR1cm4gc3RvcmVkID8gSlNPTi5wYXJzZShzdG9yZWQpIDogW11cbiAgfVxuXG4gIHN0YXRpYyBhZGRBSVByb3ZpZGVyKHByb3ZpZGVyOiBNb2NrQUlQcm92aWRlcik6IE1vY2tBSVByb3ZpZGVyIHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHJldHVybiBwcm92aWRlclxuXG4gICAgY29uc3QgcHJvdmlkZXJzID0gdGhpcy5nZXRBSVByb3ZpZGVycygpXG4gICAgcHJvdmlkZXJzLnB1c2gocHJvdmlkZXIpXG4gICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0odGhpcy5nZXRTdG9yYWdlS2V5KCdhaVByb3ZpZGVycycpLCBKU09OLnN0cmluZ2lmeShwcm92aWRlcnMpKVxuICAgIHJldHVybiBwcm92aWRlclxuICB9XG5cbiAgc3RhdGljIHVwZGF0ZUFJUHJvdmlkZXIoaWQ6IHN0cmluZywgdXBkYXRlZFByb3ZpZGVyOiBQYXJ0aWFsPE1vY2tBSVByb3ZpZGVyPik6IE1vY2tBSVByb3ZpZGVyIHwgbnVsbCB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSByZXR1cm4gbnVsbFxuXG4gICAgY29uc3QgcHJvdmlkZXJzID0gdGhpcy5nZXRBSVByb3ZpZGVycygpXG4gICAgY29uc3QgaW5kZXggPSBwcm92aWRlcnMuZmluZEluZGV4KHAgPT4gcC5pZCA9PT0gaWQpXG5cbiAgICBpZiAoaW5kZXggPT09IC0xKSByZXR1cm4gbnVsbFxuXG4gICAgcHJvdmlkZXJzW2luZGV4XSA9IHsgLi4ucHJvdmlkZXJzW2luZGV4XSwgLi4udXBkYXRlZFByb3ZpZGVyIH1cbiAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSh0aGlzLmdldFN0b3JhZ2VLZXkoJ2FpUHJvdmlkZXJzJyksIEpTT04uc3RyaW5naWZ5KHByb3ZpZGVycykpXG4gICAgcmV0dXJuIHByb3ZpZGVyc1tpbmRleF1cbiAgfVxuXG4gIHN0YXRpYyBkZWxldGVBSVByb3ZpZGVyKGlkOiBzdHJpbmcpOiBib29sZWFuIHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHJldHVybiBmYWxzZVxuXG4gICAgY29uc3QgcHJvdmlkZXJzID0gdGhpcy5nZXRBSVByb3ZpZGVycygpXG4gICAgY29uc3QgZmlsdGVyZWRQcm92aWRlcnMgPSBwcm92aWRlcnMuZmlsdGVyKHAgPT4gcC5pZCAhPT0gaWQpXG5cbiAgICBpZiAoZmlsdGVyZWRQcm92aWRlcnMubGVuZ3RoID09PSBwcm92aWRlcnMubGVuZ3RoKSByZXR1cm4gZmFsc2VcblxuICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKHRoaXMuZ2V0U3RvcmFnZUtleSgnYWlQcm92aWRlcnMnKSwgSlNPTi5zdHJpbmdpZnkoZmlsdGVyZWRQcm92aWRlcnMpKVxuICAgIHJldHVybiB0cnVlXG4gIH1cblxuICBzdGF0aWMgZ2V0QUlQcm92aWRlckJ5SWQoaWQ6IHN0cmluZyk6IE1vY2tBSVByb3ZpZGVyIHwgbnVsbCB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSByZXR1cm4gbnVsbFxuXG4gICAgY29uc3QgcHJvdmlkZXJzID0gdGhpcy5nZXRBSVByb3ZpZGVycygpXG4gICAgcmV0dXJuIHByb3ZpZGVycy5maW5kKHAgPT4gcC5pZCA9PT0gaWQpIHx8IG51bGxcbiAgfVxufVxuIl0sIm5hbWVzIjpbIm1vY2tQYWdlcyIsImlkIiwic2x1ZyIsImlzX3B1Ymxpc2hlZCIsImF1dGhvcl9pZCIsImZlYXR1cmVkX2ltYWdlIiwiY3JlYXRlZF9hdCIsInVwZGF0ZWRfYXQiLCJwcm9maWxlcyIsImZ1bGxfbmFtZSIsInBhZ2VfY29udGVudCIsInBhZ2VfaWQiLCJsYW5ndWFnZSIsInRpdGxlIiwiY29udGVudCIsIm1ldGFfZGVzY3JpcHRpb24iLCJtZXRhX2tleXdvcmRzIiwibW9ja01lbnVJdGVtcyIsInRpdGxlX2FyIiwidGl0bGVfZW4iLCJ0aXRsZV9mciIsImljb24iLCJvcmRlcl9pbmRleCIsImlzX2FjdGl2ZSIsInRhcmdldF90eXBlIiwidGFyZ2V0X3ZhbHVlIiwicGFyZW50X2lkIiwibW9ja0NhdGVnb3JpZXMiLCJuYW1lX2FyIiwibmFtZV9lbiIsIm5hbWVfZnIiLCJkZXNjcmlwdGlvbiIsIm1vY2tQcm9kdWN0cyIsIm5hbWUiLCJjYXRlZ29yeSIsInByaWNlIiwicmVudGFsX3ByaWNlIiwiY29sb3JzIiwic2l6ZXMiLCJpbWFnZXMiLCJzdG9ja19xdWFudGl0eSIsImlzX2F2YWlsYWJsZSIsInJhdGluZyIsInJldmlld3NfY291bnQiLCJmZWF0dXJlcyIsInNwZWNpZmljYXRpb25zIiwibWF0ZXJpYWwiLCJ3ZWlnaHQiLCJjYXJlIiwidGFzc2VsX2NvbG9yIiwiYWRqdXN0YWJsZSIsImVtYnJvaWRlcnkiLCJsZW5ndGgiLCJhdHRhY2htZW50IiwibGluaW5nIiwiYWNhZGVtaWNfbGV2ZWwiLCJtb2NrU2Nob29scyIsImFkbWluX2lkIiwiYWRkcmVzcyIsImNpdHkiLCJwaG9uZSIsImVtYWlsIiwid2Vic2l0ZSIsImxvZ29fdXJsIiwiZ3JhZHVhdGlvbl9kYXRlIiwic3R1ZGVudF9jb3VudCIsInNldHRpbmdzIiwiZ3JhZHVhdGlvbl9jZXJlbW9ueV9sb2NhdGlvbiIsImRyZXNzX2NvZGUiLCJwaG90b2dyYXBoeV9hbGxvd2VkIiwibW9ja09yZGVycyIsIm9yZGVyX251bWJlciIsImN1c3RvbWVyX2lkIiwiY3VzdG9tZXJfbmFtZSIsImN1c3RvbWVyX2VtYWlsIiwiY3VzdG9tZXJfcGhvbmUiLCJzdGF0dXMiLCJpdGVtcyIsIm9yZGVyX2lkIiwicHJvZHVjdF9pZCIsInByb2R1Y3RfbmFtZSIsInByb2R1Y3RfaW1hZ2UiLCJxdWFudGl0eSIsInVuaXRfcHJpY2UiLCJ0b3RhbF9wcmljZSIsImN1c3RvbWl6YXRpb25zIiwiY29sb3IiLCJzaXplIiwic3VidG90YWwiLCJ0YXgiLCJzaGlwcGluZ19jb3N0IiwidG90YWwiLCJwYXltZW50X3N0YXR1cyIsInBheW1lbnRfbWV0aG9kIiwic2hpcHBpbmdfYWRkcmVzcyIsInN0cmVldCIsInN0YXRlIiwicG9zdGFsX2NvZGUiLCJjb3VudHJ5IiwidHJhY2tpbmdfbnVtYmVyIiwibm90ZXMiLCJkZWxpdmVyeV9kYXRlIiwic2Nob29sX2lkIiwic2Nob29sX25hbWUiLCJNb2NrRGF0YU1hbmFnZXIiLCJnZXRTdG9yYWdlS2V5IiwidHlwZSIsImdldFBhZ2VzIiwic3RvcmVkIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsIkpTT04iLCJwYXJzZSIsImdldE1lbnVJdGVtcyIsImdldFByb2R1Y3RzIiwiY29uc29sZSIsImxvZyIsInByb2R1Y3RzIiwibWFwIiwicCIsImdldENhdGVnb3JpZXMiLCJnZXRTY2hvb2xzIiwiZ2V0T3JkZXJzIiwic2F2ZVBhZ2VzIiwicGFnZXMiLCJzZXRJdGVtIiwic3RyaW5naWZ5Iiwic2F2ZU1lbnVJdGVtcyIsInNhdmVQcm9kdWN0cyIsInNhdmVkIiwicGFyc2VkU2F2ZWQiLCJzYXZlQ2F0ZWdvcmllcyIsImNhdGVnb3JpZXMiLCJzYXZlU2Nob29scyIsInNjaG9vbHMiLCJzYXZlT3JkZXJzIiwib3JkZXJzIiwiZ2VuZXJhdGVJZCIsIkRhdGUiLCJub3ciLCJ0b1N0cmluZyIsIk1hdGgiLCJyYW5kb20iLCJzdWJzdHJpbmciLCJjbGVhckFsbERhdGEiLCJyZW1vdmVJdGVtIiwiZ2VuZXJhdGVPcmRlck51bWJlciIsImRhdGUiLCJ5ZWFyIiwiZ2V0RnVsbFllYXIiLCJzbGljZSIsIm1vbnRoIiwiZ2V0TW9udGgiLCJwYWRTdGFydCIsImRheSIsImdldERhdGUiLCJ0b2RheU9yZGVycyIsImZpbHRlciIsIm9yZGVyIiwic3RhcnRzV2l0aCIsIm9yZGVyQ291bnQiLCJnZXRBSU1vZGVscyIsImRlZmF1bHRBSU1vZGVscyIsInNhdmVBSU1vZGVscyIsIm1vZGVscyIsImdldE1vZGVsQWN0aXZpdGllcyIsImRlZmF1bHRNb2RlbEFjdGl2aXRpZXMiLCJzYXZlTW9kZWxBY3Rpdml0aWVzIiwiYWN0aXZpdGllcyIsImdldFBhZ2VUZW1wbGF0ZXMiLCJkZWZhdWx0UGFnZVRlbXBsYXRlcyIsInNhdmVQYWdlVGVtcGxhdGVzIiwidGVtcGxhdGVzIiwiZ2V0UGFnZVByb2plY3RzIiwiZGVmYXVsdFBhZ2VQcm9qZWN0cyIsInNhdmVQYWdlUHJvamVjdHMiLCJwcm9qZWN0cyIsImdldENvbXBvbmVudExpYnJhcnkiLCJkZWZhdWx0Q29tcG9uZW50TGlicmFyeSIsInNhdmVDb21wb25lbnRMaWJyYXJ5IiwiY29tcG9uZW50cyIsIm5hbWVBciIsIm5hbWVFbiIsIm5hbWVGciIsInByb3BzIiwic3R5bGUiLCJiYWNrZ3JvdW5kQ29sb3IiLCJwb3NpdGlvbiIsIngiLCJ5Iiwid2lkdGgiLCJoZWlnaHQiLCJpc1Zpc2libGUiLCJwcmV2aWV3IiwidGh1bWJuYWlsIiwiaXNBSUdlbmVyYXRlZCIsImlzUHJlbWl1bSIsInRhZ3MiLCJjcmVhdGVkQXQiLCJ1cGRhdGVkQXQiLCJ1c2FnZUNvdW50IiwibWV0YWRhdGEiLCJmb250cyIsImxheW91dCIsInJlc3BvbnNpdmUiLCJ0ZW1wbGF0ZUlkIiwiZ2VuZXJhdGlvbk1vZGUiLCJrZXl3b3JkcyIsImRpcmVjdGlvbiIsImlzUHVibGlzaGVkIiwiY3JlYXRlZEJ5IiwidmVyc2lvbiIsImRlZmF1bHRQcm9wcyIsImRlZmF1bHRTaXplIiwiaXNDdXN0b20iLCJnZXRBSVByb3ZpZGVycyIsImFkZEFJUHJvdmlkZXIiLCJwcm92aWRlciIsInByb3ZpZGVycyIsInB1c2giLCJ1cGRhdGVBSVByb3ZpZGVyIiwidXBkYXRlZFByb3ZpZGVyIiwiaW5kZXgiLCJmaW5kSW5kZXgiLCJkZWxldGVBSVByb3ZpZGVyIiwiZmlsdGVyZWRQcm92aWRlcnMiLCJnZXRBSVByb3ZpZGVyQnlJZCIsImZpbmQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/mockData.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmenu-items%2F%5Bid%5D%2Froute&page=%2Fapi%2Fmenu-items%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmenu-items%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CGraduation%20Toqs%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CGraduation%20Toqs%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();