"use strict";(()=>{var e={};e.id=451,e.ids=[451],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},81182:(e,t,s)=>{s.r(t),s.d(t,{patchFetch:()=>m,routeModule:()=>c,serverHooks:()=>p,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>g});var a={};s.r(a),s.d(a,{GET:()=>i});var o=s(96559),r=s(48088),u=s(37719),n=s(32190),l=s(38561);async function i(e){try{let{searchParams:t}=new URL(e.url),s=t.get("model_id"),a=t.get("provider"),o=t.get("date_from"),r=t.get("date_to"),u=t.get("type"),i=t.get("group_by")||"day",c="true"===t.get("include_details"),d=l.CN.getAIModels(),g=l.CN.getModelActivities(),p=d;s&&(p=d.filter(e=>e.id===s)),a&&(p=p.filter(e=>e.provider===a)),u&&(p=p.filter(e=>e.type===u));let m=g.filter(e=>"request"===e.type);s&&(m=m.filter(e=>e.modelId===s)),o&&(m=m.filter(e=>e.timestamp>=o)),r&&(m=m.filter(e=>e.timestamp<=r));let R={totalRequests:p.reduce((e,t)=>e+t.usage.totalRequests,0),totalTokens:p.reduce((e,t)=>e+t.usage.totalTokens,0),totalCost:p.reduce((e,t)=>e+t.usage.totalCost,0),averageResponseTime:p.length>0?Math.round(p.reduce((e,t)=>e+t.usage.averageResponseTime,0)/p.length):0,successRate:p.length>0?Math.round(p.reduce((e,t)=>e+t.usage.successRate,0)/p.length):0},h=p.reduce((e,t)=>{let s=t.provider;return e[s]||(e[s]={models:0,totalRequests:0,totalTokens:0,totalCost:0,averageResponseTime:0,successRate:0}),e[s].models+=1,e[s].totalRequests+=t.usage.totalRequests,e[s].totalTokens+=t.usage.totalTokens,e[s].totalCost+=t.usage.totalCost,e[s].averageResponseTime+=t.usage.averageResponseTime,e[s].successRate+=t.usage.successRate,e},{});Object.keys(h).forEach(e=>{let t=h[e];t.averageResponseTime=Math.round(t.averageResponseTime/t.models),t.successRate=Math.round(t.successRate/t.models)});let q=p.reduce((e,t)=>{let s=t.type;return e[s]||(e[s]={models:0,totalRequests:0,totalTokens:0,totalCost:0}),e[s].models+=1,e[s].totalRequests+=t.usage.totalRequests,e[s].totalTokens+=t.usage.totalTokens,e[s].totalCost+=t.usage.totalCost,e},{}),v=function(e,t,s,a){let o=new Date,r=s?new Date(s):o,u=new Date(t||o.getTime()-2592e6),n=[],l=new Date(u);for(;l<=r;){let t=l.toISOString().split("T")[0],s=Math.floor(100*Math.random())+10,a=s*(Math.floor(500*Math.random())+100),o=.001*a*(.01*Math.random()+.001),r=Math.floor(5*Math.random());n.push({date:t,requests:s,tokens:a,cost:Math.round(100*o)/100,errors:r,successRate:Math.round((s-r)/s*100)}),"day"===e?l.setDate(l.getDate()+1):"week"===e?l.setDate(l.getDate()+7):"month"===e&&l.setMonth(l.getMonth()+1)}return n}(i,o,r,0),f=p.filter(e=>e.usage.totalRequests>0).sort((e,t)=>t.usage.totalRequests-e.usage.totalRequests).slice(0,10).map(e=>({id:e.id,name:e.name,provider:e.provider,type:e.type,totalRequests:e.usage.totalRequests,totalCost:e.usage.totalCost,successRate:e.usage.successRate,averageResponseTime:e.usage.averageResponseTime,lastUsed:e.usage.lastUsed})),T=p.filter(e=>e.usage.totalCost>0).sort((e,t)=>t.usage.totalCost-e.usage.totalCost).slice(0,5).map(e=>({id:e.id,name:e.name,provider:e.provider,totalCost:e.usage.totalCost,totalRequests:e.usage.totalRequests,costPerRequest:e.usage.totalRequests>0?e.usage.totalCost/e.usage.totalRequests:0})),M=function(e){if(e.length<2)return{requests:"stable",cost:"stable",successRate:"stable"};let t=e.slice(-7),s=e.slice(-14,-7),a={requests:t.reduce((e,t)=>e+t.requests,0)/t.length,cost:t.reduce((e,t)=>e+t.cost,0)/t.length,successRate:t.reduce((e,t)=>e+t.successRate,0)/t.length},o={requests:s.reduce((e,t)=>e+t.requests,0)/s.length,cost:s.reduce((e,t)=>e+t.cost,0)/s.length,successRate:s.reduce((e,t)=>e+t.successRate,0)/s.length},r=(e,t)=>{let s=(e-t)/t*100;return s>10?"increasing":s<-10?"decreasing":"stable"};return{requests:r(a.requests,o.requests),cost:r(a.cost,o.cost),successRate:r(a.successRate,o.successRate),changes:{requests:Math.round((a.requests-o.requests)/o.requests*100),cost:Math.round((a.cost-o.cost)/o.cost*100),successRate:Math.round((a.successRate-o.successRate)/o.successRate*100)}}}(v),C={summary:R,byProvider:h,byType:q,timeSeries:v,topModels:f,mostExpensive:T,trends:M,period:{from:o||"all-time",to:r||new Date().toISOString(),groupBy:i}};return c&&(C.models=p.map(e=>({id:e.id,name:e.name,provider:e.provider,type:e.type,status:e.status,usage:e.usage,lastTestedAt:e.lastTestedAt,testResult:e.testResult})),C.recentActivities=m.sort((e,t)=>new Date(t.timestamp).getTime()-new Date(e.timestamp).getTime()).slice(0,50)),n.NextResponse.json(C)}catch(e){return console.error("Error fetching usage statistics:",e),n.NextResponse.json({error:"خطأ في جلب إحصائيات الاستخدام"},{status:500})}}let c=new o.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/ai-models/usage/route",pathname:"/api/ai-models/usage",filename:"route",bundlePath:"app/api/ai-models/usage/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\ai-models\\usage\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:d,workUnitAsyncStorage:g,serverHooks:p}=c;function m(){return(0,u.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:g})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[4447,580,8554],()=>s(81182));module.exports=a})();