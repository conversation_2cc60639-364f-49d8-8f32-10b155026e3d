"use strict";(()=>{var e={};e.id=4341,e.ids=[4341],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},23878:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>N,routeModule:()=>d,serverHooks:()=>x,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>f});var s={};r.r(s),r.d(s,{DELETE:()=>m,GET:()=>u,POST:()=>l,PUT:()=>g});var a=r(96559),n=r(48088),o=r(37719),i=r(32190),p=r(38561);async function u(e){try{let{searchParams:t}=new URL(e.url),r=t.get("category");t.get("language");let s="true"===t.get("include_premium"),a=t.get("sort_by")||"usageCount",n=t.get("sort_order")||"desc",o=parseInt(t.get("page")||"1"),u=parseInt(t.get("limit")||"20"),l=p.CN.getPageTemplates();r&&(l=l.filter(e=>e.category===r)),s||(l=l.filter(e=>!e.isPremium)),l.sort((e,t)=>{let r=e[a],s=t[a];return"desc"===n?s-r:r-s});let g=(o-1)*u,m=l.slice(g,g+u),d=Array.from(new Set(p.CN.getPageTemplates().map(e=>e.category))).map(e=>({category:e,count:p.CN.getPageTemplates().filter(t=>t.category===e).length,premiumCount:p.CN.getPageTemplates().filter(t=>t.category===e&&t.isPremium).length}));return i.NextResponse.json({templates:m,total:l.length,page:o,limit:u,totalPages:Math.ceil(l.length/u),categories:d,stats:{total:p.CN.getPageTemplates().length,free:p.CN.getPageTemplates().filter(e=>!e.isPremium).length,premium:p.CN.getPageTemplates().filter(e=>e.isPremium).length,aiGenerated:p.CN.getPageTemplates().filter(e=>e.isAIGenerated).length}})}catch(e){return console.error("Error fetching page templates:",e),i.NextResponse.json({error:"خطأ في جلب قوالب الصفحات"},{status:500})}}async function l(e){try{let{name:t,nameAr:r,nameEn:s,nameFr:a,description:n,category:o,components:u,preview:l,thumbnail:g,isAIGenerated:m,isPremium:d,tags:c,metadata:f}=await e.json();if(!t||!r||!o||!u)return i.NextResponse.json({error:"الاسم والفئة والمكونات مطلوبة"},{status:400});let x=p.CN.getPageTemplates();if(x.find(e=>e.name.toLowerCase()===t.toLowerCase()||e.nameAr.toLowerCase()===r.toLowerCase()))return i.NextResponse.json({error:"قالب بنفس الاسم موجود بالفعل"},{status:400});let N={id:p.CN.generateId(),name:t,nameAr:r,nameEn:s,nameFr:a,description:n,category:o,components:u.map(e=>({...e,id:e.id||p.CN.generateId()})),preview:l||"/images/templates/default-preview.jpg",thumbnail:g||"/images/templates/default-thumb.jpg",isAIGenerated:m||!1,isPremium:d||!1,tags:c||[],createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),usageCount:0,rating:0,metadata:f||{colors:[],fonts:[],layout:"single-page",responsive:!0}};return x.push(N),p.CN.savePageTemplates(x),i.NextResponse.json({message:"تم إنشاء القالب بنجاح",template:N},{status:201})}catch(e){return console.error("Error creating page template:",e),i.NextResponse.json({error:"خطأ في إنشاء القالب"},{status:500})}}async function g(e){try{let t=await e.json(),{templateId:r,action:s}=t;if(!r||!s)return i.NextResponse.json({error:"معرف القالب والإجراء مطلوبان"},{status:400});let a=p.CN.getPageTemplates(),n=a.findIndex(e=>e.id===r);if(-1===n)return i.NextResponse.json({error:"القالب غير موجود"},{status:404});let o=a[n];switch(s){case"increment_usage":o.usageCount+=1;break;case"rate":let{rating:u}=t;u>=1&&u<=5&&(o.rating=u);break;default:return i.NextResponse.json({error:"إجراء غير مدعوم"},{status:400})}return o.updatedAt=new Date().toISOString(),a[n]=o,p.CN.savePageTemplates(a),i.NextResponse.json({message:"تم تحديث القالب بنجاح",template:o})}catch(e){return console.error("Error updating template:",e),i.NextResponse.json({error:"خطأ في تحديث القالب"},{status:500})}}async function m(e){try{let{searchParams:t}=new URL(e.url),r=t.get("id");if(!r)return i.NextResponse.json({error:"معرف القالب مطلوب"},{status:400});let s=p.CN.getPageTemplates(),a=s.findIndex(e=>e.id===r);if(-1===a)return i.NextResponse.json({error:"القالب غير موجود"},{status:404});let n=s[a],o=p.CN.getPageProjects().filter(e=>e.templateId===r);if(o.length>0)return i.NextResponse.json({error:"لا يمكن حذف القالب لأنه مستخدم في مشاريع موجودة",usedInProjects:o.map(e=>({id:e.id,name:e.name}))},{status:400});return s.splice(a,1),p.CN.savePageTemplates(s),i.NextResponse.json({message:"تم حذف القالب بنجاح",deletedTemplate:{id:n.id,name:n.name,nameAr:n.nameAr}})}catch(e){return console.error("Error deleting template:",e),i.NextResponse.json({error:"خطأ في حذف القالب"},{status:500})}}let d=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/page-builder/templates/route",pathname:"/api/page-builder/templates",filename:"route",bundlePath:"app/api/page-builder/templates/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\page-builder\\templates\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:c,workUnitAsyncStorage:f,serverHooks:x}=d;function N(){return(0,o.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:f})}},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,8554],()=>r(23878));module.exports=s})();