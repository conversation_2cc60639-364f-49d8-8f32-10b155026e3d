"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2166],{4884:(e,t,r)=>{r.d(t,{bL:()=>w,zi:()=>j});var n=r(12115),a=r(85185),o=r(6101),l=r(46081),s=r(5845),i=r(45503),c=r(11275),d=r(63655),u=r(95155),p="Switch",[f,h]=(0,l.A)(p),[y,v]=f(p),g=n.forwardRef((e,t)=>{let{__scopeSwitch:r,name:l,checked:i,defaultChecked:c,required:f,disabled:h,value:v="on",onCheckedChange:g,form:m,...b}=e,[w,j]=n.useState(null),D=(0,o.s)(t,e=>j(e)),R=n.useRef(!1),C=!w||m||!!w.closest("form"),[A,M]=(0,s.i)({prop:i,defaultProp:null!=c&&c,onChange:g,caller:p});return(0,u.jsxs)(y,{scope:r,checked:A,disabled:h,children:[(0,u.jsx)(d.sG.button,{type:"button",role:"switch","aria-checked":A,"aria-required":f,"data-state":k(A),"data-disabled":h?"":void 0,disabled:h,value:v,...b,ref:D,onClick:(0,a.m)(e.onClick,e=>{M(e=>!e),C&&(R.current=e.isPropagationStopped(),R.current||e.stopPropagation())})}),C&&(0,u.jsx)(x,{control:w,bubbles:!R.current,name:l,value:v,checked:A,required:f,disabled:h,form:m,style:{transform:"translateX(-100%)"}})]})});g.displayName=p;var m="SwitchThumb",b=n.forwardRef((e,t)=>{let{__scopeSwitch:r,...n}=e,a=v(m,r);return(0,u.jsx)(d.sG.span,{"data-state":k(a.checked),"data-disabled":a.disabled?"":void 0,...n,ref:t})});b.displayName=m;var x=n.forwardRef((e,t)=>{let{__scopeSwitch:r,control:a,checked:l,bubbles:s=!0,...d}=e,p=n.useRef(null),f=(0,o.s)(p,t),h=(0,i.Z)(l),y=(0,c.X)(a);return n.useEffect(()=>{let e=p.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(h!==l&&t){let r=new Event("click",{bubbles:s});t.call(e,l),e.dispatchEvent(r)}},[h,l,s]),(0,u.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:l,...d,tabIndex:-1,ref:f,style:{...d.style,...y,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function k(e){return e?"checked":"unchecked"}x.displayName="SwitchBubbleInput";var w=g,j=b},5623:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},13717:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},15452:(e,t,r)=>{r.d(t,{G$:()=>X,Hs:()=>k,UC:()=>er,VY:()=>ea,ZL:()=>ee,bL:()=>$,bm:()=>eo,hE:()=>en,hJ:()=>et,l9:()=>Q});var n=r(12115),a=r(85185),o=r(6101),l=r(46081),s=r(61285),i=r(5845),c=r(19178),d=r(25519),u=r(34378),p=r(28905),f=r(63655),h=r(92293),y=r(93795),v=r(38168),g=r(99708),m=r(95155),b="Dialog",[x,k]=(0,l.A)(b),[w,j]=x(b),D=e=>{let{__scopeDialog:t,children:r,open:a,defaultOpen:o,onOpenChange:l,modal:c=!0}=e,d=n.useRef(null),u=n.useRef(null),[p,f]=(0,i.i)({prop:a,defaultProp:null!=o&&o,onChange:l,caller:b});return(0,m.jsx)(w,{scope:t,triggerRef:d,contentRef:u,contentId:(0,s.B)(),titleId:(0,s.B)(),descriptionId:(0,s.B)(),open:p,onOpenChange:f,onOpenToggle:n.useCallback(()=>f(e=>!e),[f]),modal:c,children:r})};D.displayName=b;var R="DialogTrigger",C=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,l=j(R,r),s=(0,o.s)(t,l.triggerRef);return(0,m.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":W(l.open),...n,ref:s,onClick:(0,a.m)(e.onClick,l.onOpenToggle)})});C.displayName=R;var A="DialogPortal",[M,I]=x(A,{forceMount:void 0}),E=e=>{let{__scopeDialog:t,forceMount:r,children:a,container:o}=e,l=j(A,t);return(0,m.jsx)(M,{scope:t,forceMount:r,children:n.Children.map(a,e=>(0,m.jsx)(p.C,{present:r||l.open,children:(0,m.jsx)(u.Z,{asChild:!0,container:o,children:e})}))})};E.displayName=A;var N="DialogOverlay",O=n.forwardRef((e,t)=>{let r=I(N,e.__scopeDialog),{forceMount:n=r.forceMount,...a}=e,o=j(N,e.__scopeDialog);return o.modal?(0,m.jsx)(p.C,{present:n||o.open,children:(0,m.jsx)(P,{...a,ref:t})}):null});O.displayName=N;var _=(0,g.TL)("DialogOverlay.RemoveScroll"),P=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=j(N,r);return(0,m.jsx)(y.A,{as:_,allowPinchZoom:!0,shards:[a.contentRef],children:(0,m.jsx)(f.sG.div,{"data-state":W(a.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),F="DialogContent",G=n.forwardRef((e,t)=>{let r=I(F,e.__scopeDialog),{forceMount:n=r.forceMount,...a}=e,o=j(F,e.__scopeDialog);return(0,m.jsx)(p.C,{present:n||o.open,children:o.modal?(0,m.jsx)(S,{...a,ref:t}):(0,m.jsx)(T,{...a,ref:t})})});G.displayName=F;var S=n.forwardRef((e,t)=>{let r=j(F,e.__scopeDialog),l=n.useRef(null),s=(0,o.s)(t,r.contentRef,l);return n.useEffect(()=>{let e=l.current;if(e)return(0,v.Eq)(e)},[]),(0,m.jsx)(q,{...e,ref:s,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,a.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=r.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,a.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,a.m)(e.onFocusOutside,e=>e.preventDefault())})}),T=n.forwardRef((e,t)=>{let r=j(F,e.__scopeDialog),a=n.useRef(!1),o=n.useRef(!1);return(0,m.jsx)(q,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var n,l;null==(n=e.onCloseAutoFocus)||n.call(e,t),t.defaultPrevented||(a.current||null==(l=r.triggerRef.current)||l.focus(),t.preventDefault()),a.current=!1,o.current=!1},onInteractOutside:t=>{var n,l;null==(n=e.onInteractOutside)||n.call(e,t),t.defaultPrevented||(a.current=!0,"pointerdown"===t.detail.originalEvent.type&&(o.current=!0));let s=t.target;(null==(l=r.triggerRef.current)?void 0:l.contains(s))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}})}),q=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:a,onOpenAutoFocus:l,onCloseAutoFocus:s,...i}=e,u=j(F,r),p=n.useRef(null),f=(0,o.s)(t,p);return(0,h.Oh)(),(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(d.n,{asChild:!0,loop:!0,trapped:a,onMountAutoFocus:l,onUnmountAutoFocus:s,children:(0,m.jsx)(c.qW,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":W(u.open),...i,ref:f,onDismiss:()=>u.onOpenChange(!1)})}),(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(K,{titleId:u.titleId}),(0,m.jsx)(Y,{contentRef:p,descriptionId:u.descriptionId})]})]})}),B="DialogTitle",H=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=j(B,r);return(0,m.jsx)(f.sG.h2,{id:a.titleId,...n,ref:t})});H.displayName=B;var L="DialogDescription",z=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=j(L,r);return(0,m.jsx)(f.sG.p,{id:a.descriptionId,...n,ref:t})});z.displayName=L;var Z="DialogClose",V=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=j(Z,r);return(0,m.jsx)(f.sG.button,{type:"button",...n,ref:t,onClick:(0,a.m)(e.onClick,()=>o.onOpenChange(!1))})});function W(e){return e?"open":"closed"}V.displayName=Z;var U="DialogTitleWarning",[X,J]=(0,l.q)(U,{contentName:F,titleName:B,docsSlug:"dialog"}),K=e=>{let{titleId:t}=e,r=J(U),a="`".concat(r.contentName,"` requires a `").concat(r.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(r.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(r.docsSlug);return n.useEffect(()=>{t&&(document.getElementById(t)||console.error(a))},[a,t]),null},Y=e=>{let{contentRef:t,descriptionId:r}=e,a=J("DialogDescriptionWarning"),o="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(a.contentName,"}.");return n.useEffect(()=>{var e;let n=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");r&&n&&(document.getElementById(r)||console.warn(o))},[o,t,r]),null},$=D,Q=C,ee=E,et=O,er=G,en=H,ea=z,eo=V},35169:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},40968:(e,t,r)=>{r.d(t,{b:()=>s});var n=r(12115),a=r(63655),o=r(95155),l=n.forwardRef((e,t)=>(0,o.jsx)(a.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var s=l},45503:(e,t,r)=>{r.d(t,{Z:()=>a});var n=r(12115);function a(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},54416:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},62525:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},78749:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},84616:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},92657:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}}]);