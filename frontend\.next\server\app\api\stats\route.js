"use strict";(()=>{var t={};t.id=4967,t.ids=[4967],t.modules={3295:t=>{t.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:t=>{t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(t,e,a)=>{a.a(t,async(t,r)=>{try{a.r(e),a.d(e,{GET:()=>c});var s=a(32190),i=a(21253),o=a(63247),n=a(32450),l=a(6710),u=t([i,o,n,l]);async function c(t){try{var e,a;if(!await (0,l.AD)())return s.NextResponse.json({error:"قاعدة البيانات غير متاحة"},{status:503});let t=await o.<PERSON>.getStats(),r=await n.I.getStats(),u=await i.L.getBestSellers(5),c=await i.L.getNewProducts(5),d=await i.L.getTopRated(5),p=await i.L.getAll({limit:1}),E=await i.L.getAll({published:!0,limit:1}),h=await i.L.getAll({available:!0,limit:1}),$={users:{total:t.total,active:t.active,verified:t.verified,byRole:t.byRole,growthRate:(e=t.total,a=t.active,0===e?0:Math.round(a/e*100))},orders:{total:r.total,pending:r.pending,confirmed:r.confirmed,delivered:r.delivered,cancelled:r.cancelled,totalRevenue:r.totalRevenue,averageOrderValue:r.averageOrderValue,conversionRate:t.total>0?r.total/t.total*100:0},products:{total:p.total,published:E.total,available:h.total,publishedRate:p.total>0?E.total/p.total*100:0,availabilityRate:p.total>0?h.total/p.total*100:0},featured:{bestSellers:u.slice(0,5),newProducts:c.slice(0,5),topRated:d.slice(0,5)},overview:{totalRevenue:r.totalRevenue,totalOrders:r.total,totalUsers:t.total,totalProducts:p.total,averageOrderValue:r.averageOrderValue,customerSatisfaction:function(t){if(0===t.length)return 0;let e=t.reduce((t,e)=>t+(e.rating||0),0)/t.length;return Math.round(e/5*100)}(d),platformHealth:function(t,e,a){let r=[t.active/Math.max(t.total,1),e.delivered/Math.max(e.total,1),(e.total-e.cancelled)/Math.max(e.total,1),Math.min(a/100,1)],s=r.reduce((t,e)=>t+e,0)/r.length;return Math.round(100*s)}(t,r,p.total)},metadata:{lastUpdated:new Date().toISOString(),source:"postgresql",version:"1.0.0"}};return s.NextResponse.json($)}catch(t){return console.error("Error fetching stats:",t),s.NextResponse.json({error:"فشل في جلب الإحصائيات"},{status:500})}}[i,o,n,l]=u.then?(await u)():u,r()}catch(t){r(t)}})},21253:(t,e,a)=>{a.a(t,async(t,r)=>{try{a.d(e,{L:()=>o});var s=a(6710),i=t([s]);s=(i.then?(await i)():i)[0];class o{static async getAll(t={}){let e=[],a=[],r=1;t.category&&(e.push(`category_id = $${r}`),a.push(t.category),r++),void 0!==t.available&&(e.push(`is_available = $${r}`),a.push(t.available),r++),void 0!==t.published&&(e.push(`is_published = $${r}`),a.push(t.published),r++),t.search&&(e.push(`(name ILIKE $${r} OR description ILIKE $${r})`),a.push(`%${t.search}%`),r++),void 0!==t.minPrice&&(e.push(`price >= $${r}`),a.push(t.minPrice),r++),void 0!==t.maxPrice&&(e.push(`price <= $${r}`),a.push(t.maxPrice),r++);let i=e.length>0?`WHERE ${e.join(" AND ")}`:"",o=t.sortBy||"created_at",n=t.sortOrder||"DESC",l=`ORDER BY ${o} ${n}`,u=t.limit||50,c=t.offset||0,d=`LIMIT $${r} OFFSET $${r+1}`;a.push(u,c);let p=`SELECT COUNT(*) as total FROM products ${i}`,E=await (0,s.P)(p,a.slice(0,-2)),h=parseInt(E.rows[0].total),$=`
      SELECT 
        id, name, description, category_id, price, rental_price,
        colors, sizes, images, stock_quantity, is_available, is_published,
        features, specifications, rating, reviews_count, created_at, updated_at
      FROM products 
      ${i} 
      ${l} 
      ${d}
    `;return{products:(await (0,s.P)($,a)).rows,total:h}}static async getById(t){return(await (0,s.P)("SELECT * FROM products WHERE id = $1",[t])).rows[0]||null}static async create(t){return(await (0,s.P)(`
      INSERT INTO products (
        name, description, category_id, price, rental_price,
        colors, sizes, images, stock_quantity, is_available, is_published,
        features, specifications, rating, reviews_count
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15
      ) RETURNING *
    `,[t.name,t.description,t.category_id,t.price,t.rental_price,t.colors,t.sizes,t.images,t.stock_quantity,t.is_available,t.is_published,t.features,JSON.stringify(t.specifications),t.rating||0,t.reviews_count||0])).rows[0]}static async update(t,e){let a=[],r=[],i=1;if(Object.entries(e).forEach(([t,e])=>{"id"!==t&&"created_at"!==t&&void 0!==e&&("specifications"===t?(a.push(`${t} = $${i}`),r.push(JSON.stringify(e))):(a.push(`${t} = $${i}`),r.push(e)),i++)}),0===a.length)throw Error("لا توجد حقول للتحديث");return a.push("updated_at = NOW()"),r.push(t),(await (0,s.P)(`
      UPDATE products 
      SET ${a.join(", ")} 
      WHERE id = $${i} 
      RETURNING *
    `,r)).rows[0]||null}static async delete(t){return(await (0,s.P)("DELETE FROM products WHERE id = $1",[t])).rowCount>0}static async updateRating(t){await (0,s.P)(`
      UPDATE products 
      SET 
        rating = (
          SELECT COALESCE(AVG(rating), 0) 
          FROM reviews 
          WHERE product_id = $1
        ),
        reviews_count = (
          SELECT COUNT(*) 
          FROM reviews 
          WHERE product_id = $1
        ),
        updated_at = NOW()
      WHERE id = $1
    `,[t])}static async updateStock(t,e){return await (0,s.Rn)(async a=>{let r=await a.query("SELECT stock_quantity FROM products WHERE id = $1 FOR UPDATE",[t]);if(0===r.rows.length)throw Error("المنتج غير موجود");let s=r.rows[0].stock_quantity+e;if(s<0)throw Error("المخزون غير كافي");return(await a.query("UPDATE products SET stock_quantity = $1, updated_at = NOW() WHERE id = $2",[s,t])).rowCount>0})}static async search(t,e=20){return(await (0,s.P)(`
      SELECT * FROM products 
      WHERE 
        is_published = true 
        AND is_available = true 
        AND (
          name ILIKE $1 
          OR description ILIKE $1 
          OR $2 = ANY(features)
        )
      ORDER BY 
        CASE 
          WHEN name ILIKE $1 THEN 1
          WHEN description ILIKE $1 THEN 2
          ELSE 3
        END,
        rating DESC
      LIMIT $3
    `,[`%${t}%`,t,e])).rows}static async getBestSellers(t=10){return(await (0,s.P)(`
      SELECT p.*, COALESCE(SUM(oi.quantity), 0) as total_sold
      FROM products p
      LEFT JOIN order_items oi ON p.id = oi.product_id
      LEFT JOIN orders o ON oi.order_id = o.id
      WHERE p.is_published = true AND p.is_available = true
        AND (o.status IS NULL OR o.status IN ('confirmed', 'processing', 'shipped', 'delivered'))
      GROUP BY p.id
      ORDER BY total_sold DESC, p.rating DESC
      LIMIT $1
    `,[t])).rows}static async getNewProducts(t=10){return(await (0,s.P)(`
      SELECT * FROM products
      WHERE is_published = true AND is_available = true
      ORDER BY created_at DESC
      LIMIT $1
    `,[t])).rows}static async getTopRated(t=10){return(await (0,s.P)(`
      SELECT * FROM products
      WHERE is_published = true AND is_available = true AND rating > 0
      ORDER BY rating DESC, reviews_count DESC
      LIMIT $1
    `,[t])).rows}}r()}catch(t){r(t)}})},29294:t=>{t.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:t=>{t.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},49464:(t,e,a)=>{a.a(t,async(t,r)=>{try{a.r(e),a.d(e,{patchFetch:()=>u,routeModule:()=>c,serverHooks:()=>E,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>p});var s=a(96559),i=a(48088),o=a(37719),n=a(12909),l=t([n]);n=(l.then?(await l)():l)[0];let c=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/stats/route",pathname:"/api/stats",filename:"route",bundlePath:"app/api/stats/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\stats\\route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:d,workUnitAsyncStorage:p,serverHooks:E}=c;function u(){return(0,o.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:p})}r()}catch(t){r(t)}})},55511:t=>{t.exports=require("crypto")},63033:t=>{t.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63247:(t,e,a)=>{a.a(t,async(t,r)=>{try{a.d(e,{F:()=>l});var s=a(6710),i=a(85665),o=a.n(i),n=t([s]);s=(n.then?(await n)():n)[0];class l{static async create(t){let e=await o().hash(t.password,12);return(await (0,s.P)(`
      INSERT INTO users (
        email, password_hash, first_name, last_name, phone, role
      ) VALUES (
        $1, $2, $3, $4, $5, $6
      ) RETURNING id, email, first_name, last_name, phone, role, is_active, email_verified, profile_image, created_at, updated_at
    `,[t.email.toLowerCase(),e,t.first_name,t.last_name,t.phone,t.role||"customer"])).rows[0]}static async findByEmail(t){return(await (0,s.P)("SELECT * FROM users WHERE email = $1",[t.toLowerCase()])).rows[0]||null}static async findById(t){return(await (0,s.P)(`
      SELECT id, email, first_name, last_name, phone, role, is_active, email_verified, profile_image, created_at, updated_at
      FROM users WHERE id = $1
    `,[t])).rows[0]||null}static async verifyPassword(t,e){let a=await this.findByEmail(t);if(!a||!await o().compare(e,a.password_hash))return null;let{password_hash:r,...s}=a;return s}static async update(t,e){let a=[],r=[],i=1;if(Object.entries(e).forEach(([t,e])=>{void 0!==e&&(a.push(`${t} = $${i}`),r.push(e),i++)}),0===a.length)throw Error("لا توجد حقول للتحديث");return a.push("updated_at = NOW()"),r.push(t),(await (0,s.P)(`
      UPDATE users 
      SET ${a.join(", ")} 
      WHERE id = $${i} 
      RETURNING id, email, first_name, last_name, phone, role, is_active, email_verified, profile_image, created_at, updated_at
    `,r)).rows[0]||null}static async changePassword(t,e){let a=await o().hash(e,12);return(await (0,s.P)("UPDATE users SET password_hash = $1, updated_at = NOW() WHERE id = $2",[a,t])).rowCount>0}static async verifyEmail(t){return(await (0,s.P)("UPDATE users SET email_verified = true, updated_at = NOW() WHERE id = $1",[t])).rowCount>0}static async toggleActive(t){return(await (0,s.P)("UPDATE users SET is_active = NOT is_active, updated_at = NOW() WHERE id = $1",[t])).rowCount>0}static async delete(t){return(await (0,s.P)("DELETE FROM users WHERE id = $1",[t])).rowCount>0}static async getAll(t={}){let e=[],a=[],r=1;t.role&&(e.push(`role = $${r}`),a.push(t.role),r++),void 0!==t.is_active&&(e.push(`is_active = $${r}`),a.push(t.is_active),r++),t.search&&(e.push(`(first_name ILIKE $${r} OR last_name ILIKE $${r} OR email ILIKE $${r})`),a.push(`%${t.search}%`),r++);let i=e.length>0?`WHERE ${e.join(" AND ")}`:"",o=t.limit||50,n=t.offset||0,l=`LIMIT $${r} OFFSET $${r+1}`;a.push(o,n);let u=`SELECT COUNT(*) as total FROM users ${i}`,c=await (0,s.P)(u,a.slice(0,-2)),d=parseInt(c.rows[0].total),p=`
      SELECT id, email, first_name, last_name, phone, role, is_active, email_verified, profile_image, created_at, updated_at
      FROM users 
      ${i} 
      ORDER BY created_at DESC 
      ${l}
    `;return{users:(await (0,s.P)(p,a)).rows,total:d}}static async getStats(){let t=await (0,s.P)(`
      SELECT 
        COUNT(*) as total,
        COUNT(*) FILTER (WHERE is_active = true) as active,
        COUNT(*) FILTER (WHERE email_verified = true) as verified
      FROM users
    `),e=await (0,s.P)(`
      SELECT role, COUNT(*) as count
      FROM users
      GROUP BY role
    `),a={};return e.rows.forEach(t=>{a[t.role]=parseInt(t.count)}),{total:parseInt(t.rows[0].total),active:parseInt(t.rows[0].active),verified:parseInt(t.rows[0].verified),byRole:a}}}r()}catch(t){r(t)}})},64939:t=>{t.exports=import("pg")}};var e=require("../../../webpack-runtime.js");e.C(t);var a=t=>e(e.s=t),r=e.X(0,[4447,580,5665,9158],()=>a(49464));module.exports=r})();