"use strict";exports.id=5068,exports.ids=[5068],exports.modules={26134:(e,t,r)=>{r.d(t,{G$:()=>Y,Hs:()=>b,UC:()=>er,VY:()=>en,ZL:()=>ee,bL:()=>Q,bm:()=>ea,hE:()=>eo,hJ:()=>et,l9:()=>X});var o=r(43210),n=r(70569),a=r(98599),l=r(11273),i=r(96963),s=r(65551),d=r(31355),u=r(32547),c=r(25028),p=r(46059),f=r(14163),g=r(1359),m=r(42247),y=r(63376),h=r(8730),v=r(60687),x="Dialog",[D,b]=(0,l.A)(x),[j,w]=D(x),A=e=>{let{__scopeDialog:t,children:r,open:n,defaultOpen:a,onOpenChange:l,modal:d=!0}=e,u=o.useRef(null),c=o.useRef(null),[p,f]=(0,s.i)({prop:n,defaultProp:a??!1,onChange:l,caller:x});return(0,v.jsx)(j,{scope:t,triggerRef:u,contentRef:c,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:p,onOpenChange:f,onOpenToggle:o.useCallback(()=>f(e=>!e),[f]),modal:d,children:r})};A.displayName=x;var R="DialogTrigger",C=o.forwardRef((e,t)=>{let{__scopeDialog:r,...o}=e,l=w(R,r),i=(0,a.s)(t,l.triggerRef);return(0,v.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":V(l.open),...o,ref:i,onClick:(0,n.m)(e.onClick,l.onOpenToggle)})});C.displayName=R;var N="DialogPortal",[I,O]=D(N,{forceMount:void 0}),E=e=>{let{__scopeDialog:t,forceMount:r,children:n,container:a}=e,l=w(N,t);return(0,v.jsx)(I,{scope:t,forceMount:r,children:o.Children.map(n,e=>(0,v.jsx)(p.C,{present:r||l.open,children:(0,v.jsx)(c.Z,{asChild:!0,container:a,children:e})}))})};E.displayName=N;var F="DialogOverlay",k=o.forwardRef((e,t)=>{let r=O(F,e.__scopeDialog),{forceMount:o=r.forceMount,...n}=e,a=w(F,e.__scopeDialog);return a.modal?(0,v.jsx)(p.C,{present:o||a.open,children:(0,v.jsx)(P,{...n,ref:t})}):null});k.displayName=F;var M=(0,h.TL)("DialogOverlay.RemoveScroll"),P=o.forwardRef((e,t)=>{let{__scopeDialog:r,...o}=e,n=w(F,r);return(0,v.jsx)(m.A,{as:M,allowPinchZoom:!0,shards:[n.contentRef],children:(0,v.jsx)(f.sG.div,{"data-state":V(n.open),...o,ref:t,style:{pointerEvents:"auto",...o.style}})})}),_="DialogContent",$=o.forwardRef((e,t)=>{let r=O(_,e.__scopeDialog),{forceMount:o=r.forceMount,...n}=e,a=w(_,e.__scopeDialog);return(0,v.jsx)(p.C,{present:o||a.open,children:a.modal?(0,v.jsx)(G,{...n,ref:t}):(0,v.jsx)(L,{...n,ref:t})})});$.displayName=_;var G=o.forwardRef((e,t)=>{let r=w(_,e.__scopeDialog),l=o.useRef(null),i=(0,a.s)(t,r.contentRef,l);return o.useEffect(()=>{let e=l.current;if(e)return(0,y.Eq)(e)},[]),(0,v.jsx)(T,{...e,ref:i,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,n.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),r.triggerRef.current?.focus()}),onPointerDownOutside:(0,n.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,n.m)(e.onFocusOutside,e=>e.preventDefault())})}),L=o.forwardRef((e,t)=>{let r=w(_,e.__scopeDialog),n=o.useRef(!1),a=o.useRef(!1);return(0,v.jsx)(T,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(n.current||r.triggerRef.current?.focus(),t.preventDefault()),n.current=!1,a.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(n.current=!0,"pointerdown"===t.detail.originalEvent.type&&(a.current=!0));let o=t.target;r.triggerRef.current?.contains(o)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),T=o.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:n,onOpenAutoFocus:l,onCloseAutoFocus:i,...s}=e,c=w(_,r),p=o.useRef(null),f=(0,a.s)(t,p);return(0,g.Oh)(),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(u.n,{asChild:!0,loop:!0,trapped:n,onMountAutoFocus:l,onUnmountAutoFocus:i,children:(0,v.jsx)(d.qW,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":V(c.open),...s,ref:f,onDismiss:()=>c.onOpenChange(!1)})}),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(z,{titleId:c.titleId}),(0,v.jsx)(K,{contentRef:p,descriptionId:c.descriptionId})]})]})}),q="DialogTitle",B=o.forwardRef((e,t)=>{let{__scopeDialog:r,...o}=e,n=w(q,r);return(0,v.jsx)(f.sG.h2,{id:n.titleId,...o,ref:t})});B.displayName=q;var Z="DialogDescription",H=o.forwardRef((e,t)=>{let{__scopeDialog:r,...o}=e,n=w(Z,r);return(0,v.jsx)(f.sG.p,{id:n.descriptionId,...o,ref:t})});H.displayName=Z;var S="DialogClose",U=o.forwardRef((e,t)=>{let{__scopeDialog:r,...o}=e,a=w(S,r);return(0,v.jsx)(f.sG.button,{type:"button",...o,ref:t,onClick:(0,n.m)(e.onClick,()=>a.onOpenChange(!1))})});function V(e){return e?"open":"closed"}U.displayName=S;var W="DialogTitleWarning",[Y,J]=(0,l.q)(W,{contentName:_,titleName:q,docsSlug:"dialog"}),z=({titleId:e})=>{let t=J(W),r=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return o.useEffect(()=>{e&&(document.getElementById(e)||console.error(r))},[r,e]),null},K=({contentRef:e,descriptionId:t})=>{let r=J("DialogDescriptionWarning"),n=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${r.contentName}}.`;return o.useEffect(()=>{let r=e.current?.getAttribute("aria-describedby");t&&r&&(document.getElementById(t)||console.warn(n))},[n,e,t]),null},Q=A,X=C,ee=E,et=k,er=$,eo=B,en=H,ea=U},41862:(e,t,r)=>{r.d(t,{A:()=>o});let o=(0,r(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},63143:(e,t,r)=>{r.d(t,{A:()=>o});let o=(0,r(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},78148:(e,t,r)=>{r.d(t,{b:()=>i});var o=r(43210),n=r(14163),a=r(60687),l=o.forwardRef((e,t)=>(0,a.jsx)(n.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var i=l},93613:(e,t,r)=>{r.d(t,{A:()=>o});let o=(0,r(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},96474:(e,t,r)=>{r.d(t,{A:()=>o});let o=(0,r(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},97895:(e,t,r)=>{r.d(t,{UC:()=>_,VY:()=>T,ZD:()=>G,ZL:()=>M,bL:()=>F,hE:()=>L,hJ:()=>P,l9:()=>k,rc:()=>$});var o=r(43210),n=r(11273),a=r(98599),l=r(26134),i=r(70569),s=r(8730),d=r(60687),u="AlertDialog",[c,p]=(0,n.A)(u,[l.Hs]),f=(0,l.Hs)(),g=e=>{let{__scopeAlertDialog:t,...r}=e,o=f(t);return(0,d.jsx)(l.bL,{...o,...r,modal:!0})};g.displayName=u;var m=o.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...o}=e,n=f(r);return(0,d.jsx)(l.l9,{...n,...o,ref:t})});m.displayName="AlertDialogTrigger";var y=e=>{let{__scopeAlertDialog:t,...r}=e,o=f(t);return(0,d.jsx)(l.ZL,{...o,...r})};y.displayName="AlertDialogPortal";var h=o.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...o}=e,n=f(r);return(0,d.jsx)(l.hJ,{...n,...o,ref:t})});h.displayName="AlertDialogOverlay";var v="AlertDialogContent",[x,D]=c(v),b=(0,s.Dc)("AlertDialogContent"),j=o.forwardRef((e,t)=>{let{__scopeAlertDialog:r,children:n,...s}=e,u=f(r),c=o.useRef(null),p=(0,a.s)(t,c),g=o.useRef(null);return(0,d.jsx)(l.G$,{contentName:v,titleName:w,docsSlug:"alert-dialog",children:(0,d.jsx)(x,{scope:r,cancelRef:g,children:(0,d.jsxs)(l.UC,{role:"alertdialog",...u,...s,ref:p,onOpenAutoFocus:(0,i.m)(s.onOpenAutoFocus,e=>{e.preventDefault(),g.current?.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,d.jsx)(b,{children:n}),(0,d.jsx)(E,{contentRef:c})]})})})});j.displayName=v;var w="AlertDialogTitle",A=o.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...o}=e,n=f(r);return(0,d.jsx)(l.hE,{...n,...o,ref:t})});A.displayName=w;var R="AlertDialogDescription",C=o.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...o}=e,n=f(r);return(0,d.jsx)(l.VY,{...n,...o,ref:t})});C.displayName=R;var N=o.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...o}=e,n=f(r);return(0,d.jsx)(l.bm,{...n,...o,ref:t})});N.displayName="AlertDialogAction";var I="AlertDialogCancel",O=o.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...o}=e,{cancelRef:n}=D(I,r),i=f(r),s=(0,a.s)(t,n);return(0,d.jsx)(l.bm,{...i,...o,ref:s})});O.displayName=I;var E=({contentRef:e})=>{let t=`\`${v}\` requires a description for the component to be accessible for screen reader users.

You can add a description to the \`${v}\` by passing a \`${R}\` component as a child, which also benefits sighted users by adding visible context to the dialog.

Alternatively, you can use your own component as a description by assigning it an \`id\` and passing the same value to the \`aria-describedby\` prop in \`${v}\`. If the description is confusing or duplicative for sighted users, you can use the \`@radix-ui/react-visually-hidden\` primitive as a wrapper around your description component.

For more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;return o.useEffect(()=>{document.getElementById(e.current?.getAttribute("aria-describedby"))||console.warn(t)},[t,e]),null},F=g,k=m,M=y,P=h,_=j,$=N,G=O,L=A,T=C}};