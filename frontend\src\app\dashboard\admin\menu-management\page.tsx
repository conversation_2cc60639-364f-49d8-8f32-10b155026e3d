"use client"

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { useMenu } from '@/contexts/MenuContext'
import { Navigation } from '@/components/Navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Plus,
  Edit,
  Trash2,
  GripVertical,
  Eye,
  EyeOff,
  ExternalLink,
  FileText,
  Link,
  Menu,
  ArrowLeft,
  Save
} from 'lucide-react'
import { toast } from 'sonner'
import { EnhancedConfirmDialog, useEnhancedConfirmDialog } from '@/components/ui/enhanced-confirm-dialog'
import { MenuItemCard } from '@/components/admin/MenuItemCard'
import { MenuDeleteConfirmation } from '@/components/admin/MenuDeleteConfirmation'
import { useSortable } from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core'
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable'
import {
  useSortable,
} from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'

// أنواع البيانات
interface MenuItem {
  id: string
  title_ar: string
  title_en?: string
  title_fr?: string
  slug: string
  icon?: string
  parent_id?: string
  order_index: number
  is_active: boolean
  target_type: 'internal' | 'external' | 'page'
  target_value: string
  created_at: string
  updated_at: string
}

// مكون عنصر القائمة القابل للسحب
interface SortableMenuItemProps {
  item: MenuItem
  onEdit: (item: MenuItem) => void
  onDelete: (id: string) => void
  onToggleStatus: (item: MenuItem) => void
  getTargetIcon: (type: string) => React.ReactNode
  childItems: MenuItem[]
}

function SortableMenuItem({
  item,
  onEdit,
  onDelete,
  onToggleStatus,
  getTargetIcon,
  childItems
}: SortableMenuItemProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: item.id })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  }

  return (
    <div ref={setNodeRef} style={style} className="space-y-2">
      {/* Main Item */}
      <div className="flex items-center justify-between p-4 border rounded-lg bg-white dark:bg-gray-800">
        <div className="flex items-center gap-4">
          <div
            {...attributes}
            {...listeners}
            className="cursor-grab active:cursor-grabbing"
          >
            <GripVertical className="h-4 w-4 text-gray-400" />
          </div>
          <div className="flex items-center gap-2">
            {getTargetIcon(item.target_type)}
            <span className="font-medium arabic-text">{item.title_ar}</span>
            {!item.is_active && (
              <Badge variant="secondary" className="arabic-text">غير مفعل</Badge>
            )}
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onToggleStatus(item)}
          >
            {item.is_active ? (
              <EyeOff className="h-4 w-4" />
            ) : (
              <Eye className="h-4 w-4" />
            )}
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onEdit(item)}
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onDelete(item.id)}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Child Items */}
      {childItems.map((childItem) => (
        <div key={childItem.id} className="mr-8 flex items-center justify-between p-3 border rounded-lg bg-gray-50 dark:bg-gray-700">
          <div className="flex items-center gap-2">
            {getTargetIcon(childItem.target_type)}
            <span className="arabic-text">{childItem.title_ar}</span>
            {!childItem.is_active && (
              <Badge variant="secondary" className="arabic-text">غير مفعل</Badge>
            )}
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onToggleStatus(childItem)}
            >
              {childItem.is_active ? (
                <EyeOff className="h-4 w-4" />
              ) : (
                <Eye className="h-4 w-4" />
              )}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onEdit(childItem)}
            >
              <Edit className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onDelete(childItem.id)}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
      ))}
    </div>
  )
}

interface MenuItemForm {
  title_ar: string
  title_en: string
  title_fr: string
  slug: string
  icon: string
  parent_id: string
  target_type: 'internal' | 'external' | 'page'
  target_value: string
  is_active: boolean
}

// مكون قابل للسحب والإفلات
function SortableMenuItemWrapper({
  item,
  onEdit,
  onDelete,
  onToggleStatus
}: {
  item: MenuItem & { children?: MenuItem[] }
  onEdit: (item: MenuItem) => void
  onDelete: (id: string) => void
  onToggleStatus: (item: MenuItem) => void
}) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: item.id })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  }

  return (
    <div ref={setNodeRef} style={style}>
      <MenuItemCard
        item={item}
        onEdit={onEdit}
        onDelete={onDelete}
        onToggleStatus={onToggleStatus}
        dragHandleProps={{ ...attributes, ...listeners }}
        isDragging={isDragging}
      />
    </div>
  )
}

export default function MenuManagementPage() {
  const { user, profile } = useAuth()
  const {
    menuItems,
    loading,
    addMenuItem,
    updateMenuItem,
    deleteMenuItem,
    toggleItemStatus,
    fetchMenuItems
  } = useMenu()
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editingItem, setEditingItem] = useState<MenuItem | null>(null)
  const [formData, setFormData] = useState<MenuItemForm>({
    title_ar: '',
    title_en: '',
    title_fr: '',
    slug: '',
    icon: '',
    parent_id: '',
    target_type: 'internal',
    target_value: '',
    is_active: true
  })
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [itemToDelete, setItemToDelete] = useState<MenuItem | null>(null)

  // استخدام مربع الحوار المحسن
  const { showDialog, hideDialog, DialogComponent } = useEnhancedConfirmDialog()

  // إعداد sensors للسحب والإفلات
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  )

  // التحقق من صلاحيات الأدمن
  useEffect(() => {
    if (!user || !profile || profile.role !== 'admin') {
      window.location.href = '/dashboard'
      return
    }
  }, [user, profile])

  // جلب عناصر القائمة مع العناصر غير المفعلة
  useEffect(() => {
    if (user && profile && profile.role === 'admin') {
      fetchMenuItems(true) // تمرير true لجلب العناصر غير المفعلة أيضاً
    }
  }, [user, profile]) // إزالة fetchMenuItems من dependencies لتجنب infinite loop

  // إعادة تعيين النموذج
  const resetForm = () => {
    setFormData({
      title_ar: '',
      title_en: '',
      title_fr: '',
      slug: '',
      icon: '',
      parent_id: '',
      target_type: 'internal',
      target_value: '',
      is_active: true
    })
    setEditingItem(null)
  }

  // فتح نموذج التحرير
  const openEditDialog = (item: MenuItem) => {
    setEditingItem(item)
    setFormData({
      title_ar: item.title_ar,
      title_en: item.title_en || '',
      title_fr: item.title_fr || '',
      slug: item.slug,
      icon: item.icon || '',
      parent_id: item.parent_id || '',
      target_type: item.target_type,
      target_value: item.target_value,
      is_active: item.is_active
    })
    setIsDialogOpen(true)
  }

  // حفظ عنصر القائمة
  const saveMenuItem = async () => {
    const itemData = {
      ...formData,
      parent_id: formData.parent_id === 'none' ? null : formData.parent_id || null,
      order_index: menuItems.length + 1
    }

    let success = false

    if (editingItem) {
      success = await updateMenuItem(editingItem.id, itemData)
    } else {
      success = await addMenuItem(itemData)
    }

    if (success) {
      setIsDialogOpen(false)
      resetForm()
      setHasUnsavedChanges(true)
    }
  }

  // حذف عنصر القائمة مع تأكيد احترافي
  const handleDeleteMenuItem = async (id: string) => {
    const item = menuItems.find(item => item.id === id)
    if (!item) return

    setItemToDelete(item)
    setDeleteDialogOpen(true)
  }

  // تأكيد حذف العنصر
  const confirmDeleteMenuItem = async () => {
    if (!itemToDelete) return

    const success = await deleteMenuItem(itemToDelete.id)
    if (success) {
      setHasUnsavedChanges(true)
      toast.success('تم حذف العنصر بنجاح')
      setItemToDelete(null)
    }
  }

  // حفظ القائمة
  const handleSaveMenu = async () => {
    try {
      // هنا يمكن إضافة منطق حفظ القائمة إذا كان مطلوباً
      // مثل إرسال ترتيب العناصر أو تحديثات أخرى
      setHasUnsavedChanges(false)
      toast.success('تم حفظ القائمة بنجاح')
    } catch (error) {
      console.error('Error saving menu:', error)
      toast.error('خطأ في حفظ القائمة')
    }
  }

  // تبديل حالة التفعيل
  const handleToggleItemStatus = async (item: MenuItem) => {
    await toggleItemStatus(item.id)
    setHasUnsavedChanges(true)
  }

  // تفعيل/إلغاء تفعيل جميع عناصر القائمة
  const toggleAllMenuItems = async (isActive: boolean) => {
    try {
      const updates = menuItems.map(async (item) => {
        if (item.is_active !== isActive) {
          return await updateMenuItem(item.id, { is_active: isActive })
        }
        return true
      })

      await Promise.all(updates)

      toast.success(isActive ? 'تم تفعيل جميع عناصر القائمة' : 'تم إلغاء تفعيل جميع عناصر القائمة')
      setHasUnsavedChanges(true)
    } catch (error) {
      console.error('Error toggling all items:', error)
      toast.error('خطأ في تحديث عناصر القائمة')
    }
  }

  // معالجة السحب والإفلات
  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event

    if (!over || active.id === over.id) {
      return
    }

    const oldIndex = mainItems.findIndex(item => item.id === active.id)
    const newIndex = mainItems.findIndex(item => item.id === over.id)

    if (oldIndex === -1 || newIndex === -1) {
      return
    }

    // إعادة ترتيب العناصر محلياً
    const newMainItems = arrayMove(mainItems, oldIndex, newIndex)
    const nonMainItems = menuItems.filter(item => item.parent_id)

    // تحديث order_index للعناصر المرتبة
    const updatedMainItems = newMainItems.map((item, index) => ({
      ...item,
      order_index: index + 1
    }))

    // دمج العناصر الرئيسية المرتبة مع العناصر الفرعية
    const reorderedItems = [...updatedMainItems, ...nonMainItems]

    await reorderMenuItems(reorderedItems)
  }

  // تصفية العناصر الرئيسية والفرعية
  const mainItems = menuItems.filter(item => !item.parent_id)
  const getChildItems = (parentId: string) =>
    menuItems.filter(item => item.parent_id === parentId)

  // أيقونات الهدف
  const getTargetIcon = (targetType: string) => {
    switch (targetType) {
      case 'internal': return <Link className="h-4 w-4" />
      case 'external': return <ExternalLink className="h-4 w-4" />
      case 'page': return <FileText className="h-4 w-4" />
      default: return <Link className="h-4 w-4" />
    }
  }

  if (!user || !profile || profile.role !== 'admin') {
    return null
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <Navigation />

      <main className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <Button variant="outline" size="sm" asChild>
              <a href="/dashboard/admin">
                <ArrowLeft className="h-4 w-4 mr-2" />
                العودة للوحة التحكم
              </a>
            </Button>
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center gap-3">
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white arabic-text">
                  إدارة القائمة الرئيسية 🗂️
                </h1>
                {hasUnsavedChanges && (
                  <div className="flex items-center gap-2 bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-200 px-3 py-1 rounded-full text-sm">
                    <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse"></div>
                    تغييرات غير محفوظة
                  </div>
                )}
              </div>
              <p className="text-gray-600 dark:text-gray-300 mt-2 arabic-text">
                تحكم في عناصر القائمة الرئيسية وترتيبها
              </p>
            </div>
            
            <div className="flex flex-wrap gap-3">
              {/* زر حفظ القائمة */}
              {hasUnsavedChanges && (
                <Button
                  onClick={handleSaveMenu}
                  className="bg-green-600 hover:bg-green-700 text-white"
                >
                  <Save className="h-4 w-4 mr-2" />
                  حفظ القائمة
                </Button>
              )}

              <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                <DialogTrigger asChild>
                  <Button onClick={resetForm}>
                    <Plus className="h-4 w-4 mr-2" />
                    إضافة عنصر جديد
                  </Button>
                </DialogTrigger>
              </Dialog>

              <Button
                variant="outline"
                onClick={() => toggleAllMenuItems(true)}
                className="text-green-600 hover:text-green-700"
              >
                <Eye className="h-4 w-4 mr-2" />
                تفعيل الكل
              </Button>

              <Button
                variant="outline"
                onClick={() => toggleAllMenuItems(false)}
                className="text-red-600 hover:text-red-700"
              >
                <EyeOff className="h-4 w-4 mr-2" />
                إلغاء تفعيل الكل
              </Button>
            </div>

            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              
              <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle className="arabic-text">
                    {editingItem ? 'تحرير عنصر القائمة' : 'إضافة عنصر قائمة جديد'}
                  </DialogTitle>
                  <DialogDescription className="arabic-text">
                    املأ البيانات أدناه لإنشاء أو تحديث عنصر القائمة
                  </DialogDescription>
                </DialogHeader>

                <Tabs defaultValue="basic" className="w-full">
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="basic" className="arabic-text">البيانات الأساسية</TabsTrigger>
                    <TabsTrigger value="translations" className="arabic-text">الترجمات</TabsTrigger>
                  </TabsList>

                  <TabsContent value="basic" className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="title_ar" className="arabic-text">العنوان بالعربية *</Label>
                        <Input
                          id="title_ar"
                          value={formData.title_ar}
                          onChange={(e) => setFormData({...formData, title_ar: e.target.value})}
                          placeholder="مثال: الرئيسية"
                          className="arabic-text"
                        />
                      </div>
                      
                      <div>
                        <Label htmlFor="slug" className="arabic-text">الرابط المختصر *</Label>
                        <Input
                          id="slug"
                          value={formData.slug}
                          onChange={(e) => setFormData({...formData, slug: e.target.value})}
                          placeholder="مثال: home"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="target_type" className="arabic-text">نوع الهدف *</Label>
                        <Select 
                          value={formData.target_type} 
                          onValueChange={(value: 'internal' | 'external' | 'page') => 
                            setFormData({...formData, target_type: value})
                          }
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="internal">رابط داخلي</SelectItem>
                            <SelectItem value="external">رابط خارجي</SelectItem>
                            <SelectItem value="page">صفحة ديناميكية</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div>
                        <Label htmlFor="target_value" className="arabic-text">قيمة الهدف *</Label>
                        <Input
                          id="target_value"
                          value={formData.target_value}
                          onChange={(e) => setFormData({...formData, target_value: e.target.value})}
                          placeholder={
                            formData.target_type === 'internal' ? '/catalog' :
                            formData.target_type === 'external' ? 'https://example.com' :
                            'معرف الصفحة'
                          }
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="icon" className="arabic-text">الأيقونة (Lucide)</Label>
                        <Input
                          id="icon"
                          value={formData.icon}
                          onChange={(e) => setFormData({...formData, icon: e.target.value})}
                          placeholder="مثال: Home"
                        />
                      </div>
                      
                      <div>
                        <Label htmlFor="parent_id" className="arabic-text">العنصر الأب</Label>
                        <Select 
                          value={formData.parent_id} 
                          onValueChange={(value) => setFormData({...formData, parent_id: value})}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="اختر العنصر الأب (اختياري)" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="none">بدون عنصر أب</SelectItem>
                            {mainItems.map((item) => (
                              <SelectItem key={item.id} value={item.id}>
                                {item.title_ar}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Switch
                        id="is_active"
                        checked={formData.is_active}
                        onCheckedChange={(checked) => setFormData({...formData, is_active: checked})}
                      />
                      <Label htmlFor="is_active" className="arabic-text">مفعل</Label>
                    </div>
                  </TabsContent>

                  <TabsContent value="translations" className="space-y-4">
                    <div>
                      <Label htmlFor="title_en" className="arabic-text">العنوان بالإنجليزية</Label>
                      <Input
                        id="title_en"
                        value={formData.title_en}
                        onChange={(e) => setFormData({...formData, title_en: e.target.value})}
                        placeholder="Example: Home"
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="title_fr" className="arabic-text">العنوان بالفرنسية</Label>
                      <Input
                        id="title_fr"
                        value={formData.title_fr}
                        onChange={(e) => setFormData({...formData, title_fr: e.target.value})}
                        placeholder="Exemple: Accueil"
                      />
                    </div>
                  </TabsContent>
                </Tabs>

                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                    إلغاء
                  </Button>
                  <Button onClick={saveMenuItem}>
                    {editingItem ? 'تحديث' : 'إضافة'}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Menu Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text">
                    إجمالي العناصر
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {menuItems.length}
                  </p>
                </div>
                <Menu className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text">
                    العناصر المفعلة
                  </p>
                  <p className="text-2xl font-bold text-green-600">
                    {menuItems.filter(item => item.is_active).length}
                  </p>
                </div>
                <Eye className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text">
                    العناصر المعطلة
                  </p>
                  <p className="text-2xl font-bold text-red-600">
                    {menuItems.filter(item => !item.is_active).length}
                  </p>
                </div>
                <EyeOff className="h-8 w-8 text-red-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400 arabic-text">
                    القوائم الفرعية
                  </p>
                  <p className="text-2xl font-bold text-purple-600">
                    {menuItems.filter(item => item.parent_id).length}
                  </p>
                </div>
                <ArrowLeft className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Menu Items List */}
        <Card>
          <CardHeader>
            <CardTitle className="arabic-text">عناصر القائمة الحالية</CardTitle>
            <CardDescription className="arabic-text">
              إدارة وترتيب عناصر القائمة الرئيسية
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-gray-600 arabic-text">جاري التحميل...</p>
              </div>
            ) : menuItems.length === 0 ? (
              <div className="text-center py-8">
                <Menu className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600 arabic-text">لا توجد عناصر قائمة</p>
              </div>
            ) : (
              <DndContext
                sensors={sensors}
                collisionDetection={closestCenter}
                onDragEnd={handleDragEnd}
              >
                <SortableContext
                  items={mainItems.map(item => item.id)}
                  strategy={verticalListSortingStrategy}
                >
                  <div className="space-y-3">
                    {mainItems.map((item) => {
                      const itemWithChildren = {
                        ...item,
                        children: getChildItems(item.id)
                      }

                      return (
                        <SortableMenuItemWrapper
                          key={item.id}
                          item={itemWithChildren}
                          onEdit={openEditDialog}
                          onDelete={handleDeleteMenuItem}
                          onToggleStatus={handleToggleItemStatus}
                        />
                      )
                    })}
                  </div>
                </SortableContext>
              </DndContext>
            )}
          </CardContent>
        </Card>

        {/* Enhanced Confirm Dialog */}
        <DialogComponent />

        {/* Menu Delete Confirmation */}
        <MenuDeleteConfirmation
          open={deleteDialogOpen}
          onOpenChange={setDeleteDialogOpen}
          item={itemToDelete}
          childItems={itemToDelete ? getChildItems(itemToDelete.id) : []}
          hasUnsavedChanges={hasUnsavedChanges}
          onConfirmDelete={confirmDeleteMenuItem}
          onSaveMenu={handleSaveMenu}
        />
      </main>
    </div>
  )
}
