(()=>{var e={};e.id=5413,e.ids=[5413],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12541:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>g,routeModule:()=>l,serverHooks:()=>f,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>d});var s={};t.r(s),t.d(s,{DELETE:()=>i,POST:()=>p});var o=t(96559),n=t(48088),a=t(37719),u=t(32190);async function p(e){try{let r=await e.formData(),t=r.getAll("files"),s=r.get("folder")||"products",o="true"===r.get("compress"),n=parseFloat(r.get("quality"))||.8;if(!t||0===t.length)return u.NextResponse.json({error:"لم يتم اختيار أي ملفات"},{status:400});let a=[],p=[];for(let e of t)try{if(!e.type.startsWith("image/")){p.push(`${e.name}: نوع الملف غير مدعوم`);continue}if(e.size>5242880){p.push(`${e.name}: حجم الملف كبير جداً (أكثر من 5 ميجابايت)`);continue}let r=e.size,t=e.name.split(".").pop(),u=`${Date.now()}-${Math.random().toString(36).substring(2)}.${t}`,i=`${s}/${u}`,l=await e.arrayBuffer(),c=Buffer.from(l).toString("base64"),d=`data:${e.type};base64,${c}`,f=o?Math.floor(r*n):r,g=o?Math.round((1-n)*100):0;a.push({name:e.name,url:d,path:i,originalSize:r,compressedSize:f,compressionRatio:g})}catch(r){console.error("Error processing file:",r),p.push(`${e.name}: خطأ في معالجة الملف`)}return u.NextResponse.json({message:`تم رفع ${a.length} من ${t.length} ملف بنجاح`,uploadedFiles:a,errors:p.length>0?p:void 0})}catch(e){return console.error("Unexpected error:",e),u.NextResponse.json({error:"خطأ غير متوقع"},{status:500})}}async function i(e){try{let{searchParams:r}=new URL(e.url);if(!r.get("path"))return u.NextResponse.json({error:"مسار الملف مطلوب"},{status:400});return u.NextResponse.json({message:"تم حذف الملف بنجاح"})}catch(e){return console.error("Unexpected error:",e),u.NextResponse.json({error:"خطأ غير متوقع"},{status:500})}}let l=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/upload/route",pathname:"/api/upload",filename:"route",bundlePath:"app/api/upload/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\upload\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:c,workUnitAsyncStorage:d,serverHooks:f}=l;function g(){return(0,a.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:d})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580],()=>t(12541));module.exports=s})();