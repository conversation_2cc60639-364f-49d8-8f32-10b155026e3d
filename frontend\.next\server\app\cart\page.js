(()=>{var e={};e.id=4005,e.ids=[4005],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3315:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>f});var a=t(60687),r=t(43210),i=t(28253),n=t(44493),l=t(29523),d=t(96834),c=t(89940),o=t(2083),x=t(28561),h=t(71057),p=t(88233);let m=(0,t(62688).A)("minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]);var u=t(96474),j=t(70334);function f(){let{cartItems:e,cartCount:s,removeFromCart:t,updateQuantity:f,clearCart:v,getCartTotal:b}=(0,i._)(),[N,g]=(0,r.useState)(!1),y=(e,s)=>{s<1?t(e):f(e,s)},w=e=>{t(e)};return 0===s?(0,a.jsx)(c.Mx,{containerClassName:"container mx-auto px-4 py-8",children:(0,a.jsxs)("div",{className:"text-center py-16",children:[(0,a.jsx)(x.A,{className:"h-24 w-24 text-gray-400 mx-auto mb-6"}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-4 arabic-text",children:"السلة فارغة"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-8 arabic-text",children:"لم تقم بإضافة أي منتجات للسلة بعد"}),(0,a.jsx)(l.$,{asChild:!0,children:(0,a.jsxs)("a",{href:"/catalog",children:[(0,a.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"تصفح المنتجات"]})})]})}):(0,a.jsxs)(c.Mx,{containerClassName:"container mx-auto px-4 py-8",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-2 arabic-text",children:"\uD83D\uDED2 سلة التسوق"}),(0,a.jsxs)("p",{className:"text-gray-600 dark:text-gray-400 arabic-text",children:["لديك ",s," منتج في السلة"]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,a.jsxs)("div",{className:"lg:col-span-2 space-y-4",children:[e.map(e=>(0,a.jsx)(n.Zp,{className:"overflow-hidden",children:(0,a.jsx)(n.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(o.u,{src:e.image,alt:e.name,width:120,height:120,className:"w-20 h-20 md:w-24 md:h-24 object-cover rounded-lg border"})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 dark:text-white arabic-text line-clamp-2",children:e.name}),(0,a.jsx)(l.$,{size:"sm",variant:"ghost",onClick:()=>w(e.id),className:"text-red-600 hover:text-red-700 hover:bg-red-50",children:(0,a.jsx)(p.A,{className:"h-4 w-4"})})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-3",children:[(0,a.jsx)(d.E,{variant:"rental"===e.type?"secondary":"default",children:"rental"===e.type?"إيجار":"شراء"}),(0,a.jsxs)("span",{className:"text-lg font-bold text-blue-600",children:[e.price," Dhs"]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"الكمية:"}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(l.$,{size:"sm",variant:"outline",onClick:()=>y(e.id,e.quantity-1),disabled:e.quantity<=1,children:(0,a.jsx)(m,{className:"h-3 w-3"})}),(0,a.jsx)("span",{className:"w-8 text-center font-medium",children:e.quantity}),(0,a.jsx)(l.$,{size:"sm",variant:"outline",onClick:()=>y(e.id,e.quantity+1),children:(0,a.jsx)(u.A,{className:"h-3 w-3"})})]}),(0,a.jsxs)("span",{className:"text-sm text-gray-500 mr-auto",children:["المجموع: ",(e.price*e.quantity).toFixed(2)," Dhs"]})]})]})]})})},`${e.id}-${e.type}`)),(0,a.jsx)("div",{className:"flex justify-end",children:(0,a.jsxs)(l.$,{variant:"outline",onClick:()=>{v()},className:"text-red-600 hover:text-red-700 hover:bg-red-50",children:[(0,a.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"إفراغ السلة"]})})]}),(0,a.jsx)("div",{className:"lg:col-span-1",children:(0,a.jsxs)(n.Zp,{className:"sticky top-4",children:[(0,a.jsx)(n.aR,{children:(0,a.jsx)(n.ZB,{className:"arabic-text",children:"ملخص الطلب"})}),(0,a.jsxs)(n.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{className:"arabic-text",children:"عدد المنتجات:"}),(0,a.jsx)("span",{children:s})]}),(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{className:"arabic-text",children:"المجموع الفرعي:"}),(0,a.jsxs)("span",{children:[b().toFixed(2)," Dhs"]})]}),(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{className:"arabic-text",children:"الشحن:"}),(0,a.jsx)("span",{className:"text-green-600",children:"مجاني"})]}),(0,a.jsx)("div",{className:"border-t pt-2",children:(0,a.jsxs)("div",{className:"flex justify-between font-bold text-lg",children:[(0,a.jsx)("span",{className:"arabic-text",children:"المجموع الكلي:"}),(0,a.jsxs)("span",{className:"text-blue-600",children:[b().toFixed(2)," Dhs"]})]})})]}),(0,a.jsx)(l.$,{className:"w-full",size:"lg",onClick:()=>{g(!0),setTimeout(()=>{g(!1),window.location.href="/checkout"},500)},disabled:N,children:N?"جاري المعالجة...":(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"متابعة للدفع"]})}),(0,a.jsx)(l.$,{variant:"outline",className:"w-full",asChild:!0,children:(0,a.jsxs)("a",{href:"/catalog",children:[(0,a.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"متابعة التسوق"]})})]})]})})]})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16100:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>h,tree:()=>c});var a=t(65239),r=t(48088),i=t(88170),n=t.n(i),l=t(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);t.d(s,d);let c={children:["",{children:["cart",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,70905)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\cart\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,54431)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\cart\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/cart/page",pathname:"/cart",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24889:(e,s,t)=>{Promise.resolve().then(t.bind(t,70905))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},61337:(e,s,t)=>{Promise.resolve().then(t.bind(t,3315))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70334:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},70905:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Graduation Toqs\\\\frontend\\\\src\\\\app\\\\cart\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\cart\\page.tsx","default")},78272:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},79551:e=>{"use strict";e.exports=require("url")},96474:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[4447,8773,2762,3932,7801,408],()=>t(16100));module.exports=a})();