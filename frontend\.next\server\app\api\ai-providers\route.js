"use strict";(()=>{var e={};e.id=9077,e.ids=[9077],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47134:(e,r,s)=>{s.r(r),s.d(r,{patchFetch:()=>R,routeModule:()=>l,serverHooks:()=>j,workAsyncStorage:()=>v,workUnitAsyncStorage:()=>g});var t={};s.r(t),s.d(t,{DELETE:()=>x,GET:()=>p,POST:()=>c,PUT:()=>d});var o=s(96559),n=s(48088),a=s(37719),i=s(32190),u=s(38561);async function p(){try{return i.NextResponse.json({success:!0,providers:[],message:"تم جلب المزودين بنجاح",useLocalStorage:!0})}catch(e){return console.error("Error fetching AI providers:",e),i.NextResponse.json({success:!1,error:"فشل في جلب المزودين",providers:[]},{status:500})}}async function c(e){try{let{provider:r,providerName:s,baseUrl:t,apiKey:o,models:n,description:a,status:p="active"}=await e.json();if(!r||!s||!t||!o||!n||0===n.length)return i.NextResponse.json({success:!1,error:"جميع البيانات مطلوبة (المزود، الاسم، الرابط، مفتاح API، النماذج)"},{status:400});let c={id:u.CN.generateId(),provider:r,providerName:s,baseUrl:t,apiKey:o,models:n,description:a||"",status:p,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},d=u.CN.addAIProvider(c);return i.NextResponse.json({success:!0,provider:d,message:`تم إضافة مزود ${s} بنجاح`})}catch(e){return console.error("Error adding AI provider:",e),i.NextResponse.json({success:!1,error:"فشل في إضافة المزود"},{status:500})}}async function d(e){try{let{id:r,provider:s,providerName:t,baseUrl:o,apiKey:n,models:a,description:p,status:c}=await e.json();if(!r)return i.NextResponse.json({success:!1,error:"معرف المزود مطلوب"},{status:400});let d={id:r,provider:s,providerName:t,baseUrl:o,apiKey:n,models:a,description:p||"",status:c,updatedAt:new Date().toISOString()},x=u.CN.updateAIProvider(r,d);if(!x)return i.NextResponse.json({success:!1,error:"المزود غير موجود"},{status:404});return i.NextResponse.json({success:!0,provider:x,message:`تم تحديث مزود ${t} بنجاح`})}catch(e){return console.error("Error updating AI provider:",e),i.NextResponse.json({success:!1,error:"فشل في تحديث المزود"},{status:500})}}async function x(e){try{let{searchParams:r}=new URL(e.url),s=r.get("id");if(!s)return i.NextResponse.json({success:!1,error:"معرف المزود مطلوب"},{status:400});if(!u.CN.deleteAIProvider(s))return i.NextResponse.json({success:!1,error:"المزود غير موجود"},{status:404});return i.NextResponse.json({success:!0,message:"تم حذف المزود بنجاح"})}catch(e){return console.error("Error deleting AI provider:",e),i.NextResponse.json({success:!1,error:"فشل في حذف المزود"},{status:500})}}let l=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/ai-providers/route",pathname:"/api/ai-providers",filename:"route",bundlePath:"app/api/ai-providers/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\ai-providers\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:v,workUnitAsyncStorage:g,serverHooks:j}=l;function R(){return(0,a.patchFetch)({workAsyncStorage:v,workUnitAsyncStorage:g})}},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[4447,580,8554],()=>s(47134));module.exports=t})();