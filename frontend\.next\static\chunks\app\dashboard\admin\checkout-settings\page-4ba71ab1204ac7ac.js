(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7888],{17313:(e,a,s)=>{"use strict";s.d(a,{Xi:()=>d,av:()=>c,j7:()=>l,tU:()=>n});var t=s(95155);s(12115);var i=s(60704),r=s(59434);function n(e){let{className:a,...s}=e;return(0,t.jsx)(i.bL,{"data-slot":"tabs",className:(0,r.cn)("flex flex-col gap-2",a),...s})}function l(e){let{className:a,...s}=e;return(0,t.jsx)(i.B8,{"data-slot":"tabs-list",className:(0,r.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",a),...s})}function d(e){let{className:a,...s}=e;return(0,t.jsx)(i.l9,{"data-slot":"tabs-trigger",className:(0,r.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...s})}function c(e){let{className:a,...s}=e;return(0,t.jsx)(i.UC,{"data-slot":"tabs-content",className:(0,r.cn)("flex-1 outline-none",a),...s})}},53416:(e,a,s)=>{"use strict";s.d(a,{B:()=>i});let t={fields:[{id:"fullName",name:"fullName",label:"الاسم الكامل",type:"text",required:!0,enabled:!0,placeholder:"أدخل اسمك الكامل",order:1,section:"personal",validation:{minLength:2,maxLength:100}},{id:"email",name:"email",label:"البريد الإلكتروني",type:"email",required:!0,enabled:!0,placeholder:"<EMAIL>",order:2,section:"personal"},{id:"phone",name:"phone",label:"رقم الهاتف",type:"tel",required:!0,enabled:!0,placeholder:"+971-XX-XXX-XXXX",order:3,section:"personal"},{id:"address",name:"address",label:"العنوان",type:"textarea",required:!0,enabled:!0,placeholder:"أدخل عنوانك الكامل",order:4,section:"shipping"},{id:"city",name:"city",label:"المدينة",type:"text",required:!0,enabled:!0,placeholder:"اسم المدينة",order:5,section:"shipping"},{id:"state",name:"state",label:"الإمارة/المنطقة",type:"select",required:!0,enabled:!0,order:6,section:"shipping",options:["أبوظبي","دبي","الشارقة","عجمان","أم القيوين","رأس الخيمة","الفجيرة"]},{id:"zipCode",name:"zipCode",label:"الرمز البريدي",type:"text",required:!1,enabled:!0,placeholder:"12345",order:7,section:"shipping"},{id:"specialInstructions",name:"specialInstructions",label:"تعليمات خاصة",type:"textarea",required:!1,enabled:!0,placeholder:"أي تعليمات خاصة للتوصيل...",order:8,section:"shipping"}],paymentMethods:[{id:"card",name:"بطاقة ائتمان/خصم",description:"Visa, Mastercard, American Express",icon:"CreditCard",enabled:!0,order:1,config:{requiresCard:!0,additionalFields:[{id:"cardNumber",name:"cardNumber",label:"رقم البطاقة",type:"text",required:!0,enabled:!0,placeholder:"1234 5678 9012 3456",order:1,section:"billing"},{id:"expiryDate",name:"expiryDate",label:"تاريخ الانتهاء",type:"text",required:!0,enabled:!0,placeholder:"MM/YY",order:2,section:"billing"},{id:"cvv",name:"cvv",label:"رمز الأمان",type:"text",required:!0,enabled:!0,placeholder:"123",order:3,section:"billing"}]}},{id:"cash",name:"الدفع عند الاستلام",description:"ادفع نقداً عند وصول الطلب",icon:"Banknote",enabled:!0,order:2},{id:"bank_transfer",name:"تحويل بنكي",description:"تحويل مباشر إلى حساب البنك",icon:"Building2",enabled:!0,order:3},{id:"digital_wallet",name:"المحفظة الرقمية",description:"Apple Pay, Google Pay, Samsung Pay",icon:"Smartphone",enabled:!1,order:4}],deliveryOptions:[{id:"standard",name:"التوصيل العادي",description:"3-5 أيام عمل",price:25,estimatedDays:"3-5 أيام",enabled:!0,order:1,icon:"Truck"},{id:"express",name:"التوصيل السريع",description:"1-2 أيام عمل",price:50,estimatedDays:"1-2 أيام",enabled:!0,order:2,icon:"Zap",restrictions:{minOrderValue:100}},{id:"same_day",name:"التوصيل في نفس اليوم",description:"خلال 6 ساعات",price:100,estimatedDays:"6 ساعات",enabled:!0,order:3,icon:"Clock",restrictions:{minOrderValue:200,availableRegions:["دبي","أبوظبي"]}},{id:"pickup",name:"الاستلام من المتجر",description:"استلم طلبك من فرعنا",price:0,estimatedDays:"فوري",enabled:!1,order:4,icon:"Store"}],general:{requireTermsAcceptance:!0,allowGuestCheckout:!0,showOrderSummary:!0,enableSpecialInstructions:!0,defaultCountry:"الإمارات العربية المتحدة",currency:"AED",taxRate:.05}};class i{static getStorageKey(){return"checkoutSettings"}static getSettings(){let e=localStorage.getItem(this.getStorageKey());return e?JSON.parse(e):t}static saveSettings(e){localStorage.setItem(this.getStorageKey(),JSON.stringify(e))}static resetToDefaults(){this.saveSettings(t)}static addField(e){let a=this.getSettings();a.fields.push(e),this.saveSettings(a)}static updateField(e,a){let s=this.getSettings(),t=s.fields.findIndex(a=>a.id===e);-1!==t&&(s.fields[t]={...s.fields[t],...a},this.saveSettings(s))}static removeField(e){let a=this.getSettings();a.fields=a.fields.filter(a=>a.id!==e),this.saveSettings(a)}static updatePaymentMethod(e,a){let s=this.getSettings(),t=s.paymentMethods.findIndex(a=>a.id===e);-1!==t&&(s.paymentMethods[t]={...s.paymentMethods[t],...a},this.saveSettings(s))}static updateDeliveryOption(e,a){let s=this.getSettings(),t=s.deliveryOptions.findIndex(a=>a.id===e);-1!==t&&(s.deliveryOptions[t]={...s.deliveryOptions[t],...a},this.saveSettings(s))}}},62523:(e,a,s)=>{"use strict";s.d(a,{p:()=>r});var t=s(95155);s(12115);var i=s(59434);function r(e){let{className:a,type:s,...r}=e;return(0,t.jsx)("input",{type:s,"data-slot":"input",className:(0,i.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",a),...r})}},63770:(e,a,s)=>{Promise.resolve().then(s.bind(s,88306))},80333:(e,a,s)=>{"use strict";s.d(a,{d:()=>n});var t=s(95155);s(12115);var i=s(4884),r=s(59434);function n(e){let{className:a,...s}=e;return(0,t.jsx)(i.bL,{"data-slot":"switch",className:(0,r.cn)("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",a),...s,children:(0,t.jsx)(i.zi,{"data-slot":"switch-thumb",className:(0,r.cn)("bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0")})})}},85057:(e,a,s)=>{"use strict";s.d(a,{J:()=>n});var t=s(95155);s(12115);var i=s(40968),r=s(59434);function n(e){let{className:a,...s}=e;return(0,t.jsx)(i.b,{"data-slot":"label",className:(0,r.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...s})}},88306:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>O});var t=s(95155),i=s(12115),r=s(86566),n=s(66695),l=s(30285),d=s(62523),c=s(85057),o=s(80333),x=s(17313),m=s(26126),h=s(22346),u=s(35169),p=s(40133),g=s(4229),b=s(71007),j=s(81586),v=s(29799),f=s(381),y=s(48021),N=s(92657),k=s(78749),w=s(13717),S=s(62525),C=s(84616),A=s(53416);function O(){let[e,a]=(0,i.useState)(null),[s,O]=(0,i.useState)("fields"),[q,B]=(0,i.useState)(!0),[X,E]=(0,i.useState)(!1);(0,i.useEffect)(()=>{a(A.B.getSettings()),B(!1)},[]);let _=s=>{if(!e)return;let t=e.fields.map(e=>e.id===s?{...e,enabled:!e.enabled}:e);a({...e,fields:t})},z=s=>{if(!e)return;let t=e.paymentMethods.map(e=>e.id===s?{...e,enabled:!e.enabled}:e);a({...e,paymentMethods:t})},D=s=>{if(!e)return;let t=e.deliveryOptions.map(e=>e.id===s?{...e,enabled:!e.enabled}:e);a({...e,deliveryOptions:t})};return q||!e?(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",children:[(0,t.jsx)(r.V,{}),(0,t.jsx)("main",{className:"container mx-auto px-4 py-8",children:(0,t.jsxs)("div",{className:"text-center py-16",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"}),(0,t.jsx)("p",{className:"mt-4 text-gray-600 dark:text-gray-400",children:"جاري تحميل الإعدادات..."})]})})]}):(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",children:[(0,t.jsx)(r.V,{}),(0,t.jsxs)("main",{className:"container mx-auto px-4 py-8",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)(l.$,{variant:"outline",size:"sm",asChild:!0,className:"mb-4",children:(0,t.jsxs)("a",{href:"/dashboard/admin",children:[(0,t.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"العودة للوحة التحكم"]})}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white arabic-text",children:"⚙️ إعدادات صفحة الدفع"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-300 mt-2 arabic-text",children:"تخصيص حقول ومعلومات صفحة إتمام الطلب"})]}),(0,t.jsxs)("div",{className:"flex gap-3",children:[(0,t.jsxs)(l.$,{variant:"outline",onClick:()=>{confirm("هل أنت متأكد من إعادة تعيين جميع الإعدادات للقيم الافتراضية؟")&&(A.B.resetToDefaults(),a(A.B.getSettings()))},children:[(0,t.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"إعادة تعيين"]}),(0,t.jsxs)(l.$,{onClick:()=>{e&&(E(!0),A.B.saveSettings(e),setTimeout(()=>E(!1),1e3))},disabled:X,children:[(0,t.jsx)(g.A,{className:"h-4 w-4 mr-2"}),X?"جاري الحفظ...":"حفظ التغييرات"]})]})]})]}),(0,t.jsxs)(x.tU,{value:s,onValueChange:O,className:"space-y-6",children:[(0,t.jsxs)(x.j7,{className:"grid w-full grid-cols-4",children:[(0,t.jsxs)(x.Xi,{value:"fields",className:"arabic-text",children:[(0,t.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"الحقول"]}),(0,t.jsxs)(x.Xi,{value:"payment",className:"arabic-text",children:[(0,t.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"طرق الدفع"]}),(0,t.jsxs)(x.Xi,{value:"delivery",className:"arabic-text",children:[(0,t.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"التوصيل"]}),(0,t.jsxs)(x.Xi,{value:"general",className:"arabic-text",children:[(0,t.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"عام"]})]}),(0,t.jsx)(x.av,{value:"fields",className:"space-y-6",children:(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsx)(n.ZB,{className:"arabic-text",children:"إدارة حقول المعلومات"}),(0,t.jsx)(n.BT,{className:"arabic-text",children:"تحكم في الحقول المطلوبة في صفحة الدفع"})]}),(0,t.jsxs)(n.Wu,{className:"space-y-4",children:[e.fields.sort((e,a)=>e.order-a.order).map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)(y.A,{className:"h-4 w-4 text-gray-400"}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("h4",{className:"font-medium arabic-text",children:e.label}),e.required&&(0,t.jsx)(m.E,{variant:"destructive",className:"text-xs",children:"مطلوب"}),(0,t.jsx)(m.E,{variant:"outline",className:"text-xs",children:e.type})]}),(0,t.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:["القسم: ","personal"===e.section?"شخصي":"shipping"===e.section?"الشحن":"الفواتير"]})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(o.d,{checked:e.enabled,onCheckedChange:()=>_(e.id)}),e.enabled?(0,t.jsx)(N.A,{className:"h-4 w-4 text-green-600"}):(0,t.jsx)(k.A,{className:"h-4 w-4 text-gray-400"}),(0,t.jsx)(l.$,{size:"sm",variant:"outline",children:(0,t.jsx)(w.A,{className:"h-4 w-4"})}),(0,t.jsx)(l.$,{size:"sm",variant:"outline",children:(0,t.jsx)(S.A,{className:"h-4 w-4"})})]})]},e.id)),(0,t.jsxs)(l.$,{variant:"outline",className:"w-full",children:[(0,t.jsx)(C.A,{className:"h-4 w-4 mr-2"}),"إضافة حقل جديد"]})]})]})}),(0,t.jsx)(x.av,{value:"payment",className:"space-y-6",children:(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsx)(n.ZB,{className:"arabic-text",children:"طرق الدفع المتاحة"}),(0,t.jsx)(n.BT,{className:"arabic-text",children:"تفعيل أو إلغاء طرق الدفع المختلفة"})]}),(0,t.jsx)(n.Wu,{className:"space-y-4",children:e.paymentMethods.sort((e,a)=>e.order-a.order).map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)(j.A,{className:"h-5 w-5 text-blue-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium arabic-text",children:e.name}),(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:e.description})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(o.d,{checked:e.enabled,onCheckedChange:()=>z(e.id)}),e.enabled?(0,t.jsx)(m.E,{variant:"default",children:"مفعل"}):(0,t.jsx)(m.E,{variant:"secondary",children:"معطل"}),(0,t.jsx)(l.$,{size:"sm",variant:"outline",children:(0,t.jsx)(w.A,{className:"h-4 w-4"})})]})]},e.id))})]})}),(0,t.jsx)(x.av,{value:"delivery",className:"space-y-6",children:(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsx)(n.ZB,{className:"arabic-text",children:"خيارات التوصيل"}),(0,t.jsx)(n.BT,{className:"arabic-text",children:"إدارة طرق وأسعار التوصيل المختلفة"})]}),(0,t.jsxs)(n.Wu,{className:"space-y-4",children:[e.deliveryOptions.sort((e,a)=>e.order-a.order).map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)(v.A,{className:"h-5 w-5 text-green-600"}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("h4",{className:"font-medium arabic-text",children:e.name}),(0,t.jsxs)(m.E,{variant:"outline",children:[e.price," درهم"]})]}),(0,t.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:[e.description," - ",e.estimatedDays]})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(o.d,{checked:e.enabled,onCheckedChange:()=>D(e.id)}),e.enabled?(0,t.jsx)(m.E,{variant:"default",children:"متاح"}):(0,t.jsx)(m.E,{variant:"secondary",children:"غير متاح"}),(0,t.jsx)(l.$,{size:"sm",variant:"outline",children:(0,t.jsx)(w.A,{className:"h-4 w-4"})})]})]},e.id)),(0,t.jsxs)(l.$,{variant:"outline",className:"w-full",children:[(0,t.jsx)(C.A,{className:"h-4 w-4 mr-2"}),"إضافة خيار توصيل جديد"]})]})]})}),(0,t.jsx)(x.av,{value:"general",className:"space-y-6",children:(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsx)(n.ZB,{className:"arabic-text",children:"الإعدادات العامة"}),(0,t.jsx)(n.BT,{className:"arabic-text",children:"إعدادات عامة لصفحة الدفع"})]}),(0,t.jsxs)(n.Wu,{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(c.J,{className:"arabic-text",children:"العملة الافتراضية"}),(0,t.jsx)(d.p,{value:e.general.currency,onChange:s=>a({...e,general:{...e.general,currency:s.target.value}})})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(c.J,{className:"arabic-text",children:"البلد الافتراضي"}),(0,t.jsx)(d.p,{value:e.general.defaultCountry,onChange:s=>a({...e,general:{...e.general,defaultCountry:s.target.value}})})]})]}),(0,t.jsx)(h.w,{}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(c.J,{className:"arabic-text",children:"طلب الموافقة على الشروط والأحكام"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"إجبار المستخدمين على الموافقة قبل إتمام الطلب"})]}),(0,t.jsx)(o.d,{checked:e.general.requireTermsAcceptance,onCheckedChange:s=>a({...e,general:{...e.general,requireTermsAcceptance:s}})})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(c.J,{className:"arabic-text",children:"السماح بالدفع كضيف"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"السماح للمستخدمين بإتمام الطلب بدون تسجيل"})]}),(0,t.jsx)(o.d,{checked:e.general.allowGuestCheckout,onCheckedChange:s=>a({...e,general:{...e.general,allowGuestCheckout:s}})})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(c.J,{className:"arabic-text",children:"عرض ملخص الطلب"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"إظهار تفاصيل الطلب والأسعار"})]}),(0,t.jsx)(o.d,{checked:e.general.showOrderSummary,onCheckedChange:s=>a({...e,general:{...e.general,showOrderSummary:s}})})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(c.J,{className:"arabic-text",children:"تفعيل التعليمات الخاصة"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"السماح للعملاء بإضافة ملاحظات خاصة"})]}),(0,t.jsx)(o.d,{checked:e.general.enableSpecialInstructions,onCheckedChange:s=>a({...e,general:{...e.general,enableSpecialInstructions:s}})})]})]})]})]})})]})]})]})}}},e=>{var a=a=>e(e.s=a);e.O(0,[7598,5486,380,2433,6874,8698,6671,7889,9221,7982,2632,3898,6566,8441,1684,7358],()=>a(63770)),_N_E=e.O()}]);