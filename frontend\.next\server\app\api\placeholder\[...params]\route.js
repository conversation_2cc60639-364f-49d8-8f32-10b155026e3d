(()=>{var e={};e.id=194,e.ids=[194],e.modules={1433:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>f,routeModule:()=>p,serverHooks:()=>h,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>c});var s={};r.r(s),r.d(s,{GET:()=>l});var a=r(96559),o=r(48088),i=r(37719),n=r(32190);async function l(e,{params:t}){try{let[e,r]=t.params,s=parseInt(e)||400,a=parseInt(r)||300,o=Math.min(Math.max(s,50),2e3),i=Math.min(Math.max(a,50),2e3),l=`
      <svg width="${o}" height="${i}" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#e3f2fd;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#bbdefb;stop-opacity:1" />
          </linearGradient>
        </defs>
        <rect width="100%" height="100%" fill="url(#grad)"/>
        <rect x="10" y="10" width="${o-20}" height="${i-20}" 
              fill="none" stroke="#90caf9" stroke-width="2" stroke-dasharray="5,5"/>
        
        <!-- أيقونة الصورة -->
        <g transform="translate(${o/2-30}, ${i/2-30})">
          <rect x="10" y="15" width="40" height="30" fill="none" stroke="#64b5f6" stroke-width="2" rx="2"/>
          <circle cx="20" cy="25" r="3" fill="#64b5f6"/>
          <polygon points="15,35 25,25 35,30 45,20 45,40 15,40" fill="#64b5f6"/>
        </g>
        
        <!-- النص -->
        <text x="${o/2}" y="${i/2+50}" 
              text-anchor="middle" 
              font-family="Arial, sans-serif" 
              font-size="14" 
              fill="#1976d2">
          ${o} \xd7 ${i}
        </text>
        
        <!-- نص عربي -->
        <text x="${o/2}" y="${i/2+70}" 
              text-anchor="middle" 
              font-family="Arial, sans-serif" 
              font-size="12" 
              fill="#666">
          صورة تجريبية
        </text>
      </svg>
    `;return new n.NextResponse(l,{headers:{"Content-Type":"image/svg+xml","Cache-Control":"public, max-age=31536000, immutable"}})}catch(t){console.error("Error generating placeholder:",t);let e=`
      <svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
        <rect width="100%" height="100%" fill="#f5f5f5"/>
        <text x="200" y="150" text-anchor="middle" font-family="Arial" font-size="16" fill="#999">
          خطأ في تحميل الصورة
        </text>
      </svg>
    `;return new n.NextResponse(e,{headers:{"Content-Type":"image/svg+xml","Cache-Control":"no-cache"}})}}let p=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/placeholder/[...params]/route",pathname:"/api/placeholder/[...params]",filename:"route",bundlePath:"app/api/placeholder/[...params]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\placeholder\\[...params]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:c,serverHooks:h}=p;function f(){return(0,i.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:c})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580],()=>r(1433));module.exports=s})();