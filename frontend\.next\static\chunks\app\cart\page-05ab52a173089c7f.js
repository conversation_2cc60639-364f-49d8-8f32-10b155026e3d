(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4005],{84616:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},92138:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},98015:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>u});var t=a(95155),l=a(12115),c=a(5323),i=a(66695),r=a(30285),n=a(26126),x=a(50983),d=a(372),h=a(27809),m=a(86151),j=a(62525);let o=(0,a(19946).A)("minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]);var N=a(84616),p=a(92138);function u(){let{cartItems:e,cartCount:s,removeFromCart:a,updateQuantity:u,clearCart:y,getCartTotal:b}=(0,c._)(),[g,v]=(0,l.useState)(!1),f=(e,s)=>{s<1?a(e):u(e,s)},w=e=>{a(e)};return 0===s?(0,t.jsx)(x.Mx,{containerClassName:"container mx-auto px-4 py-8",children:(0,t.jsxs)("div",{className:"text-center py-16",children:[(0,t.jsx)(h.A,{className:"h-24 w-24 text-gray-400 mx-auto mb-6"}),(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-4 arabic-text",children:"السلة فارغة"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-8 arabic-text",children:"لم تقم بإضافة أي منتجات للسلة بعد"}),(0,t.jsx)(r.$,{asChild:!0,children:(0,t.jsxs)("a",{href:"/catalog",children:[(0,t.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"تصفح المنتجات"]})})]})}):(0,t.jsxs)(x.Mx,{containerClassName:"container mx-auto px-4 py-8",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-2 arabic-text",children:"\uD83D\uDED2 سلة التسوق"}),(0,t.jsxs)("p",{className:"text-gray-600 dark:text-gray-400 arabic-text",children:["لديك ",s," منتج في السلة"]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,t.jsxs)("div",{className:"lg:col-span-2 space-y-4",children:[e.map(e=>(0,t.jsx)(i.Zp,{className:"overflow-hidden",children:(0,t.jsx)(i.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex gap-4",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)(d.u,{src:e.image,alt:e.name,width:120,height:120,className:"w-20 h-20 md:w-24 md:h-24 object-cover rounded-lg border"})}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-900 dark:text-white arabic-text line-clamp-2",children:e.name}),(0,t.jsx)(r.$,{size:"sm",variant:"ghost",onClick:()=>w(e.id),className:"text-red-600 hover:text-red-700 hover:bg-red-50",children:(0,t.jsx)(j.A,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-3",children:[(0,t.jsx)(n.E,{variant:"rental"===e.type?"secondary":"default",children:"rental"===e.type?"إيجار":"شراء"}),(0,t.jsxs)("span",{className:"text-lg font-bold text-blue-600",children:[e.price," Dhs"]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"الكمية:"}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(r.$,{size:"sm",variant:"outline",onClick:()=>f(e.id,e.quantity-1),disabled:e.quantity<=1,children:(0,t.jsx)(o,{className:"h-3 w-3"})}),(0,t.jsx)("span",{className:"w-8 text-center font-medium",children:e.quantity}),(0,t.jsx)(r.$,{size:"sm",variant:"outline",onClick:()=>f(e.id,e.quantity+1),children:(0,t.jsx)(N.A,{className:"h-3 w-3"})})]}),(0,t.jsxs)("span",{className:"text-sm text-gray-500 mr-auto",children:["المجموع: ",(e.price*e.quantity).toFixed(2)," Dhs"]})]})]})]})})},"".concat(e.id,"-").concat(e.type))),(0,t.jsx)("div",{className:"flex justify-end",children:(0,t.jsxs)(r.$,{variant:"outline",onClick:()=>{y()},className:"text-red-600 hover:text-red-700 hover:bg-red-50",children:[(0,t.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"إفراغ السلة"]})})]}),(0,t.jsx)("div",{className:"lg:col-span-1",children:(0,t.jsxs)(i.Zp,{className:"sticky top-4",children:[(0,t.jsx)(i.aR,{children:(0,t.jsx)(i.ZB,{className:"arabic-text",children:"ملخص الطلب"})}),(0,t.jsxs)(i.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,t.jsx)("span",{className:"arabic-text",children:"عدد المنتجات:"}),(0,t.jsx)("span",{children:s})]}),(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,t.jsx)("span",{className:"arabic-text",children:"المجموع الفرعي:"}),(0,t.jsxs)("span",{children:[b().toFixed(2)," Dhs"]})]}),(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,t.jsx)("span",{className:"arabic-text",children:"الشحن:"}),(0,t.jsx)("span",{className:"text-green-600",children:"مجاني"})]}),(0,t.jsx)("div",{className:"border-t pt-2",children:(0,t.jsxs)("div",{className:"flex justify-between font-bold text-lg",children:[(0,t.jsx)("span",{className:"arabic-text",children:"المجموع الكلي:"}),(0,t.jsxs)("span",{className:"text-blue-600",children:[b().toFixed(2)," Dhs"]})]})})]}),(0,t.jsx)(r.$,{className:"w-full",size:"lg",onClick:()=>{v(!0),setTimeout(()=>{v(!1),window.location.href="/checkout"},500)},disabled:g,children:g?"جاري المعالجة...":(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"متابعة للدفع"]})}),(0,t.jsx)(r.$,{variant:"outline",className:"w-full",asChild:!0,children:(0,t.jsxs)("a",{href:"/catalog",children:[(0,t.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"متابعة التسوق"]})})]})]})})]})]})}},99079:(e,s,a)=>{Promise.resolve().then(a.bind(a,98015))}},e=>{var s=s=>e(e.s=s);e.O(0,[7598,5486,380,2433,6874,8698,6671,7889,9221,2632,3898,6566,9614,8441,1684,7358],()=>s(99079)),_N_E=e.O()}]);