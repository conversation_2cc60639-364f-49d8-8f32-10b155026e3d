(()=>{var e={};e.id=6347,e.ids=[6347],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6710:(e,t,a)=>{"use strict";a.a(e,async(e,s)=>{try{a.d(t,{AD:()=>d,P:()=>o,Rn:()=>n,wU:()=>c});var r=a(64939),i=e([r]);let u=new(r=(i.then?(await i)():i)[0]).Pool({user:process.env.POSTGRES_USER||"postgres",host:process.env.POSTGRES_HOST||"localhost",database:process.env.POSTGRES_DB||"graduation_platform",password:process.env.POSTGRES_PASSWORD||"password",port:parseInt(process.env.POSTGRES_PORT||"5432"),max:20,idleTimeoutMillis:3e4,connectionTimeoutMillis:2e3,ssl:{rejectUnauthorized:!1}});async function E(){try{return await u.connect()}catch(e){throw console.error("خطأ في الاتصال بقاعدة البيانات:",e),Error("فشل في الاتصال بقاعدة البيانات")}}async function o(e,t){let a=await E();try{Date.now();let s=await a.query(e,t);return Date.now(),s}catch(e){throw console.error("خطأ في تنفيذ الاستعلام:",e),e}finally{a.release()}}async function n(e){let t=await E();try{await t.query("BEGIN");let a=await e(t);return await t.query("COMMIT"),a}catch(e){throw await t.query("ROLLBACK"),e}finally{t.release()}}async function d(){try{return(await o("SELECT NOW() as current_time")).rows.length>0}catch(e){return console.error("فشل في فحص حالة قاعدة البيانات:",e),!1}}async function c(){try{console.log("بدء تهيئة قاعدة البيانات..."),await o(`
      CREATE TABLE IF NOT EXISTS users (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        first_name VARCHAR(100) NOT NULL,
        last_name VARCHAR(100) NOT NULL,
        phone VARCHAR(20),
        role VARCHAR(20) DEFAULT 'customer' CHECK (role IN ('admin', 'customer', 'school', 'delivery')),
        is_active BOOLEAN DEFAULT true,
        email_verified BOOLEAN DEFAULT false,
        profile_image TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `),await o(`
      CREATE TABLE IF NOT EXISTS categories (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name_ar VARCHAR(100) NOT NULL,
        name_en VARCHAR(100),
        name_fr VARCHAR(100),
        slug VARCHAR(100) UNIQUE NOT NULL,
        description TEXT,
        icon VARCHAR(50),
        parent_id UUID REFERENCES categories(id) ON DELETE CASCADE,
        order_index INTEGER DEFAULT 0,
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `),await o(`
      CREATE TABLE IF NOT EXISTS products (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name VARCHAR(255) NOT NULL,
        description TEXT,
        category_id UUID REFERENCES categories(id) ON DELETE SET NULL,
        price DECIMAL(10,2) NOT NULL,
        rental_price DECIMAL(10,2),
        colors TEXT[] DEFAULT '{}',
        sizes TEXT[] DEFAULT '{}',
        images TEXT[] DEFAULT '{}',
        stock_quantity INTEGER DEFAULT 0,
        is_available BOOLEAN DEFAULT true,
        is_published BOOLEAN DEFAULT true,
        features TEXT[] DEFAULT '{}',
        specifications JSONB DEFAULT '{}',
        rating DECIMAL(3,2) DEFAULT 0,
        reviews_count INTEGER DEFAULT 0,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `),await o(`
      CREATE TABLE IF NOT EXISTS schools (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        admin_id UUID REFERENCES users(id) ON DELETE CASCADE,
        name VARCHAR(255) NOT NULL,
        name_en VARCHAR(255),
        name_fr VARCHAR(255),
        address TEXT,
        city VARCHAR(100),
        phone VARCHAR(20),
        email VARCHAR(255),
        website VARCHAR(255),
        logo_url TEXT,
        graduation_date DATE,
        student_count INTEGER DEFAULT 0,
        is_active BOOLEAN DEFAULT true,
        settings JSONB DEFAULT '{}',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `),await o(`
      CREATE TABLE IF NOT EXISTS orders (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID REFERENCES users(id) ON DELETE CASCADE,
        school_id UUID REFERENCES schools(id) ON DELETE SET NULL,
        order_number VARCHAR(50) UNIQUE NOT NULL,
        status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled')),
        total_amount DECIMAL(10,2) NOT NULL,
        shipping_amount DECIMAL(10,2) DEFAULT 0,
        tax_amount DECIMAL(10,2) DEFAULT 0,
        discount_amount DECIMAL(10,2) DEFAULT 0,
        payment_method VARCHAR(50),
        payment_status VARCHAR(20) DEFAULT 'pending' CHECK (payment_status IN ('pending', 'paid', 'failed', 'refunded')),
        shipping_address JSONB,
        billing_address JSONB,
        notes TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `),await o(`
      CREATE TABLE IF NOT EXISTS order_items (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
        product_id UUID REFERENCES products(id) ON DELETE CASCADE,
        quantity INTEGER NOT NULL,
        unit_price DECIMAL(10,2) NOT NULL,
        total_price DECIMAL(10,2) NOT NULL,
        type VARCHAR(20) DEFAULT 'purchase' CHECK (type IN ('purchase', 'rental')),
        size VARCHAR(50),
        color VARCHAR(50),
        customizations JSONB DEFAULT '{}',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `),await o(`
      CREATE TABLE IF NOT EXISTS reviews (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID REFERENCES users(id) ON DELETE CASCADE,
        product_id UUID REFERENCES products(id) ON DELETE CASCADE,
        order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
        rating INTEGER CHECK (rating >= 1 AND rating <= 5),
        comment TEXT,
        images TEXT[] DEFAULT '{}',
        is_verified BOOLEAN DEFAULT false,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(user_id, product_id, order_id)
      )
    `),await o(`
      CREATE TABLE IF NOT EXISTS menu_items (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        title_ar VARCHAR(100) NOT NULL,
        title_en VARCHAR(100),
        title_fr VARCHAR(100),
        slug VARCHAR(100) NOT NULL,
        icon VARCHAR(50),
        parent_id UUID REFERENCES menu_items(id) ON DELETE CASCADE,
        order_index INTEGER DEFAULT 0,
        is_active BOOLEAN DEFAULT true,
        target_type VARCHAR(20) DEFAULT 'internal' CHECK (target_type IN ('internal', 'external', 'page')),
        target_value VARCHAR(255),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `),await o("CREATE INDEX IF NOT EXISTS idx_products_category ON products(category_id)"),await o("CREATE INDEX IF NOT EXISTS idx_products_published ON products(is_published)"),await o("CREATE INDEX IF NOT EXISTS idx_products_available ON products(is_available)"),await o("CREATE INDEX IF NOT EXISTS idx_orders_user ON orders(user_id)"),await o("CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status)"),await o("CREATE INDEX IF NOT EXISTS idx_order_items_order ON order_items(order_id)"),await o("CREATE INDEX IF NOT EXISTS idx_reviews_product ON reviews(product_id)"),await o("CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)"),await o("CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)"),console.log("تم تهيئة قاعدة البيانات بنجاح!")}catch(e){throw console.error("خطأ في تهيئة قاعدة البيانات:",e),e}}s()}catch(e){s(e)}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},21253:(e,t,a)=>{"use strict";a.a(e,async(e,s)=>{try{a.d(t,{L:()=>E});var r=a(6710),i=e([r]);r=(i.then?(await i)():i)[0];class E{static async getAll(e={}){let t=[],a=[],s=1;e.category&&(t.push(`category_id = $${s}`),a.push(e.category),s++),void 0!==e.available&&(t.push(`is_available = $${s}`),a.push(e.available),s++),void 0!==e.published&&(t.push(`is_published = $${s}`),a.push(e.published),s++),e.search&&(t.push(`(name ILIKE $${s} OR description ILIKE $${s})`),a.push(`%${e.search}%`),s++),void 0!==e.minPrice&&(t.push(`price >= $${s}`),a.push(e.minPrice),s++),void 0!==e.maxPrice&&(t.push(`price <= $${s}`),a.push(e.maxPrice),s++);let i=t.length>0?`WHERE ${t.join(" AND ")}`:"",E=e.sortBy||"created_at",o=e.sortOrder||"DESC",n=`ORDER BY ${E} ${o}`,d=e.limit||50,c=e.offset||0,u=`LIMIT $${s} OFFSET $${s+1}`;a.push(d,c);let T=`SELECT COUNT(*) as total FROM products ${i}`,p=await (0,r.P)(T,a.slice(0,-2)),l=parseInt(p.rows[0].total),A=`
      SELECT 
        id, name, description, category_id, price, rental_price,
        colors, sizes, images, stock_quantity, is_available, is_published,
        features, specifications, rating, reviews_count, created_at, updated_at
      FROM products 
      ${i} 
      ${n} 
      ${u}
    `;return{products:(await (0,r.P)(A,a)).rows,total:l}}static async getById(e){return(await (0,r.P)("SELECT * FROM products WHERE id = $1",[e])).rows[0]||null}static async create(e){return(await (0,r.P)(`
      INSERT INTO products (
        name, description, category_id, price, rental_price,
        colors, sizes, images, stock_quantity, is_available, is_published,
        features, specifications, rating, reviews_count
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15
      ) RETURNING *
    `,[e.name,e.description,e.category_id,e.price,e.rental_price,e.colors,e.sizes,e.images,e.stock_quantity,e.is_available,e.is_published,e.features,JSON.stringify(e.specifications),e.rating||0,e.reviews_count||0])).rows[0]}static async update(e,t){let a=[],s=[],i=1;if(Object.entries(t).forEach(([e,t])=>{"id"!==e&&"created_at"!==e&&void 0!==t&&("specifications"===e?(a.push(`${e} = $${i}`),s.push(JSON.stringify(t))):(a.push(`${e} = $${i}`),s.push(t)),i++)}),0===a.length)throw Error("لا توجد حقول للتحديث");return a.push("updated_at = NOW()"),s.push(e),(await (0,r.P)(`
      UPDATE products 
      SET ${a.join(", ")} 
      WHERE id = $${i} 
      RETURNING *
    `,s)).rows[0]||null}static async delete(e){return(await (0,r.P)("DELETE FROM products WHERE id = $1",[e])).rowCount>0}static async updateRating(e){await (0,r.P)(`
      UPDATE products 
      SET 
        rating = (
          SELECT COALESCE(AVG(rating), 0) 
          FROM reviews 
          WHERE product_id = $1
        ),
        reviews_count = (
          SELECT COUNT(*) 
          FROM reviews 
          WHERE product_id = $1
        ),
        updated_at = NOW()
      WHERE id = $1
    `,[e])}static async updateStock(e,t){return await (0,r.Rn)(async a=>{let s=await a.query("SELECT stock_quantity FROM products WHERE id = $1 FOR UPDATE",[e]);if(0===s.rows.length)throw Error("المنتج غير موجود");let r=s.rows[0].stock_quantity+t;if(r<0)throw Error("المخزون غير كافي");return(await a.query("UPDATE products SET stock_quantity = $1, updated_at = NOW() WHERE id = $2",[r,e])).rowCount>0})}static async search(e,t=20){return(await (0,r.P)(`
      SELECT * FROM products 
      WHERE 
        is_published = true 
        AND is_available = true 
        AND (
          name ILIKE $1 
          OR description ILIKE $1 
          OR $2 = ANY(features)
        )
      ORDER BY 
        CASE 
          WHEN name ILIKE $1 THEN 1
          WHEN description ILIKE $1 THEN 2
          ELSE 3
        END,
        rating DESC
      LIMIT $3
    `,[`%${e}%`,e,t])).rows}static async getBestSellers(e=10){return(await (0,r.P)(`
      SELECT p.*, COALESCE(SUM(oi.quantity), 0) as total_sold
      FROM products p
      LEFT JOIN order_items oi ON p.id = oi.product_id
      LEFT JOIN orders o ON oi.order_id = o.id
      WHERE p.is_published = true AND p.is_available = true
        AND (o.status IS NULL OR o.status IN ('confirmed', 'processing', 'shipped', 'delivered'))
      GROUP BY p.id
      ORDER BY total_sold DESC, p.rating DESC
      LIMIT $1
    `,[e])).rows}static async getNewProducts(e=10){return(await (0,r.P)(`
      SELECT * FROM products
      WHERE is_published = true AND is_available = true
      ORDER BY created_at DESC
      LIMIT $1
    `,[e])).rows}static async getTopRated(e=10){return(await (0,r.P)(`
      SELECT * FROM products
      WHERE is_published = true AND is_available = true AND rating > 0
      ORDER BY rating DESC, reviews_count DESC
      LIMIT $1
    `,[e])).rows}}s()}catch(e){s(e)}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30093:(e,t,a)=>{"use strict";a.a(e,async(e,s)=>{try{a.r(t),a.d(t,{DELETE:()=>c,GET:()=>d,POST:()=>n});var r=a(32190),i=a(21253),E=a(6710),o=e([i,E]);[i,E]=o.then?(await o)():o;let u=[{name:"ثوب التخرج الكلاسيكي",description:"ثوب تخرج أنيق مصنوع من أجود الخامات، مناسب لجميع المناسبات الأكاديمية",price:299.99,rental_price:99.99,colors:["أسود","أزرق داكن","بني"],sizes:["XS","S","M","L","XL","XXL"],images:["/images/products/gown-classic-1.jpg","/images/products/gown-classic-2.jpg"],stock_quantity:25,is_available:!0,is_published:!0,features:["مقاوم للتجاعيد","قابل للغسل","خامة عالية الجودة","تصميم كلاسيكي"],specifications:{material:"بوليستر عالي الجودة",weight:"400 جرام",care:"غسيل جاف أو غسيل عادي",origin:"صنع في المغرب"}},{name:"قبعة التخرج التقليدية",description:"قبعة تخرج تقليدية مع شرابة ذهبية، رمز الإنجاز الأكاديمي",price:79.99,rental_price:29.99,colors:["أسود","أزرق"],sizes:["S","M","L"],images:["/images/products/cap-traditional-1.jpg"],stock_quantity:50,is_available:!0,is_published:!0,features:["شرابة ذهبية","قابل للتعديل","خفيف الوزن"],specifications:{material:"قطن مخلوط",tassel:"خيط ذهبي",adjustable:"نعم"}},{name:"وشاح التخرج المطرز",description:"وشاح تخرج مطرز بخيوط ذهبية، يضيف لمسة من الأناقة والتميز",price:149.99,rental_price:49.99,colors:["ذهبي","فضي","أزرق"],sizes:["واحد"],images:["/images/products/sash-embroidered-1.jpg"],stock_quantity:30,is_available:!0,is_published:!0,features:["تطريز ذهبي","حرير طبيعي","تصميم أنيق"],specifications:{material:"حرير طبيعي",embroidery:"خيط ذهبي",length:"150 سم"}},{name:"شرابة التخرج الذهبية",description:"شرابة تخرج ذهبية اللون، رمز التفوق والإنجاز الأكاديمي",price:39.99,rental_price:15.99,colors:["ذهبي","فضي"],sizes:["واحد"],images:["/images/products/tassel-gold-1.jpg"],stock_quantity:100,is_available:!0,is_published:!0,features:["خيط ذهبي","تصميم تقليدي","جودة عالية"],specifications:{material:"خيط ذهبي",length:"20 سم",weight:"50 جرام"}},{name:"قلنسوة الدكتوراه الفاخرة",description:"قلنسوة دكتوراه فاخرة مصممة خصيصاً لحفلات التخرج الأكاديمية العليا",price:199.99,rental_price:79.99,colors:["أسود","أزرق داكن"],sizes:["S","M","L"],images:["/images/products/doctoral-cap-1.jpg"],stock_quantity:15,is_available:!0,is_published:!0,features:["تصميم فاخر","خامة مميزة","للدكتوراه فقط"],specifications:{material:"مخمل فاخر",degree:"دكتوراه",style:"تقليدي"}}];async function n(e){try{console.log("بدء إضافة البيانات الأولية..."),await (0,E.AD)()||(console.log("قاعدة البيانات غير متاحة، محاولة التهيئة..."),await (0,E.wU)());let e=await i.L.getAll({limit:1});if(e.total>0)return r.NextResponse.json({message:"البيانات الأولية موجودة بالفعل",existingCount:e.total,status:"already_seeded"});let t=[];for(let e of u)try{let a=await i.L.create(e);t.push(a),console.log(`تم إنشاء المنتج: ${a.name}`)}catch(t){console.error(`فشل في إنشاء المنتج: ${e.name}`,t)}return console.log(`تم إنشاء ${t.length} منتج بنجاح`),r.NextResponse.json({message:"تم إضافة البيانات الأولية بنجاح",createdCount:t.length,products:t.map(e=>({id:e.id,name:e.name,price:e.price})),status:"success"})}catch(e){return console.error("خطأ في إضافة البيانات الأولية:",e),r.NextResponse.json({error:"فشل في إضافة البيانات الأولية",details:e instanceof Error?e.message:"خطأ غير معروف",status:"error"},{status:500})}}async function d(e){try{if(!await (0,E.AD)())return r.NextResponse.json({status:"database_unavailable",message:"قاعدة البيانات غير متاحة"},{status:503});let e=await i.L.getAll({limit:1});return r.NextResponse.json({status:e.total>0?"seeded":"not_seeded",totalProducts:e.total,message:e.total>0?`يوجد ${e.total} منتج في قاعدة البيانات`:"لا توجد منتجات في قاعدة البيانات",seedDataAvailable:u.length,timestamp:new Date().toISOString()})}catch(e){return console.error("خطأ في فحص البيانات الأولية:",e),r.NextResponse.json({status:"error",message:"فشل في فحص حالة البيانات الأولية",details:e instanceof Error?e.message:"خطأ غير معروف"},{status:500})}}async function c(e){try{return r.NextResponse.json({error:"غير مسموح في بيئة الإنتاج"},{status:403})}catch(e){return console.error("خطأ في حذف البيانات:",e),r.NextResponse.json({error:"فشل في حذف البيانات",details:e instanceof Error?e.message:"خطأ غير معروف"},{status:500})}}s()}catch(e){s(e)}})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63204:(e,t,a)=>{"use strict";a.a(e,async(e,s)=>{try{a.r(t),a.d(t,{patchFetch:()=>d,routeModule:()=>c,serverHooks:()=>p,workAsyncStorage:()=>u,workUnitAsyncStorage:()=>T});var r=a(96559),i=a(48088),E=a(37719),o=a(30093),n=e([o]);o=(n.then?(await n)():n)[0];let c=new r.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/database/seed/route",pathname:"/api/database/seed",filename:"route",bundlePath:"app/api/database/seed/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\database\\seed\\route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:u,workUnitAsyncStorage:T,serverHooks:p}=c;function d(){return(0,E.patchFetch)({workAsyncStorage:u,workUnitAsyncStorage:T})}s()}catch(e){s(e)}})},64939:e=>{"use strict";e.exports=import("pg")},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[4447,580],()=>a(63204));module.exports=s})();