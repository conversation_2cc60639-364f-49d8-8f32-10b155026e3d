"use client"

import { useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { useTranslation } from '@/hooks/useTranslation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  MobileForm,
  MobileFormGroup,
  MobileFormLabel,
  MobileFormInput,
  MobileFormButton,
  MobileFormError
} from '@/components/ui/mobile-form'
import { Eye, EyeOff, Mail, Lock } from 'lucide-react'
import Link from 'next/link'

interface LoginFormProps {
  onToggleMode: () => void
}

export function LoginForm({ onToggleMode }: LoginFormProps) {
  const { signIn, loading } = useAuth()
  const { t } = useTranslation()
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  })
  const [showPassword, setShowPassword] = useState(false)
  const [error, setError] = useState('')
  const [rememberMe, setRememberMe] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')

    if (!formData.email || !formData.password) {
      setError('يرجى ملء جميع الحقول المطلوبة')
      return
    }

    const { error } = await signIn(formData.email, formData.password)
    
    if (error) {
      setError(error.message)
    }
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  return (
    <Card className="w-full max-w-md mx-auto mobile-card">
      <CardHeader className="text-center mobile-spacing">
        <CardTitle className="mobile-text-xl sm:text-2xl font-bold">
          {t('auth.login')}
        </CardTitle>
        <CardDescription className="mobile-text-sm">
          أدخل بياناتك للوصول إلى حسابك
        </CardDescription>
      </CardHeader>
      <CardContent className="mobile-spacing">
        <MobileForm onSubmit={handleSubmit}>
          {error && (
            <Alert variant="destructive" className="mobile-alert mobile-alert-error">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <MobileFormGroup>
            <MobileFormLabel htmlFor="email" required>
              {t('auth.email')}
            </MobileFormLabel>
            <div className="relative">
              <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <MobileFormInput
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className="pl-10"
                required
              />
            </div>
          </MobileFormGroup>

          <MobileFormGroup>
            <MobileFormLabel htmlFor="password" required>
              {t('auth.password')}
            </MobileFormLabel>
            <div className="relative">
              <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <MobileFormInput
                id="password"
                type={showPassword ? 'text' : 'password'}
                placeholder="••••••••"
                value={formData.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                className="pl-10 pr-10"
                required
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-3 text-gray-400 hover:text-gray-600 touch-target"
              >
                {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </button>
            </div>
          </MobileFormGroup>

          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-0">
            <div className="flex items-center space-x-2">
              <input
                id="remember"
                type="checkbox"
                checked={rememberMe}
                onChange={(e) => setRememberMe(e.target.checked)}
                className="rounded border-gray-300 touch-target"
              />
              <MobileFormLabel htmlFor="remember" className="mobile-text-sm">
                {t('auth.rememberMe')}
              </MobileFormLabel>
            </div>
            <Link
              href="/auth/forgot-password"
              className="mobile-text-sm text-blue-600 hover:underline touch-target"
            >
              {t('auth.forgotPassword')}
            </Link>
          </div>

          <MobileFormButton
            type="submit"
            loading={loading}
            disabled={loading}
          >
            {loading ? t('common.loading') : t('auth.login')}
          </MobileFormButton>

          <div className="text-center">
            <span className="mobile-text-sm text-gray-600">
              {t('auth.dontHaveAccount')}{' '}
              <button
                type="button"
                onClick={onToggleMode}
                className="text-blue-600 hover:underline font-medium touch-target"
              >
                {t('auth.register')}
              </button>
            </span>
          </div>
        </MobileForm>
      </CardContent>
    </Card>
  )
}
