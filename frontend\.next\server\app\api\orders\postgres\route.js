"use strict";(()=>{var e={};e.id=6337,e.ids=[6337],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},24356:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{patchFetch:()=>p,routeModule:()=>u,serverHooks:()=>g,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>l});var o=r(96559),a=r(48088),i=r(37719),n=r(76467),d=e([n]);n=(d.then?(await d)():d)[0];let u=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/orders/postgres/route",pathname:"/api/orders/postgres",filename:"route",bundlePath:"app/api/orders/postgres/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\api\\orders\\postgres\\route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:c,workUnitAsyncStorage:l,serverHooks:g}=u;function p(){return(0,i.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:l})}s()}catch(e){s(e)}})},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:e=>{e.exports=import("pg")},76467:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{GET:()=>d,POST:()=>p});var o=r(32190),a=r(32450),i=r(6710),n=e([a,i]);async function d(e){try{if(!await (0,i.AD)())return o.NextResponse.json({error:"قاعدة البيانات غير متاحة"},{status:503});let{searchParams:t}=new URL(e.url),r={status:t.get("status")||void 0,user_id:t.get("user_id")||void 0,school_id:t.get("school_id")||void 0,payment_status:t.get("payment_status")||void 0,search:t.get("search")||void 0,limit:t.get("limit")?parseInt(t.get("limit")):50,offset:t.get("offset")?parseInt(t.get("offset")):0,sortBy:t.get("sortBy")||"created_at",sortOrder:t.get("sortOrder")||"DESC"},s=await a.I.getAll(r);return o.NextResponse.json({orders:s.orders,total:s.total,page:Math.floor(r.offset/r.limit)+1,totalPages:Math.ceil(s.total/r.limit),filters:r,source:"postgresql"})}catch(e){return console.error("Error fetching orders from PostgreSQL:",e),o.NextResponse.json({error:"فشل في جلب الطلبات من قاعدة البيانات"},{status:500})}}async function p(e){try{let t=await e.json();if(!t.user_id||!t.items||!Array.isArray(t.items)||0===t.items.length)return o.NextResponse.json({error:"معرف المستخدم وعناصر الطلب مطلوبة"},{status:400});for(let e of t.items)if(!e.product_id||!e.quantity||!e.unit_price)return o.NextResponse.json({error:"معرف المنتج والكمية والسعر مطلوبة لكل عنصر"},{status:400});let r=await a.I.create({user_id:t.user_id,school_id:t.school_id,items:t.items,shipping_address:t.shipping_address,billing_address:t.billing_address,payment_method:t.payment_method,notes:t.notes});return o.NextResponse.json({message:"تم إنشاء الطلب بنجاح",order:r},{status:201})}catch(e){return console.error("Error creating order:",e),o.NextResponse.json({error:"فشل في إنشاء الطلب"},{status:500})}}[a,i]=n.then?(await n)():n,s()}catch(e){s(e)}})}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,9158],()=>r(24356));module.exports=s})();