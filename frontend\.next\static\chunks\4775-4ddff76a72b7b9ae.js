"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4775],{1243:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(19946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},4229:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(19946).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},4884:(e,t,a)=>{a.d(t,{bL:()=>w,zi:()=>M});var r=a(12115),n=a(85185),o=a(6101),l=a(46081),i=a(5845),s=a(45503),d=a(11275),c=a(63655),u=a(95155),p="Switch",[h,y]=(0,l.A)(p),[f,v]=h(p),x=r.forwardRef((e,t)=>{let{__scopeSwitch:a,name:l,checked:s,defaultChecked:d,required:h,disabled:y,value:v="on",onCheckedChange:x,form:k,...m}=e,[w,M]=r.useState(null),C=(0,o.s)(t,e=>M(e)),A=r.useRef(!1),j=!w||k||!!w.closest("form"),[T,E]=(0,i.i)({prop:s,defaultProp:null!=d&&d,onChange:x,caller:p});return(0,u.jsxs)(f,{scope:a,checked:T,disabled:y,children:[(0,u.jsx)(c.sG.button,{type:"button",role:"switch","aria-checked":T,"aria-required":h,"data-state":g(T),"data-disabled":y?"":void 0,disabled:y,value:v,...m,ref:C,onClick:(0,n.m)(e.onClick,e=>{E(e=>!e),j&&(A.current=e.isPropagationStopped(),A.current||e.stopPropagation())})}),j&&(0,u.jsx)(b,{control:w,bubbles:!A.current,name:l,value:v,checked:T,required:h,disabled:y,form:k,style:{transform:"translateX(-100%)"}})]})});x.displayName=p;var k="SwitchThumb",m=r.forwardRef((e,t)=>{let{__scopeSwitch:a,...r}=e,n=v(k,a);return(0,u.jsx)(c.sG.span,{"data-state":g(n.checked),"data-disabled":n.disabled?"":void 0,...r,ref:t})});m.displayName=k;var b=r.forwardRef((e,t)=>{let{__scopeSwitch:a,control:n,checked:l,bubbles:i=!0,...c}=e,p=r.useRef(null),h=(0,o.s)(p,t),y=(0,s.Z)(l),f=(0,d.X)(n);return r.useEffect(()=>{let e=p.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(y!==l&&t){let a=new Event("click",{bubbles:i});t.call(e,l),e.dispatchEvent(a)}},[y,l,i]),(0,u.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:l,...c,tabIndex:-1,ref:h,style:{...c.style,...f,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function g(e){return e?"checked":"unchecked"}b.displayName="SwitchBubbleInput";var w=x,M=m},5623:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(19946).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},15968:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(19946).A)("list",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]])},20690:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(19946).A)("wand-sparkles",[["path",{d:"m21.64 3.64-1.28-1.28a1.21 1.21 0 0 0-1.72 0L2.36 18.64a1.21 1.21 0 0 0 0 1.72l1.28 1.28a1.2 1.2 0 0 0 1.72 0L21.64 5.36a1.2 1.2 0 0 0 0-1.72",key:"ul74o6"}],["path",{d:"m14 7 3 3",key:"1r5n42"}],["path",{d:"M5 6v4",key:"ilb8ba"}],["path",{d:"M19 14v4",key:"blhpug"}],["path",{d:"M10 2v2",key:"7u0qdc"}],["path",{d:"M7 8H3",key:"zfb6yr"}],["path",{d:"M21 16h-4",key:"1cnmox"}],["path",{d:"M11 3H9",key:"1obp7u"}]])},24357:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(19946).A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},27213:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(19946).A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},29869:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(19946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},32568:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(19946).A)("rotate-cw",[["path",{d:"M21 12a9 9 0 1 1-9-9c2.52 0 4.93 1 6.74 2.74L21 8",key:"1p45f6"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}]])},33109:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(19946).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},38564:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(19946).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},42118:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(19946).A)("file-image",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["circle",{cx:"10",cy:"12",r:"2",key:"737tya"}],["path",{d:"m20 17-1.296-1.296a2.41 2.41 0 0 0-3.408 0L9 22",key:"wt3hpn"}]])},43332:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(19946).A)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},49376:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(19946).A)("brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]])},55863:(e,t,a)=>{a.d(t,{C1:()=>g,bL:()=>b});var r=a(12115),n=a(46081),o=a(63655),l=a(95155),i="Progress",[s,d]=(0,n.A)(i),[c,u]=s(i),p=r.forwardRef((e,t)=>{var a,r,n,i;let{__scopeProgress:s,value:d=null,max:u,getValueLabel:p=f,...h}=e;(u||0===u)&&!k(u)&&console.error((a="".concat(u),r="Progress","Invalid prop `max` of value `".concat(a,"` supplied to `").concat(r,"`. Only numbers greater than 0 are valid max values. Defaulting to `").concat(100,"`.")));let y=k(u)?u:100;null===d||m(d,y)||console.error((n="".concat(d),i="Progress","Invalid prop `value` of value `".concat(n,"` supplied to `").concat(i,"`. The `value` prop must be:\n  - a positive number\n  - less than the value passed to `max` (or ").concat(100," if no `max` prop is set)\n  - `null` or `undefined` if the progress is indeterminate.\n\nDefaulting to `null`.")));let b=m(d,y)?d:null,g=x(b)?p(b,y):void 0;return(0,l.jsx)(c,{scope:s,value:b,max:y,children:(0,l.jsx)(o.sG.div,{"aria-valuemax":y,"aria-valuemin":0,"aria-valuenow":x(b)?b:void 0,"aria-valuetext":g,role:"progressbar","data-state":v(b,y),"data-value":null!=b?b:void 0,"data-max":y,...h,ref:t})})});p.displayName=i;var h="ProgressIndicator",y=r.forwardRef((e,t)=>{var a;let{__scopeProgress:r,...n}=e,i=u(h,r);return(0,l.jsx)(o.sG.div,{"data-state":v(i.value,i.max),"data-value":null!=(a=i.value)?a:void 0,"data-max":i.max,...n,ref:t})});function f(e,t){return"".concat(Math.round(e/t*100),"%")}function v(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function x(e){return"number"==typeof e}function k(e){return x(e)&&!isNaN(e)&&e>0}function m(e,t){return x(e)&&!isNaN(e)&&e<=t&&e>=0}y.displayName=h;var b=p,g=y},60704:(e,t,a)=>{a.d(t,{B8:()=>L,UC:()=>P,bL:()=>E,l9:()=>R});var r=a(12115),n=a(85185),o=a(46081),l=a(89196),i=a(28905),s=a(63655),d=a(94315),c=a(5845),u=a(61285),p=a(95155),h="Tabs",[y,f]=(0,o.A)(h,[l.RG]),v=(0,l.RG)(),[x,k]=y(h),m=r.forwardRef((e,t)=>{let{__scopeTabs:a,value:r,onValueChange:n,defaultValue:o,orientation:l="horizontal",dir:i,activationMode:y="automatic",...f}=e,v=(0,d.jH)(i),[k,m]=(0,c.i)({prop:r,onChange:n,defaultProp:null!=o?o:"",caller:h});return(0,p.jsx)(x,{scope:a,baseId:(0,u.B)(),value:k,onValueChange:m,orientation:l,dir:v,activationMode:y,children:(0,p.jsx)(s.sG.div,{dir:v,"data-orientation":l,...f,ref:t})})});m.displayName=h;var b="TabsList",g=r.forwardRef((e,t)=>{let{__scopeTabs:a,loop:r=!0,...n}=e,o=k(b,a),i=v(a);return(0,p.jsx)(l.bL,{asChild:!0,...i,orientation:o.orientation,dir:o.dir,loop:r,children:(0,p.jsx)(s.sG.div,{role:"tablist","aria-orientation":o.orientation,...n,ref:t})})});g.displayName=b;var w="TabsTrigger",M=r.forwardRef((e,t)=>{let{__scopeTabs:a,value:r,disabled:o=!1,...i}=e,d=k(w,a),c=v(a),u=j(d.baseId,r),h=T(d.baseId,r),y=r===d.value;return(0,p.jsx)(l.q7,{asChild:!0,...c,focusable:!o,active:y,children:(0,p.jsx)(s.sG.button,{type:"button",role:"tab","aria-selected":y,"aria-controls":h,"data-state":y?"active":"inactive","data-disabled":o?"":void 0,disabled:o,id:u,...i,ref:t,onMouseDown:(0,n.m)(e.onMouseDown,e=>{o||0!==e.button||!1!==e.ctrlKey?e.preventDefault():d.onValueChange(r)}),onKeyDown:(0,n.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&d.onValueChange(r)}),onFocus:(0,n.m)(e.onFocus,()=>{let e="manual"!==d.activationMode;y||o||!e||d.onValueChange(r)})})})});M.displayName=w;var C="TabsContent",A=r.forwardRef((e,t)=>{let{__scopeTabs:a,value:n,forceMount:o,children:l,...d}=e,c=k(C,a),u=j(c.baseId,n),h=T(c.baseId,n),y=n===c.value,f=r.useRef(y);return r.useEffect(()=>{let e=requestAnimationFrame(()=>f.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,p.jsx)(i.C,{present:o||y,children:a=>{let{present:r}=a;return(0,p.jsx)(s.sG.div,{"data-state":y?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":u,hidden:!r,id:h,tabIndex:0,...d,ref:t,style:{...e.style,animationDuration:f.current?"0s":void 0},children:r&&l})}})});function j(e,t){return"".concat(e,"-trigger-").concat(t)}function T(e,t){return"".concat(e,"-content-").concat(t)}A.displayName=C;var E=m,L=g,R=M,P=A},89613:(e,t,a)=>{a.d(t,{Kq:()=>O,UC:()=>F,bL:()=>B,l9:()=>V});var r=a(12115),n=a(85185),o=a(6101),l=a(46081),i=a(19178),s=a(61285),d=a(35152),c=(a(34378),a(28905)),u=a(63655),p=a(99708),h=a(5845),y=a(2564),f=a(95155),[v,x]=(0,l.A)("Tooltip",[d.Bk]),k=(0,d.Bk)(),m="TooltipProvider",b="tooltip.open",[g,w]=v(m),M=e=>{let{__scopeTooltip:t,delayDuration:a=700,skipDelayDuration:n=300,disableHoverableContent:o=!1,children:l}=e,i=r.useRef(!0),s=r.useRef(!1),d=r.useRef(0);return r.useEffect(()=>{let e=d.current;return()=>window.clearTimeout(e)},[]),(0,f.jsx)(g,{scope:t,isOpenDelayedRef:i,delayDuration:a,onOpen:r.useCallback(()=>{window.clearTimeout(d.current),i.current=!1},[]),onClose:r.useCallback(()=>{window.clearTimeout(d.current),d.current=window.setTimeout(()=>i.current=!0,n)},[n]),isPointerInTransitRef:s,onPointerInTransitChange:r.useCallback(e=>{s.current=e},[]),disableHoverableContent:o,children:l})};M.displayName=m;var C="Tooltip",[A,j]=v(C),T=e=>{let{__scopeTooltip:t,children:a,open:n,defaultOpen:o,onOpenChange:l,disableHoverableContent:i,delayDuration:c}=e,u=w(C,e.__scopeTooltip),p=k(t),[y,v]=r.useState(null),x=(0,s.B)(),m=r.useRef(0),g=null!=i?i:u.disableHoverableContent,M=null!=c?c:u.delayDuration,j=r.useRef(!1),[T,E]=(0,h.i)({prop:n,defaultProp:null!=o&&o,onChange:e=>{e?(u.onOpen(),document.dispatchEvent(new CustomEvent(b))):u.onClose(),null==l||l(e)},caller:C}),L=r.useMemo(()=>T?j.current?"delayed-open":"instant-open":"closed",[T]),R=r.useCallback(()=>{window.clearTimeout(m.current),m.current=0,j.current=!1,E(!0)},[E]),P=r.useCallback(()=>{window.clearTimeout(m.current),m.current=0,E(!1)},[E]),N=r.useCallback(()=>{window.clearTimeout(m.current),m.current=window.setTimeout(()=>{j.current=!0,E(!0),m.current=0},M)},[M,E]);return r.useEffect(()=>()=>{m.current&&(window.clearTimeout(m.current),m.current=0)},[]),(0,f.jsx)(d.bL,{...p,children:(0,f.jsx)(A,{scope:t,contentId:x,open:T,stateAttribute:L,trigger:y,onTriggerChange:v,onTriggerEnter:r.useCallback(()=>{u.isOpenDelayedRef.current?N():R()},[u.isOpenDelayedRef,N,R]),onTriggerLeave:r.useCallback(()=>{g?P():(window.clearTimeout(m.current),m.current=0)},[P,g]),onOpen:R,onClose:P,disableHoverableContent:g,children:a})})};T.displayName=C;var E="TooltipTrigger",L=r.forwardRef((e,t)=>{let{__scopeTooltip:a,...l}=e,i=j(E,a),s=w(E,a),c=k(a),p=r.useRef(null),h=(0,o.s)(t,p,i.onTriggerChange),y=r.useRef(!1),v=r.useRef(!1),x=r.useCallback(()=>y.current=!1,[]);return r.useEffect(()=>()=>document.removeEventListener("pointerup",x),[x]),(0,f.jsx)(d.Mz,{asChild:!0,...c,children:(0,f.jsx)(u.sG.button,{"aria-describedby":i.open?i.contentId:void 0,"data-state":i.stateAttribute,...l,ref:h,onPointerMove:(0,n.m)(e.onPointerMove,e=>{"touch"!==e.pointerType&&(v.current||s.isPointerInTransitRef.current||(i.onTriggerEnter(),v.current=!0))}),onPointerLeave:(0,n.m)(e.onPointerLeave,()=>{i.onTriggerLeave(),v.current=!1}),onPointerDown:(0,n.m)(e.onPointerDown,()=>{i.open&&i.onClose(),y.current=!0,document.addEventListener("pointerup",x,{once:!0})}),onFocus:(0,n.m)(e.onFocus,()=>{y.current||i.onOpen()}),onBlur:(0,n.m)(e.onBlur,i.onClose),onClick:(0,n.m)(e.onClick,i.onClose)})})});L.displayName=E;var[R,P]=v("TooltipPortal",{forceMount:void 0}),N="TooltipContent",z=r.forwardRef((e,t)=>{let a=P(N,e.__scopeTooltip),{forceMount:r=a.forceMount,side:n="top",...o}=e,l=j(N,e.__scopeTooltip);return(0,f.jsx)(c.C,{present:r||l.open,children:l.disableHoverableContent?(0,f.jsx)(H,{side:n,...o,ref:t}):(0,f.jsx)(I,{side:n,...o,ref:t})})}),I=r.forwardRef((e,t)=>{let a=j(N,e.__scopeTooltip),n=w(N,e.__scopeTooltip),l=r.useRef(null),i=(0,o.s)(t,l),[s,d]=r.useState(null),{trigger:c,onClose:u}=a,p=l.current,{onPointerInTransitChange:h}=n,y=r.useCallback(()=>{d(null),h(!1)},[h]),v=r.useCallback((e,t)=>{let a=e.currentTarget,r={x:e.clientX,y:e.clientY},n=function(e,t){let a=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),n=Math.abs(t.right-e.x),o=Math.abs(t.left-e.x);switch(Math.min(a,r,n,o)){case o:return"left";case n:return"right";case a:return"top";case r:return"bottom";default:throw Error("unreachable")}}(r,a.getBoundingClientRect());d(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:1*!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let a=0;a<e.length;a++){let r=e[a];for(;t.length>=2;){let e=t[t.length-1],a=t[t.length-2];if((e.x-a.x)*(r.y-a.y)>=(e.y-a.y)*(r.x-a.x))t.pop();else break}t.push(r)}t.pop();let a=[];for(let t=e.length-1;t>=0;t--){let r=e[t];for(;a.length>=2;){let e=a[a.length-1],t=a[a.length-2];if((e.x-t.x)*(r.y-t.y)>=(e.y-t.y)*(r.x-t.x))a.pop();else break}a.push(r)}return(a.pop(),1===t.length&&1===a.length&&t[0].x===a[0].x&&t[0].y===a[0].y)?t:t.concat(a)}(t)}([...function(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,r=[];switch(t){case"top":r.push({x:e.x-a,y:e.y+a},{x:e.x+a,y:e.y+a});break;case"bottom":r.push({x:e.x-a,y:e.y-a},{x:e.x+a,y:e.y-a});break;case"left":r.push({x:e.x+a,y:e.y-a},{x:e.x+a,y:e.y+a});break;case"right":r.push({x:e.x-a,y:e.y-a},{x:e.x-a,y:e.y+a})}return r}(r,n),...function(e){let{top:t,right:a,bottom:r,left:n}=e;return[{x:n,y:t},{x:a,y:t},{x:a,y:r},{x:n,y:r}]}(t.getBoundingClientRect())])),h(!0)},[h]);return r.useEffect(()=>()=>y(),[y]),r.useEffect(()=>{if(c&&p){let e=e=>v(e,p),t=e=>v(e,c);return c.addEventListener("pointerleave",e),p.addEventListener("pointerleave",t),()=>{c.removeEventListener("pointerleave",e),p.removeEventListener("pointerleave",t)}}},[c,p,v,y]),r.useEffect(()=>{if(s){let e=e=>{let t=e.target,a={x:e.clientX,y:e.clientY},r=(null==c?void 0:c.contains(t))||(null==p?void 0:p.contains(t)),n=!function(e,t){let{x:a,y:r}=e,n=!1;for(let e=0,o=t.length-1;e<t.length;o=e++){let l=t[e],i=t[o],s=l.x,d=l.y,c=i.x,u=i.y;d>r!=u>r&&a<(c-s)*(r-d)/(u-d)+s&&(n=!n)}return n}(a,s);r?y():n&&(y(),u())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[c,p,s,u,y]),(0,f.jsx)(H,{...e,ref:i})}),[q,D]=v(C,{isInside:!1}),_=(0,p.Dc)("TooltipContent"),H=r.forwardRef((e,t)=>{let{__scopeTooltip:a,children:n,"aria-label":o,onEscapeKeyDown:l,onPointerDownOutside:s,...c}=e,u=j(N,a),p=k(a),{onClose:h}=u;return r.useEffect(()=>(document.addEventListener(b,h),()=>document.removeEventListener(b,h)),[h]),r.useEffect(()=>{if(u.trigger){let e=e=>{let t=e.target;(null==t?void 0:t.contains(u.trigger))&&h()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[u.trigger,h]),(0,f.jsx)(i.qW,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:l,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:h,children:(0,f.jsxs)(d.UC,{"data-state":u.stateAttribute,...p,...c,ref:t,style:{...c.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,f.jsx)(_,{children:n}),(0,f.jsx)(q,{scope:a,isInside:!0,children:(0,f.jsx)(y.bL,{id:u.contentId,role:"tooltip",children:o||n})})]})})});z.displayName=N;var G="TooltipArrow";r.forwardRef((e,t)=>{let{__scopeTooltip:a,...r}=e,n=k(a);return D(G,a).isInside?null:(0,f.jsx)(d.i3,{...n,...r,ref:t})}).displayName=G;var O=M,B=T,V=L,F=z},91788:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(19946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},92657:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},94449:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(19946).A)("wifi-off",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}],["path",{d:"M5 12.859a10 10 0 0 1 5.17-2.69",key:"1dl1wf"}],["path",{d:"M19 12.859a10 10 0 0 0-2.007-1.523",key:"4k23kn"}],["path",{d:"M2 8.82a15 15 0 0 1 4.177-2.643",key:"1grhjp"}],["path",{d:"M22 8.82a15 15 0 0 0-11.288-3.764",key:"z3jwby"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])}}]);