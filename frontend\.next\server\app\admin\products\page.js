(()=>{var e={};e.id=7575,e.ids=[7575],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8819:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13861:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23496:(e,s,a)=>{Promise.resolve().then(a.bind(a,42701))},23928:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},25541:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34729:(e,s,a)=>{"use strict";a.d(s,{T:()=>i});var t=a(60687);a(43210);var r=a(4780);function i({className:e,...s}){return(0,t.jsx)("textarea",{"data-slot":"textarea",className:(0,r.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...s})}},38571:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t});let t=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Graduation Toqs\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\admin\\products\\page.tsx","default")},42701:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>P});var t=a(60687),r=a(43210),i=a(87801),l=a(44493),n=a(29523),c=a(96834),d=a(89940),o=a(2083),p=a(89667),x=a(80013),u=a(34729),h=a(54987),m=a(93613),b=a(96474),j=a(11860),v=a(8819),f=a(52581);function g({product:e,onSave:s,onCancel:a,isLoading:i=!1}){let[d,o]=(0,r.useState)({name:"",description:"",price:0,rental_price:void 0,colors:[],sizes:[],images:[],stock_quantity:0,is_available:!0,is_published:!0,features:[],specifications:{},...e}),[g,y]=(0,r.useState)(""),[N,k]=(0,r.useState)(""),[w,_]=(0,r.useState)(""),[A,C]=(0,r.useState)({}),q=()=>{let e={};return d.name.trim()||(e.name="اسم المنتج مطلوب"),d.description.trim()||(e.description="وصف المنتج مطلوب"),d.price<=0&&(e.price="السعر يجب أن يكون أكبر من صفر"),d.stock_quantity<0&&(e.stock_quantity="كمية المخزون لا يمكن أن تكون سالبة"),C(e),0===Object.keys(e).length},P=()=>{g.trim()&&!d.colors.includes(g.trim())&&(o(e=>({...e,colors:[...e.colors,g.trim()]})),y(""))},E=e=>{o(s=>({...s,colors:s.colors.filter(s=>s!==e)}))},z=()=>{N.trim()&&!d.sizes.includes(N.trim())&&(o(e=>({...e,sizes:[...e.sizes,N.trim()]})),k(""))},S=e=>{o(s=>({...s,sizes:s.sizes.filter(s=>s!==e)}))},T=()=>{w.trim()&&!d.features.includes(w.trim())&&(o(e=>({...e,features:[...e.features,w.trim()]})),_(""))},D=e=>{o(s=>({...s,features:s.features.filter(s=>s!==e)}))};return(0,t.jsxs)(l.Zp,{className:"w-full max-w-2xl mx-auto",children:[(0,t.jsxs)(l.aR,{children:[(0,t.jsx)(l.ZB,{className:"arabic-text",children:e?"تعديل المنتج":"إضافة منتج جديد"}),(0,t.jsx)(l.BT,{className:"arabic-text",children:e?"تعديل بيانات المنتج الموجود":"إضافة منتج جديد إلى المتجر"})]}),(0,t.jsxs)(l.Wu,{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(x.J,{htmlFor:"name",className:"arabic-text",children:"اسم المنتج *"}),(0,t.jsx)(p.p,{id:"name",value:d.name,onChange:e=>o(s=>({...s,name:e.target.value})),placeholder:"أدخل اسم المنتج",className:A.name?"border-red-500":""}),A.name&&(0,t.jsxs)("p",{className:"text-sm text-red-500 flex items-center gap-1",children:[(0,t.jsx)(m.A,{className:"h-3 w-3"}),A.name]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(x.J,{htmlFor:"price",className:"arabic-text",children:"السعر (Dhs) *"}),(0,t.jsx)(p.p,{id:"price",type:"number",min:"0",step:"0.01",value:d.price,onChange:e=>o(s=>({...s,price:parseFloat(e.target.value)||0})),placeholder:"0.00",className:A.price?"border-red-500":""}),A.price&&(0,t.jsxs)("p",{className:"text-sm text-red-500 flex items-center gap-1",children:[(0,t.jsx)(m.A,{className:"h-3 w-3"}),A.price]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(x.J,{htmlFor:"rental_price",className:"arabic-text",children:"سعر الإيجار (Dhs)"}),(0,t.jsx)(p.p,{id:"rental_price",type:"number",min:"0",step:"0.01",value:d.rental_price||"",onChange:e=>o(s=>({...s,rental_price:e.target.value?parseFloat(e.target.value):void 0})),placeholder:"0.00"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(x.J,{htmlFor:"stock_quantity",className:"arabic-text",children:"كمية المخزون *"}),(0,t.jsx)(p.p,{id:"stock_quantity",type:"number",min:"0",value:d.stock_quantity,onChange:e=>o(s=>({...s,stock_quantity:parseInt(e.target.value)||0})),placeholder:"0",className:A.stock_quantity?"border-red-500":""}),A.stock_quantity&&(0,t.jsxs)("p",{className:"text-sm text-red-500 flex items-center gap-1",children:[(0,t.jsx)(m.A,{className:"h-3 w-3"}),A.stock_quantity]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(x.J,{htmlFor:"description",className:"arabic-text",children:"وصف المنتج *"}),(0,t.jsx)(u.T,{id:"description",value:d.description,onChange:e=>o(s=>({...s,description:e.target.value})),placeholder:"أدخل وصف مفصل للمنتج",rows:4,className:A.description?"border-red-500":""}),A.description&&(0,t.jsxs)("p",{className:"text-sm text-red-500 flex items-center gap-1",children:[(0,t.jsx)(m.A,{className:"h-3 w-3"}),A.description]})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(x.J,{className:"arabic-text",children:"الألوان المتاحة"}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(p.p,{value:g,onChange:e=>y(e.target.value),placeholder:"أضف لون جديد",onKeyPress:e=>"Enter"===e.key&&P()}),(0,t.jsx)(n.$,{type:"button",onClick:P,size:"sm",children:(0,t.jsx)(b.A,{className:"h-4 w-4"})})]}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:d.colors.map((e,s)=>(0,t.jsxs)(c.E,{variant:"secondary",className:"flex items-center gap-1",children:[e,(0,t.jsx)("button",{type:"button",onClick:()=>E(e),className:"ml-1 hover:text-red-500",children:(0,t.jsx)(j.A,{className:"h-3 w-3"})})]},s))})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(x.J,{className:"arabic-text",children:"المقاسات المتاحة"}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(p.p,{value:N,onChange:e=>k(e.target.value),placeholder:"أضف مقاس جديد",onKeyPress:e=>"Enter"===e.key&&z()}),(0,t.jsx)(n.$,{type:"button",onClick:z,size:"sm",children:(0,t.jsx)(b.A,{className:"h-4 w-4"})})]}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:d.sizes.map((e,s)=>(0,t.jsxs)(c.E,{variant:"secondary",className:"flex items-center gap-1",children:[e,(0,t.jsx)("button",{type:"button",onClick:()=>S(e),className:"ml-1 hover:text-red-500",children:(0,t.jsx)(j.A,{className:"h-3 w-3"})})]},s))})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(x.J,{className:"arabic-text",children:"ميزات المنتج"}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(p.p,{value:w,onChange:e=>_(e.target.value),placeholder:"أضف ميزة جديدة",onKeyPress:e=>"Enter"===e.key&&T()}),(0,t.jsx)(n.$,{type:"button",onClick:T,size:"sm",children:(0,t.jsx)(b.A,{className:"h-4 w-4"})})]}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:d.features.map((e,s)=>(0,t.jsxs)(c.E,{variant:"outline",className:"flex items-center gap-1",children:[e,(0,t.jsx)("button",{type:"button",onClick:()=>D(e),className:"ml-1 hover:text-red-500",children:(0,t.jsx)(j.A,{className:"h-3 w-3"})})]},s))})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(h.d,{id:"is_available",checked:d.is_available,onCheckedChange:e=>o(s=>({...s,is_available:e}))}),(0,t.jsx)(x.J,{htmlFor:"is_available",className:"arabic-text",children:"متاح للبيع"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(h.d,{id:"is_published",checked:d.is_published,onCheckedChange:e=>o(s=>({...s,is_published:e}))}),(0,t.jsx)(x.J,{htmlFor:"is_published",className:"arabic-text",children:"منشور في المتجر"})]})]}),(0,t.jsxs)("div",{className:"flex justify-end gap-3 pt-6 border-t",children:[(0,t.jsxs)(n.$,{variant:"outline",onClick:a,disabled:i,children:[(0,t.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"إلغاء"]}),(0,t.jsxs)(n.$,{onClick:()=>{q()?s(d):f.o.error("يرجى تصحيح الأخطاء في النموذج")},disabled:i,children:[(0,t.jsx)(v.A,{className:"h-4 w-4 mr-2"}),i?"جاري الحفظ...":"حفظ المنتج"]})]})]})]})}var y=a(78122),N=a(61611),k=a(19080),w=a(13861),_=a(25541),A=a(23928),C=a(63143),q=a(88233);function P(){let[e,s]=(0,r.useState)([]),[a,p]=(0,r.useState)(!0),[x,u]=(0,r.useState)(!1),[h,j]=(0,r.useState)(!1),[v,P]=(0,r.useState)(void 0),[E,z]=(0,r.useState)(!1),[S,T]=(0,r.useState)({total:0,published:0,available:0,totalValue:0}),D=async()=>{try{p(!0);let e=await fetch("/api/products"),a=await e.json();e.ok?(s(a.products||[]),M(a.products||[])):f.o.error(a.error||"فشل في جلب المنتجات")}catch(e){console.error("Error fetching products:",e),f.o.error("خطأ في الاتصال بالخادم")}finally{p(!1)}},M=e=>{T({total:e.length,published:e.filter(e=>e.is_published).length,available:e.filter(e=>e.is_available).length,totalValue:e.reduce((e,s)=>e+s.price*s.stock_quantity,0)})},$=async()=>{try{u(!0);let e=await fetch("/api/database/seed",{method:"POST"}),s=await e.json();e.ok?(f.o.success(s.message),D()):f.o.error(s.error||"فشل في إضافة البيانات الأولية")}catch(e){console.error("Error seeding database:",e),f.o.error("خطأ في إضافة البيانات الأولية")}finally{u(!1)}},G=async(e,s)=>{if(confirm(`هل أنت متأكد من حذف المنتج "${s}"؟`))try{let s=await fetch(`/api/products/${e}`,{method:"DELETE"}),a=await s.json();s.ok?(f.o.success(a.message),D()):f.o.error(a.error||"فشل في حذف المنتج")}catch(e){console.error("Error deleting product:",e),f.o.error("خطأ في حذف المنتج")}},J=async(e,s)=>{try{let a=await fetch(`/api/products/${e}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({is_published:!s})}),t=await a.json();a.ok?(f.o.success(t.message),D()):f.o.error(t.error||"فشل في تحديث المنتج")}catch(e){console.error("Error updating product:",e),f.o.error("خطأ في تحديث المنتج")}},R=async e=>{try{z(!0);let s=await fetch("/api/products",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),a=await s.json();s.ok?(f.o.success(a.message),j(!1),D()):f.o.error(a.error||"فشل في إنشاء المنتج")}catch(e){console.error("Error creating product:",e),f.o.error("خطأ في إنشاء المنتج")}finally{z(!1)}},Z=async e=>{if(v?.id)try{z(!0);let s=await fetch(`/api/products/${v.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),a=await s.json();s.ok?(f.o.success(a.message),j(!1),P(void 0),D()):f.o.error(a.error||"فشل في تحديث المنتج")}catch(e){console.error("Error updating product:",e),f.o.error("خطأ في تحديث المنتج")}finally{z(!1)}},F=e=>{P(e),j(!0)};return(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",children:[(0,t.jsx)(i.V,{}),(0,t.jsxs)(d.Mx,{containerClassName:"container mx-auto px-4 py-8",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-2 arabic-text",children:"\uD83D\uDECD️ إدارة المنتجات"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-400 arabic-text",children:"إدارة وتحرير منتجات المنصة"})]}),(0,t.jsxs)("div",{className:"flex gap-3",children:[(0,t.jsxs)(n.$,{onClick:D,variant:"outline",disabled:a,children:[(0,t.jsx)(y.A,{className:`h-4 w-4 mr-2 ${a?"animate-spin":""}`}),"تحديث"]}),(0,t.jsxs)(n.$,{onClick:$,variant:"outline",disabled:x||e.length>0,children:[(0,t.jsx)(N.A,{className:`h-4 w-4 mr-2 ${x?"animate-pulse":""}`}),x?"جاري الإضافة...":"إضافة بيانات أولية"]}),(0,t.jsxs)(n.$,{onClick:()=>{P(void 0),j(!0)},children:[(0,t.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"إضافة منتج جديد"]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[(0,t.jsx)(l.Zp,{children:(0,t.jsx)(l.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"إجمالي المنتجات"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:S.total})]}),(0,t.jsx)(k.A,{className:"h-8 w-8 text-blue-600"})]})})}),(0,t.jsx)(l.Zp,{children:(0,t.jsx)(l.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"منشور"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-green-600",children:S.published})]}),(0,t.jsx)(w.A,{className:"h-8 w-8 text-green-600"})]})})}),(0,t.jsx)(l.Zp,{children:(0,t.jsx)(l.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"متاح"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:S.available})]}),(0,t.jsx)(_.A,{className:"h-8 w-8 text-blue-600"})]})})}),(0,t.jsx)(l.Zp,{children:(0,t.jsx)(l.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 arabic-text",children:"قيمة المخزون"}),(0,t.jsxs)("p",{className:"text-2xl font-bold text-purple-600",children:[S.totalValue.toFixed(2)," Dhs"]})]}),(0,t.jsx)(A.A,{className:"h-8 w-8 text-purple-600"})]})})})]})]}),a?(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)(y.A,{className:"h-8 w-8 animate-spin mx-auto mb-4 text-blue-600"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-400 arabic-text",children:"جاري تحميل المنتجات..."})]}):0===e.length?(0,t.jsx)(l.Zp,{children:(0,t.jsxs)(l.Wu,{className:"text-center py-12",children:[(0,t.jsx)(m.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-2 arabic-text",children:"لا توجد منتجات"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-4 arabic-text",children:"لم يتم العثور على أي منتجات في قاعدة البيانات"}),(0,t.jsxs)(n.$,{onClick:$,disabled:x,children:[(0,t.jsx)(N.A,{className:`h-4 w-4 mr-2 ${x?"animate-pulse":""}`}),x?"جاري الإضافة...":"إضافة بيانات أولية"]})]})}):(0,t.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6",children:e.map(e=>(0,t.jsxs)(l.Zp,{className:"overflow-hidden",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(o.u,{src:e.images[0]||"/images/placeholder-product.jpg",alt:e.name,width:400,height:200,className:"w-full h-48 object-cover"}),(0,t.jsxs)("div",{className:"absolute top-2 right-2 flex gap-2",children:[(0,t.jsx)(c.E,{variant:e.is_published?"default":"secondary",children:e.is_published?"منشور":"مخفي"}),(0,t.jsx)(c.E,{variant:e.is_available?"default":"destructive",children:e.is_available?"متاح":"غير متاح"})]})]}),(0,t.jsxs)(l.aR,{children:[(0,t.jsx)(l.ZB,{className:"arabic-text line-clamp-1",children:e.name}),(0,t.jsx)(l.BT,{className:"arabic-text line-clamp-2",children:e.description})]}),(0,t.jsx)(l.Wu,{children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("span",{className:"text-lg font-bold text-blue-600",children:[e.price," Dhs"]}),e.rental_price&&(0,t.jsxs)("span",{className:"text-sm text-gray-600",children:["إيجار: ",e.rental_price," Dhs"]})]}),(0,t.jsxs)("div",{className:"flex justify-between text-sm text-gray-600",children:[(0,t.jsxs)("span",{children:["المخزون: ",e.stock_quantity]}),(0,t.jsxs)("span",{children:["التقييم: ",e.rating,"/5 (",e.reviews_count,")"]})]}),(0,t.jsxs)("div",{className:"flex gap-2 pt-3",children:[(0,t.jsxs)(n.$,{size:"sm",variant:"outline",className:"flex-1",onClick:()=>F(e),children:[(0,t.jsx)(C.A,{className:"h-3 w-3 mr-1"}),"تعديل"]}),(0,t.jsxs)(n.$,{size:"sm",variant:"outline",onClick:()=>J(e.id,e.is_published),children:[(0,t.jsx)(w.A,{className:"h-3 w-3 mr-1"}),e.is_published?"إخفاء":"نشر"]}),(0,t.jsx)(n.$,{size:"sm",variant:"destructive",onClick:()=>G(e.id,e.name),children:(0,t.jsx)(q.A,{className:"h-3 w-3"})})]})]})})]},e.id))}),h&&(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:(0,t.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto",children:(0,t.jsx)(g,{product:v,onSave:e=>{v?Z(e):R(e)},onCancel:()=>{j(!1),P(void 0)},isLoading:E})})})]})]})}},54748:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>l.a,__next_app__:()=>p,pages:()=>o,routeModule:()=>x,tree:()=>d});var t=a(65239),r=a(48088),i=a(88170),l=a.n(i),n=a(30893),c={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);a.d(s,c);let d={children:["",{children:["admin",{children:["products",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,38571)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\admin\\products\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(a.bind(a,54431)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,54413)),"C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Desktop\\Graduation Toqs\\frontend\\src\\app\\admin\\products\\page.tsx"],p={require:a,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/admin/products/page",pathname:"/admin/products",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},54987:(e,s,a)=>{"use strict";a.d(s,{d:()=>l});var t=a(60687);a(43210);var r=a(90270),i=a(4780);function l({className:e,...s}){return(0,t.jsx)(r.bL,{"data-slot":"switch",className:(0,i.cn)("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...s,children:(0,t.jsx)(r.zi,{"data-slot":"switch-thumb",className:(0,i.cn)("bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0")})})}},59944:(e,s,a)=>{Promise.resolve().then(a.bind(a,38571))},61611:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},78148:(e,s,a)=>{"use strict";a.d(s,{b:()=>n});var t=a(43210),r=a(14163),i=a(60687),l=t.forwardRef((e,s)=>(0,i.jsx)(r.sG.label,{...e,ref:s,onMouseDown:s=>{s.target.closest("button, input, select, textarea")||(e.onMouseDown?.(s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));l.displayName="Label";var n=l},78272:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},79551:e=>{"use strict";e.exports=require("url")},80013:(e,s,a)=>{"use strict";a.d(s,{J:()=>l});var t=a(60687);a(43210);var r=a(78148),i=a(4780);function l({className:e,...s}){return(0,t.jsx)(r.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...s})}},83721:(e,s,a)=>{"use strict";a.d(s,{Z:()=>r});var t=a(43210);function r(e){let s=t.useRef({value:e,previous:e});return t.useMemo(()=>(s.current.value!==e&&(s.current.previous=s.current.value,s.current.value=e),s.current.previous),[e])}},90270:(e,s,a)=>{"use strict";a.d(s,{bL:()=>N,zi:()=>k});var t=a(43210),r=a(70569),i=a(98599),l=a(11273),n=a(65551),c=a(83721),d=a(18853),o=a(14163),p=a(60687),x="Switch",[u,h]=(0,l.A)(x),[m,b]=u(x),j=t.forwardRef((e,s)=>{let{__scopeSwitch:a,name:l,checked:c,defaultChecked:d,required:u,disabled:h,value:b="on",onCheckedChange:j,form:v,...f}=e,[N,k]=t.useState(null),w=(0,i.s)(s,e=>k(e)),_=t.useRef(!1),A=!N||v||!!N.closest("form"),[C,q]=(0,n.i)({prop:c,defaultProp:d??!1,onChange:j,caller:x});return(0,p.jsxs)(m,{scope:a,checked:C,disabled:h,children:[(0,p.jsx)(o.sG.button,{type:"button",role:"switch","aria-checked":C,"aria-required":u,"data-state":y(C),"data-disabled":h?"":void 0,disabled:h,value:b,...f,ref:w,onClick:(0,r.m)(e.onClick,e=>{q(e=>!e),A&&(_.current=e.isPropagationStopped(),_.current||e.stopPropagation())})}),A&&(0,p.jsx)(g,{control:N,bubbles:!_.current,name:l,value:b,checked:C,required:u,disabled:h,form:v,style:{transform:"translateX(-100%)"}})]})});j.displayName=x;var v="SwitchThumb",f=t.forwardRef((e,s)=>{let{__scopeSwitch:a,...t}=e,r=b(v,a);return(0,p.jsx)(o.sG.span,{"data-state":y(r.checked),"data-disabled":r.disabled?"":void 0,...t,ref:s})});f.displayName=v;var g=t.forwardRef(({__scopeSwitch:e,control:s,checked:a,bubbles:r=!0,...l},n)=>{let o=t.useRef(null),x=(0,i.s)(o,n),u=(0,c.Z)(a),h=(0,d.X)(s);return t.useEffect(()=>{let e=o.current;if(!e)return;let s=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(u!==a&&s){let t=new Event("click",{bubbles:r});s.call(e,a),e.dispatchEvent(t)}},[u,a,r]),(0,p.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:a,...l,tabIndex:-1,ref:x,style:{...l.style,...h,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function y(e){return e?"checked":"unchecked"}g.displayName="SwitchBubbleInput";var N=j,k=f},93613:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},96474:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}};var s=require("../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[4447,8773,2762,3932,7801,408],()=>a(54748));module.exports=t})();